# 课程保存API实现总结

## 功能概述

实现了点击右下角"保存"按钮发送POST请求到 `/api/v1/course-management/courses` 来创建/更新课程的功能。

## 核心修改

### 1. 添加课程保存函数

```javascript
// 保存单个课程到后端
const saveCourse = async (course: CourseItem) => {
  try {
    // 验证必要的数据
    if (!course.title || course.title.trim() === '') {
      throw new Error('课程标题不能为空');
    }

    // 检查是否有未上传完成的文件
    const blobIssues = checkForBlobUrls(course);
    if (blobIssues.length > 0) {
      throw new Error(`以下文件尚未上传完成：${blobIssues.join('、')}`);
    }

    // 准备课程数据
    const courseData = {
      seriesId: seriesId,
      title: course.title,
      description: course.description || '',
      coverImage: course.coverImage || '',
      hasVideo: course.contentConfig?.video?.url ? 1 : 0,
      hasDocument: course.contentConfig?.document?.url ? 1 : 0,
      hasAudio: 0,
      videoDuration: 0,
      contentConfig: course.contentConfig || {},
      teachingInfo: convertTeachingInfoForAPI(course.teachingInfo || []),
      additionalResources: course.additionalResources || [],
      orderIndex: course.orderIndex || 1
    };

    let result: any;

    // 判断是新课程还是更新课程
    if (course.id && course.id > 1000000) {
      // 新课程，使用创建API
      const { data: response } = await courseApi.createCourse(courseData);
      result = response;
    } else {
      // 现有课程，使用更新API
      const { data: response } = await courseApi.updateCourse(course.id, courseData);
      result = response;
    }

    return result;
  } catch (error) {
    console.error('❌ 保存课程失败:', error);
    throw error;
  }
};
```

### 2. 添加blob URL检测函数

```javascript
// 检查是否有未上传完成的文件（blob URL）
const checkForBlobUrls = (course: CourseItem) => {
  const issues = [];
  
  // 检查封面图片
  if (course.coverImage && course.coverImage.startsWith('blob:')) {
    issues.push('课程封面图片');
  }
  
  // 检查视频文件
  if (course.contentConfig?.video?.url && course.contentConfig.video.url.startsWith('blob:')) {
    issues.push('课程视频');
  }
  
  // 检查文档附件
  if (course.contentConfig?.document?.url && course.contentConfig.document.url.startsWith('blob:')) {
    issues.push('课程文档');
  }
  
  // 检查教学材料
  if (course.additionalResources) {
    course.additionalResources.forEach((resource, index) => {
      if (resource.url && resource.url.startsWith('blob:')) {
        issues.push(`教学材料${index + 1}`);
      }
    });
  }

  return issues;
};
```

### 3. 增强保存主函数

```javascript
// 保存课程列表
const handleSave = async () => {
  try {
    // 检查是否有文件正在上传
    if (uploadingFiles.size > 0) {
      alert('有文件正在上传中，请等待上传完成后再保存');
      return;
    }

    console.log('💾 开始保存课程列表');

    // 如果有选中的课程且在编辑状态，先保存当前课程
    if (selectedCourseId && rightPanelType === 'course') {
      const selectedCourse = courseList.find(c => c.id === selectedCourseId);
      if (selectedCourse) {
        // 更新课程数据
        const updatedCourse = {
          ...selectedCourse,
          title: courseDetail.title,
          description: courseDetail.description,
          coverImage: courseDetail.coverImage,
          hasVideo: courseDetail.isVideoEnabled ? 1 : 0,
          hasDocument: courseDetail.isAttachmentEnabled ? 1 : 0,
          contentConfig: courseDetail.contentConfig,
          teachingInfo: courseDetail.teachingInfo,
          additionalResources: courseDetail.teachingMaterials?.map(material => ({
            title: material.name,
            url: material.url,
            description: material.name
          })) || []
        };

        // 先更新课程列表中的数据
        setCourseList(prev => prev.map(course =>
          course.id === selectedCourseId ? updatedCourse : course
        ));

        // 然后保存到后端
        await saveCourse(updatedCourse);
      }
    }

    // 保存系列课程信息
    const data = {
      title: editingTitle,
      courseGoals,
      courseObjectives,
      courseList
    };

    onSave(data);
    onClose();

    console.log('✅ 课程列表保存完成');
  } catch (error: any) {
    console.error('❌ 保存失败:', error);
    alert(`保存失败: ${error.message || '请重试'}`);
  }
};
```

### 4. 更新保存按钮UI

```javascript
<button 
  onClick={handleSave} 
  className="course-list-btn course-list-btn-save"
  disabled={uploadingFiles.size > 0}
  title={uploadingFiles.size > 0 ? '有文件正在上传中，请等待上传完成' : '保存课程'}
>
  {uploadingFiles.size > 0 ? '上传中...' : '保存'}
</button>
```

## API调用流程

### 1. 数据验证
- 检查课程标题是否为空
- 检查是否有文件正在上传
- 检查是否存在blob URL（未上传完成的文件）

### 2. 数据准备
```javascript
const courseData = {
  seriesId: 123,                    // 系列ID
  title: "课程标题",                 // 课程标题
  description: "课程描述",           // 课程描述
  coverImage: "https://...",        // 封面图片OSS URL
  hasVideo: 1,                      // 是否有视频 (0/1)
  hasDocument: 0,                   // 是否有文档 (0/1)
  hasAudio: 0,                      // 是否有音频 (0/1)
  videoDuration: 0,                 // 视频时长
  contentConfig: {                  // 内容配置
    video: {
      url: "https://...",           // 视频OSS URL
      name: "video.mp4"             // 视频文件名
    },
    document: {
      url: "https://...",           // 文档OSS URL
      name: "document.pdf"          // 文档文件名
    }
  },
  teachingInfo: [                   // 教学信息
    {
      title: "教学目标",
      content: ["目标1", "目标2"]
    }
  ],
  additionalResources: [            // 附加资源
    {
      title: "资源名称",
      url: "https://...",
      description: "资源描述"
    }
  ],
  orderIndex: 1                     // 排序索引
};
```

### 3. API调用
- **新课程**: `POST /api/v1/course-management/courses`
- **更新课程**: `PUT /api/v1/course-management/courses/{id}`

### 4. 响应处理
```javascript
// 成功响应示例
{
  "code": 200,
  "message": "课程创建成功",
  "data": {
    "id": 4291,
    "seriesId": 123,
    "title": "课程标题",
    "description": "课程描述",
    "coverImage": "https://logicleap.oss-cn-guangzhou.aliyuncs.com/uploads/...",
    "status": 0,
    "statusLabel": "草稿",
    "hasVideo": 1,
    "hasDocument": 0,
    "contentConfig": {
      "video": {
        "url": "https://logicleap.oss-cn-guangzhou.aliyuncs.com/uploads/...",
        "name": "video.mp4"
      }
    },
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

## 安全保障

### 1. 文件上传验证
- 确保所有文件都已上传到OSS
- 防止blob URL被保存到数据库
- 上传状态实时监控

### 2. 数据完整性
- 必填字段验证
- 数据格式验证
- 错误处理和用户提示

### 3. 用户体验
- 上传进度显示
- 保存状态反馈
- 错误信息提示
- 按钮状态控制

## 测试验证

创建了完整的测试页面 `course-save-test.html`，包含：

1. **课程信息填写**: 标题、描述、系列ID
2. **文件上传测试**: 封面图片、视频文件
3. **状态监控**: 上传状态、课程状态
4. **API调用模拟**: 请求数据格式、响应处理
5. **错误场景测试**: 验证失败、网络错误

## 使用流程

1. **填写课程信息**: 输入标题和描述
2. **上传文件**: 选择封面图片和视频文件
3. **等待上传完成**: 确保所有文件都上传到OSS
4. **点击保存**: 系统自动验证并调用API
5. **查看结果**: 成功保存或错误提示

通过这些修改，现在点击"保存"按钮会：
- ✅ 验证数据完整性
- ✅ 确保文件已上传到OSS
- ✅ 发送POST请求到正确的API
- ✅ 处理成功和错误响应
- ✅ 提供用户友好的反馈
