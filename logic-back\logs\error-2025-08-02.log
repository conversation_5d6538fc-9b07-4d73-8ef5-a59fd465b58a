2025-08-02 00:00:26.373 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":32224,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-02 00:00:26.376 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":32224,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-01T16:00:26.375Z"}
