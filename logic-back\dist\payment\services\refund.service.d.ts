import { RefundRequestDto, RefundQueryDto, RefundResultDto } from '../dto/refund-dto';
import { PaymentOrderService } from '../../util/database/mysql/payment_order/payment-order.service';
import { PaymentRefundService } from '../../util/database/mysql/payment_refund/payment-refund.service';
import { PaymentLogService } from '../../util/database/mysql/payment_log/payment-log.service';
import { AlipayStrategy } from '../strategies/alipay.strategy';
import { WechatPayStrategy } from '../strategies/wechat-pay.strategy';
import { NotifyService } from '../notification/notify.service';
import { LockManager } from '../lock/lock.manager';
export declare class RefundService {
    private readonly paymentOrderService;
    private readonly paymentRefundService;
    private readonly paymentLogService;
    private readonly alipayStrategy;
    private readonly wechatPayStrategy;
    private readonly notifyService;
    private readonly lockManager;
    private readonly logger;
    constructor(paymentOrderService: PaymentOrderService, paymentRefundService: PaymentRefundService, paymentLogService: PaymentLogService, alipayStrategy: AlipayStrategy, wechatPayStrategy: WechatPayStrategy, notifyService: NotifyService, lockManager: LockManager);
    private normalizePaymentChannel;
    private generateRefundNo;
    createRefund(userId: string, refundRequestDto: RefundRequestDto): Promise<RefundResultDto>;
    queryRefundStatus(refundQueryDto: RefundQueryDto): Promise<RefundResultDto>;
    checkRefundStatus(refundNo: string): Promise<RefundResultDto>;
}
