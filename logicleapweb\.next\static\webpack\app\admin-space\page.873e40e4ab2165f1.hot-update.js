"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-space/page",{

/***/ "(app-pages-browser)/./app/admin-space/components/course-management.tsx":
/*!**********************************************************!*\
  !*** ./app/admin-space/components/course-management.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/popconfirm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/upload/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EditOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/InboxOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/UploadOutlined.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst { Search } = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nconst { Option } = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\nconst CourseManagement = ()=>{\n    var _publishSeriesListForModal_find, _publishCourseListForModal_find;\n    _s();\n    const [courseList, setCourseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCourseModalVisible, setIsCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddCourseModalVisible, setIsAddCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditCourseModalVisible, setIsEditCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddSeriesModalVisible, setIsAddSeriesModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddTagModalVisible, setIsAddTagModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishSeriesModalVisible, setIsPublishSeriesModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishCourseModalVisible, setIsPublishCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingCourse, setEditingCourse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchKeyword, setSearchKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [teachers, setTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseTags, setCourseTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coverImageUrl, setCoverImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 新增：系列课程和子课程管理相关状态\n    const [seriesList, setSeriesList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [seriesCoursesMap, setSeriesCoursesMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [expandedSeries, setExpandedSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [seriesLoading, setSeriesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 发布课程相关状态\n    const [selectedSeriesForPublish, setSelectedSeriesForPublish] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [selectedCourseForPublish, setSelectedCourseForPublish] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [publishSeriesCourses, setPublishSeriesCourses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [publishLoading, setPublishLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishSeriesOptions, setPublishSeriesOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 新的发布课程状态\n    const [publishSeriesListForModal, setPublishSeriesListForModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [publishCourseListForModal, setPublishCourseListForModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [publishFormLoading, setPublishFormLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [courseSeries, setCourseSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseCoverImageUrl, setCourseCoverImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [additionalFiles, setAdditionalFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseVideoUrl, setCourseVideoUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseVideoName, setCourseVideoName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseDocumentUrl, setCourseDocumentUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseDocumentName, setCourseDocumentName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseAudioUrl, setCourseAudioUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseAudioName, setCourseAudioName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoDuration, setVideoDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [addCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [editCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [addSeriesForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [addTagForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [publishSeriesForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [publishCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__.GetNotification)();\n    // 获取系列课程列表\n    const fetchSeriesList = async ()=>{\n        try {\n            var _res_data;\n            setSeriesLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取系列课程列表成功:\", res.data.list);\n                setSeriesList(res.data.list);\n            } else {\n                console.error(\"❌ 获取系列课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程列表失败，请重试\");\n        } finally{\n            setSeriesLoading(false);\n        }\n    };\n    // 获取指定系列下的子课程列表\n    const fetchSeriesCourses = async (seriesId)=>{\n        try {\n            var _res_data;\n            console.log(\"\\uD83D\\uDCDD 获取系列子课程列表，系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getSeriesCourseList(seriesId, {\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取系列子课程列表成功:\", res.data.list);\n                setSeriesCoursesMap((prev)=>new Map(prev.set(seriesId, res.data.list)));\n                setExpandedSeries((prev)=>new Set(prev.add(seriesId)));\n            } else {\n                console.error(\"❌ 获取系列子课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取子课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列子课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取子课程列表失败，请重试\");\n        }\n    };\n    // 获取课程列表（保留原有功能）\n    const fetchCourseList = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDCDD 获取课程列表...\");\n            // 获取系列课程列表\n            await fetchSeriesList();\n        } catch (error) {\n            console.error(\"❌ 获取课程列表失败:\", error);\n            notification.error(\"获取课程列表失败，请重试\");\n        }\n    };\n    // 添加课程\n    const handleAddCourse = async (values)=>{\n        try {\n            // 构建内容配置，只包含有效的媒体文件\n            const contentConfig = {\n                hasVideo: courseVideoUrl ? 1 : 0,\n                hasDocument: courseDocumentUrl ? 1 : 0,\n                hasAudio: courseAudioUrl ? 1 : 0\n            };\n            if (courseVideoUrl) {\n                contentConfig.video = {\n                    url: courseVideoUrl,\n                    name: courseVideoName || \"课程视频.mp4\"\n                };\n            }\n            if (courseDocumentUrl) {\n                contentConfig.document = {\n                    url: courseDocumentUrl,\n                    name: courseDocumentName || \"课程文档.pdf\"\n                };\n            }\n            if (courseAudioUrl) {\n                contentConfig.audio = {\n                    url: courseAudioUrl,\n                    name: courseAudioName || \"课程音频.mp3\"\n                };\n            }\n            const courseData = {\n                seriesId: parseInt(values.seriesId),\n                title: values.title.trim(),\n                description: values.description.trim(),\n                coverImage: courseCoverImageUrl,\n                hasVideo: courseVideoUrl ? 1 : 0,\n                hasDocument: courseDocumentUrl ? 1 : 0,\n                hasAudio: courseAudioUrl ? 1 : 0,\n                videoDuration: videoDuration || 0,\n                contentConfig,\n                teachingInfo: values.teachingObjectives && values.teachingObjectives.length > 0 ? [\n                    {\n                        title: \"教学目标\",\n                        content: Array.isArray(values.teachingObjectives) ? values.teachingObjectives : [\n                            values.teachingObjectives\n                        ]\n                    }\n                ] : [],\n                additionalResources: additionalFiles.map((file)=>({\n                        title: file.split(\"/\").pop() || \"file\",\n                        url: file,\n                        description: \"课程附件资源\"\n                    })),\n                orderIndex: parseInt(values.orderIndex) || 0\n            };\n            // 验证必要字段\n            if (!courseData.seriesId) {\n                notification.error(\"请选择所属系列课程\");\n                return;\n            }\n            if (!courseData.title) {\n                notification.error(\"请输入课程名称\");\n                return;\n            }\n            if (!courseData.coverImage) {\n                notification.error(\"请上传课程封面\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDCE4 提交课程数据:\", courseData);\n            console.log(\"\\uD83D\\uDCCA 数据大小估算:\", JSON.stringify(courseData).length, \"字符\");\n            // 添加重试机制\n            let retryCount = 0;\n            const maxRetries = 2;\n            let lastError;\n            while(retryCount <= maxRetries){\n                try {\n                    const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourse(courseData);\n                    // 如果成功，跳出重试循环\n                    if (res.code === 200) {\n                        notification.success(\"创建课程成功\");\n                        fetchCourseList();\n                        setIsAddCourseModalVisible(false);\n                        addCourseForm.resetFields();\n                        setCourseCoverImageUrl(\"\");\n                        setAdditionalFiles([]);\n                        setCourseVideoUrl(\"\");\n                        setCourseVideoName(\"\");\n                        setCourseDocumentUrl(\"\");\n                        setCourseDocumentName(\"\");\n                        setCourseAudioUrl(\"\");\n                        setCourseAudioName(\"\");\n                        setVideoDuration(0);\n                        return;\n                    } else {\n                        notification.error(res.msg || \"创建课程失败\");\n                        return;\n                    }\n                } catch (error) {\n                    lastError = error;\n                    retryCount++;\n                    if (retryCount <= maxRetries) {\n                        console.log(\"\\uD83D\\uDD04 第\".concat(retryCount, \"次重试...\"));\n                        notification.warning(\"网络异常，正在重试 (\".concat(retryCount, \"/\").concat(maxRetries, \")\"));\n                        // 等待1秒后重试\n                        await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    }\n                }\n            }\n            // 如果所有重试都失败了，抛出最后的错误\n            throw lastError;\n        } catch (error) {\n            var _error_message, _error_response_data, _error_response, _error_response1, _error_response2, _error_response3, _error_response4, _error_response5;\n            console.error(\"❌ 创建课程失败:\", error);\n            // 更详细的错误处理\n            if (error.code === \"ECONNRESET\" || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"ECONNRESET\")) || ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) && error.response.data.message.includes(\"ECONNRESET\")) {\n                notification.error(\"网络连接中断，可能是网络不稳定或服务器繁忙。请稍后重试或联系管理员。\");\n            } else if (error.code === \"NETWORK_ERROR\") {\n                notification.error(\"网络错误，请检查网络连接\");\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 413) {\n                notification.error(\"上传文件过大，请压缩后重试\");\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 400) {\n                var _error_response_data1, _error_response6;\n                const errorMsg = ((_error_response6 = error.response) === null || _error_response6 === void 0 ? void 0 : (_error_response_data1 = _error_response6.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || error.message;\n                notification.error(\"请求参数错误: \".concat(errorMsg));\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 500) {\n                notification.error(\"服务器内部错误，请联系管理员\");\n            } else {\n                notification.error(\"创建课程失败: \".concat(error.message || \"请稍后重试\"));\n            }\n            console.log(\"\\uD83D\\uDD0D 完整错误信息:\", {\n                message: error.message,\n                code: error.code,\n                status: (_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status,\n                data: (_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.data\n            });\n        }\n    };\n    // 编辑课程\n    const handleEditCourse = async (values)=>{\n        if (!editingCourse) return;\n        try {\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.updateCourse(editingCourse.id, values);\n            if (res.code === 200) {\n                notification.success(\"更新课程成功\");\n                fetchCourseList();\n                setIsEditCourseModalVisible(false);\n                setEditingCourse(null);\n                editCourseForm.resetFields();\n            } else {\n                notification.error(res.msg || \"更新课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 更新课程失败:\", error);\n            notification.error(\"更新课程失败，请重试\");\n        }\n    };\n    // 删除课程\n    const handleDeleteCourse = async (courseId)=>{\n        try {\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.deleteCourse(courseId);\n            if (res.code === 200) {\n                notification.success(\"删除课程成功\");\n                fetchCourseList();\n            } else {\n                notification.error(res.msg || \"删除课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 删除课程失败:\", error);\n            notification.error(\"删除课程失败，请重试\");\n        }\n    };\n    // 删除子课程\n    const handleDeleteSubCourse = async (courseId, seriesId)=>{\n        try {\n            console.log(\"\\uD83D\\uDDD1️ 删除子课程，课程ID:\", courseId, \"系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.deleteCourse(courseId);\n            if (res.code === 200) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"删除子课程成功\");\n                console.log(\"✅ 子课程删除成功，重新获取系列子课程列表\");\n                // 重新获取该系列的子课程列表\n                await fetchSeriesCourses(seriesId);\n                console.log(\"\\uD83D\\uDD04 子课程列表已刷新\");\n            } else {\n                console.error(\"❌ 删除子课程失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"删除子课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 删除子课程异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"删除子课程失败，请重试\");\n        }\n    };\n    // 切换系列展开/收起状态\n    const toggleSeriesExpansion = async (seriesId)=>{\n        console.log(\"\\uD83D\\uDD04 切换系列展开状态，系列ID:\", seriesId);\n        console.log(\"\\uD83D\\uDCCA 当前展开状态:\", expandedSeries.has(seriesId));\n        if (expandedSeries.has(seriesId)) {\n            // 收起\n            console.log(\"\\uD83D\\uDCC1 收起系列:\", seriesId);\n            setExpandedSeries((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(seriesId);\n                return newSet;\n            });\n        } else {\n            // 展开，需要获取子课程数据\n            console.log(\"\\uD83D\\uDCC2 展开系列，获取子课程:\", seriesId);\n            await fetchSeriesCourses(seriesId);\n        }\n    };\n    // 展开所有系列\n    const expandAllSeries = async ()=>{\n        console.log(\"\\uD83D\\uDCC2 展开所有系列课程\");\n        for (const series of seriesList){\n            if (!expandedSeries.has(series.id)) {\n                await fetchSeriesCourses(series.id);\n            }\n        }\n    };\n    // 收起所有系列\n    const collapseAllSeries = ()=>{\n        console.log(\"\\uD83D\\uDCC1 收起所有系列课程\");\n        setExpandedSeries(new Set());\n    };\n    // 添加系列课程\n    const handleAddSeries = async (values)=>{\n        try {\n            const seriesData = {\n                ...values,\n                coverImage: coverImageUrl\n            };\n            console.log(\"创建系列课程数据:\", seriesData);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourseSeries(seriesData);\n            if (res.code === 200) {\n                notification.success(\"创建系列课程成功\");\n                fetchCourseList();\n                setIsAddSeriesModalVisible(false);\n                addSeriesForm.resetFields();\n                setCoverImageUrl(\"\");\n            } else {\n                notification.error(res.msg || \"创建系列课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 创建系列课程失败:\", error);\n            notification.error(\"创建系列课程失败，请重试\");\n        }\n    };\n    // 创建课程标签\n    const handleAddTag = async (values)=>{\n        try {\n            console.log(\"\\uD83C\\uDFF7️ 创建课程标签数据:\", values);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourseTag(values);\n            if (res.code === 200) {\n                notification.success(\"创建标签成功\");\n                setIsAddTagModalVisible(false);\n                addTagForm.resetFields();\n                // 重新获取标签列表\n                fetchCourseTags();\n            } else {\n                notification.error(res.msg || \"创建标签失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 创建标签失败:\", error);\n            notification.error(\"创建标签失败，请重试\");\n        }\n    };\n    // 发布系列课程\n    const handlePublishSeries = async (values)=>{\n        try {\n            console.log(\"\\uD83D\\uDCE2 发布系列课程数据:\", values);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourseSeries(values.seriesId);\n            if (res.code === 200) {\n                notification.success(\"发布系列课程成功\");\n                setIsPublishSeriesModalVisible(false);\n                publishSeriesForm.resetFields();\n                // 显示发布结果信息\n                const publishData = res.data;\n                console.log(\"✅ 发布成功，系列信息:\", publishData);\n                // 可以选择显示发布统计信息\n                if (publishData.publishStats) {\n                    const stats = publishData.publishStats;\n                    const statsMessage = \"已发布 \".concat(publishData.publishedCourses, \"/\").concat(publishData.totalCourses, \" 个课程，包含 \").concat(stats.videoCourseCount, \" 个视频课程，总时长 \").concat(Math.round(stats.totalVideoDuration / 60), \" 分钟\");\n                    notification.info(statsMessage);\n                }\n            } else {\n                notification.error(res.msg || \"发布系列课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 发布系列课程失败:\", error);\n            notification.error(\"发布系列课程失败，请重试\");\n        }\n    };\n    // 获取发布用的系列课程列表\n    const fetchSeriesForPublish = async ()=>{\n        try {\n            var _res_data;\n            setPublishLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取发布用系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取发布用系列课程列表成功:\", res.data.list);\n                return res.data.list;\n            } else {\n                console.error(\"❌ 获取发布用系列课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程列表失败\");\n                return [];\n            }\n        } catch (error) {\n            console.error(\"❌ 获取发布用系列课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程列表失败，请重试\");\n            return [];\n        } finally{\n            setPublishLoading(false);\n        }\n    };\n    // 获取指定系列的课程详情\n    const fetchSeriesDetailForPublish = async (seriesId)=>{\n        try {\n            setPublishLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取系列课程详情，系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeriesDetail(seriesId);\n            if (res.code === 200 && res.data) {\n                console.log(\"✅ 获取系列课程详情成功:\", res.data);\n                return res.data;\n            } else {\n                console.error(\"❌ 获取系列课程详情失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程详情失败\");\n                return null;\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列课程详情异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程详情失败，请重试\");\n            return null;\n        } finally{\n            setPublishLoading(false);\n        }\n    };\n    // 获取发布弹窗的系列课程列表\n    const fetchPublishSeriesList = async ()=>{\n        try {\n            var _res_data;\n            setPublishFormLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取发布弹窗的系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取发布弹窗系列课程列表成功:\", res.data.list);\n                setPublishSeriesListForModal(res.data.list);\n            } else {\n                console.error(\"❌ 获取发布弹窗系列课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取发布弹窗系列课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程列表失败，请重试\");\n        } finally{\n            setPublishFormLoading(false);\n        }\n    };\n    // 获取指定系列下的子课程列表（用于发布弹窗）\n    const fetchPublishCourseList = async (seriesId)=>{\n        try {\n            console.log(\"\\uD83D\\uDCDD 获取发布弹窗的子课程列表，系列ID:\", seriesId);\n            // 首先尝试使用课程市场API\n            try {\n                console.log(\"\\uD83D\\uDD04 尝试使用课程市场API获取系列详情...\");\n                const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeriesDetail(seriesId);\n                console.log(\"\\uD83D\\uDD0D 课程市场API响应:\", res);\n                if (res.code === 200 && res.data) {\n                    // 检查不同可能的数据结构\n                    let courseList = [];\n                    if (res.data.courseList) {\n                        courseList = res.data.courseList;\n                        console.log(\"✅ 从courseList字段获取子课程:\", courseList);\n                    } else if (res.data.courses) {\n                        courseList = res.data.courses;\n                        console.log(\"✅ 从courses字段获取子课程:\", courseList);\n                    } else if (Array.isArray(res.data)) {\n                        courseList = res.data;\n                        console.log(\"✅ 直接从data数组获取子课程:\", courseList);\n                    } else {\n                        console.log(\"⚠️ 课程市场API未找到子课程数据，尝试备用API...\");\n                        throw new Error(\"No course list found in marketplace API\");\n                    }\n                    if (courseList && courseList.length > 0) {\n                        console.log(\"✅ 设置子课程列表，数量:\", courseList.length);\n                        setPublishCourseListForModal(courseList);\n                        return;\n                    }\n                }\n            } catch (marketplaceError) {\n                console.log(\"⚠️ 课程市场API失败，尝试使用课程管理API...\");\n                // 备用方案：使用课程管理API\n                try {\n                    var _res2_data;\n                    const { data: res2 } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getSeriesCourseList(seriesId, {\n                        page: 1,\n                        pageSize: 50\n                    });\n                    console.log(\"\\uD83D\\uDD0D 课程管理API响应:\", res2);\n                    if (res2.code === 200 && ((_res2_data = res2.data) === null || _res2_data === void 0 ? void 0 : _res2_data.list)) {\n                        console.log(\"✅ 从课程管理API获取子课程列表成功:\", res2.data.list);\n                        setPublishCourseListForModal(res2.data.list);\n                        return;\n                    }\n                } catch (managementError) {\n                    console.error(\"❌ 课程管理API也失败:\", managementError);\n                }\n            }\n            // 如果所有API都失败\n            console.log(\"⚠️ 该系列暂无子课程或API调用失败\");\n            setPublishCourseListForModal([]);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"该系列暂无子课程\");\n        } catch (error) {\n            console.error(\"❌ 获取发布弹窗子课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取子课程列表失败，请重试\");\n            setPublishCourseListForModal([]);\n        }\n    };\n    // 处理系列选择（发布弹窗）\n    const handlePublishSeriesChange = async (seriesId)=>{\n        console.log(\"\\uD83D\\uDCDA 发布弹窗选择系列ID:\", seriesId);\n        console.log(\"\\uD83D\\uDCDA 当前系列列表:\", publishSeriesListForModal);\n        setSelectedSeriesForPublish(seriesId);\n        setSelectedCourseForPublish(undefined);\n        setPublishCourseListForModal([]);\n        // 重置表单中的课程选择\n        publishCourseForm.setFieldsValue({\n            courseId: undefined\n        });\n        // 获取该系列下的子课程\n        if (seriesId) {\n            console.log(\"\\uD83D\\uDD04 开始获取系列子课程...\");\n            setPublishFormLoading(true);\n            try {\n                await fetchPublishCourseList(seriesId);\n                console.log(\"✅ 子课程获取完成\");\n            } catch (error) {\n                console.error(\"❌ 子课程获取失败:\", error);\n            } finally{\n                setPublishFormLoading(false);\n            }\n        }\n    };\n    // 处理课程选择（发布弹窗）\n    const handlePublishCourseChange = (courseId)=>{\n        console.log(\"\\uD83D\\uDCD6 发布弹窗选择课程ID:\", courseId);\n        setSelectedCourseForPublish(courseId);\n    };\n    // 重置发布课程弹窗状态\n    const resetPublishCourseModal = ()=>{\n        setIsPublishCourseModalVisible(false);\n        setSelectedSeriesForPublish(undefined);\n        setSelectedCourseForPublish(undefined);\n        setPublishSeriesListForModal([]);\n        setPublishCourseListForModal([]);\n        publishCourseForm.resetFields();\n    };\n    // 打开发布课程弹窗\n    const openPublishCourseModal = async ()=>{\n        setIsPublishCourseModalVisible(true);\n        await fetchPublishSeriesList();\n    };\n    // 发布课程\n    const handlePublishCourse = async (values)=>{\n        try {\n            if (!selectedCourseForPublish || !selectedSeriesForPublish) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"请选择系列课程和子课程\");\n                return;\n            }\n            setPublishFormLoading(true);\n            console.log(\"\\uD83D\\uDCE2 发布课程，课程ID:\", selectedCourseForPublish);\n            console.log(\"\\uD83D\\uDCE2 系列ID:\", selectedSeriesForPublish);\n            console.log(\"\\uD83D\\uDCE4 表单数据:\", values);\n            // 获取当前选中的课程信息\n            const selectedCourse = publishCourseListForModal.find((c)=>c.id === selectedCourseForPublish);\n            if (!selectedCourse) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"未找到选中的课程信息\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDCD6 当前课程信息:\", selectedCourse);\n            // 使用专门的发布课程API\n            console.log(\"\\uD83D\\uDCE4 调用发布课程API，课程ID:\", selectedCourseForPublish);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourse(selectedCourseForPublish);\n            if (res.code === 200) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"发布课程成功\");\n                resetPublishCourseModal();\n                // 显示发布结果信息\n                console.log(\"✅ 发布成功，课程信息:\", res.data);\n                // 刷新课程列表\n                await fetchCourseList();\n                // 如果当前系列已展开，刷新子课程列表\n                if (selectedSeriesForPublish && expandedSeries.has(selectedSeriesForPublish)) {\n                    await fetchSeriesCourses(selectedSeriesForPublish);\n                }\n            } else {\n                console.error(\"❌ 发布课程失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"发布课程失败\");\n            }\n        } catch (error) {\n            var _error_response;\n            console.error(\"❌ 发布课程异常:\", error);\n            console.error(\"❌ 错误详情:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"发布课程失败，请重试\");\n        } finally{\n            setPublishFormLoading(false);\n        }\n    };\n    // 重置发布弹窗状态\n    const resetPublishModal = ()=>{\n        setIsPublishCourseModalVisible(false);\n        setSelectedSeriesForPublish(undefined);\n        setSelectedCourseForPublish(undefined);\n        setPublishSeriesCourses([]);\n        setPublishSeriesOptions([]);\n        publishCourseForm.resetFields();\n    };\n    // 处理图片上传\n    const handleImageUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"系列封面图片上传成功，URL:\", url);\n            setCoverImageUrl(url);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"图片上传成功\");\n        } catch (error) {\n            console.error(\"系列封面图片上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理图片删除\n    const handleImageRemove = async ()=>{\n        setCoverImageUrl(\"\");\n        return true;\n    };\n    // 处理课程封面图片上传\n    const handleCourseCoverUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程封面图片上传成功，URL:\", url);\n            setCourseCoverImageUrl(url);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程封面上传成功\");\n        } catch (error) {\n            console.error(\"课程封面图片上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理课程封面删除\n    const handleCourseCoverRemove = async ()=>{\n        setCourseCoverImageUrl(\"\");\n        return true;\n    };\n    // 处理附件资源上传\n    const handleAdditionalResourceUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"附件资源上传成功，URL:\", url);\n            setAdditionalFiles((prev)=>[\n                    ...prev,\n                    url\n                ]);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url,\n                name: file.name\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"附件 \".concat(file.name, \" 上传成功\"));\n        } catch (error) {\n            console.error(\"附件资源上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"附件 \".concat(file.name, \" 上传失败: \").concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理附件删除\n    const handleAdditionalResourceRemove = async (file)=>{\n        var _file_response;\n        const url = file.url || ((_file_response = file.response) === null || _file_response === void 0 ? void 0 : _file_response.url);\n        setAdditionalFiles((prev)=>prev.filter((f)=>f !== url));\n        return true;\n    };\n    // 处理视频上传\n    const handleVideoUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程视频上传成功，URL:\", url);\n            setCourseVideoUrl(url);\n            setCourseVideoName(file.name);\n            // 如果是视频文件，尝试获取时长\n            const videoElement = document.createElement(\"video\");\n            videoElement.src = url;\n            videoElement.onloadedmetadata = ()=>{\n                setVideoDuration(Math.floor(videoElement.duration));\n            };\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程视频上传成功\");\n        } catch (error) {\n            console.error(\"课程视频上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"视频上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理视频删除\n    const handleVideoRemove = async ()=>{\n        setCourseVideoUrl(\"\");\n        setCourseVideoName(\"\");\n        setVideoDuration(0);\n        return true;\n    };\n    // 处理文档上传\n    const handleDocumentUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程文档上传成功，URL:\", url);\n            setCourseDocumentUrl(url);\n            setCourseDocumentName(file.name);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程文档上传成功\");\n        } catch (error) {\n            console.error(\"课程文档上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"文档上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理文档删除\n    const handleDocumentRemove = async ()=>{\n        setCourseDocumentUrl(\"\");\n        setCourseDocumentName(\"\");\n        return true;\n    };\n    // 处理音频上传\n    const handleAudioUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程音频上传成功，URL:\", url);\n            setCourseAudioUrl(url);\n            setCourseAudioName(file.name);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程音频上传成功\");\n        } catch (error) {\n            console.error(\"课程音频上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"音频上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理音频删除\n    const handleAudioRemove = async ()=>{\n        setCourseAudioUrl(\"\");\n        setCourseAudioName(\"\");\n        return true;\n    };\n    // 打开编辑模态框\n    const openEditModal = async (course)=>{\n        setEditingCourse(course);\n        editCourseForm.setFieldsValue(course);\n        setIsEditCourseModalVisible(true);\n    };\n    // 过滤课程列表\n    const filteredCourses = (courseList || []).filter((course)=>course.name.toLowerCase().includes(searchKeyword.toLowerCase()) || course.description.toLowerCase().includes(searchKeyword.toLowerCase()) || course.category.toLowerCase().includes(searchKeyword.toLowerCase()));\n    // 准备表格数据：将系列课程和子课程合并为一个扁平列表\n    const prepareTableData = ()=>{\n        const tableData = [];\n        console.log(\"\\uD83D\\uDD04 准备表格数据...\");\n        console.log(\"\\uD83D\\uDCCA 系列课程列表:\", seriesList);\n        console.log(\"\\uD83D\\uDCCA 展开的系列:\", Array.from(expandedSeries));\n        console.log(\"\\uD83D\\uDCCA 子课程映射:\", seriesCoursesMap);\n        seriesList.forEach((series)=>{\n            // 添加系列课程行\n            tableData.push({\n                key: \"series-\".concat(series.id),\n                id: series.id,\n                title: series.title,\n                status: series.status,\n                type: \"series\",\n                isExpanded: expandedSeries.has(series.id),\n                seriesId: series.id\n            });\n            // 如果系列已展开，添加子课程行\n            if (expandedSeries.has(series.id)) {\n                const subCourses = seriesCoursesMap.get(series.id) || [];\n                console.log(\"\\uD83D\\uDCDA 系列 \".concat(series.id, \" 的子课程:\"), subCourses);\n                subCourses.forEach((course)=>{\n                    tableData.push({\n                        key: \"course-\".concat(course.id),\n                        id: course.id,\n                        title: course.title,\n                        status: course.status,\n                        type: \"course\",\n                        seriesId: series.id,\n                        parentSeriesTitle: series.title\n                    });\n                });\n            }\n        });\n        console.log(\"\\uD83D\\uDCCB 最终表格数据:\", tableData);\n        return tableData;\n    };\n    // 表格列定义\n    const columns = [\n        {\n            title: \"系列课程ID\",\n            dataIndex: \"id\",\n            key: \"id\",\n            width: 120\n        },\n        {\n            title: \"系列课程/子课程名称\",\n            dataIndex: \"title\",\n            key: \"title\",\n            render: (text, record)=>{\n                if (record.type === \"series\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                type: \"text\",\n                                size: \"small\",\n                                onClick: ()=>toggleSeriesExpansion(record.id),\n                                className: \"p-0 min-w-0 hover:bg-blue-50\",\n                                style: {\n                                    minWidth: \"20px\",\n                                    height: \"20px\"\n                                },\n                                children: record.isExpanded ? \"▼\" : \"▶\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 981,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-blue-600 text-base\",\n                                children: text\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 990,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                color: \"blue\",\n                                className: \"text-xs\",\n                                children: \"系列\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 991,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 980,\n                        columnNumber: 13\n                    }, undefined);\n                } else {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-8 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400\",\n                                children: \"└─\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 997,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-700\",\n                                children: text\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 998,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                color: \"green\",\n                                className: \"text-xs\",\n                                children: \"子课程\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 999,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 996,\n                        columnNumber: 13\n                    }, undefined);\n                }\n            }\n        },\n        {\n            title: \"发布状态\",\n            dataIndex: \"status\",\n            key: \"status\",\n            width: 100,\n            render: (status, record)=>{\n                const getStatusConfig = (status)=>{\n                    switch(status){\n                        case 1:\n                            return {\n                                color: \"green\",\n                                text: \"已发布\"\n                            };\n                        case 0:\n                            return {\n                                color: \"orange\",\n                                text: \"草稿\"\n                            };\n                        case 2:\n                            return {\n                                color: \"red\",\n                                text: \"已归档\"\n                            };\n                        default:\n                            return {\n                                color: \"gray\",\n                                text: \"未知\"\n                            };\n                    }\n                };\n                const config = getStatusConfig(status);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    color: config.color,\n                    children: config.text\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1021,\n                    columnNumber: 16\n                }, undefined);\n            }\n        },\n        {\n            title: \"操作\",\n            key: \"action\",\n            width: 150,\n            render: (record)=>{\n                if (record.type === \"series\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            type: \"link\",\n                            size: \"small\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1035,\n                                columnNumber: 23\n                            }, void 0),\n                            onClick: ()=>{\n                                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"系列课程编辑功能待实现\");\n                            },\n                            children: \"编辑\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1032,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1031,\n                        columnNumber: 13\n                    }, undefined);\n                } else {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        size: \"small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                type: \"link\",\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1050,\n                                    columnNumber: 23\n                                }, void 0),\n                                onClick: ()=>{\n                                    _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"子课程编辑功能待实现\");\n                                },\n                                className: \"text-blue-600 hover:text-blue-800\",\n                                children: \"编辑\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1047,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"确定要删除这个子课程吗？\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1061,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: [\n                                                \"课程名称：\",\n                                                record.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1062,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: [\n                                                \"所属系列：\",\n                                                record.parentSeriesTitle\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1063,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1060,\n                                    columnNumber: 19\n                                }, void 0),\n                                onConfirm: ()=>{\n                                    console.log(\"\\uD83D\\uDDD1️ 用户确认删除子课程:\", record);\n                                    handleDeleteSubCourse(record.id, record.seriesId);\n                                },\n                                okText: \"确定删除\",\n                                cancelText: \"取消\",\n                                okType: \"danger\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    type: \"link\",\n                                    size: \"small\",\n                                    danger: true,\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1078,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    className: \"text-red-600 hover:text-red-800\",\n                                    children: \"删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1074,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1058,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1046,\n                        columnNumber: 13\n                    }, undefined);\n                }\n            }\n        }\n    ];\n    // 获取教师列表\n    // const fetchTeachers = async () => {\n    //   try {\n    //     const { data: res } = await courseApi.getTeachers();\n    //     if (res.code === 200) {\n    //       setTeachers(res.data);\n    //       console.log('成功获取教师列表:', res.data);\n    //     } else {\n    //       console.log('API返回无数据，使用模拟教师数据');\n    //       // 使用模拟数据\n    //       const mockTeachers = [\n    //         { id: 1, name: '张老师', email: '<EMAIL>', subject: '数学', school: '实验小学', avatar: '', phone: '13800138001' },\n    //         { id: 2, name: '李老师', email: '<EMAIL>', subject: '语文', school: '实验小学', avatar: '', phone: '13800138002' },\n    //         { id: 3, name: '王老师', email: '<EMAIL>', subject: '英语', school: '第二小学', avatar: '', phone: '13800138003' },\n    //         { id: 4, name: '赵老师', email: '<EMAIL>', subject: '科学', school: '第二小学', avatar: '', phone: '13800138004' },\n    //         { id: 5, name: '刘老师', email: '<EMAIL>', subject: '编程', school: '实验中学', avatar: '', phone: '13800138005' },\n    //         { id: 6, name: '陈老师', email: '<EMAIL>', subject: '信息技术', school: '实验中学', avatar: '', phone: '13800138006' }\n    //       ];\n    //       setTeachers(mockTeachers);\n    //     }\n    //   } catch (error) {\n    //     console.error('获取教师列表失败:', error);\n    //     // 使用模拟数据\n    //     const mockTeachers = [\n    //       { id: 1, name: '张老师', email: '<EMAIL>', subject: '数学', school: '实验小学', avatar: '', phone: '13800138001' },\n    //       { id: 2, name: '李老师', email: '<EMAIL>', subject: '语文', school: '实验小学', avatar: '', phone: '13800138002' },\n    //       { id: 3, name: '王老师', email: '<EMAIL>', subject: '英语', school: '第二小学', avatar: '', phone: '13800138003' },\n    //       { id: 4, name: '赵老师', email: '<EMAIL>', subject: '科学', school: '第二小学', avatar: '', phone: '13800138004' },\n    //       { id: 5, name: '刘老师', email: '<EMAIL>', subject: '编程', school: '实验中学', avatar: '', phone: '13800138005' },\n    //       { id: 6, name: '陈老师', email: '<EMAIL>', subject: '信息技术', school: '实验中学', avatar: '', phone: '13800138006' }\n    //     ];\n    //     setTeachers(mockTeachers);\n    //     console.log('使用模拟教师数据:', mockTeachers);\n    //   }\n    // };\n    // 获取课程标签列表 - 使用课程市场API\n    const fetchCourseTags = async ()=>{\n        try {\n            console.log(\"\\uD83C\\uDFF7️ 开始获取课程标签列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseTags({\n                page: 1,\n                pageSize: 100,\n                status: 1 // 只获取启用的标签\n            });\n            console.log(\"\\uD83D\\uDCE8 getCourseTags API响应:\", res);\n            if (res.code === 200 && res.data && res.data.list) {\n                const tags = res.data.list.map((tag)=>({\n                        id: tag.id,\n                        name: tag.name,\n                        color: tag.color,\n                        category: tag.category,\n                        description: tag.description || \"\"\n                    }));\n                setCourseTags(tags);\n                console.log(\"✅ 成功获取课程标签列表:\", tags);\n            } else {\n                console.warn(\"⚠️ API返回数据格式异常:\", res);\n                setCourseTags([]);\n                notification.warning(\"获取标签列表失败，请检查网络连接\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取课程标签失败:\", error);\n            setCourseTags([]);\n            notification.error(\"获取标签列表失败，请重试\");\n        }\n    };\n    // 获取课程系列列表 - 使用课程市场API\n    const fetchCourseSeries = async ()=>{\n        try {\n            var _res_data_pagination, _res_data;\n            console.log(\"\\uD83D\\uDD04 开始获取课程市场系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50 // 课程市场API限制最大50\n            });\n            console.log(\"\\uD83D\\uDCE8 getMarketplaceSeries API响应:\", res);\n            // 检查是否有更多数据\n            if (((_res_data = res.data) === null || _res_data === void 0 ? void 0 : (_res_data_pagination = _res_data.pagination) === null || _res_data_pagination === void 0 ? void 0 : _res_data_pagination.total) > 50) {\n                console.log(\"⚠️ 注意：总共有 \".concat(res.data.pagination.total, \" 个系列课程，当前只显示前50个\"));\n            }\n            if (res.code === 200 && res.data) {\n                console.log(\"\\uD83D\\uDCCA API返回的完整数据结构:\", res.data);\n                if (res.data.list && Array.isArray(res.data.list)) {\n                    console.log(\"\\uD83D\\uDCCB 获取到 \".concat(res.data.list.length, \" 个系列课程\"));\n                    // 将课程市场API返回的数据转换为组件需要的格式\n                    const formattedSeries = res.data.list.map((item, index)=>{\n                        var _item_tags;\n                        console.log(\"\\uD83D\\uDD0D 处理第 \".concat(index + 1, \" 个系列:\"), {\n                            id: item.id,\n                            title: item.title,\n                            category: item.category,\n                            categoryLabel: item.categoryLabel,\n                            tags: item.tags\n                        });\n                        return {\n                            id: item.id,\n                            title: item.title,\n                            description: item.description,\n                            coverImage: item.coverImage || \"\",\n                            category: item.categoryLabel || (item.category === 0 ? \"官方\" : \"社区\"),\n                            teacherIds: [],\n                            tagIds: ((_item_tags = item.tags) === null || _item_tags === void 0 ? void 0 : _item_tags.map((tag)=>tag.id)) || [],\n                            createdAt: item.createdAt || new Date().toISOString(),\n                            updatedAt: item.updatedAt || new Date().toISOString()\n                        };\n                    });\n                    setCourseSeries(formattedSeries);\n                    console.log(\"✅ 成功获取系列课程列表:\", formattedSeries);\n                } else {\n                    console.warn(\"⚠️ API返回数据中没有list字段或list不是数组:\", res.data);\n                    setCourseSeries([]);\n                }\n            } else {\n                console.warn(\"⚠️ API返回数据格式异常:\", {\n                    code: res.code,\n                    message: res.message,\n                    data: res.data\n                });\n                setCourseSeries([]);\n            }\n        } catch (error) {\n            console.error(\"❌ 获取课程系列失败:\", error);\n            setCourseSeries([]);\n            notification.error(\"获取系列课程列表失败，请重试\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchCourseList();\n        fetchCourseTags();\n        fetchCourseSeries();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                title: \"课程管理\",\n                extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    type: \"primary\",\n                    onClick: ()=>{\n                        fetchCourseList();\n                        setIsCourseModalVisible(true);\n                    },\n                    children: \"查看全部\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1239,\n                    columnNumber: 16\n                }, void 0),\n                className: \"shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddCourseModalVisible(true),\n                            children: \"添加课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1246,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddSeriesModalVisible(true),\n                            children: \"添加系列课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1249,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddTagModalVisible(true),\n                            type: \"dashed\",\n                            children: \"添加课程标签\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1252,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: openPublishCourseModal,\n                            style: {\n                                backgroundColor: \"white\",\n                                borderColor: \"#d9d9d9\",\n                                color: \"#000000d9\"\n                            },\n                            children: \"发布课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1255,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsPublishSeriesModalVisible(true),\n                            style: {\n                                backgroundColor: \"white\",\n                                borderColor: \"#d9d9d9\",\n                                color: \"#000000d9\"\n                            },\n                            children: \"发布系列课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1258,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1245,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1237,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"课程管理\",\n                open: isCourseModalVisible,\n                onCancel: ()=>setIsCourseModalVisible(false),\n                footer: null,\n                width: 1000,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Search, {\n                                        placeholder: \"搜索系列课程名称\",\n                                        allowClear: true,\n                                        style: {\n                                            width: 300\n                                        },\n                                        onSearch: setSearchKeyword,\n                                        onChange: (e)=>setSearchKeyword(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1274,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                type: \"primary\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1284,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                onClick: ()=>setIsAddCourseModalVisible(true),\n                                                children: \"添加课程\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1282,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                type: \"default\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1291,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                onClick: ()=>setIsAddSeriesModalVisible(true),\n                                                children: \"添加系列课程\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1289,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1281,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1273,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center bg-gray-50 p-3 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-4 text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"系列课程总数: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"text-blue-600\",\n                                                        children: seriesList.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                        lineNumber: 1302,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1302,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"已展开系列: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"text-green-600\",\n                                                        children: expandedSeries.size\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                        lineNumber: 1303,\n                                                        columnNumber: 28\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1303,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"已加载子课程: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"text-orange-600\",\n                                                        children: Array.from(seriesCoursesMap.values()).reduce((total, courses)=>total + courses.length, 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                        lineNumber: 1304,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1304,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1301,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: \"small\",\n                                                type: \"text\",\n                                                onClick: expandAllSeries,\n                                                disabled: seriesLoading,\n                                                className: \"text-blue-600 hover:text-blue-800\",\n                                                children: \"展开所有\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1310,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: \"small\",\n                                                type: \"text\",\n                                                onClick: collapseAllSeries,\n                                                disabled: seriesLoading,\n                                                className: \"text-gray-600 hover:text-gray-800\",\n                                                children: \"收起所有\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1319,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1309,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1300,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1272,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        columns: columns,\n                        dataSource: prepareTableData(),\n                        rowKey: \"key\",\n                        loading: seriesLoading,\n                        pagination: {\n                            pageSize: 20,\n                            showSizeChanger: false,\n                            showTotal: (total)=>\"共 \".concat(total, \" 条记录\")\n                        },\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1332,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1265,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"添加课程\",\n                open: isAddCourseModalVisible,\n                onCancel: ()=>{\n                    setIsAddCourseModalVisible(false);\n                    addCourseForm.resetFields();\n                    setCourseCoverImageUrl(\"\");\n                    setAdditionalFiles([]);\n                    setCourseVideoUrl(\"\");\n                    setCourseVideoName(\"\");\n                    setCourseDocumentUrl(\"\");\n                    setCourseDocumentName(\"\");\n                    setCourseAudioUrl(\"\");\n                    setCourseAudioName(\"\");\n                    setVideoDuration(0);\n                },\n                onOk: ()=>addCourseForm.submit(),\n                okText: \"确定\",\n                cancelText: \"取消\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddCourse,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"所属系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择所属系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                showSearch: true,\n                                optionFilterProp: \"children\",\n                                style: {\n                                    width: \"100%\"\n                                },\n                                children: courseSeries.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        title: \"\".concat(series.title, \" - \").concat(series.description),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                overflow: \"hidden\",\n                                                textOverflow: \"ellipsis\",\n                                                whiteSpace: \"nowrap\",\n                                                maxWidth: \"100%\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontWeight: 500\n                                                    },\n                                                    children: series.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1391,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"12px\",\n                                                        color: \"#666\",\n                                                        marginLeft: \"8px\"\n                                                    },\n                                                    children: [\n                                                        \"(\",\n                                                        series.category,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1392,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1385,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, series.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1384,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1377,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1372,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"title\",\n                            label: \"课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程名称\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1406,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1401,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程描述\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程描述\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 4,\n                                placeholder: \"请详细描述课程内容、目标和特色...\",\n                                showCount: true,\n                                maxLength: 500\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1414,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1409,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程封面\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请上传课程封面\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseCover\",\n                                customRequest: handleCourseCoverUpload,\n                                onRemove: handleCourseCoverRemove,\n                                accept: \"image/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseCoverImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: courseCoverImageUrl,\n                                        alt: \"课程封面预览\",\n                                        style: {\n                                            width: \"100%\",\n                                            maxHeight: \"200px\",\n                                            objectFit: \"cover\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1436,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1435,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1441,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1440,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1443,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持单个文件上传，建议上传jpg、png格式图片，大小不超过2MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1444,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1439,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1426,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1422,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"orderIndex\",\n                            label: \"课程序号\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程序号\"\n                                },\n                                {\n                                    type: \"number\",\n                                    min: 0,\n                                    message: \"课程序号必须大于等于0\",\n                                    transform: (value)=>Number(value)\n                                }\n                            ],\n                            tooltip: \"在系列课程中的排序位置，数字越小排序越靠前，从0开始\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                type: \"number\",\n                                placeholder: \"请输入课程在系列中的序号（从0开始）\",\n                                min: 0\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1466,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1452,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程视频\",\n                            tooltip: \"上传课程视频文件，系统将自动识别时长等信息\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseVideo\",\n                                customRequest: handleVideoUpload,\n                                onRemove: handleVideoRemove,\n                                accept: \"video/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseVideoUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            src: courseVideoUrl,\n                                            style: {\n                                                width: \"100%\",\n                                                maxHeight: \"200px\"\n                                            },\n                                            controls: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1486,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                marginTop: 8,\n                                                color: \"#666\"\n                                            },\n                                            children: courseVideoName || \"课程视频\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1491,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1485,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1498,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1497,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽视频文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1500,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持MP4、AVI、MOV等格式，大小不超过100MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1501,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1496,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1476,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1472,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程文档\",\n                            tooltip: \"上传课程相关文档，如PPT、PDF等\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseDocument\",\n                                customRequest: handleDocumentUpload,\n                                onRemove: handleDocumentRemove,\n                                accept: \".pdf,.doc,.docx,.ppt,.pptx\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseDocumentUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"20px\",\n                                            textAlign: \"center\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                style: {\n                                                    fontSize: \"48px\",\n                                                    color: \"#1890ff\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1525,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    marginTop: 8,\n                                                    color: \"#666\"\n                                                },\n                                                children: courseDocumentName || \"课程文档\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1526,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1524,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1523,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1534,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1533,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文档文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1536,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持PDF、Word、PPT格式，大小不超过50MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1537,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1532,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1514,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1510,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程音频\",\n                            tooltip: \"上传课程音频文件\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseAudio\",\n                                customRequest: handleAudioUpload,\n                                onRemove: handleAudioRemove,\n                                accept: \"audio/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseAudioUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                                            src: courseAudioUrl,\n                                            style: {\n                                                width: \"100%\"\n                                            },\n                                            controls: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1560,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                marginTop: 8,\n                                                color: \"#666\"\n                                            },\n                                            children: courseAudioName || \"课程音频\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1565,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1559,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1572,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1571,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽音频文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1574,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持MP3、WAV、AAC等格式，大小不超过50MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1575,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1570,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1550,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1546,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"teachingObjectives\",\n                            label: \"教学目标\",\n                            tooltip: \"学员完成本课程后应该达到的学习目标\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                mode: \"tags\",\n                                placeholder: \"理解Node.js的基本概念和特点，掌握Node.js的安装和环境配置\",\n                                style: {\n                                    width: \"100%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1588,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1583,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"附件资源\",\n                            tooltip: \"上传课程相关的附件资源，如PPT、文档、代码等\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    name: \"additionalResources\",\n                                    customRequest: handleAdditionalResourceUpload,\n                                    onRemove: handleAdditionalResourceRemove,\n                                    multiple: true,\n                                    accept: \".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.zip,.rar,.txt\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1606,\n                                            columnNumber: 29\n                                        }, void 0),\n                                        children: \"上传附件资源\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1606,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1599,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: \"12px\",\n                                        color: \"#666\",\n                                        marginTop: 4\n                                    },\n                                    children: \"支持上传PDF、Office文档、压缩包等格式文件，单个文件不超过10MB\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1608,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1595,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1367,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1347,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"编辑课程\",\n                open: isEditCourseModalVisible,\n                onCancel: ()=>{\n                    setIsEditCourseModalVisible(false);\n                    setEditingCourse(null);\n                    editCourseForm.resetFields();\n                },\n                onOk: ()=>editCourseForm.submit(),\n                okText: \"确定\",\n                cancelText: \"取消\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: editCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handleEditCourse,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"name\",\n                            label: \"课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程名称\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1638,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1633,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程描述\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程描述\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 3,\n                                placeholder: \"请输入课程描述\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1646,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1641,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"课程分类\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择课程分类\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择课程分类\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"编程基础\",\n                                        children: \"编程基础\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1655,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"编程进阶\",\n                                        children: \"编程进阶\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1656,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"算法思维\",\n                                        children: \"算法思维\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1657,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"项目实战\",\n                                        children: \"项目实战\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1658,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1654,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1649,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"status\",\n                            label: \"课程状态\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择课程状态\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"active\",\n                                        children: \"启用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1668,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"inactive\",\n                                        children: \"禁用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1669,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1667,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1662,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1628,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1616,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"创建系列课程\",\n                open: isAddSeriesModalVisible,\n                onCancel: ()=>{\n                    setIsAddSeriesModalVisible(false);\n                    addSeriesForm.resetFields();\n                    setCoverImageUrl(\"\");\n                },\n                onOk: ()=>addSeriesForm.submit(),\n                okText: \"创建系列课程\",\n                cancelText: \"取消\",\n                width: 800,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addSeriesForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddSeries,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"title\",\n                            label: \"系列课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入系列课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"例如：React全栈开发实战\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1699,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1694,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程介绍\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程介绍\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 4,\n                                placeholder: \"请详细描述系列课程的内容、目标和特色...\",\n                                showCount: true,\n                                maxLength: 500\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1707,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1702,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"封面图片\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请上传封面图片\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"coverImage\",\n                                customRequest: handleImageUpload,\n                                onRemove: handleImageRemove,\n                                accept: \"image/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: coverImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: coverImageUrl,\n                                        alt: \"封面预览\",\n                                        style: {\n                                            width: \"100%\",\n                                            maxHeight: \"200px\",\n                                            objectFit: \"cover\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1729,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1728,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1734,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1733,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1736,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持单个文件上传，建议上传jpg、png格式图片，大小不超过2MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1737,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1732,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1719,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1715,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"是否为官方系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择是否为官方系列课程\"\n                                }\n                            ],\n                            initialValue: 0,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"是（官方）\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1754,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"否（社区）\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1755,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1753,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1747,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"projectMembers\",\n                            label: \"课程成员\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程成员\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程成员，如：王老师、李助教、张同学\",\n                                showCount: true,\n                                maxLength: 200\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1764,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1759,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"tagIds\",\n                            label: \"标签选择\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                mode: \"multiple\",\n                                placeholder: \"请选择相关标签\",\n                                optionLabelProp: \"label\",\n                                children: courseTags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: tag.id,\n                                        label: tag.name,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            color: tag.color,\n                                            children: tag.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1783,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, tag.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1782,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1776,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1771,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1689,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1676,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"创建课程标签\",\n                open: isAddTagModalVisible,\n                onCancel: ()=>{\n                    setIsAddTagModalVisible(false);\n                    addTagForm.resetFields();\n                },\n                onOk: ()=>addTagForm.submit(),\n                okText: \"创建标签\",\n                cancelText: \"取消\",\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addTagForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddTag,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"name\",\n                            label: \"标签名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入标签名称\"\n                                },\n                                {\n                                    max: 20,\n                                    message: \"标签名称不能超过20个字符\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"例如：高级、编程、实战\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1817,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1809,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"color\",\n                            label: \"标签颜色\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签颜色\"\n                                }\n                            ],\n                            initialValue: \"#007bff\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签颜色\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#007bff\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#007bff\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1829,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"蓝色 (#007bff)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1828,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1827,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#28a745\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#28a745\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1835,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"绿色 (#28a745)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1834,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1833,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#dc3545\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#dc3545\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1841,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"红色 (#dc3545)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1840,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1839,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#ffc107\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#ffc107\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1847,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"黄色 (#ffc107)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1846,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1845,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#6f42c1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#6f42c1\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1853,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"紫色 (#6f42c1)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1852,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1851,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#fd7e14\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#fd7e14\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1859,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"橙色 (#fd7e14)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1858,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1857,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#20c997\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#20c997\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1865,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"青色 (#20c997)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1864,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1863,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#6c757d\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#6c757d\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1871,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"灰色 (#6c757d)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1870,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1869,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1826,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1820,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"标签分类\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签分类\"\n                                }\n                            ],\n                            initialValue: 1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签分类\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"难度标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1885,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"类型标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1886,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 2,\n                                        children: \"特色标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1887,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 3,\n                                        children: \"其他标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1888,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1884,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1878,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"标签描述\",\n                            rules: [\n                                {\n                                    max: 100,\n                                    message: \"标签描述不能超过100个字符\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 3,\n                                placeholder: \"请输入标签的详细描述...\",\n                                showCount: true,\n                                maxLength: 100\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1897,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1892,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"status\",\n                            label: \"标签状态\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签状态\"\n                                }\n                            ],\n                            initialValue: 1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签状态\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"启用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1912,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"禁用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1913,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1911,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1905,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1804,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1792,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"发布系列课程\",\n                open: isPublishSeriesModalVisible,\n                onCancel: ()=>{\n                    setIsPublishSeriesModalVisible(false);\n                    publishSeriesForm.resetFields();\n                },\n                onOk: ()=>publishSeriesForm.submit(),\n                okText: \"发布系列\",\n                cancelText: \"取消\",\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: publishSeriesForm,\n                    layout: \"vertical\",\n                    onFinish: handlePublishSeries,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"选择要发布的系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择要发布的系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                showSearch: true,\n                                filterOption: (input, option)=>{\n                                    var _option_children;\n                                    return option === null || option === void 0 ? void 0 : (_option_children = option.children) === null || _option_children === void 0 ? void 0 : _option_children.toLowerCase().includes(input.toLowerCase());\n                                },\n                                children: courseSeries.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        children: [\n                                            series.title,\n                                            \" (\",\n                                            series.category,\n                                            \")\"\n                                        ]\n                                    }, series.id, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1950,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1942,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1937,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"publishNote\",\n                            label: \"发布说明\",\n                            rules: [\n                                {\n                                    required: false\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                placeholder: \"请输入发布说明（可选）\",\n                                rows: 3,\n                                maxLength: 200,\n                                showCount: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1962,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1957,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"发布说明：\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1971,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-gray-600 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布后系列课程将在课程市场中公开显示\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1973,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 只有已完成的课程才会被发布\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1974,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布后可以查看详细的发布统计信息\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1975,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布状态可以随时修改\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1976,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1972,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1970,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1932,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1920,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"发布课程\",\n                open: isPublishCourseModalVisible,\n                onCancel: resetPublishCourseModal,\n                footer: null,\n                width: 700,\n                destroyOnClose: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: publishCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handlePublishCourse,\n                    className: \"mt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"选择系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                loading: publishFormLoading,\n                                onChange: handlePublishSeriesChange,\n                                showSearch: true,\n                                filterOption: (input, option)=>{\n                                    var _option_children;\n                                    return option === null || option === void 0 ? void 0 : (_option_children = option.children) === null || _option_children === void 0 ? void 0 : _option_children.toLowerCase().includes(input.toLowerCase());\n                                },\n                                children: publishSeriesListForModal.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        children: [\n                                            series.title,\n                                            \" (ID: \",\n                                            series.id,\n                                            \")\"\n                                        ]\n                                    }, series.id, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2013,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2003,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1998,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"courseId\",\n                            label: \"选择子课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择要发布的子课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: selectedSeriesForPublish ? \"请选择要发布的子课程\" : \"请先选择系列课程\",\n                                disabled: !selectedSeriesForPublish,\n                                loading: publishFormLoading && !!selectedSeriesForPublish,\n                                onChange: handlePublishCourseChange,\n                                showSearch: true,\n                                filterOption: (input, option)=>{\n                                    var _option_children;\n                                    return option === null || option === void 0 ? void 0 : (_option_children = option.children) === null || _option_children === void 0 ? void 0 : _option_children.toLowerCase().includes(input.toLowerCase());\n                                },\n                                notFoundContent: publishFormLoading && selectedSeriesForPublish ? \"正在加载子课程...\" : selectedSeriesForPublish ? \"该系列暂无子课程\" : \"请先选择系列课程\",\n                                children: publishCourseListForModal.length > 0 ? publishCourseListForModal.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: course.id,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: course.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 2047,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            color: course.status === 1 ? \"green\" : course.status === 0 ? \"orange\" : \"red\",\n                                                            className: \"text-xs\",\n                                                            children: course.status === 1 ? \"已发布\" : course.status === 0 ? \"草稿\" : \"已归档\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 2049,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400 text-xs\",\n                                                            children: [\n                                                                \"ID: \",\n                                                                course.id\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 2052,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 2048,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2046,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, course.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2045,\n                                        columnNumber: 19\n                                    }, undefined)) : selectedSeriesForPublish && !publishFormLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                    disabled: true,\n                                    value: \"no-courses\",\n                                    children: \"该系列暂无子课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2059,\n                                    columnNumber: 19\n                                }, undefined) : null\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2026,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 2021,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-3 rounded-lg mb-4 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-gray-700 mb-2\",\n                                    children: \"调试信息\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2069,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1 text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"已选择系列ID: \",\n                                                selectedSeriesForPublish || \"未选择\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2071,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"已选择课程ID: \",\n                                                selectedCourseForPublish || \"未选择\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2072,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"系列列表数量: \",\n                                                publishSeriesListForModal.length\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2073,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"子课程列表数量: \",\n                                                publishCourseListForModal.length\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2074,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"加载状态: \",\n                                                publishFormLoading ? \"加载中\" : \"空闲\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2075,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        publishCourseListForModal.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"子课程列表:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 2078,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"ml-4 list-disc\",\n                                                    children: publishCourseListForModal.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                \"ID: \",\n                                                                course.id,\n                                                                \", 名称: \",\n                                                                course.title\n                                                            ]\n                                                        }, course.id, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 2081,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 2079,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2077,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2070,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 2068,\n                            columnNumber: 11\n                        }, undefined),\n                        selectedCourseForPublish && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 p-4 rounded-lg mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-blue-900 mb-2\",\n                                    children: \"即将发布的课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2092,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"课程ID: \",\n                                                selectedCourseForPublish\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2094,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"所属系列: \",\n                                                (_publishSeriesListForModal_find = publishSeriesListForModal.find((s)=>s.id === selectedSeriesForPublish)) === null || _publishSeriesListForModal_find === void 0 ? void 0 : _publishSeriesListForModal_find.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2095,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"课程名称: \",\n                                                (_publishCourseListForModal_find = publishCourseListForModal.find((c)=>c.id === selectedCourseForPublish)) === null || _publishCourseListForModal_find === void 0 ? void 0 : _publishCourseListForModal_find.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2096,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-blue-600 font-medium\",\n                                            children: [\n                                                '点击\"发布此课程\"将调用发布API：POST /api/v1/course-management/courses/',\n                                                selectedCourseForPublish,\n                                                \"/publish\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2097,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2093,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 2091,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: selectedCourseForPublish ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-600\",\n                                        children: \"✓ 已选择课程，可以发布\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2105,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"请先选择系列课程和子课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2107,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2103,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            onClick: resetPublishCourseModal,\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2111,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            type: \"primary\",\n                                            htmlType: \"submit\",\n                                            loading: publishFormLoading,\n                                            disabled: !selectedCourseForPublish,\n                                            className: selectedCourseForPublish ? \"bg-green-600 hover:bg-green-700 border-green-600\" : \"\",\n                                            children: publishFormLoading ? \"发布中...\" : \"发布此课程\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2114,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2110,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 2102,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1991,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1983,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(CourseManagement, \"BJPzNCW7DZRrfGWDDhF5pcIAstQ=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm\n    ];\n});\n_c = CourseManagement;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseManagement);\nvar _c;\n$RefreshReg$(_c, \"CourseManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin-space/components/course-management.tsx\n"));

/***/ })

});