<!DOCTYPE html>
<html>
<head>
    <title>课程状态显示测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-section { margin-bottom: 30px; padding: 20px; border: 1px solid #e8e8e8; border-radius: 8px; }
        .test-title { font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #333; }
        
        /* 模拟课程列表样式 */
        .course-list-items { padding: 16px; background: #f9fafb; border-radius: 8px; }
        .course-list-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .course-list-item:hover {
            border-color: #3b82f6;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
        }
        .course-list-item.active {
            border-color: #3b82f6;
            background: #eff6ff;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
        }
        .course-list-item-content {
            display: flex;
            align-items: center;
            flex: 1;
            gap: 8px;
        }
        .course-list-item-text {
            font-size: 14px;
            color: #374151;
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .course-status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
            flex-shrink: 0;
        }
        .course-status-badge.published {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        .course-status-badge.draft {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fde68a;
        }
        .course-list-item-delete {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            border: none;
            background: #f3f4f6;
            color: #6b7280;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            opacity: 0;
        }
        .course-list-item:hover .course-list-item-delete {
            opacity: 1;
        }
        .course-list-item-delete:hover {
            background: #fee2e2;
            color: #dc2626;
        }
        
        /* API测试区域 */
        .api-test { background: #f8f9fa; padding: 15px; border-radius: 6px; margin: 15px 0; }
        .api-url { font-family: monospace; background: #e9ecef; padding: 8px; border-radius: 4px; margin: 10px 0; }
        .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; font-size: 14px; }
        .btn-primary { background: #1890ff; color: white; }
        .btn-success { background: #52c41a; color: white; }
        .btn:disabled { background: #d9d9d9; cursor: not-allowed; }
        .log-area { background: #f5f5f5; padding: 15px; border-radius: 6px; font-family: monospace; font-size: 12px; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
        .status-stats { display: flex; gap: 20px; margin: 15px 0; }
        .stat-item { padding: 10px 15px; background: #f8f9fa; border-radius: 6px; text-align: center; }
        .stat-number { font-size: 24px; font-weight: bold; margin-bottom: 5px; }
        .stat-label { font-size: 12px; color: #666; }
        .stat-draft .stat-number { color: #92400e; }
        .stat-published .stat-number { color: #166534; }
        .stat-total .stat-number { color: #1890ff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>课程状态显示测试</h1>
        
        <div class="test-section">
            <div class="test-title">API接口测试</div>
            <div class="api-test">
                <div class="api-url">GET /api/v1/course-management/series/{seriesId}/courses</div>
                <div>
                    <label>系列ID: </label>
                    <input type="number" id="seriesId" value="123" style="padding: 5px; margin: 0 10px;">
                    <button class="btn btn-primary" onclick="fetchCourseList()">获取课程列表</button>
                    <button class="btn btn-success" onclick="loadMockData()">加载模拟数据</button>
                </div>
            </div>
            
            <div class="status-stats" id="statusStats" style="display: none;">
                <div class="stat-item stat-total">
                    <div class="stat-number" id="totalCount">0</div>
                    <div class="stat-label">总课程数</div>
                </div>
                <div class="stat-item stat-published">
                    <div class="stat-number" id="publishedCount">0</div>
                    <div class="stat-label">已发布</div>
                </div>
                <div class="stat-item stat-draft">
                    <div class="stat-number" id="draftCount">0</div>
                    <div class="stat-label">未发布</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">课程列表显示效果</div>
            <div class="course-list-items" id="courseList">
                <div style="text-align: center; color: #666; padding: 40px;">
                    点击上方按钮获取课程数据
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">测试日志</div>
            <div class="log-area" id="testLog">等待操作...</div>
        </div>
    </div>

    <script>
        let testLogs = [];
        let selectedCourseId = null;

        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            testLogs.push(`[${timestamp}] ${message}`);
            document.getElementById('testLog').textContent = testLogs.join('\n');
            console.log(message);
        }

        function updateStats(courses) {
            const total = courses.length;
            const published = courses.filter(c => c.status === 1).length;
            const draft = courses.filter(c => c.status === 0).length;
            
            document.getElementById('totalCount').textContent = total;
            document.getElementById('publishedCount').textContent = published;
            document.getElementById('draftCount').textContent = draft;
            document.getElementById('statusStats').style.display = 'flex';
            
            addLog(`📊 课程状态统计: 总数=${total}, 已发布=${published}, 未发布=${draft}`);
        }

        function renderCourseList(courses) {
            const courseListElement = document.getElementById('courseList');
            
            if (courses.length === 0) {
                courseListElement.innerHTML = `
                    <div style="text-align: center; color: #666; padding: 40px;">
                        暂无课程数据
                    </div>
                `;
                return;
            }
            
            courseListElement.innerHTML = courses.map(course => `
                <div class="course-list-item ${selectedCourseId === course.id ? 'active' : ''}" 
                     onclick="selectCourse(${course.id})">
                    <div class="course-list-item-content">
                        <span class="course-list-item-text">${course.title}</span>
                        <span class="course-status-badge ${course.status === 1 ? 'published' : 'draft'}">
                            ${course.status === 1 ? '已发布' : '未发布'}
                        </span>
                    </div>
                    <button class="course-list-item-delete" onclick="deleteCourse(event, ${course.id})">
                        ×
                    </button>
                </div>
            `).join('');
            
            addLog(`✅ 渲染课程列表完成，共 ${courses.length} 个课程`);
        }

        function selectCourse(courseId) {
            selectedCourseId = courseId;
            addLog(`🎯 选中课程: ID=${courseId}`);
            
            // 重新渲染以更新选中状态
            const courses = JSON.parse(localStorage.getItem('currentCourses') || '[]');
            renderCourseList(courses);
        }

        function deleteCourse(event, courseId) {
            event.stopPropagation();
            addLog(`🗑️ 删除课程: ID=${courseId}`);
            
            const courses = JSON.parse(localStorage.getItem('currentCourses') || '[]');
            const updatedCourses = courses.filter(c => c.id !== courseId);
            localStorage.setItem('currentCourses', JSON.stringify(updatedCourses));
            
            renderCourseList(updatedCourses);
            updateStats(updatedCourses);
        }

        // 模拟API调用
        async function fetchCourseList() {
            const seriesId = document.getElementById('seriesId').value;
            addLog(`📤 发送请求: GET /api/v1/course-management/series/${seriesId}/courses`);
            addLog('🔍 参数: { page: 1, pageSize: 100 }');
            addLog('📝 说明: 不传status参数，获取所有状态的课程');
            
            try {
                // 模拟网络延迟
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 模拟API响应
                const mockResponse = {
                    code: 200,
                    message: '获取成功',
                    data: {
                        list: [
                            {
                                id: 4291,
                                title: '第1课时：JavaScript基础语法',
                                description: '学习JavaScript的基本语法和概念',
                                status: 1,
                                statusLabel: '已发布',
                                orderIndex: 1,
                                hasVideo: 1,
                                hasDocument: 0,
                                createdAt: '2024-01-15T10:00:00Z'
                            },
                            {
                                id: 4292,
                                title: '第2课时：变量和数据类型',
                                description: '深入了解JavaScript的变量声明和数据类型',
                                status: 0,
                                statusLabel: '草稿',
                                orderIndex: 2,
                                hasVideo: 0,
                                hasDocument: 1,
                                createdAt: '2024-01-16T14:30:00Z'
                            },
                            {
                                id: 4293,
                                title: '第3课时：函数和作用域',
                                description: '学习函数定义、调用和作用域概念',
                                status: 1,
                                statusLabel: '已发布',
                                orderIndex: 3,
                                hasVideo: 1,
                                hasDocument: 1,
                                createdAt: '2024-01-17T09:15:00Z'
                            },
                            {
                                id: 4294,
                                title: '第4课时：对象和数组',
                                description: '掌握JavaScript中的对象和数组操作',
                                status: 0,
                                statusLabel: '草稿',
                                orderIndex: 4,
                                hasVideo: 0,
                                hasDocument: 0,
                                createdAt: '2024-01-18T16:45:00Z'
                            },
                            {
                                id: 4295,
                                title: '第5课时：DOM操作',
                                description: '学习如何操作网页DOM元素',
                                status: 1,
                                statusLabel: '已发布',
                                orderIndex: 5,
                                hasVideo: 1,
                                hasDocument: 1,
                                createdAt: '2024-01-19T11:20:00Z'
                            }
                        ],
                        pagination: {
                            page: 1,
                            pageSize: 100,
                            total: 5,
                            totalPages: 1,
                            hasNext: false,
                            hasPrev: false
                        }
                    }
                };
                
                addLog('✅ API调用成功');
                addLog(`📡 响应数据: ${JSON.stringify(mockResponse, null, 2)}`);
                
                const courses = mockResponse.data.list;
                localStorage.setItem('currentCourses', JSON.stringify(courses));
                
                renderCourseList(courses);
                updateStats(courses);
                
            } catch (error) {
                addLog(`❌ API调用失败: ${error.message}`);
            }
        }

        // 加载模拟数据
        function loadMockData() {
            addLog('📝 加载预设模拟数据...');
            
            const mockCourses = [
                { id: 1001, title: '课程1 - 基础入门', status: 1, orderIndex: 1 },
                { id: 1002, title: '课程2 - 进阶学习', status: 0, orderIndex: 2 },
                { id: 1003, title: '课程3 - 实战项目', status: 1, orderIndex: 3 },
                { id: 1004, title: '课程4 - 高级技巧', status: 0, orderIndex: 4 },
                { id: 1005, title: '课程5 - 综合应用', status: 1, orderIndex: 5 },
                { id: 1006, title: '课程6 - 案例分析', status: 0, orderIndex: 6 }
            ];
            
            localStorage.setItem('currentCourses', JSON.stringify(mockCourses));
            renderCourseList(mockCourses);
            updateStats(mockCourses);
        }

        // 初始化
        addLog('🎯 课程状态显示测试页面已加载');
        addLog('📋 测试功能:');
        addLog('  1. 调用 GET /api/v1/course-management/series/{seriesId}/courses');
        addLog('  2. 获取包含 status=0(未发布) 和 status=1(已发布) 的课程数据');
        addLog('  3. 在课程列表中显示状态标注');
        addLog('  4. 统计各状态课程数量');
    </script>
</body>
</html>
