"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var PaymentController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const payment_service_1 = require("../services/payment.service");
const payment_request_dto_1 = require("../dto/payment-request.dto");
const common_2 = require("@nestjs/common");
const payment_notify_handler_1 = require("../notification/payment-notify.handler");
const refund_notify_handler_1 = require("../notification/refund-notify.handler");
const payment_logger_service_1 = require("../services/payment-logger.service");
const payment_log_dto_1 = require("../../util/database/mysql/payment_log/dto/payment-log.dto");
const lock_manager_1 = require("../lock/lock.manager");
const notification_record_service_1 = require("../../util/database/mysql/notification_record/notification-record.service");
const notification_record_dto_1 = require("../../util/database/mysql/notification_record/dto/notification-record.dto");
const encrypt_decorator_1 = require("../../util/encrypt/encrypt.decorator");
const not_login_decorator_1 = require("../../web/router_guard/not-login.decorator");
let PaymentController = PaymentController_1 = class PaymentController {
    paymentService;
    paymentNotifyHandler;
    refundNotifyHandler;
    paymentLogger;
    lockManager;
    notificationRecordService;
    logger = new common_2.Logger(PaymentController_1.name);
    constructor(paymentService, paymentNotifyHandler, refundNotifyHandler, paymentLogger, lockManager, notificationRecordService) {
        this.paymentService = paymentService;
        this.paymentNotifyHandler = paymentNotifyHandler;
        this.refundNotifyHandler = refundNotifyHandler;
        this.paymentLogger = paymentLogger;
        this.lockManager = lockManager;
        this.notificationRecordService = notificationRecordService;
    }
    async createPayment(createPaymentDto) {
        try {
            const startTime = Date.now();
            const requestId = `req_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
            this.logger.log(`[${requestId}] 创建支付请求: ${JSON.stringify(createPaymentDto)}`);
            const result = await this.paymentService.createPayment(createPaymentDto);
            const executionTime = Date.now() - startTime;
            await this.paymentLogger.log({
                logType: payment_log_dto_1.LogType.PAYMENT,
                operation: payment_log_dto_1.OperationType.CREATE,
                paymentChannel: createPaymentDto.channel,
                requestData: createPaymentDto,
                responseData: result,
                status: result.orderNo ? payment_log_dto_1.LogStatus.SUCCESS : payment_log_dto_1.LogStatus.FAIL,
                errorMessage: result.orderNo ? undefined : 'Payment creation failed',
                executionTime: executionTime,
                operatorId: createPaymentDto.userId,
                clientIp: createPaymentDto.clientIp,
            });
            return result;
        }
        catch (error) {
            this.logger.error(`创建支付失败: ${error.message}`, error.stack);
            await this.paymentLogger.log({
                logType: payment_log_dto_1.LogType.PAYMENT,
                operation: payment_log_dto_1.OperationType.CREATE,
                paymentChannel: createPaymentDto.channel,
                requestData: createPaymentDto,
                responseData: { error: error.message },
                status: payment_log_dto_1.LogStatus.FAIL,
                errorMessage: error.message,
            });
            return {
                success: false,
                errorMessage: error.message,
            };
        }
    }
    async queryPaymentStatus(outTradeNo) {
        try {
            const startTime = Date.now();
            const requestId = `req_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
            this.logger.log(`[${requestId}] 查询支付状态: ${outTradeNo}`);
            const result = await this.paymentService.queryPaymentStatus(outTradeNo);
            const executionTime = Date.now() - startTime;
            await this.paymentLogger.log({
                logType: payment_log_dto_1.LogType.PAYMENT,
                operation: payment_log_dto_1.OperationType.QUERY,
                paymentChannel: result.channel || 'unknown',
                requestData: { outTradeNo },
                responseData: result,
                status: result.status === 'success' ? payment_log_dto_1.LogStatus.SUCCESS : payment_log_dto_1.LogStatus.FAIL,
                executionTime: executionTime,
            });
            return result;
        }
        catch (error) {
            this.logger.error(`查询支付状态失败: ${error.message}`, error.stack);
            await this.paymentLogger.log({
                logType: payment_log_dto_1.LogType.PAYMENT,
                operation: payment_log_dto_1.OperationType.QUERY,
                paymentChannel: 'unknown',
                requestData: { outTradeNo },
                responseData: { error: error.message },
                status: payment_log_dto_1.LogStatus.FAIL,
                errorMessage: error.message,
            });
            return {
                success: false,
                errorMessage: error.message,
            };
        }
    }
    async closePayment(closePaymentDto) {
        const result = await this.paymentService.closePayment(closePaymentDto.orderNo, closePaymentDto.reason);
        return {
            code: 200,
            message: 'success',
            data: { success: result },
        };
    }
    async handlePaymentNotify(channel, notifyData, req) {
        const startTime = Date.now();
        const requestId = `req_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
        try {
            let processedNotifyData = { ...notifyData };
            if (channel === 'wechatpay') {
                this.logger.log(`[${requestId}] 接收微信支付通知: ${JSON.stringify(notifyData).substring(0, 500)}...`);
                processedNotifyData = {
                    headers: req.headers,
                    body: notifyData,
                    rawBody: JSON.stringify(notifyData)
                };
                if (notifyData.resource && notifyData.resource.ciphertext) {
                    this.logger.debug(`[${requestId}] 微信支付通知包含加密数据，将进行验证和解密`);
                    try {
                        const strategy = this.paymentService.getPaymentStrategy('wechatpay');
                        const verifyResult = await strategy.verifyNotify(processedNotifyData);
                        if (verifyResult.verified && verifyResult.rawNotify) {
                            processedNotifyData.decryptedResource = verifyResult.rawNotify;
                            processedNotifyData.outTradeNo = verifyResult.outTradeNo;
                            this.logger.debug(`[${requestId}] 成功解密微信支付通知数据: ${JSON.stringify(verifyResult.rawNotify).substring(0, 200)}...`);
                        }
                    }
                    catch (error) {
                        this.logger.warn(`[${requestId}] 预处理微信支付通知数据失败: ${error.message}`);
                    }
                }
            }
            else if (channel === 'alipay') {
                this.logger.log(`[${requestId}] 接收支付宝通知: out_trade_no=${notifyData.out_trade_no}, trade_status=${notifyData.trade_status}`);
            }
            let outTradeNo = '';
            if (processedNotifyData.outTradeNo) {
                outTradeNo = processedNotifyData.outTradeNo;
                this.logger.debug(`[${requestId}] 从预处理数据中获取订单号: ${outTradeNo}`);
            }
            else if (channel === 'alipay' && notifyData.out_trade_no) {
                outTradeNo = notifyData.out_trade_no;
                this.logger.debug(`[${requestId}] 从支付宝通知中获取订单号: ${outTradeNo}`);
            }
            else if (channel === 'wechatpay') {
                if (processedNotifyData.decryptedResource && processedNotifyData.decryptedResource.out_trade_no) {
                    outTradeNo = processedNotifyData.decryptedResource.out_trade_no;
                    this.logger.debug(`[${requestId}] 从解密数据中获取订单号: ${outTradeNo}`);
                }
            }
            if (outTradeNo) {
                const notifications = await this.notificationRecordService.findByTargetIdAndType(outTradeNo, 'payment_success', notification_record_dto_1.NotificationStatus.PROCESSED);
                if (notifications && notifications.length > 0) {
                    this.logger.log(`[${requestId}] 订单 ${outTradeNo} 已有成功通知记录(状态: ${notification_record_dto_1.NotificationStatus.PROCESSED})，跳过处理`);
                    await this.paymentLogger.log({
                        logType: payment_log_dto_1.LogType.PAYMENT,
                        operation: payment_log_dto_1.OperationType.NOTIFY,
                        paymentChannel: channel,
                        requestData: { requestId, duplicateNotify: true },
                        responseData: { success: true, message: '重复通知，已跳过处理' },
                        status: payment_log_dto_1.LogStatus.SUCCESS,
                        errorMessage: '重复通知，已跳过处理',
                        executionTime: Date.now() - startTime,
                    });
                    if (channel === 'alipay') {
                        return 'success';
                    }
                    else if (channel === 'wechatpay') {
                        return {
                            code: 'SUCCESS',
                            message: 'OK',
                        };
                    }
                    return {
                        success: true,
                        message: '通知已处理',
                    };
                }
            }
            this.logger.debug(`[${requestId}] 验证${channel}支付通知签名`);
            const verifyResponse = await this.paymentService.handlePaymentNotify(channel, processedNotifyData);
            this.logger.log(`[${requestId}] 开始处理${channel}支付通知，准备获取锁`);
            const result = await this.paymentNotifyHandler.handlePaymentNotify(channel, processedNotifyData);
            const executionTime = Date.now() - startTime;
            await this.paymentLogger.log({
                logType: payment_log_dto_1.LogType.PAYMENT,
                operation: payment_log_dto_1.OperationType.NOTIFY,
                paymentChannel: channel,
                requestData: {
                    requestId,
                    originalNotify: channel === 'wechatpay' ? '(微信支付通知数据已省略)' : notifyData,
                    outTradeNo
                },
                responseData: result,
                status: result.success ? payment_log_dto_1.LogStatus.SUCCESS : payment_log_dto_1.LogStatus.FAIL,
                errorMessage: result.message || 'Notification processing complete',
                executionTime: executionTime,
            });
            if (result.success) {
                this.logger.log(`[${requestId}] ${channel}支付通知处理成功`);
                if (channel === 'alipay') {
                    return 'success';
                }
                else if (channel === 'wechatpay') {
                    return {
                        code: 'SUCCESS',
                        message: 'OK',
                    };
                }
                return {
                    success: true,
                    message: '通知处理成功',
                };
            }
            else {
                this.logger.warn(`[${requestId}] ${channel}支付通知处理失败: ${result.message}`);
                if (channel === 'wechatpay') {
                    return {
                        code: 'SUCCESS',
                        message: 'OK',
                    };
                }
                if (channel === 'alipay') {
                    return 'fail';
                }
                return {
                    success: false,
                    message: result.message,
                };
            }
        }
        catch (error) {
            const executionTime = Date.now() - startTime;
            this.logger.error(`[${requestId}] 处理${channel}支付通知异常: ${error.message}`, error.stack);
            await this.paymentLogger.log({
                logType: payment_log_dto_1.LogType.PAYMENT,
                operation: payment_log_dto_1.OperationType.NOTIFY,
                paymentChannel: channel,
                requestData: { requestId, error: error.message },
                responseData: { success: false, error: error.message },
                status: payment_log_dto_1.LogStatus.FAIL,
                errorMessage: error.message,
                executionTime: executionTime,
            });
            if (channel === 'wechatpay') {
                return {
                    code: 'SUCCESS',
                    message: 'OK',
                };
            }
            if (channel === 'alipay') {
                return 'fail';
            }
            return {
                success: false,
                message: error.message,
            };
        }
    }
    async handleRefundNotify(channel, req, res) {
        const requestId = `req_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
        this.logger.log(`[${requestId}] 接收到${channel}退款通知`);
        let notifyData;
        if (channel === 'wechatpay') {
            notifyData = {
                headers: req.headers,
                body: req.body,
            };
            this.logger.debug(`[${requestId}] 微信支付退款通知数据: ${JSON.stringify(notifyData.headers)}`);
        }
        else {
            notifyData = req.body;
            this.logger.debug(`[${requestId}] 支付宝退款通知数据: ${JSON.stringify(notifyData)}`);
        }
        try {
            this.logger.log(`[${requestId}] 开始处理${channel}退款通知，准备获取锁`);
            const result = await this.refundNotifyHandler.handleRefundNotify(channel, notifyData);
            if (result.success) {
                this.logger.log(`[${requestId}] ${channel}退款通知处理成功`);
                if (channel === 'wechatpay') {
                    return res.status(common_1.HttpStatus.OK).json({
                        code: 'SUCCESS',
                        message: 'OK',
                    });
                }
                else {
                    return res.status(common_1.HttpStatus.OK).send('success');
                }
            }
            else {
                this.logger.warn(`[${requestId}] ${channel}退款通知处理失败: ${result.message}`);
                if (channel === 'wechatpay') {
                    return res.status(common_1.HttpStatus.OK).json({
                        code: 'FAIL',
                        message: result.message || 'Notification processing failed',
                    });
                }
                else {
                    return res.status(common_1.HttpStatus.OK).send('fail');
                }
            }
        }
        catch (error) {
            this.logger.error(`[${requestId}] 处理${channel}退款通知异常: ${error.message}`, error.stack);
            if (channel === 'wechatpay') {
                return res.status(common_1.HttpStatus.OK).json({
                    code: 'FAIL',
                    message: error.message,
                });
            }
            else {
                return res.status(common_1.HttpStatus.OK).send('fail');
            }
        }
    }
    async paymentReturn(channel, query, res) {
        const redirectUrl = '/payment-return.html';
        const orderNo = channel === 'alipay' ? query.out_trade_no : '';
        return res.redirect(`${redirectUrl}?out_trade_no=${orderNo}`);
    }
    async wechatNotify(req, res) {
        const startTime = Date.now();
        this.logger.log('收到微信支付通知');
        try {
            const headers = req.headers;
            const rawBody = req.body;
            await this.paymentLogger.log({
                logType: payment_log_dto_1.LogType.PAYMENT,
                operation: payment_log_dto_1.OperationType.CALLBACK,
                paymentChannel: 'wechatpay',
                requestData: { headers, body: rawBody },
                status: payment_log_dto_1.LogStatus.SUCCESS,
            });
            const result = await this.paymentNotifyHandler.handlePaymentNotify('wechatpay', {
                headers,
                body: rawBody,
            });
            await this.paymentLogger.log({
                logType: payment_log_dto_1.LogType.PAYMENT,
                operation: payment_log_dto_1.OperationType.CALLBACK,
                paymentChannel: 'wechatpay',
                responseData: result,
                status: result.success ? payment_log_dto_1.LogStatus.SUCCESS : payment_log_dto_1.LogStatus.FAIL,
                executionTime: Date.now() - startTime,
            });
            res.send(result.success ?
                JSON.stringify({ code: 'SUCCESS', message: 'OK' }) :
                JSON.stringify({ code: 'FAIL', message: result.message || 'Failed to process notification' }));
        }
        catch (error) {
            this.logger.error(`处理微信支付通知异常: ${error.message}`, error.stack);
            res.send(JSON.stringify({ code: 'FAIL', message: 'Internal server error' }));
        }
    }
    async alipayRefundNotify(notifyData, res) {
        const startTime = Date.now();
        this.logger.log('收到支付宝退款通知');
        try {
            await this.paymentLogger.log({
                logType: payment_log_dto_1.LogType.REFUND,
                operation: payment_log_dto_1.OperationType.CALLBACK,
                paymentChannel: 'alipay',
                requestData: notifyData,
                status: payment_log_dto_1.LogStatus.SUCCESS,
            });
            const result = await this.refundNotifyHandler.handleRefundNotify('alipay', notifyData);
            await this.paymentLogger.log({
                logType: payment_log_dto_1.LogType.REFUND,
                operation: payment_log_dto_1.OperationType.CALLBACK,
                paymentChannel: 'alipay',
                responseData: result,
                status: result.success ? payment_log_dto_1.LogStatus.SUCCESS : payment_log_dto_1.LogStatus.FAIL,
                errorMessage: result.success ? undefined : result.message,
                executionTime: Date.now() - startTime,
            });
            res.send(result.success ? 'success' : 'fail');
        }
        catch (error) {
            this.logger.error(`处理支付宝退款通知异常: ${error.message}`, error.stack);
            res.send('fail');
        }
    }
    async wechatRefundNotify(req, res) {
        const startTime = Date.now();
        this.logger.log('收到微信支付退款通知');
        try {
            const headers = req.headers;
            const rawBody = req.body;
            await this.paymentLogger.log({
                logType: payment_log_dto_1.LogType.REFUND,
                operation: payment_log_dto_1.OperationType.CALLBACK,
                paymentChannel: 'wechatpay',
                requestData: { headers, body: rawBody },
                status: payment_log_dto_1.LogStatus.SUCCESS,
            });
            const result = await this.refundNotifyHandler.handleRefundNotify('wechatpay', {
                headers,
                body: rawBody,
            });
            await this.paymentLogger.log({
                logType: payment_log_dto_1.LogType.REFUND,
                operation: payment_log_dto_1.OperationType.CALLBACK,
                paymentChannel: 'wechatpay',
                responseData: result,
                status: result.success ? payment_log_dto_1.LogStatus.SUCCESS : payment_log_dto_1.LogStatus.FAIL,
                errorMessage: result.success ? undefined : result.message,
                executionTime: Date.now() - startTime,
            });
            res.send(result.success ?
                JSON.stringify({ code: 'SUCCESS', message: 'OK' }) :
                JSON.stringify({ code: 'FAIL', message: result.message || 'Failed to process refund notification' }));
        }
        catch (error) {
            this.logger.error(`处理微信支付退款通知异常: ${error.message}`, error.stack);
            res.send(JSON.stringify({ code: 'FAIL', message: 'Internal server error' }));
        }
    }
    async getOrderDetail(orderNo) {
        try {
            const startTime = Date.now();
            const requestId = `req_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
            this.logger.log(`[${requestId}] 获取订单详情: ${orderNo}`);
            const result = await this.paymentService.getOrderDetail(orderNo);
            const executionTime = Date.now() - startTime;
            await this.paymentLogger.log({
                logType: payment_log_dto_1.LogType.PAYMENT,
                operation: payment_log_dto_1.OperationType.QUERY,
                paymentChannel: result.channel || 'unknown',
                requestData: { orderNo },
                responseData: { success: true },
                status: payment_log_dto_1.LogStatus.SUCCESS,
                executionTime: executionTime,
            });
            return result;
        }
        catch (error) {
            this.logger.error(`获取订单详情失败: ${error.message}`, error.stack);
            await this.paymentLogger.log({
                logType: payment_log_dto_1.LogType.PAYMENT,
                operation: payment_log_dto_1.OperationType.QUERY,
                paymentChannel: 'unknown',
                requestData: { orderNo },
                responseData: { error: error.message },
                status: payment_log_dto_1.LogStatus.FAIL,
                errorMessage: error.message,
            });
            return {
                success: false,
                errorMessage: error.message,
            };
        }
    }
    async testWechatRefundNotify(testData) {
        try {
            this.logger.log(`收到微信退款测试请求: ${JSON.stringify(testData)}`);
            const { refundNo, mchId, serialNo } = testData;
            if (!refundNo) {
                return {
                    success: false,
                    message: '缺少退款单号'
                };
            }
            const mockTimestamp = Math.floor(Date.now() / 1000).toString();
            const mockNonce = Math.random().toString(36).substring(2, 15);
            const mockDecryptedData = {
                mchid: mchId || "1230000109",
                out_trade_no: `order_${Date.now()}`,
                transaction_id: `****************${Math.floor(Math.random() * ********)}`,
                out_refund_no: refundNo,
                refund_id: `**************${Math.floor(Math.random() * ********)}`,
                refund_status: "SUCCESS",
                success_time: new Date().toISOString(),
                amount: {
                    total: 100,
                    refund: 100,
                    payer_total: 100,
                    payer_refund: 100
                },
                user_received_account: "微信零钱"
            };
            const mockNotifyData = {
                id: `${mockTimestamp}-${mockNonce}`,
                create_time: mockTimestamp,
                resource_type: "encrypt-resource",
                event_type: "REFUND.SUCCESS",
                summary: "退款成功",
                resource: {
                    original_type: "refund",
                    algorithm: "AEAD_AES_256_GCM",
                    ciphertext: "mock_ciphertext",
                    associated_data: "mock_associated_data",
                    nonce: "mock_nonce"
                }
            };
            const mockNotify = {
                headers: {
                    'wechatpay-signature': 'mock_signature',
                    'wechatpay-timestamp': mockTimestamp,
                    'wechatpay-nonce': mockNonce,
                    'wechatpay-serial': serialNo || 'mock_serial_no'
                },
                body: {
                    ...mockNotifyData,
                    resource_decoded: mockDecryptedData
                }
            };
            const result = await this.refundNotifyHandler.handleRefundNotify('wechatpay', mockNotify);
            return {
                success: true,
                message: '模拟微信退款通知处理完成',
                result,
                mockNotify
            };
        }
        catch (error) {
            this.logger.error(`测试微信退款通知失败: ${error.message}`, error.stack);
            return {
                success: false,
                error: error.message
            };
        }
    }
};
exports.PaymentController = PaymentController;
__decorate([
    (0, common_1.Post)('create'),
    (0, swagger_1.ApiOperation)({ summary: '创建支付' }),
    (0, encrypt_decorator_1.SecureEncrypt)({
        enabled: true,
        decryptRequest: true
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [payment_request_dto_1.CreatePaymentDto]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "createPayment", null);
__decorate([
    (0, common_1.Get)('query/:outTradeNo'),
    (0, encrypt_decorator_1.SecureEncrypt)({
        enabled: true,
        decryptRequest: true
    }),
    (0, swagger_1.ApiOperation)({ summary: '查询支付状态' }),
    __param(0, (0, common_1.Param)('outTradeNo')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "queryPaymentStatus", null);
__decorate([
    (0, common_1.Post)('close'),
    (0, encrypt_decorator_1.SecureEncrypt)({
        enabled: true,
        decryptRequest: true
    }),
    (0, swagger_1.ApiOperation)({ summary: '关闭支付订单' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '关闭成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [payment_request_dto_1.ClosePaymentDto]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "closePayment", null);
__decorate([
    (0, common_1.Post)('notify/:channel'),
    (0, encrypt_decorator_1.SecureEncrypt)({
        enabled: false,
        decryptRequest: false
    }),
    (0, not_login_decorator_1.NotLogin)(),
    (0, swagger_1.ApiOperation)({ summary: '支付回调通知' }),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Param)('channel')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "handlePaymentNotify", null);
__decorate([
    (0, encrypt_decorator_1.SecureEncrypt)({
        enabled: false,
        decryptRequest: false
    }),
    (0, common_1.Post)('refund/notify/:channel'),
    (0, swagger_1.ApiOperation)({ summary: '退款结果通知回调' }),
    (0, swagger_1.ApiParam)({ name: 'channel', description: '支付渠道(alipay/wechatpay)' }),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Param)('channel')),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "handleRefundNotify", null);
__decorate([
    (0, encrypt_decorator_1.SecureEncrypt)({
        enabled: false,
        decryptRequest: false
    }),
    (0, common_1.Get)('return/:channel'),
    (0, swagger_1.ApiOperation)({ summary: '支付完成跳转' }),
    (0, swagger_1.ApiParam)({ name: 'channel', description: '支付渠道(alipay/wechatpay)' }),
    __param(0, (0, common_1.Param)('channel')),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "paymentReturn", null);
__decorate([
    (0, common_1.Post)('notify/wechatpay'),
    (0, encrypt_decorator_1.SecureEncrypt)({
        enabled: true,
        decryptRequest: true
    }),
    (0, common_1.HttpCode)(200),
    (0, swagger_1.ApiOperation)({ summary: '微信支付通知回调' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "wechatNotify", null);
__decorate([
    (0, common_1.Post)('notify/alipay/refund'),
    (0, encrypt_decorator_1.SecureEncrypt)({
        enabled: true,
        decryptRequest: true
    }),
    (0, swagger_1.ApiOperation)({ summary: '支付宝退款通知回调' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "alipayRefundNotify", null);
__decorate([
    (0, common_1.Post)('notify/wechatpay/refund'),
    (0, encrypt_decorator_1.SecureEncrypt)({
        enabled: true,
        decryptRequest: true
    }),
    (0, common_1.HttpCode)(200),
    (0, swagger_1.ApiOperation)({ summary: '微信支付退款通知回调' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "wechatRefundNotify", null);
__decorate([
    (0, common_1.Get)('order/:orderNo'),
    (0, encrypt_decorator_1.SecureEncrypt)({
        enabled: true,
        decryptRequest: true
    }),
    (0, swagger_1.ApiOperation)({ summary: '获取订单详情' }),
    (0, swagger_1.ApiParam)({ name: 'orderNo', description: '订单编号' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '成功获取订单详情' }),
    __param(0, (0, common_1.Param)('orderNo')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "getOrderDetail", null);
__decorate([
    (0, common_1.Post)('test/wechat/refund'),
    (0, encrypt_decorator_1.SecureEncrypt)({
        enabled: true,
        decryptRequest: true
    }),
    (0, swagger_1.ApiOperation)({ summary: '测试微信退款通知' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentController.prototype, "testWechatRefundNotify", null);
exports.PaymentController = PaymentController = PaymentController_1 = __decorate([
    (0, swagger_1.ApiTags)('支付管理'),
    (0, common_1.Controller)('v1/payment'),
    __metadata("design:paramtypes", [payment_service_1.PaymentService,
        payment_notify_handler_1.PaymentNotifyHandler,
        refund_notify_handler_1.RefundNotifyHandler,
        payment_logger_service_1.PaymentLoggerService,
        lock_manager_1.LockManager,
        notification_record_service_1.NotificationRecordService])
], PaymentController);
//# sourceMappingURL=payment.controller.js.map