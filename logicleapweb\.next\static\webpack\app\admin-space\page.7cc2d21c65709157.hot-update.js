"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-space/page",{

/***/ "(app-pages-browser)/./app/admin-space/components/course-management.tsx":
/*!**********************************************************!*\
  !*** ./app/admin-space/components/course-management.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/popconfirm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/upload/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EditOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/InboxOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/UploadOutlined.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst { Search } = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nconst { Option } = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\nconst CourseManagement = ()=>{\n    var _publishSeriesListForModal_find, _publishCourseListForModal_find;\n    _s();\n    const [courseList, setCourseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCourseModalVisible, setIsCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddCourseModalVisible, setIsAddCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditCourseModalVisible, setIsEditCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddSeriesModalVisible, setIsAddSeriesModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddTagModalVisible, setIsAddTagModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishSeriesModalVisible, setIsPublishSeriesModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishCourseModalVisible, setIsPublishCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingCourse, setEditingCourse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchKeyword, setSearchKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [teachers, setTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseTags, setCourseTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coverImageUrl, setCoverImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 新增：系列课程和子课程管理相关状态\n    const [seriesList, setSeriesList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [seriesCoursesMap, setSeriesCoursesMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [expandedSeries, setExpandedSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [seriesLoading, setSeriesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 发布课程相关状态\n    const [selectedSeriesForPublish, setSelectedSeriesForPublish] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [selectedCourseForPublish, setSelectedCourseForPublish] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [publishSeriesCourses, setPublishSeriesCourses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [publishLoading, setPublishLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishSeriesOptions, setPublishSeriesOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 新的发布课程状态\n    const [publishSeriesListForModal, setPublishSeriesListForModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [publishCourseListForModal, setPublishCourseListForModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [publishFormLoading, setPublishFormLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [courseSeries, setCourseSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseCoverImageUrl, setCourseCoverImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [additionalFiles, setAdditionalFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseVideoUrl, setCourseVideoUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseVideoName, setCourseVideoName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseDocumentUrl, setCourseDocumentUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseDocumentName, setCourseDocumentName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoDuration, setVideoDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // 教学信息状态\n    const [teachingInfoList, setTeachingInfoList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            title: \"\",\n            content: [\n                \"\"\n            ]\n        }\n    ]);\n    const [addCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [editCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [addSeriesForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [addTagForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [publishSeriesForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [publishCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__.GetNotification)();\n    // 获取系列课程列表\n    const fetchSeriesList = async ()=>{\n        try {\n            var _res_data;\n            setSeriesLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取系列课程列表成功:\", res.data.list);\n                setSeriesList(res.data.list);\n            } else {\n                console.error(\"❌ 获取系列课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程列表失败，请重试\");\n        } finally{\n            setSeriesLoading(false);\n        }\n    };\n    // 获取指定系列下的子课程列表\n    const fetchSeriesCourses = async (seriesId)=>{\n        try {\n            var _res_data;\n            console.log(\"\\uD83D\\uDCDD 获取系列子课程列表，系列ID:\", seriesId);\n            // 使用课程管理API获取所有状态的课程\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getManagementSeriesCourses(seriesId, {\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取系列子课程列表成功:\", res.data.list);\n                setSeriesCoursesMap((prev)=>new Map(prev.set(seriesId, res.data.list)));\n                setExpandedSeries((prev)=>new Set(prev.add(seriesId)));\n            } else {\n                console.error(\"❌ 获取系列子课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取子课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列子课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取子课程列表失败，请重试\");\n        }\n    };\n    // 获取课程列表（保留原有功能）\n    const fetchCourseList = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDCDD 获取课程列表...\");\n            // 获取系列课程列表\n            await fetchSeriesList();\n        } catch (error) {\n            console.error(\"❌ 获取课程列表失败:\", error);\n            notification.error(\"获取课程列表失败，请重试\");\n        }\n    };\n    // 添加课程\n    const handleAddCourse = async (values)=>{\n        try {\n            // 构建内容配置，只包含有效的媒体文件\n            const contentConfig = {\n                hasVideo: courseVideoUrl ? 1 : 0,\n                hasDocument: courseDocumentUrl ? 1 : 0\n            };\n            if (courseVideoUrl) {\n                contentConfig.video = {\n                    url: courseVideoUrl,\n                    name: courseVideoName || \"课程视频.mp4\"\n                };\n            }\n            if (courseDocumentUrl) {\n                contentConfig.document = {\n                    url: courseDocumentUrl,\n                    name: courseDocumentName || \"课程文档.pdf\"\n                };\n            }\n            const courseData = {\n                seriesId: parseInt(values.seriesId),\n                title: values.title.trim(),\n                description: values.description.trim(),\n                coverImage: courseCoverImageUrl,\n                hasVideo: courseVideoUrl ? 1 : 0,\n                hasDocument: courseDocumentUrl ? 1 : 0,\n                videoDuration: videoDuration || 0,\n                contentConfig,\n                teachingInfo: teachingInfoList.filter((info)=>info.title.trim() && info.content.some((c)=>c.trim())),\n                additionalResources: additionalFiles.map((file)=>({\n                        title: file.split(\"/\").pop() || \"file\",\n                        url: file,\n                        description: \"课程附件资源\"\n                    })),\n                orderIndex: parseInt(values.orderIndex) || 0\n            };\n            // 验证必要字段\n            if (!courseData.seriesId) {\n                notification.error(\"请选择所属系列课程\");\n                return;\n            }\n            if (!courseData.title) {\n                notification.error(\"请输入课程名称\");\n                return;\n            }\n            if (!courseData.coverImage) {\n                notification.error(\"请上传课程封面\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDCE4 提交课程数据:\", courseData);\n            console.log(\"\\uD83D\\uDCCA 数据大小估算:\", JSON.stringify(courseData).length, \"字符\");\n            // 添加重试机制\n            let retryCount = 0;\n            const maxRetries = 2;\n            let lastError;\n            while(retryCount <= maxRetries){\n                try {\n                    const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourse(courseData);\n                    // 如果成功，跳出重试循环\n                    if (res.code === 200) {\n                        notification.success(\"创建课程成功\");\n                        fetchCourseList();\n                        setIsAddCourseModalVisible(false);\n                        addCourseForm.resetFields();\n                        setCourseCoverImageUrl(\"\");\n                        setAdditionalFiles([]);\n                        setCourseVideoUrl(\"\");\n                        setCourseVideoName(\"\");\n                        setCourseDocumentUrl(\"\");\n                        setCourseDocumentName(\"\");\n                        setVideoDuration(0);\n                        setTeachingInfoList([\n                            {\n                                title: \"\",\n                                content: [\n                                    \"\"\n                                ]\n                            }\n                        ]);\n                        return;\n                    } else {\n                        notification.error(res.msg || \"创建课程失败\");\n                        return;\n                    }\n                } catch (error) {\n                    lastError = error;\n                    retryCount++;\n                    if (retryCount <= maxRetries) {\n                        console.log(\"\\uD83D\\uDD04 第\".concat(retryCount, \"次重试...\"));\n                        notification.warning(\"网络异常，正在重试 (\".concat(retryCount, \"/\").concat(maxRetries, \")\"));\n                        // 等待1秒后重试\n                        await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    }\n                }\n            }\n            // 如果所有重试都失败了，抛出最后的错误\n            throw lastError;\n        } catch (error) {\n            var _error_message, _error_response_data, _error_response, _error_response1, _error_response2, _error_response3, _error_response4, _error_response5;\n            console.error(\"❌ 创建课程失败:\", error);\n            // 更详细的错误处理\n            if (error.code === \"ECONNRESET\" || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"ECONNRESET\")) || ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) && error.response.data.message.includes(\"ECONNRESET\")) {\n                notification.error(\"网络连接中断，可能是网络不稳定或服务器繁忙。请稍后重试或联系管理员。\");\n            } else if (error.code === \"NETWORK_ERROR\") {\n                notification.error(\"网络错误，请检查网络连接\");\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 413) {\n                notification.error(\"上传文件过大，请压缩后重试\");\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 400) {\n                var _error_response_data1, _error_response6;\n                const errorMsg = ((_error_response6 = error.response) === null || _error_response6 === void 0 ? void 0 : (_error_response_data1 = _error_response6.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || error.message;\n                notification.error(\"请求参数错误: \".concat(errorMsg));\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 500) {\n                notification.error(\"服务器内部错误，请联系管理员\");\n            } else {\n                notification.error(\"创建课程失败: \".concat(error.message || \"请稍后重试\"));\n            }\n            console.log(\"\\uD83D\\uDD0D 完整错误信息:\", {\n                message: error.message,\n                code: error.code,\n                status: (_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status,\n                data: (_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.data\n            });\n        }\n    };\n    // 编辑课程\n    const handleEditCourse = async (values)=>{\n        if (!editingCourse) return;\n        try {\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.updateCourse(editingCourse.id, values);\n            if (res.code === 200) {\n                notification.success(\"更新课程成功\");\n                fetchCourseList();\n                setIsEditCourseModalVisible(false);\n                setEditingCourse(null);\n                editCourseForm.resetFields();\n            } else {\n                notification.error(res.msg || \"更新课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 更新课程失败:\", error);\n            notification.error(\"更新课程失败，请重试\");\n        }\n    };\n    // 删除课程\n    const handleDeleteCourse = async (courseId)=>{\n        try {\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.deleteCourse(courseId);\n            if (res.code === 200) {\n                notification.success(\"删除课程成功\");\n                fetchCourseList();\n            } else {\n                notification.error(res.msg || \"删除课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 删除课程失败:\", error);\n            notification.error(\"删除课程失败，请重试\");\n        }\n    };\n    // 删除子课程\n    const handleDeleteSubCourse = async (courseId, seriesId)=>{\n        try {\n            console.log(\"\\uD83D\\uDDD1️ 删除子课程，课程ID:\", courseId, \"系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.deleteCourse(courseId);\n            if (res.code === 200) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"删除子课程成功\");\n                console.log(\"✅ 子课程删除成功，重新获取系列子课程列表\");\n                // 重新获取该系列的子课程列表\n                await fetchSeriesCourses(seriesId);\n                console.log(\"\\uD83D\\uDD04 子课程列表已刷新\");\n            } else {\n                console.error(\"❌ 删除子课程失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"删除子课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 删除子课程异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"删除子课程失败，请重试\");\n        }\n    };\n    // 切换系列展开/收起状态\n    const toggleSeriesExpansion = async (seriesId)=>{\n        console.log(\"\\uD83D\\uDD04 切换系列展开状态，系列ID:\", seriesId);\n        console.log(\"\\uD83D\\uDCCA 当前展开状态:\", expandedSeries.has(seriesId));\n        if (expandedSeries.has(seriesId)) {\n            // 收起\n            console.log(\"\\uD83D\\uDCC1 收起系列:\", seriesId);\n            setExpandedSeries((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(seriesId);\n                return newSet;\n            });\n        } else {\n            // 展开，需要获取子课程数据\n            console.log(\"\\uD83D\\uDCC2 展开系列，获取子课程:\", seriesId);\n            await fetchSeriesCourses(seriesId);\n        }\n    };\n    // 展开所有系列\n    const expandAllSeries = async ()=>{\n        console.log(\"\\uD83D\\uDCC2 展开所有系列课程\");\n        for (const series of seriesList){\n            if (!expandedSeries.has(series.id)) {\n                await fetchSeriesCourses(series.id);\n            }\n        }\n    };\n    // 收起所有系列\n    const collapseAllSeries = ()=>{\n        console.log(\"\\uD83D\\uDCC1 收起所有系列课程\");\n        setExpandedSeries(new Set());\n    };\n    // 添加系列课程\n    const handleAddSeries = async (values)=>{\n        try {\n            const seriesData = {\n                ...values,\n                coverImage: coverImageUrl\n            };\n            console.log(\"创建系列课程数据:\", seriesData);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourseSeries(seriesData);\n            if (res.code === 200) {\n                notification.success(\"创建系列课程成功\");\n                fetchCourseList();\n                setIsAddSeriesModalVisible(false);\n                addSeriesForm.resetFields();\n                setCoverImageUrl(\"\");\n            } else {\n                notification.error(res.msg || \"创建系列课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 创建系列课程失败:\", error);\n            notification.error(\"创建系列课程失败，请重试\");\n        }\n    };\n    // 创建课程标签\n    const handleAddTag = async (values)=>{\n        try {\n            console.log(\"\\uD83C\\uDFF7️ 创建课程标签数据:\", values);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourseTag(values);\n            if (res.code === 200) {\n                notification.success(\"创建标签成功\");\n                setIsAddTagModalVisible(false);\n                addTagForm.resetFields();\n                // 重新获取标签列表\n                fetchCourseTags();\n            } else {\n                notification.error(res.msg || \"创建标签失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 创建标签失败:\", error);\n            notification.error(\"创建标签失败，请重试\");\n        }\n    };\n    // 发布系列课程\n    const handlePublishSeries = async (values)=>{\n        try {\n            console.log(\"\\uD83D\\uDCE2 发布系列课程数据:\", values);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourseSeries(values.seriesId);\n            if (res.code === 200) {\n                notification.success(\"发布系列课程成功\");\n                setIsPublishSeriesModalVisible(false);\n                publishSeriesForm.resetFields();\n                // 显示发布结果信息\n                const publishData = res.data;\n                console.log(\"✅ 发布成功，系列信息:\", publishData);\n                // 可以选择显示发布统计信息\n                if (publishData.publishStats) {\n                    const stats = publishData.publishStats;\n                    const statsMessage = \"已发布 \".concat(publishData.publishedCourses, \"/\").concat(publishData.totalCourses, \" 个课程，包含 \").concat(stats.videoCourseCount, \" 个视频课程，总时长 \").concat(Math.round(stats.totalVideoDuration / 60), \" 分钟\");\n                    notification.info(statsMessage);\n                }\n            } else {\n                notification.error(res.msg || \"发布系列课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 发布系列课程失败:\", error);\n            notification.error(\"发布系列课程失败，请重试\");\n        }\n    };\n    // 获取发布用的系列课程列表\n    const fetchSeriesForPublish = async ()=>{\n        try {\n            var _res_data;\n            setPublishLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取发布用系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取发布用系列课程列表成功:\", res.data.list);\n                return res.data.list;\n            } else {\n                console.error(\"❌ 获取发布用系列课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程列表失败\");\n                return [];\n            }\n        } catch (error) {\n            console.error(\"❌ 获取发布用系列课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程列表失败，请重试\");\n            return [];\n        } finally{\n            setPublishLoading(false);\n        }\n    };\n    // 获取指定系列的课程详情\n    const fetchSeriesDetailForPublish = async (seriesId)=>{\n        try {\n            setPublishLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取系列课程详情，系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeriesDetail(seriesId);\n            if (res.code === 200 && res.data) {\n                console.log(\"✅ 获取系列课程详情成功:\", res.data);\n                return res.data;\n            } else {\n                console.error(\"❌ 获取系列课程详情失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程详情失败\");\n                return null;\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列课程详情异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程详情失败，请重试\");\n            return null;\n        } finally{\n            setPublishLoading(false);\n        }\n    };\n    // 获取发布弹窗的系列课程列表\n    const fetchPublishSeriesList = async ()=>{\n        try {\n            var _res_data;\n            setPublishFormLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取发布弹窗的系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取发布弹窗系列课程列表成功:\", res.data.list);\n                setPublishSeriesListForModal(res.data.list);\n            } else {\n                console.error(\"❌ 获取发布弹窗系列课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取发布弹窗系列课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程列表失败，请重试\");\n        } finally{\n            setPublishFormLoading(false);\n        }\n    };\n    // 获取指定系列下的子课程列表（用于发布弹窗）\n    const fetchPublishCourseList = async (seriesId)=>{\n        try {\n            var _res_data;\n            console.log(\"\\uD83D\\uDCDD 获取发布弹窗的子课程列表，系列ID:\", seriesId);\n            console.log(\"\\uD83D\\uDD04 使用课程管理API获取草稿状态的子课程...\");\n            // 使用课程管理API获取草稿状态的课程\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getManagementSeriesCourses(seriesId, {\n                page: 1,\n                pageSize: 50,\n                status: 0 // 只获取草稿状态的课程\n            });\n            console.log(\"\\uD83D\\uDD0D 课程管理API响应:\", res);\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取草稿状态子课程列表成功，数量:\", res.data.list.length);\n                setPublishCourseListForModal(res.data.list);\n                if (res.data.list.length === 0) {\n                    _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"该系列暂无草稿状态的课程可发布\");\n                }\n            } else {\n                console.log(\"⚠️ 该系列暂无子课程或API调用失败\");\n                setPublishCourseListForModal([]);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"该系列暂无草稿状态的课程可发布\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取发布弹窗子课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取子课程列表失败，请重试\");\n            setPublishCourseListForModal([]);\n        }\n    };\n    // 处理系列选择（发布弹窗）\n    const handlePublishSeriesChange = async (seriesId)=>{\n        console.log(\"\\uD83D\\uDCDA 发布弹窗选择系列ID:\", seriesId);\n        console.log(\"\\uD83D\\uDCDA 当前系列列表:\", publishSeriesListForModal);\n        setSelectedSeriesForPublish(seriesId);\n        setSelectedCourseForPublish(undefined);\n        setPublishCourseListForModal([]);\n        // 重置表单中的课程选择\n        publishCourseForm.setFieldsValue({\n            courseId: undefined\n        });\n        // 获取该系列下的子课程\n        if (seriesId) {\n            console.log(\"\\uD83D\\uDD04 开始获取系列子课程...\");\n            setPublishFormLoading(true);\n            try {\n                await fetchPublishCourseList(seriesId);\n                console.log(\"✅ 子课程获取完成\");\n            } catch (error) {\n                console.error(\"❌ 子课程获取失败:\", error);\n            } finally{\n                setPublishFormLoading(false);\n            }\n        }\n    };\n    // 处理课程选择（发布弹窗）\n    const handlePublishCourseChange = (courseId)=>{\n        console.log(\"\\uD83D\\uDCD6 发布弹窗选择课程ID:\", courseId);\n        setSelectedCourseForPublish(courseId);\n    };\n    // 重置发布课程弹窗状态\n    const resetPublishCourseModal = ()=>{\n        setIsPublishCourseModalVisible(false);\n        setSelectedSeriesForPublish(undefined);\n        setSelectedCourseForPublish(undefined);\n        setPublishSeriesListForModal([]);\n        setPublishCourseListForModal([]);\n        publishCourseForm.resetFields();\n    };\n    // 打开发布课程弹窗\n    const openPublishCourseModal = async ()=>{\n        setIsPublishCourseModalVisible(true);\n        await fetchPublishSeriesList();\n    };\n    // 发布课程\n    const handlePublishCourse = async (values)=>{\n        try {\n            if (!selectedCourseForPublish || !selectedSeriesForPublish) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"请选择系列课程和子课程\");\n                return;\n            }\n            setPublishFormLoading(true);\n            console.log(\"\\uD83D\\uDCE2 发布课程，课程ID:\", selectedCourseForPublish);\n            console.log(\"\\uD83D\\uDCE2 系列ID:\", selectedSeriesForPublish);\n            console.log(\"\\uD83D\\uDCE4 表单数据:\", values);\n            // 获取当前选中的课程信息\n            const selectedCourse = publishCourseListForModal.find((c)=>c.id === selectedCourseForPublish);\n            if (!selectedCourse) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"未找到选中的课程信息\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDCD6 当前课程信息:\", selectedCourse);\n            // 使用专门的发布课程API\n            console.log(\"\\uD83D\\uDCE4 调用发布课程API，课程ID:\", selectedCourseForPublish);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourse(selectedCourseForPublish);\n            if (res.code === 200) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"发布课程成功\");\n                resetPublishCourseModal();\n                // 显示发布结果信息\n                console.log(\"✅ 发布成功，课程信息:\", res.data);\n                // 刷新课程列表\n                await fetchCourseList();\n                // 如果当前系列已展开，刷新子课程列表\n                if (selectedSeriesForPublish && expandedSeries.has(selectedSeriesForPublish)) {\n                    await fetchSeriesCourses(selectedSeriesForPublish);\n                }\n            } else {\n                console.error(\"❌ 发布课程失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"发布课程失败\");\n            }\n        } catch (error) {\n            var _error_response;\n            console.error(\"❌ 发布课程异常:\", error);\n            console.error(\"❌ 错误详情:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"发布课程失败，请重试\");\n        } finally{\n            setPublishFormLoading(false);\n        }\n    };\n    // 重置发布弹窗状态\n    const resetPublishModal = ()=>{\n        setIsPublishCourseModalVisible(false);\n        setSelectedSeriesForPublish(undefined);\n        setSelectedCourseForPublish(undefined);\n        setPublishSeriesCourses([]);\n        setPublishSeriesOptions([]);\n        publishCourseForm.resetFields();\n    };\n    // 处理图片上传\n    const handleImageUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"系列封面图片上传成功，URL:\", url);\n            setCoverImageUrl(url);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"图片上传成功\");\n        } catch (error) {\n            console.error(\"系列封面图片上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理图片删除\n    const handleImageRemove = async ()=>{\n        setCoverImageUrl(\"\");\n        return true;\n    };\n    // 处理课程封面图片上传\n    const handleCourseCoverUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程封面图片上传成功，URL:\", url);\n            setCourseCoverImageUrl(url);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程封面上传成功\");\n        } catch (error) {\n            console.error(\"课程封面图片上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理课程封面删除\n    const handleCourseCoverRemove = async ()=>{\n        setCourseCoverImageUrl(\"\");\n        return true;\n    };\n    // 处理附件资源上传\n    const handleAdditionalResourceUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"附件资源上传成功，URL:\", url);\n            setAdditionalFiles((prev)=>[\n                    ...prev,\n                    url\n                ]);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url,\n                name: file.name\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"附件 \".concat(file.name, \" 上传成功\"));\n        } catch (error) {\n            console.error(\"附件资源上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"附件 \".concat(file.name, \" 上传失败: \").concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理附件删除\n    const handleAdditionalResourceRemove = async (file)=>{\n        var _file_response;\n        const url = file.url || ((_file_response = file.response) === null || _file_response === void 0 ? void 0 : _file_response.url);\n        setAdditionalFiles((prev)=>prev.filter((f)=>f !== url));\n        return true;\n    };\n    // 处理视频上传\n    const handleVideoUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程视频上传成功，URL:\", url);\n            setCourseVideoUrl(url);\n            setCourseVideoName(file.name);\n            // 如果是视频文件，尝试获取时长\n            const videoElement = document.createElement(\"video\");\n            videoElement.src = url;\n            videoElement.onloadedmetadata = ()=>{\n                setVideoDuration(Math.floor(videoElement.duration));\n            };\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程视频上传成功\");\n        } catch (error) {\n            console.error(\"课程视频上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"视频上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理视频删除\n    const handleVideoRemove = async ()=>{\n        setCourseVideoUrl(\"\");\n        setCourseVideoName(\"\");\n        setVideoDuration(0);\n        return true;\n    };\n    // 处理文档上传\n    const handleDocumentUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程文档上传成功，URL:\", url);\n            setCourseDocumentUrl(url);\n            setCourseDocumentName(file.name);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程文档上传成功\");\n        } catch (error) {\n            console.error(\"课程文档上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"文档上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理文档删除\n    const handleDocumentRemove = async ()=>{\n        setCourseDocumentUrl(\"\");\n        setCourseDocumentName(\"\");\n        return true;\n    };\n    // 教学信息管理函数\n    const addTeachingInfo = ()=>{\n        setTeachingInfoList([\n            ...teachingInfoList,\n            {\n                title: \"\",\n                content: [\n                    \"\"\n                ]\n            }\n        ]);\n    };\n    const removeTeachingInfo = (index)=>{\n        if (teachingInfoList.length > 1) {\n            const newList = teachingInfoList.filter((_, i)=>i !== index);\n            setTeachingInfoList(newList);\n        }\n    };\n    const updateTeachingInfoTitle = (index, title)=>{\n        const newList = [\n            ...teachingInfoList\n        ];\n        newList[index].title = title;\n        setTeachingInfoList(newList);\n    };\n    const updateTeachingInfoContent = (index, contentIndex, content)=>{\n        const newList = [\n            ...teachingInfoList\n        ];\n        newList[index].content[contentIndex] = content;\n        setTeachingInfoList(newList);\n    };\n    const addTeachingInfoContent = (index)=>{\n        const newList = [\n            ...teachingInfoList\n        ];\n        newList[index].content.push(\"\");\n        setTeachingInfoList(newList);\n    };\n    const removeTeachingInfoContent = (index, contentIndex)=>{\n        const newList = [\n            ...teachingInfoList\n        ];\n        if (newList[index].content.length > 1) {\n            newList[index].content.splice(contentIndex, 1);\n            setTeachingInfoList(newList);\n        }\n    };\n    // 打开编辑模态框\n    const openEditModal = async (course)=>{\n        setEditingCourse(course);\n        editCourseForm.setFieldsValue(course);\n        setIsEditCourseModalVisible(true);\n    };\n    // 过滤课程列表\n    const filteredCourses = (courseList || []).filter((course)=>course.name.toLowerCase().includes(searchKeyword.toLowerCase()) || course.description.toLowerCase().includes(searchKeyword.toLowerCase()) || course.category.toLowerCase().includes(searchKeyword.toLowerCase()));\n    // 准备表格数据：将系列课程和子课程合并为一个扁平列表\n    const prepareTableData = ()=>{\n        const tableData = [];\n        console.log(\"\\uD83D\\uDD04 准备表格数据...\");\n        console.log(\"\\uD83D\\uDCCA 系列课程列表:\", seriesList);\n        console.log(\"\\uD83D\\uDCCA 展开的系列:\", Array.from(expandedSeries));\n        console.log(\"\\uD83D\\uDCCA 子课程映射:\", seriesCoursesMap);\n        seriesList.forEach((series)=>{\n            // 添加系列课程行\n            tableData.push({\n                key: \"series-\".concat(series.id),\n                id: series.id,\n                title: series.title,\n                status: series.status,\n                type: \"series\",\n                isExpanded: expandedSeries.has(series.id),\n                seriesId: series.id\n            });\n            // 如果系列已展开，添加子课程行\n            if (expandedSeries.has(series.id)) {\n                const subCourses = seriesCoursesMap.get(series.id) || [];\n                console.log(\"\\uD83D\\uDCDA 系列 \".concat(series.id, \" 的子课程:\"), subCourses);\n                subCourses.forEach((course)=>{\n                    tableData.push({\n                        key: \"course-\".concat(course.id),\n                        id: course.id,\n                        title: course.title,\n                        status: course.status,\n                        type: \"course\",\n                        seriesId: series.id,\n                        parentSeriesTitle: series.title\n                    });\n                });\n            }\n        });\n        console.log(\"\\uD83D\\uDCCB 最终表格数据:\", tableData);\n        return tableData;\n    };\n    // 表格列定义\n    const columns = [\n        {\n            title: \"系列课程ID\",\n            dataIndex: \"id\",\n            key: \"id\",\n            width: 120\n        },\n        {\n            title: \"系列课程/子课程名称\",\n            dataIndex: \"title\",\n            key: \"title\",\n            render: (text, record)=>{\n                if (record.type === \"series\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                type: \"text\",\n                                size: \"small\",\n                                onClick: ()=>toggleSeriesExpansion(record.id),\n                                className: \"p-0 min-w-0 hover:bg-blue-50\",\n                                style: {\n                                    minWidth: \"20px\",\n                                    height: \"20px\"\n                                },\n                                children: record.isExpanded ? \"▼\" : \"▶\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 952,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-blue-600 text-base\",\n                                children: text\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 961,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                color: \"blue\",\n                                className: \"text-xs\",\n                                children: \"系列\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 962,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 951,\n                        columnNumber: 13\n                    }, undefined);\n                } else {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-8 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400\",\n                                children: \"└─\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 968,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-700\",\n                                children: text\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 969,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                color: \"green\",\n                                className: \"text-xs\",\n                                children: \"子课程\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 970,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 967,\n                        columnNumber: 13\n                    }, undefined);\n                }\n            }\n        },\n        {\n            title: \"发布状态\",\n            dataIndex: \"status\",\n            key: \"status\",\n            width: 100,\n            render: (status, record)=>{\n                const getStatusConfig = (status)=>{\n                    switch(status){\n                        case 1:\n                            return {\n                                color: \"green\",\n                                text: \"已发布\"\n                            };\n                        case 0:\n                            return {\n                                color: \"orange\",\n                                text: \"草稿\"\n                            };\n                        case 2:\n                            return {\n                                color: \"red\",\n                                text: \"已归档\"\n                            };\n                        default:\n                            return {\n                                color: \"gray\",\n                                text: \"未知\"\n                            };\n                    }\n                };\n                const config = getStatusConfig(status);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    color: config.color,\n                    children: config.text\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 992,\n                    columnNumber: 16\n                }, undefined);\n            }\n        },\n        {\n            title: \"操作\",\n            key: \"action\",\n            width: 150,\n            render: (record)=>{\n                if (record.type === \"series\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            type: \"link\",\n                            size: \"small\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1006,\n                                columnNumber: 23\n                            }, void 0),\n                            onClick: ()=>{\n                                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"系列课程编辑功能待实现\");\n                            },\n                            children: \"编辑\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1003,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1002,\n                        columnNumber: 13\n                    }, undefined);\n                } else {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        size: \"small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                type: \"link\",\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1021,\n                                    columnNumber: 23\n                                }, void 0),\n                                onClick: ()=>{\n                                    _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"子课程编辑功能待实现\");\n                                },\n                                className: \"text-blue-600 hover:text-blue-800\",\n                                children: \"编辑\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1018,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"确定要删除这个子课程吗？\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1032,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: [\n                                                \"课程名称：\",\n                                                record.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1033,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: [\n                                                \"所属系列：\",\n                                                record.parentSeriesTitle\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1034,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1031,\n                                    columnNumber: 19\n                                }, void 0),\n                                onConfirm: ()=>{\n                                    console.log(\"\\uD83D\\uDDD1️ 用户确认删除子课程:\", record);\n                                    handleDeleteSubCourse(record.id, record.seriesId);\n                                },\n                                okText: \"确定删除\",\n                                cancelText: \"取消\",\n                                okType: \"danger\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    type: \"link\",\n                                    size: \"small\",\n                                    danger: true,\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1049,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    className: \"text-red-600 hover:text-red-800\",\n                                    children: \"删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1045,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1029,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1017,\n                        columnNumber: 13\n                    }, undefined);\n                }\n            }\n        }\n    ];\n    // 获取教师列表\n    // const fetchTeachers = async () => {\n    //   try {\n    //     const { data: res } = await courseApi.getTeachers();\n    //     if (res.code === 200) {\n    //       setTeachers(res.data);\n    //       console.log('成功获取教师列表:', res.data);\n    //     } else {\n    //       console.log('API返回无数据，使用模拟教师数据');\n    //       // 使用模拟数据\n    //       const mockTeachers = [\n    //         { id: 1, name: '张老师', email: '<EMAIL>', subject: '数学', school: '实验小学', avatar: '', phone: '13800138001' },\n    //         { id: 2, name: '李老师', email: '<EMAIL>', subject: '语文', school: '实验小学', avatar: '', phone: '13800138002' },\n    //         { id: 3, name: '王老师', email: '<EMAIL>', subject: '英语', school: '第二小学', avatar: '', phone: '13800138003' },\n    //         { id: 4, name: '赵老师', email: '<EMAIL>', subject: '科学', school: '第二小学', avatar: '', phone: '13800138004' },\n    //         { id: 5, name: '刘老师', email: '<EMAIL>', subject: '编程', school: '实验中学', avatar: '', phone: '13800138005' },\n    //         { id: 6, name: '陈老师', email: '<EMAIL>', subject: '信息技术', school: '实验中学', avatar: '', phone: '13800138006' }\n    //       ];\n    //       setTeachers(mockTeachers);\n    //     }\n    //   } catch (error) {\n    //     console.error('获取教师列表失败:', error);\n    //     // 使用模拟数据\n    //     const mockTeachers = [\n    //       { id: 1, name: '张老师', email: '<EMAIL>', subject: '数学', school: '实验小学', avatar: '', phone: '13800138001' },\n    //       { id: 2, name: '李老师', email: '<EMAIL>', subject: '语文', school: '实验小学', avatar: '', phone: '13800138002' },\n    //       { id: 3, name: '王老师', email: '<EMAIL>', subject: '英语', school: '第二小学', avatar: '', phone: '13800138003' },\n    //       { id: 4, name: '赵老师', email: '<EMAIL>', subject: '科学', school: '第二小学', avatar: '', phone: '13800138004' },\n    //       { id: 5, name: '刘老师', email: '<EMAIL>', subject: '编程', school: '实验中学', avatar: '', phone: '13800138005' },\n    //       { id: 6, name: '陈老师', email: '<EMAIL>', subject: '信息技术', school: '实验中学', avatar: '', phone: '13800138006' }\n    //     ];\n    //     setTeachers(mockTeachers);\n    //     console.log('使用模拟教师数据:', mockTeachers);\n    //   }\n    // };\n    // 获取课程标签列表 - 使用课程市场API\n    const fetchCourseTags = async ()=>{\n        try {\n            console.log(\"\\uD83C\\uDFF7️ 开始获取课程标签列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseTags({\n                page: 1,\n                pageSize: 100,\n                status: 1 // 只获取启用的标签\n            });\n            console.log(\"\\uD83D\\uDCE8 getCourseTags API响应:\", res);\n            if (res.code === 200 && res.data && res.data.list) {\n                const tags = res.data.list.map((tag)=>({\n                        id: tag.id,\n                        name: tag.name,\n                        color: tag.color,\n                        category: tag.category,\n                        description: tag.description || \"\"\n                    }));\n                setCourseTags(tags);\n                console.log(\"✅ 成功获取课程标签列表:\", tags);\n            } else {\n                console.warn(\"⚠️ API返回数据格式异常:\", res);\n                setCourseTags([]);\n                notification.warning(\"获取标签列表失败，请检查网络连接\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取课程标签失败:\", error);\n            setCourseTags([]);\n            notification.error(\"获取标签列表失败，请重试\");\n        }\n    };\n    // 获取课程系列列表 - 使用课程市场API\n    const fetchCourseSeries = async ()=>{\n        try {\n            var _res_data_pagination, _res_data;\n            console.log(\"\\uD83D\\uDD04 开始获取课程市场系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50 // 课程市场API限制最大50\n            });\n            console.log(\"\\uD83D\\uDCE8 getMarketplaceSeries API响应:\", res);\n            // 检查是否有更多数据\n            if (((_res_data = res.data) === null || _res_data === void 0 ? void 0 : (_res_data_pagination = _res_data.pagination) === null || _res_data_pagination === void 0 ? void 0 : _res_data_pagination.total) > 50) {\n                console.log(\"⚠️ 注意：总共有 \".concat(res.data.pagination.total, \" 个系列课程，当前只显示前50个\"));\n            }\n            if (res.code === 200 && res.data) {\n                console.log(\"\\uD83D\\uDCCA API返回的完整数据结构:\", res.data);\n                if (res.data.list && Array.isArray(res.data.list)) {\n                    console.log(\"\\uD83D\\uDCCB 获取到 \".concat(res.data.list.length, \" 个系列课程\"));\n                    // 将课程市场API返回的数据转换为组件需要的格式\n                    const formattedSeries = res.data.list.map((item, index)=>{\n                        var _item_tags;\n                        console.log(\"\\uD83D\\uDD0D 处理第 \".concat(index + 1, \" 个系列:\"), {\n                            id: item.id,\n                            title: item.title,\n                            category: item.category,\n                            categoryLabel: item.categoryLabel,\n                            tags: item.tags\n                        });\n                        return {\n                            id: item.id,\n                            title: item.title,\n                            description: item.description,\n                            coverImage: item.coverImage || \"\",\n                            category: item.categoryLabel || (item.category === 0 ? \"官方\" : \"社区\"),\n                            teacherIds: [],\n                            tagIds: ((_item_tags = item.tags) === null || _item_tags === void 0 ? void 0 : _item_tags.map((tag)=>tag.id)) || [],\n                            createdAt: item.createdAt || new Date().toISOString(),\n                            updatedAt: item.updatedAt || new Date().toISOString()\n                        };\n                    });\n                    setCourseSeries(formattedSeries);\n                    console.log(\"✅ 成功获取系列课程列表:\", formattedSeries);\n                } else {\n                    console.warn(\"⚠️ API返回数据中没有list字段或list不是数组:\", res.data);\n                    setCourseSeries([]);\n                }\n            } else {\n                console.warn(\"⚠️ API返回数据格式异常:\", {\n                    code: res.code,\n                    message: res.message,\n                    data: res.data\n                });\n                setCourseSeries([]);\n            }\n        } catch (error) {\n            console.error(\"❌ 获取课程系列失败:\", error);\n            setCourseSeries([]);\n            notification.error(\"获取系列课程列表失败，请重试\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchCourseList();\n        fetchCourseTags();\n        fetchCourseSeries();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                title: \"课程管理\",\n                extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    type: \"primary\",\n                    onClick: ()=>{\n                        fetchCourseList();\n                        setIsCourseModalVisible(true);\n                    },\n                    children: \"查看全部\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1210,\n                    columnNumber: 16\n                }, void 0),\n                className: \"shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddCourseModalVisible(true),\n                            children: \"添加课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1217,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddSeriesModalVisible(true),\n                            children: \"添加系列课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1220,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddTagModalVisible(true),\n                            type: \"dashed\",\n                            children: \"添加课程标签\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1223,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: openPublishCourseModal,\n                            style: {\n                                backgroundColor: \"white\",\n                                borderColor: \"#d9d9d9\",\n                                color: \"#000000d9\"\n                            },\n                            children: \"发布课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1226,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsPublishSeriesModalVisible(true),\n                            style: {\n                                backgroundColor: \"white\",\n                                borderColor: \"#d9d9d9\",\n                                color: \"#000000d9\"\n                            },\n                            children: \"发布系列课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1229,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1216,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1208,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"课程管理\",\n                open: isCourseModalVisible,\n                onCancel: ()=>setIsCourseModalVisible(false),\n                footer: null,\n                width: 1000,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Search, {\n                                        placeholder: \"搜索系列课程名称\",\n                                        allowClear: true,\n                                        style: {\n                                            width: 300\n                                        },\n                                        onSearch: setSearchKeyword,\n                                        onChange: (e)=>setSearchKeyword(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1245,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                type: \"primary\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1255,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                onClick: ()=>setIsAddCourseModalVisible(true),\n                                                children: \"添加课程\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1253,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                type: \"default\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1262,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                onClick: ()=>setIsAddSeriesModalVisible(true),\n                                                children: \"添加系列课程\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1260,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1252,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1244,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center bg-gray-50 p-3 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-4 text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"系列课程总数: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"text-blue-600\",\n                                                        children: seriesList.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                        lineNumber: 1273,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1273,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"已展开系列: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"text-green-600\",\n                                                        children: expandedSeries.size\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                        lineNumber: 1274,\n                                                        columnNumber: 28\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1274,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"已加载子课程: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"text-orange-600\",\n                                                        children: Array.from(seriesCoursesMap.values()).reduce((total, courses)=>total + courses.length, 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                        lineNumber: 1275,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1275,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1272,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: \"small\",\n                                                type: \"text\",\n                                                onClick: expandAllSeries,\n                                                disabled: seriesLoading,\n                                                className: \"text-blue-600 hover:text-blue-800\",\n                                                children: \"展开所有\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1281,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: \"small\",\n                                                type: \"text\",\n                                                onClick: collapseAllSeries,\n                                                disabled: seriesLoading,\n                                                className: \"text-gray-600 hover:text-gray-800\",\n                                                children: \"收起所有\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1290,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1280,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1271,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1243,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        columns: columns,\n                        dataSource: prepareTableData(),\n                        rowKey: \"key\",\n                        loading: seriesLoading,\n                        pagination: {\n                            pageSize: 20,\n                            showSizeChanger: false,\n                            showTotal: (total)=>\"共 \".concat(total, \" 条记录\")\n                        },\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1303,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1236,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"添加课程\",\n                open: isAddCourseModalVisible,\n                onCancel: ()=>{\n                    setIsAddCourseModalVisible(false);\n                    addCourseForm.resetFields();\n                    setCourseCoverImageUrl(\"\");\n                    setAdditionalFiles([]);\n                    setCourseVideoUrl(\"\");\n                    setCourseVideoName(\"\");\n                    setCourseDocumentUrl(\"\");\n                    setCourseDocumentName(\"\");\n                    setVideoDuration(0);\n                    setTeachingInfoList([\n                        {\n                            title: \"\",\n                            content: [\n                                \"\"\n                            ]\n                        }\n                    ]);\n                },\n                onOk: ()=>addCourseForm.submit(),\n                okText: \"确定\",\n                cancelText: \"取消\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddCourse,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"所属系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择所属系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                showSearch: true,\n                                optionFilterProp: \"children\",\n                                style: {\n                                    width: \"100%\"\n                                },\n                                children: courseSeries.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        title: \"\".concat(series.title, \" - \").concat(series.description),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                overflow: \"hidden\",\n                                                textOverflow: \"ellipsis\",\n                                                whiteSpace: \"nowrap\",\n                                                maxWidth: \"100%\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontWeight: 500\n                                                    },\n                                                    children: series.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1361,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"12px\",\n                                                        color: \"#666\",\n                                                        marginLeft: \"8px\"\n                                                    },\n                                                    children: [\n                                                        \"(\",\n                                                        series.category,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1362,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1355,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, series.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1354,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1347,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1342,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"title\",\n                            label: \"课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程名称\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1376,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1371,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程描述\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程描述\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 4,\n                                placeholder: \"请详细描述课程内容、目标和特色...\",\n                                showCount: true,\n                                maxLength: 500\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1384,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1379,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程封面\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请上传课程封面\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseCover\",\n                                customRequest: handleCourseCoverUpload,\n                                onRemove: handleCourseCoverRemove,\n                                accept: \"image/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseCoverImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: courseCoverImageUrl,\n                                        alt: \"课程封面预览\",\n                                        style: {\n                                            width: \"100%\",\n                                            maxHeight: \"200px\",\n                                            objectFit: \"cover\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1406,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1405,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1411,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1410,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1413,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持单个文件上传，建议上传jpg、png格式图片，大小不超过2MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1414,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1409,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1396,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1392,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"orderIndex\",\n                            label: \"课程序号\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程序号\"\n                                },\n                                {\n                                    type: \"number\",\n                                    min: 0,\n                                    message: \"课程序号必须大于等于0\",\n                                    transform: (value)=>Number(value)\n                                }\n                            ],\n                            tooltip: \"在系列课程中的排序位置，数字越小排序越靠前，从0开始\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                type: \"number\",\n                                placeholder: \"请输入课程在系列中的序号（从0开始）\",\n                                min: 0\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1436,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1422,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程视频\",\n                            tooltip: \"上传课程视频文件，系统将自动识别时长等信息\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseVideo\",\n                                customRequest: handleVideoUpload,\n                                onRemove: handleVideoRemove,\n                                accept: \"video/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseVideoUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            src: courseVideoUrl,\n                                            style: {\n                                                width: \"100%\",\n                                                maxHeight: \"200px\"\n                                            },\n                                            controls: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1456,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                marginTop: 8,\n                                                color: \"#666\"\n                                            },\n                                            children: courseVideoName || \"课程视频\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1461,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1455,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1468,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1467,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽视频文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1470,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持MP4、AVI、MOV等格式，大小不超过100MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1471,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1466,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1446,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1442,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程文档\",\n                            tooltip: \"上传课程相关文档，如PPT、PDF等\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseDocument\",\n                                customRequest: handleDocumentUpload,\n                                onRemove: handleDocumentRemove,\n                                accept: \".pdf,.doc,.docx,.ppt,.pptx\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseDocumentUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"20px\",\n                                            textAlign: \"center\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                style: {\n                                                    fontSize: \"48px\",\n                                                    color: \"#1890ff\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1495,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    marginTop: 8,\n                                                    color: \"#666\"\n                                                },\n                                                children: courseDocumentName || \"课程文档\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1496,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1494,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1493,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1504,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1503,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文档文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1506,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持PDF、Word、PPT格式，大小不超过50MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1507,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1502,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1484,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1480,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"teachingObjectives\",\n                            label: \"教学目标\",\n                            tooltip: \"学员完成本课程后应该达到的学习目标\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                mode: \"tags\",\n                                placeholder: \"理解Node.js的基本概念和特点，掌握Node.js的安装和环境配置\",\n                                style: {\n                                    width: \"100%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1522,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1517,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"附件资源\",\n                            tooltip: \"上传课程相关的附件资源，如PPT、文档、代码等\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    name: \"additionalResources\",\n                                    customRequest: handleAdditionalResourceUpload,\n                                    onRemove: handleAdditionalResourceRemove,\n                                    multiple: true,\n                                    accept: \".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.zip,.rar,.txt\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1540,\n                                            columnNumber: 29\n                                        }, void 0),\n                                        children: \"上传附件资源\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1540,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1533,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: \"12px\",\n                                        color: \"#666\",\n                                        marginTop: 4\n                                    },\n                                    children: \"支持上传PDF、Office文档、压缩包等格式文件，单个文件不超过10MB\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1542,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1529,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1337,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1318,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"编辑课程\",\n                open: isEditCourseModalVisible,\n                onCancel: ()=>{\n                    setIsEditCourseModalVisible(false);\n                    setEditingCourse(null);\n                    editCourseForm.resetFields();\n                },\n                onOk: ()=>editCourseForm.submit(),\n                okText: \"确定\",\n                cancelText: \"取消\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: editCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handleEditCourse,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"name\",\n                            label: \"课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程名称\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1572,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1567,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程描述\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程描述\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 3,\n                                placeholder: \"请输入课程描述\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1580,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1575,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"课程分类\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择课程分类\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择课程分类\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"编程基础\",\n                                        children: \"编程基础\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1589,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"编程进阶\",\n                                        children: \"编程进阶\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1590,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"算法思维\",\n                                        children: \"算法思维\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1591,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"项目实战\",\n                                        children: \"项目实战\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1592,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1588,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1583,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"status\",\n                            label: \"课程状态\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择课程状态\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"active\",\n                                        children: \"启用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1602,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"inactive\",\n                                        children: \"禁用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1603,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1601,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1596,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1562,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1550,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"创建系列课程\",\n                open: isAddSeriesModalVisible,\n                onCancel: ()=>{\n                    setIsAddSeriesModalVisible(false);\n                    addSeriesForm.resetFields();\n                    setCoverImageUrl(\"\");\n                },\n                onOk: ()=>addSeriesForm.submit(),\n                okText: \"创建系列课程\",\n                cancelText: \"取消\",\n                width: 800,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addSeriesForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddSeries,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"title\",\n                            label: \"系列课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入系列课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"例如：React全栈开发实战\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1633,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1628,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程介绍\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程介绍\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 4,\n                                placeholder: \"请详细描述系列课程的内容、目标和特色...\",\n                                showCount: true,\n                                maxLength: 500\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1641,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1636,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"封面图片\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请上传封面图片\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"coverImage\",\n                                customRequest: handleImageUpload,\n                                onRemove: handleImageRemove,\n                                accept: \"image/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: coverImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: coverImageUrl,\n                                        alt: \"封面预览\",\n                                        style: {\n                                            width: \"100%\",\n                                            maxHeight: \"200px\",\n                                            objectFit: \"cover\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1663,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1662,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1668,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1667,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1670,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持单个文件上传，建议上传jpg、png格式图片，大小不超过2MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1671,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1666,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1653,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1649,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"是否为官方系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择是否为官方系列课程\"\n                                }\n                            ],\n                            initialValue: 0,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"是（官方）\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1688,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"否（社区）\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1689,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1687,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1681,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"projectMembers\",\n                            label: \"课程成员\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程成员\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程成员，如：王老师、李助教、张同学\",\n                                showCount: true,\n                                maxLength: 200\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1698,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1693,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"tagIds\",\n                            label: \"标签选择\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                mode: \"multiple\",\n                                placeholder: \"请选择相关标签\",\n                                optionLabelProp: \"label\",\n                                children: courseTags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: tag.id,\n                                        label: tag.name,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            color: tag.color,\n                                            children: tag.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1717,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, tag.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1716,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1710,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1705,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1623,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1610,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"创建课程标签\",\n                open: isAddTagModalVisible,\n                onCancel: ()=>{\n                    setIsAddTagModalVisible(false);\n                    addTagForm.resetFields();\n                },\n                onOk: ()=>addTagForm.submit(),\n                okText: \"创建标签\",\n                cancelText: \"取消\",\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addTagForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddTag,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"name\",\n                            label: \"标签名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入标签名称\"\n                                },\n                                {\n                                    max: 20,\n                                    message: \"标签名称不能超过20个字符\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"例如：高级、编程、实战\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1751,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1743,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"color\",\n                            label: \"标签颜色\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签颜色\"\n                                }\n                            ],\n                            initialValue: \"#007bff\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签颜色\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#007bff\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#007bff\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1763,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"蓝色 (#007bff)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1762,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1761,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#28a745\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#28a745\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1769,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"绿色 (#28a745)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1768,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1767,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#dc3545\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#dc3545\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1775,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"红色 (#dc3545)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1774,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1773,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#ffc107\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#ffc107\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1781,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"黄色 (#ffc107)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1780,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1779,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#6f42c1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#6f42c1\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1787,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"紫色 (#6f42c1)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1786,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1785,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#fd7e14\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#fd7e14\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1793,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"橙色 (#fd7e14)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1792,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1791,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#20c997\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#20c997\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1799,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"青色 (#20c997)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1798,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1797,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#6c757d\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#6c757d\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1805,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"灰色 (#6c757d)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1804,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1803,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1760,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1754,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"标签分类\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签分类\"\n                                }\n                            ],\n                            initialValue: 1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签分类\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"难度标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1819,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"类型标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1820,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 2,\n                                        children: \"特色标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1821,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 3,\n                                        children: \"其他标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1822,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1818,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1812,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"标签描述\",\n                            rules: [\n                                {\n                                    max: 100,\n                                    message: \"标签描述不能超过100个字符\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 3,\n                                placeholder: \"请输入标签的详细描述...\",\n                                showCount: true,\n                                maxLength: 100\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1831,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1826,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"status\",\n                            label: \"标签状态\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签状态\"\n                                }\n                            ],\n                            initialValue: 1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签状态\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"启用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1846,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"禁用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1847,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1845,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1839,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1738,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1726,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"发布系列课程\",\n                open: isPublishSeriesModalVisible,\n                onCancel: ()=>{\n                    setIsPublishSeriesModalVisible(false);\n                    publishSeriesForm.resetFields();\n                },\n                onOk: ()=>publishSeriesForm.submit(),\n                okText: \"发布系列\",\n                cancelText: \"取消\",\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: publishSeriesForm,\n                    layout: \"vertical\",\n                    onFinish: handlePublishSeries,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"选择要发布的系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择要发布的系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                showSearch: true,\n                                filterOption: (input, option)=>{\n                                    var _option_children;\n                                    return option === null || option === void 0 ? void 0 : (_option_children = option.children) === null || _option_children === void 0 ? void 0 : _option_children.toLowerCase().includes(input.toLowerCase());\n                                },\n                                children: courseSeries.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        children: [\n                                            series.title,\n                                            \" (\",\n                                            series.category,\n                                            \")\"\n                                        ]\n                                    }, series.id, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1884,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1876,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1871,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"publishNote\",\n                            label: \"发布说明\",\n                            rules: [\n                                {\n                                    required: false\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                placeholder: \"请输入发布说明（可选）\",\n                                rows: 3,\n                                maxLength: 200,\n                                showCount: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1896,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1891,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"发布说明：\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1905,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-gray-600 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布后系列课程将在课程市场中公开显示\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1907,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 只有已完成的课程才会被发布\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1908,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布后可以查看详细的发布统计信息\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1909,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布状态可以随时修改\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1910,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1906,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1904,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1866,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1854,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"发布课程\",\n                open: isPublishCourseModalVisible,\n                onCancel: resetPublishCourseModal,\n                footer: null,\n                width: 700,\n                destroyOnClose: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: publishCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handlePublishCourse,\n                    className: \"mt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"选择系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                loading: publishFormLoading,\n                                onChange: handlePublishSeriesChange,\n                                showSearch: true,\n                                filterOption: (input, option)=>{\n                                    var _option_children;\n                                    return option === null || option === void 0 ? void 0 : (_option_children = option.children) === null || _option_children === void 0 ? void 0 : _option_children.toLowerCase().includes(input.toLowerCase());\n                                },\n                                children: publishSeriesListForModal.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        children: [\n                                            series.title,\n                                            \" (ID: \",\n                                            series.id,\n                                            \")\"\n                                        ]\n                                    }, series.id, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1947,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1937,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1932,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"courseId\",\n                            label: \"选择子课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择要发布的子课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: selectedSeriesForPublish ? \"请选择要发布的子课程\" : \"请先选择系列课程\",\n                                disabled: !selectedSeriesForPublish,\n                                loading: publishFormLoading && !!selectedSeriesForPublish,\n                                onChange: handlePublishCourseChange,\n                                showSearch: true,\n                                filterOption: (input, option)=>{\n                                    var _option_children;\n                                    return option === null || option === void 0 ? void 0 : (_option_children = option.children) === null || _option_children === void 0 ? void 0 : _option_children.toLowerCase().includes(input.toLowerCase());\n                                },\n                                notFoundContent: publishFormLoading && selectedSeriesForPublish ? \"正在加载子课程...\" : selectedSeriesForPublish ? \"该系列暂无子课程\" : \"请先选择系列课程\",\n                                children: publishCourseListForModal.length > 0 ? publishCourseListForModal.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: course.id,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: course.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1981,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            color: course.status === 1 ? \"green\" : course.status === 0 ? \"orange\" : \"red\",\n                                                            className: \"text-xs\",\n                                                            children: course.status === 1 ? \"已发布\" : course.status === 0 ? \"草稿\" : \"已归档\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 1983,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400 text-xs\",\n                                                            children: [\n                                                                \"ID: \",\n                                                                course.id\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 1986,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1982,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1980,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, course.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1979,\n                                        columnNumber: 19\n                                    }, undefined)) : selectedSeriesForPublish && !publishFormLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                    disabled: true,\n                                    value: \"no-courses\",\n                                    children: \"该系列暂无子课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1993,\n                                    columnNumber: 19\n                                }, undefined) : null\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1960,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1955,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-3 rounded-lg mb-4 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-gray-700 mb-2\",\n                                    children: \"调试信息\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2003,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1 text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"已选择系列ID: \",\n                                                selectedSeriesForPublish || \"未选择\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2005,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"已选择课程ID: \",\n                                                selectedCourseForPublish || \"未选择\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2006,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"系列列表数量: \",\n                                                publishSeriesListForModal.length\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2007,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"子课程列表数量: \",\n                                                publishCourseListForModal.length\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2008,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"加载状态: \",\n                                                publishFormLoading ? \"加载中\" : \"空闲\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2009,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        publishCourseListForModal.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"子课程列表:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 2012,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"ml-4 list-disc\",\n                                                    children: publishCourseListForModal.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                \"ID: \",\n                                                                course.id,\n                                                                \", 名称: \",\n                                                                course.title\n                                                            ]\n                                                        }, course.id, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 2015,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 2013,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2011,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2004,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 2002,\n                            columnNumber: 11\n                        }, undefined),\n                        selectedCourseForPublish && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 p-4 rounded-lg mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-blue-900 mb-2\",\n                                    children: \"即将发布的课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2026,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"课程ID: \",\n                                                selectedCourseForPublish\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2028,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"所属系列: \",\n                                                (_publishSeriesListForModal_find = publishSeriesListForModal.find((s)=>s.id === selectedSeriesForPublish)) === null || _publishSeriesListForModal_find === void 0 ? void 0 : _publishSeriesListForModal_find.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2029,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"课程名称: \",\n                                                (_publishCourseListForModal_find = publishCourseListForModal.find((c)=>c.id === selectedCourseForPublish)) === null || _publishCourseListForModal_find === void 0 ? void 0 : _publishCourseListForModal_find.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2030,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-blue-600 font-medium\",\n                                            children: [\n                                                '点击\"发布此课程\"将调用发布API：POST /api/v1/course-management/courses/',\n                                                selectedCourseForPublish,\n                                                \"/publish\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2031,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2027,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 2025,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: selectedCourseForPublish ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-600\",\n                                        children: \"✓ 已选择课程，可以发布\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2039,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"请先选择系列课程和子课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2041,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2037,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            onClick: resetPublishCourseModal,\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2045,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            type: \"primary\",\n                                            htmlType: \"submit\",\n                                            loading: publishFormLoading,\n                                            disabled: !selectedCourseForPublish,\n                                            className: selectedCourseForPublish ? \"bg-green-600 hover:bg-green-700 border-green-600\" : \"\",\n                                            children: publishFormLoading ? \"发布中...\" : \"发布此课程\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2048,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2044,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 2036,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1925,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1917,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(CourseManagement, \"eXIpukOnbym/dueUi79QqXPXDE8=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm\n    ];\n});\n_c = CourseManagement;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseManagement);\nvar _c;\n$RefreshReg$(_c, \"CourseManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9hZG1pbi1zcGFjZS9jb21wb25lbnRzL2NvdXJzZS1tYW5hZ2VtZW50LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUU0QztBQUNvRTtBQUNjO0FBQ2xEO0FBQ2dGO0FBQy9HO0FBRzdDLE1BQU0sRUFBRXNCLE1BQU0sRUFBRSxHQUFHZixpSkFBS0E7QUFDeEIsTUFBTSxFQUFFZ0IsTUFBTSxFQUFFLEdBQUdmLGlKQUFNQTtBQXdCekIsTUFBTWdCLG1CQUFvRDtRQXk4RGhDQyxpQ0FDQUM7O0lBejhEeEIsTUFBTSxDQUFDQyxZQUFZQyxjQUFjLEdBQUc1QiwrQ0FBUUEsQ0FBVyxFQUFFO0lBQ3pELE1BQU0sQ0FBQzZCLFNBQVNDLFdBQVcsR0FBRzlCLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQytCLHNCQUFzQkMsd0JBQXdCLEdBQUdoQywrQ0FBUUEsQ0FBQztJQUNqRSxNQUFNLENBQUNpQyx5QkFBeUJDLDJCQUEyQixHQUFHbEMsK0NBQVFBLENBQUM7SUFDdkUsTUFBTSxDQUFDbUMsMEJBQTBCQyw0QkFBNEIsR0FBR3BDLCtDQUFRQSxDQUFDO0lBQ3pFLE1BQU0sQ0FBQ3FDLHlCQUF5QkMsMkJBQTJCLEdBQUd0QywrQ0FBUUEsQ0FBQztJQUN2RSxNQUFNLENBQUN1QyxzQkFBc0JDLHdCQUF3QixHQUFHeEMsK0NBQVFBLENBQUM7SUFDakUsTUFBTSxDQUFDeUMsNkJBQTZCQywrQkFBK0IsR0FBRzFDLCtDQUFRQSxDQUFDO0lBQy9FLE1BQU0sQ0FBQzJDLDZCQUE2QkMsK0JBQStCLEdBQUc1QywrQ0FBUUEsQ0FBQztJQUMvRSxNQUFNLENBQUM2QyxlQUFlQyxpQkFBaUIsR0FBRzlDLCtDQUFRQSxDQUFnQjtJQUNsRSxNQUFNLENBQUMrQyxlQUFlQyxpQkFBaUIsR0FBR2hELCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ2lELFVBQVVDLFlBQVksR0FBR2xELCtDQUFRQSxDQUFZLEVBQUU7SUFDdEQsTUFBTSxDQUFDbUQsWUFBWUMsY0FBYyxHQUFHcEQsK0NBQVFBLENBQWMsRUFBRTtJQUM1RCxNQUFNLENBQUNxRCxlQUFlQyxpQkFBaUIsR0FBR3RELCtDQUFRQSxDQUFTO0lBRTNELG9CQUFvQjtJQUNwQixNQUFNLENBQUN1RCxZQUFZQyxjQUFjLEdBQUd4RCwrQ0FBUUEsQ0FBUSxFQUFFO0lBQ3RELE1BQU0sQ0FBQ3lELGtCQUFrQkMsb0JBQW9CLEdBQUcxRCwrQ0FBUUEsQ0FBcUIsSUFBSTJEO0lBQ2pGLE1BQU0sQ0FBQ0MsZ0JBQWdCQyxrQkFBa0IsR0FBRzdELCtDQUFRQSxDQUFjLElBQUk4RDtJQUN0RSxNQUFNLENBQUNDLGVBQWVDLGlCQUFpQixHQUFHaEUsK0NBQVFBLENBQUM7SUFFbkQsV0FBVztJQUNYLE1BQU0sQ0FBQ2lFLDBCQUEwQkMsNEJBQTRCLEdBQUdsRSwrQ0FBUUEsQ0FBcUJtRTtJQUM3RixNQUFNLENBQUNDLDBCQUEwQkMsNEJBQTRCLEdBQUdyRSwrQ0FBUUEsQ0FBcUJtRTtJQUM3RixNQUFNLENBQUNHLHNCQUFzQkMsd0JBQXdCLEdBQUd2RSwrQ0FBUUEsQ0FBUSxFQUFFO0lBQzFFLE1BQU0sQ0FBQ3dFLGdCQUFnQkMsa0JBQWtCLEdBQUd6RSwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUMwRSxzQkFBc0JDLHdCQUF3QixHQUFHM0UsK0NBQVFBLENBQVEsRUFBRTtJQUUxRSxXQUFXO0lBQ1gsTUFBTSxDQUFDeUIsMkJBQTJCbUQsNkJBQTZCLEdBQUc1RSwrQ0FBUUEsQ0FBUSxFQUFFO0lBQ3BGLE1BQU0sQ0FBQzBCLDJCQUEyQm1ELDZCQUE2QixHQUFHN0UsK0NBQVFBLENBQVEsRUFBRTtJQUNwRixNQUFNLENBQUM4RSxvQkFBb0JDLHNCQUFzQixHQUFHL0UsK0NBQVFBLENBQUM7SUFFN0QsTUFBTSxDQUFDZ0YsY0FBY0MsZ0JBQWdCLEdBQUdqRiwrQ0FBUUEsQ0FBaUIsRUFBRTtJQUNuRSxNQUFNLENBQUNrRixxQkFBcUJDLHVCQUF1QixHQUFHbkYsK0NBQVFBLENBQVM7SUFDdkUsTUFBTSxDQUFDb0YsaUJBQWlCQyxtQkFBbUIsR0FBR3JGLCtDQUFRQSxDQUFXLEVBQUU7SUFDbkUsTUFBTSxDQUFDc0YsZ0JBQWdCQyxrQkFBa0IsR0FBR3ZGLCtDQUFRQSxDQUFTO0lBQzdELE1BQU0sQ0FBQ3dGLGlCQUFpQkMsbUJBQW1CLEdBQUd6RiwrQ0FBUUEsQ0FBUztJQUMvRCxNQUFNLENBQUMwRixtQkFBbUJDLHFCQUFxQixHQUFHM0YsK0NBQVFBLENBQVM7SUFDbkUsTUFBTSxDQUFDNEYsb0JBQW9CQyxzQkFBc0IsR0FBRzdGLCtDQUFRQSxDQUFTO0lBQ3JFLE1BQU0sQ0FBQzhGLGVBQWVDLGlCQUFpQixHQUFHL0YsK0NBQVFBLENBQVM7SUFFM0QsU0FBUztJQUNULE1BQU0sQ0FBQ2dHLGtCQUFrQkMsb0JBQW9CLEdBQUdqRywrQ0FBUUEsQ0FHcEQ7UUFBQztZQUFFa0csT0FBTztZQUFJQyxTQUFTO2dCQUFDO2FBQUc7UUFBQztLQUFFO0lBRWxDLE1BQU0sQ0FBQ0MsY0FBYyxHQUFHOUYsaUpBQUlBLENBQUMrRixPQUFPO0lBQ3BDLE1BQU0sQ0FBQ0MsZUFBZSxHQUFHaEcsaUpBQUlBLENBQUMrRixPQUFPO0lBQ3JDLE1BQU0sQ0FBQ0UsY0FBYyxHQUFHakcsaUpBQUlBLENBQUMrRixPQUFPO0lBQ3BDLE1BQU0sQ0FBQ0csV0FBVyxHQUFHbEcsaUpBQUlBLENBQUMrRixPQUFPO0lBQ2pDLE1BQU0sQ0FBQ0ksa0JBQWtCLEdBQUduRyxpSkFBSUEsQ0FBQytGLE9BQU87SUFDeEMsTUFBTSxDQUFDSyxrQkFBa0IsR0FBR3BHLGlKQUFJQSxDQUFDK0YsT0FBTztJQUN4QyxNQUFNTSxlQUFleEYsMEZBQWVBO0lBSXBDLFdBQVc7SUFDWCxNQUFNeUYsa0JBQWtCO1FBQ3RCLElBQUk7Z0JBU3NCQztZQVJ4QjdDLGlCQUFpQjtZQUNqQjhDLFFBQVFDLEdBQUcsQ0FBQztZQUVaLE1BQU0sRUFBRUMsTUFBTUgsR0FBRyxFQUFFLEdBQUcsTUFBTXpGLHNEQUFTQSxDQUFDNkYsb0JBQW9CLENBQUM7Z0JBQ3pEQyxNQUFNO2dCQUNOQyxVQUFVO1lBQ1o7WUFFQSxJQUFJTixJQUFJTyxJQUFJLEtBQUssU0FBT1AsWUFBQUEsSUFBSUcsSUFBSSxjQUFSSCxnQ0FBQUEsVUFBVVEsSUFBSSxHQUFFO2dCQUN0Q1AsUUFBUUMsR0FBRyxDQUFDLGlCQUFpQkYsSUFBSUcsSUFBSSxDQUFDSyxJQUFJO2dCQUMxQzdELGNBQWNxRCxJQUFJRyxJQUFJLENBQUNLLElBQUk7WUFDN0IsT0FBTztnQkFDTFAsUUFBUVEsS0FBSyxDQUFDLGlCQUFpQlQsSUFBSVUsR0FBRztnQkFDdEMxRyxpSkFBT0EsQ0FBQ3lHLEtBQUssQ0FBQ1QsSUFBSVUsR0FBRyxJQUFJO1lBQzNCO1FBQ0YsRUFBRSxPQUFPRCxPQUFPO1lBQ2RSLFFBQVFRLEtBQUssQ0FBQyxpQkFBaUJBO1lBQy9CekcsaUpBQU9BLENBQUN5RyxLQUFLLENBQUM7UUFDaEIsU0FBVTtZQUNSdEQsaUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQSxnQkFBZ0I7SUFDaEIsTUFBTXdELHFCQUFxQixPQUFPQztRQUNoQyxJQUFJO2dCQVVzQlo7WUFUeEJDLFFBQVFDLEdBQUcsQ0FBQyxnQ0FBc0JVO1lBRWxDLHFCQUFxQjtZQUNyQixNQUFNLEVBQUVULE1BQU1ILEdBQUcsRUFBRSxHQUFHLE1BQU16RixzREFBU0EsQ0FBQ3NHLDBCQUEwQixDQUFDRCxVQUFVO2dCQUN6RVAsTUFBTTtnQkFDTkMsVUFBVTtZQUVaO1lBRUEsSUFBSU4sSUFBSU8sSUFBSSxLQUFLLFNBQU9QLFlBQUFBLElBQUlHLElBQUksY0FBUkgsZ0NBQUFBLFVBQVVRLElBQUksR0FBRTtnQkFDdENQLFFBQVFDLEdBQUcsQ0FBQyxrQkFBa0JGLElBQUlHLElBQUksQ0FBQ0ssSUFBSTtnQkFDM0MzRCxvQkFBb0JpRSxDQUFBQSxPQUFRLElBQUloRSxJQUFJZ0UsS0FBS0MsR0FBRyxDQUFDSCxVQUFVWixJQUFJRyxJQUFJLENBQUNLLElBQUk7Z0JBQ3BFeEQsa0JBQWtCOEQsQ0FBQUEsT0FBUSxJQUFJN0QsSUFBSTZELEtBQUtFLEdBQUcsQ0FBQ0o7WUFDN0MsT0FBTztnQkFDTFgsUUFBUVEsS0FBSyxDQUFDLGtCQUFrQlQsSUFBSVUsR0FBRztnQkFDdkMxRyxpSkFBT0EsQ0FBQ3lHLEtBQUssQ0FBQ1QsSUFBSVUsR0FBRyxJQUFJO1lBQzNCO1FBQ0YsRUFBRSxPQUFPRCxPQUFPO1lBQ2RSLFFBQVFRLEtBQUssQ0FBQyxrQkFBa0JBO1lBQ2hDekcsaUpBQU9BLENBQUN5RyxLQUFLLENBQUM7UUFDaEI7SUFDRjtJQUVBLGlCQUFpQjtJQUNqQixNQUFNUSxrQkFBa0I7UUFDdEIsSUFBSTtZQUNGaEIsUUFBUUMsR0FBRyxDQUFDO1lBRVosV0FBVztZQUNYLE1BQU1IO1FBQ1IsRUFBRSxPQUFPVSxPQUFPO1lBQ2RSLFFBQVFRLEtBQUssQ0FBQyxlQUFlQTtZQUM3QlgsYUFBYVcsS0FBSyxDQUFDO1FBQ3JCO0lBQ0Y7SUFFQSxPQUFPO0lBQ1AsTUFBTVMsa0JBQWtCLE9BQU9DO1FBQzdCLElBQUk7WUFDRixvQkFBb0I7WUFDcEIsTUFBTUMsZ0JBQXFCO2dCQUN6QkMsVUFBVTVDLGlCQUFpQixJQUFJO2dCQUMvQjZDLGFBQWF6QyxvQkFBb0IsSUFBSTtZQUN2QztZQUVBLElBQUlKLGdCQUFnQjtnQkFDbEIyQyxjQUFjRyxLQUFLLEdBQUc7b0JBQ3BCQyxLQUFLL0M7b0JBQ0xnRCxNQUFNOUMsbUJBQW1CO2dCQUMzQjtZQUNGO1lBRUEsSUFBSUUsbUJBQW1CO2dCQUNyQnVDLGNBQWNNLFFBQVEsR0FBRztvQkFDdkJGLEtBQUszQztvQkFDTDRDLE1BQU0xQyxzQkFBc0I7Z0JBQzlCO1lBQ0Y7WUFFQSxNQUFNNEMsYUFBYTtnQkFDakJmLFVBQVVnQixTQUFTVCxPQUFPUCxRQUFRO2dCQUNsQ3ZCLE9BQU84QixPQUFPOUIsS0FBSyxDQUFDd0MsSUFBSTtnQkFDeEJDLGFBQWFYLE9BQU9XLFdBQVcsQ0FBQ0QsSUFBSTtnQkFDcENFLFlBQVkxRDtnQkFDWmdELFVBQVU1QyxpQkFBaUIsSUFBSTtnQkFDL0I2QyxhQUFhekMsb0JBQW9CLElBQUk7Z0JBQ3JDSSxlQUFlQSxpQkFBaUI7Z0JBQ2hDbUM7Z0JBQ0FZLGNBQWM3QyxpQkFBaUI4QyxNQUFNLENBQUNDLENBQUFBLE9BQVFBLEtBQUs3QyxLQUFLLENBQUN3QyxJQUFJLE1BQU1LLEtBQUs1QyxPQUFPLENBQUM2QyxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVQLElBQUk7Z0JBQ2hHUSxxQkFBcUI5RCxnQkFBZ0IrRCxHQUFHLENBQUNDLENBQUFBLE9BQVM7d0JBQ2hEbEQsT0FBT2tELEtBQUtDLEtBQUssQ0FBQyxLQUFLQyxHQUFHLE1BQU07d0JBQ2hDakIsS0FBS2U7d0JBQ0xULGFBQWE7b0JBQ2Y7Z0JBQ0FZLFlBQVlkLFNBQVNULE9BQU91QixVQUFVLEtBQUs7WUFDN0M7WUFFQSxTQUFTO1lBQ1QsSUFBSSxDQUFDZixXQUFXZixRQUFRLEVBQUU7Z0JBQ3hCZCxhQUFhVyxLQUFLLENBQUM7Z0JBQ25CO1lBQ0Y7WUFDQSxJQUFJLENBQUNrQixXQUFXdEMsS0FBSyxFQUFFO2dCQUNyQlMsYUFBYVcsS0FBSyxDQUFDO2dCQUNuQjtZQUNGO1lBQ0EsSUFBSSxDQUFDa0IsV0FBV0ksVUFBVSxFQUFFO2dCQUMxQmpDLGFBQWFXLEtBQUssQ0FBQztnQkFDbkI7WUFDRjtZQUVBUixRQUFRQyxHQUFHLENBQUMsd0JBQWN5QjtZQUMxQjFCLFFBQVFDLEdBQUcsQ0FBQyx3QkFBY3lDLEtBQUtDLFNBQVMsQ0FBQ2pCLFlBQVlrQixNQUFNLEVBQUU7WUFFN0QsU0FBUztZQUNULElBQUlDLGFBQWE7WUFDakIsTUFBTUMsYUFBYTtZQUNuQixJQUFJQztZQUVKLE1BQU9GLGNBQWNDLFdBQVk7Z0JBQy9CLElBQUk7b0JBQ0YsTUFBTSxFQUFFNUMsTUFBTUgsR0FBRyxFQUFFLEdBQUcsTUFBTXpGLHNEQUFTQSxDQUFDMEksWUFBWSxDQUFDdEI7b0JBRW5ELGNBQWM7b0JBQ2QsSUFBSTNCLElBQUlPLElBQUksS0FBSyxLQUFLO3dCQUNwQlQsYUFBYW9ELE9BQU8sQ0FBQzt3QkFDckJqQzt3QkFDQTVGLDJCQUEyQjt3QkFDM0JrRSxjQUFjNEQsV0FBVzt3QkFDekI3RSx1QkFBdUI7d0JBQ3ZCRSxtQkFBbUIsRUFBRTt3QkFDckJFLGtCQUFrQjt3QkFDbEJFLG1CQUFtQjt3QkFDbkJFLHFCQUFxQjt3QkFDckJFLHNCQUFzQjt3QkFDdEJFLGlCQUFpQjt3QkFDakJFLG9CQUFvQjs0QkFBQztnQ0FBRUMsT0FBTztnQ0FBSUMsU0FBUztvQ0FBQztpQ0FBRzs0QkFBQzt5QkFBRTt3QkFDbEQ7b0JBQ0YsT0FBTzt3QkFDTFEsYUFBYVcsS0FBSyxDQUFDVCxJQUFJVSxHQUFHLElBQUk7d0JBQzlCO29CQUNGO2dCQUNGLEVBQUUsT0FBT0QsT0FBWTtvQkFDbkJ1QyxZQUFZdkM7b0JBQ1pxQztvQkFFQSxJQUFJQSxjQUFjQyxZQUFZO3dCQUM1QjlDLFFBQVFDLEdBQUcsQ0FBQyxpQkFBa0IsT0FBWDRDLFlBQVc7d0JBQzlCaEQsYUFBYXNELE9BQU8sQ0FBQyxjQUE0QkwsT0FBZEQsWUFBVyxLQUFjLE9BQVhDLFlBQVc7d0JBQzVELFVBQVU7d0JBQ1YsTUFBTSxJQUFJTSxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO29CQUNuRDtnQkFDRjtZQUNGO1lBRUEscUJBQXFCO1lBQ3JCLE1BQU1OO1FBQ1IsRUFBRSxPQUFPdkMsT0FBWTtnQkFJZ0JBLGdCQUM5QkEsc0JBQUFBLGlCQUlNQSxrQkFFQUEsa0JBR0FBLGtCQVNEQSxrQkFDRkE7WUF2QlJSLFFBQVFRLEtBQUssQ0FBQyxhQUFhQTtZQUUzQixXQUFXO1lBQ1gsSUFBSUEsTUFBTUYsSUFBSSxLQUFLLGtCQUFnQkUsaUJBQUFBLE1BQU16RyxPQUFPLGNBQWJ5RyxxQ0FBQUEsZUFBZStDLFFBQVEsQ0FBQyxrQkFDdEQvQyxFQUFBQSxrQkFBQUEsTUFBTWdELFFBQVEsY0FBZGhELHVDQUFBQSx1QkFBQUEsZ0JBQWdCTixJQUFJLGNBQXBCTSwyQ0FBQUEscUJBQXNCekcsT0FBTyxLQUFJeUcsTUFBTWdELFFBQVEsQ0FBQ3RELElBQUksQ0FBQ25HLE9BQU8sQ0FBQ3dKLFFBQVEsQ0FBQyxlQUFnQjtnQkFDekYxRCxhQUFhVyxLQUFLLENBQUM7WUFDckIsT0FBTyxJQUFJQSxNQUFNRixJQUFJLEtBQUssaUJBQWlCO2dCQUN6Q1QsYUFBYVcsS0FBSyxDQUFDO1lBQ3JCLE9BQU8sSUFBSUEsRUFBQUEsbUJBQUFBLE1BQU1nRCxRQUFRLGNBQWRoRCx1Q0FBQUEsaUJBQWdCaUQsTUFBTSxNQUFLLEtBQUs7Z0JBQ3pDNUQsYUFBYVcsS0FBSyxDQUFDO1lBQ3JCLE9BQU8sSUFBSUEsRUFBQUEsbUJBQUFBLE1BQU1nRCxRQUFRLGNBQWRoRCx1Q0FBQUEsaUJBQWdCaUQsTUFBTSxNQUFLLEtBQUs7b0JBQ3hCakQsdUJBQUFBO2dCQUFqQixNQUFNa0QsV0FBV2xELEVBQUFBLG1CQUFBQSxNQUFNZ0QsUUFBUSxjQUFkaEQsd0NBQUFBLHdCQUFBQSxpQkFBZ0JOLElBQUksY0FBcEJNLDRDQUFBQSxzQkFBc0J6RyxPQUFPLEtBQUl5RyxNQUFNekcsT0FBTztnQkFDL0Q4RixhQUFhVyxLQUFLLENBQUMsV0FBb0IsT0FBVGtEO1lBQ2hDLE9BQU8sSUFBSWxELEVBQUFBLG1CQUFBQSxNQUFNZ0QsUUFBUSxjQUFkaEQsdUNBQUFBLGlCQUFnQmlELE1BQU0sTUFBSyxLQUFLO2dCQUN6QzVELGFBQWFXLEtBQUssQ0FBQztZQUNyQixPQUFPO2dCQUNMWCxhQUFhVyxLQUFLLENBQUMsV0FBb0MsT0FBekJBLE1BQU16RyxPQUFPLElBQUk7WUFDakQ7WUFFQWlHLFFBQVFDLEdBQUcsQ0FBQyx3QkFBYztnQkFDeEJsRyxTQUFTeUcsTUFBTXpHLE9BQU87Z0JBQ3RCdUcsTUFBTUUsTUFBTUYsSUFBSTtnQkFDaEJtRCxNQUFNLEdBQUVqRCxtQkFBQUEsTUFBTWdELFFBQVEsY0FBZGhELHVDQUFBQSxpQkFBZ0JpRCxNQUFNO2dCQUM5QnZELElBQUksR0FBRU0sbUJBQUFBLE1BQU1nRCxRQUFRLGNBQWRoRCx1Q0FBQUEsaUJBQWdCTixJQUFJO1lBQzVCO1FBQ0Y7SUFDRjtJQUVBLE9BQU87SUFDUCxNQUFNeUQsbUJBQW1CLE9BQU96QztRQUM5QixJQUFJLENBQUNuRixlQUFlO1FBRXBCLElBQUk7WUFDRixNQUFNLEVBQUVtRSxNQUFNSCxHQUFHLEVBQUUsR0FBRyxNQUFNekYsc0RBQVNBLENBQUNzSixZQUFZLENBQUM3SCxjQUFjOEgsRUFBRSxFQUFFM0M7WUFDckUsSUFBSW5CLElBQUlPLElBQUksS0FBSyxLQUFLO2dCQUNwQlQsYUFBYW9ELE9BQU8sQ0FBQztnQkFDckJqQztnQkFDQTFGLDRCQUE0QjtnQkFDNUJVLGlCQUFpQjtnQkFDakJ3RCxlQUFlMEQsV0FBVztZQUM1QixPQUFPO2dCQUNMckQsYUFBYVcsS0FBSyxDQUFDVCxJQUFJVSxHQUFHLElBQUk7WUFDaEM7UUFDRixFQUFFLE9BQU9ELE9BQU87WUFDZFIsUUFBUVEsS0FBSyxDQUFDLGFBQWFBO1lBQzNCWCxhQUFhVyxLQUFLLENBQUM7UUFDckI7SUFDRjtJQUVBLE9BQU87SUFDUCxNQUFNc0QscUJBQXFCLE9BQU9DO1FBQ2hDLElBQUk7WUFDRixNQUFNLEVBQUU3RCxNQUFNSCxHQUFHLEVBQUUsR0FBRyxNQUFNekYsc0RBQVNBLENBQUMwSixZQUFZLENBQUNEO1lBQ25ELElBQUloRSxJQUFJTyxJQUFJLEtBQUssS0FBSztnQkFDcEJULGFBQWFvRCxPQUFPLENBQUM7Z0JBQ3JCakM7WUFDRixPQUFPO2dCQUNMbkIsYUFBYVcsS0FBSyxDQUFDVCxJQUFJVSxHQUFHLElBQUk7WUFDaEM7UUFDRixFQUFFLE9BQU9ELE9BQU87WUFDZFIsUUFBUVEsS0FBSyxDQUFDLGFBQWFBO1lBQzNCWCxhQUFhVyxLQUFLLENBQUM7UUFDckI7SUFDRjtJQUVBLFFBQVE7SUFDUixNQUFNeUQsd0JBQXdCLE9BQU9GLFVBQWtCcEQ7UUFDckQsSUFBSTtZQUNGWCxRQUFRQyxHQUFHLENBQUMsNkJBQW1COEQsVUFBVSxTQUFTcEQ7WUFFbEQsTUFBTSxFQUFFVCxNQUFNSCxHQUFHLEVBQUUsR0FBRyxNQUFNekYsc0RBQVNBLENBQUMwSixZQUFZLENBQUNEO1lBRW5ELElBQUloRSxJQUFJTyxJQUFJLEtBQUssS0FBSztnQkFDcEJ2RyxpSkFBT0EsQ0FBQ2tKLE9BQU8sQ0FBQztnQkFDaEJqRCxRQUFRQyxHQUFHLENBQUM7Z0JBRVosZ0JBQWdCO2dCQUNoQixNQUFNUyxtQkFBbUJDO2dCQUV6QlgsUUFBUUMsR0FBRyxDQUFDO1lBQ2QsT0FBTztnQkFDTEQsUUFBUVEsS0FBSyxDQUFDLGNBQWNULElBQUlVLEdBQUc7Z0JBQ25DMUcsaUpBQU9BLENBQUN5RyxLQUFLLENBQUNULElBQUlVLEdBQUcsSUFBSTtZQUMzQjtRQUNGLEVBQUUsT0FBT0QsT0FBTztZQUNkUixRQUFRUSxLQUFLLENBQUMsY0FBY0E7WUFDNUJ6RyxpSkFBT0EsQ0FBQ3lHLEtBQUssQ0FBQztRQUNoQjtJQUNGO0lBRUEsY0FBYztJQUNkLE1BQU0wRCx3QkFBd0IsT0FBT3ZEO1FBQ25DWCxRQUFRQyxHQUFHLENBQUMsK0JBQXFCVTtRQUNqQ1gsUUFBUUMsR0FBRyxDQUFDLHdCQUFjbkQsZUFBZXFILEdBQUcsQ0FBQ3hEO1FBRTdDLElBQUk3RCxlQUFlcUgsR0FBRyxDQUFDeEQsV0FBVztZQUNoQyxLQUFLO1lBQ0xYLFFBQVFDLEdBQUcsQ0FBQyxzQkFBWVU7WUFDeEI1RCxrQkFBa0I4RCxDQUFBQTtnQkFDaEIsTUFBTXVELFNBQVMsSUFBSXBILElBQUk2RDtnQkFDdkJ1RCxPQUFPQyxNQUFNLENBQUMxRDtnQkFDZCxPQUFPeUQ7WUFDVDtRQUNGLE9BQU87WUFDTCxlQUFlO1lBQ2ZwRSxRQUFRQyxHQUFHLENBQUMsNEJBQWtCVTtZQUM5QixNQUFNRCxtQkFBbUJDO1FBQzNCO0lBQ0Y7SUFFQSxTQUFTO0lBQ1QsTUFBTTJELGtCQUFrQjtRQUN0QnRFLFFBQVFDLEdBQUcsQ0FBQztRQUNaLEtBQUssTUFBTXNFLFVBQVU5SCxXQUFZO1lBQy9CLElBQUksQ0FBQ0ssZUFBZXFILEdBQUcsQ0FBQ0ksT0FBT1YsRUFBRSxHQUFHO2dCQUNsQyxNQUFNbkQsbUJBQW1CNkQsT0FBT1YsRUFBRTtZQUNwQztRQUNGO0lBQ0Y7SUFFQSxTQUFTO0lBQ1QsTUFBTVcsb0JBQW9CO1FBQ3hCeEUsUUFBUUMsR0FBRyxDQUFDO1FBQ1psRCxrQkFBa0IsSUFBSUM7SUFDeEI7SUFFQSxTQUFTO0lBQ1QsTUFBTXlILGtCQUFrQixPQUFPdkQ7UUFDN0IsSUFBSTtZQUNGLE1BQU13RCxhQUFhO2dCQUNqQixHQUFHeEQsTUFBTTtnQkFDVFksWUFBWXZGO1lBQ2Q7WUFFQXlELFFBQVFDLEdBQUcsQ0FBQyxhQUFheUU7WUFFekIsTUFBTSxFQUFFeEUsTUFBTUgsR0FBRyxFQUFFLEdBQUcsTUFBTXpGLHNEQUFTQSxDQUFDcUssa0JBQWtCLENBQUNEO1lBRXpELElBQUkzRSxJQUFJTyxJQUFJLEtBQUssS0FBSztnQkFDcEJULGFBQWFvRCxPQUFPLENBQUM7Z0JBQ3JCakM7Z0JBQ0F4RiwyQkFBMkI7Z0JBQzNCaUUsY0FBY3lELFdBQVc7Z0JBQ3pCMUcsaUJBQWlCO1lBQ25CLE9BQU87Z0JBQ0xxRCxhQUFhVyxLQUFLLENBQUNULElBQUlVLEdBQUcsSUFBSTtZQUNoQztRQUNGLEVBQUUsT0FBT0QsT0FBTztZQUNkUixRQUFRUSxLQUFLLENBQUMsZUFBZUE7WUFDN0JYLGFBQWFXLEtBQUssQ0FBQztRQUNyQjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQU1vRSxlQUFlLE9BQU8xRDtRQUMxQixJQUFJO1lBQ0ZsQixRQUFRQyxHQUFHLENBQUMsMkJBQWlCaUI7WUFFN0IsTUFBTSxFQUFFaEIsTUFBTUgsR0FBRyxFQUFFLEdBQUcsTUFBTXpGLHNEQUFTQSxDQUFDdUssZUFBZSxDQUFDM0Q7WUFFdEQsSUFBSW5CLElBQUlPLElBQUksS0FBSyxLQUFLO2dCQUNwQlQsYUFBYW9ELE9BQU8sQ0FBQztnQkFDckJ2SCx3QkFBd0I7Z0JBQ3hCZ0UsV0FBV3dELFdBQVc7Z0JBQ3RCLFdBQVc7Z0JBQ1g0QjtZQUNGLE9BQU87Z0JBQ0xqRixhQUFhVyxLQUFLLENBQUNULElBQUlVLEdBQUcsSUFBSTtZQUNoQztRQUNGLEVBQUUsT0FBT0QsT0FBTztZQUNkUixRQUFRUSxLQUFLLENBQUMsYUFBYUE7WUFDM0JYLGFBQWFXLEtBQUssQ0FBQztRQUNyQjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQU11RSxzQkFBc0IsT0FBTzdEO1FBQ2pDLElBQUk7WUFDRmxCLFFBQVFDLEdBQUcsQ0FBQywwQkFBZ0JpQjtZQUU1QixNQUFNLEVBQUVoQixNQUFNSCxHQUFHLEVBQUUsR0FBRyxNQUFNekYsc0RBQVNBLENBQUMwSyxtQkFBbUIsQ0FBQzlELE9BQU9QLFFBQVE7WUFFekUsSUFBSVosSUFBSU8sSUFBSSxLQUFLLEtBQUs7Z0JBQ3BCVCxhQUFhb0QsT0FBTyxDQUFDO2dCQUNyQnJILCtCQUErQjtnQkFDL0IrRCxrQkFBa0J1RCxXQUFXO2dCQUU3QixXQUFXO2dCQUNYLE1BQU0rQixjQUFjbEYsSUFBSUcsSUFBSTtnQkFDNUJGLFFBQVFDLEdBQUcsQ0FBQyxnQkFBZ0JnRjtnQkFFNUIsZUFBZTtnQkFDZixJQUFJQSxZQUFZQyxZQUFZLEVBQUU7b0JBQzVCLE1BQU1DLFFBQVFGLFlBQVlDLFlBQVk7b0JBQ3RDLE1BQU1FLGVBQWUsT0FBdUNILE9BQWhDQSxZQUFZSSxnQkFBZ0IsRUFBQyxLQUFzQ0YsT0FBbkNGLFlBQVlLLFlBQVksRUFBQyxZQUE4Q0MsT0FBcENKLE1BQU1LLGdCQUFnQixFQUFDLGVBQXVELE9BQTFDRCxLQUFLRSxLQUFLLENBQUNOLE1BQU1PLGtCQUFrQixHQUFHLEtBQUk7b0JBQzdLN0YsYUFBYW9DLElBQUksQ0FBQ21EO2dCQUNwQjtZQUNGLE9BQU87Z0JBQ0x2RixhQUFhVyxLQUFLLENBQUNULElBQUlVLEdBQUcsSUFBSTtZQUNoQztRQUNGLEVBQUUsT0FBT0QsT0FBTztZQUNkUixRQUFRUSxLQUFLLENBQUMsZUFBZUE7WUFDN0JYLGFBQWFXLEtBQUssQ0FBQztRQUNyQjtJQUNGO0lBRUEsZUFBZTtJQUNmLE1BQU1tRix3QkFBd0I7UUFDNUIsSUFBSTtnQkFTc0I1RjtZQVJ4QnBDLGtCQUFrQjtZQUNsQnFDLFFBQVFDLEdBQUcsQ0FBQztZQUVaLE1BQU0sRUFBRUMsTUFBTUgsR0FBRyxFQUFFLEdBQUcsTUFBTXpGLHNEQUFTQSxDQUFDNkYsb0JBQW9CLENBQUM7Z0JBQ3pEQyxNQUFNO2dCQUNOQyxVQUFVO1lBQ1o7WUFFQSxJQUFJTixJQUFJTyxJQUFJLEtBQUssU0FBT1AsWUFBQUEsSUFBSUcsSUFBSSxjQUFSSCxnQ0FBQUEsVUFBVVEsSUFBSSxHQUFFO2dCQUN0Q1AsUUFBUUMsR0FBRyxDQUFDLG9CQUFvQkYsSUFBSUcsSUFBSSxDQUFDSyxJQUFJO2dCQUM3QyxPQUFPUixJQUFJRyxJQUFJLENBQUNLLElBQUk7WUFDdEIsT0FBTztnQkFDTFAsUUFBUVEsS0FBSyxDQUFDLG9CQUFvQlQsSUFBSVUsR0FBRztnQkFDekMxRyxpSkFBT0EsQ0FBQ3lHLEtBQUssQ0FBQ1QsSUFBSVUsR0FBRyxJQUFJO2dCQUN6QixPQUFPLEVBQUU7WUFDWDtRQUNGLEVBQUUsT0FBT0QsT0FBTztZQUNkUixRQUFRUSxLQUFLLENBQUMsb0JBQW9CQTtZQUNsQ3pHLGlKQUFPQSxDQUFDeUcsS0FBSyxDQUFDO1lBQ2QsT0FBTyxFQUFFO1FBQ1gsU0FBVTtZQUNSN0Msa0JBQWtCO1FBQ3BCO0lBQ0Y7SUFFQSxjQUFjO0lBQ2QsTUFBTWlJLDhCQUE4QixPQUFPakY7UUFDekMsSUFBSTtZQUNGaEQsa0JBQWtCO1lBQ2xCcUMsUUFBUUMsR0FBRyxDQUFDLCtCQUFxQlU7WUFFakMsTUFBTSxFQUFFVCxNQUFNSCxHQUFHLEVBQUUsR0FBRyxNQUFNekYsc0RBQVNBLENBQUN1TCwwQkFBMEIsQ0FBQ2xGO1lBRWpFLElBQUlaLElBQUlPLElBQUksS0FBSyxPQUFPUCxJQUFJRyxJQUFJLEVBQUU7Z0JBQ2hDRixRQUFRQyxHQUFHLENBQUMsaUJBQWlCRixJQUFJRyxJQUFJO2dCQUNyQyxPQUFPSCxJQUFJRyxJQUFJO1lBQ2pCLE9BQU87Z0JBQ0xGLFFBQVFRLEtBQUssQ0FBQyxpQkFBaUJULElBQUlVLEdBQUc7Z0JBQ3RDMUcsaUpBQU9BLENBQUN5RyxLQUFLLENBQUNULElBQUlVLEdBQUcsSUFBSTtnQkFDekIsT0FBTztZQUNUO1FBQ0YsRUFBRSxPQUFPRCxPQUFPO1lBQ2RSLFFBQVFRLEtBQUssQ0FBQyxpQkFBaUJBO1lBQy9CekcsaUpBQU9BLENBQUN5RyxLQUFLLENBQUM7WUFDZCxPQUFPO1FBQ1QsU0FBVTtZQUNSN0Msa0JBQWtCO1FBQ3BCO0lBQ0Y7SUFFQSxnQkFBZ0I7SUFDaEIsTUFBTW1JLHlCQUF5QjtRQUM3QixJQUFJO2dCQVNzQi9GO1lBUnhCOUIsc0JBQXNCO1lBQ3RCK0IsUUFBUUMsR0FBRyxDQUFDO1lBRVosTUFBTSxFQUFFQyxNQUFNSCxHQUFHLEVBQUUsR0FBRyxNQUFNekYsc0RBQVNBLENBQUM2RixvQkFBb0IsQ0FBQztnQkFDekRDLE1BQU07Z0JBQ05DLFVBQVU7WUFDWjtZQUVBLElBQUlOLElBQUlPLElBQUksS0FBSyxTQUFPUCxZQUFBQSxJQUFJRyxJQUFJLGNBQVJILGdDQUFBQSxVQUFVUSxJQUFJLEdBQUU7Z0JBQ3RDUCxRQUFRQyxHQUFHLENBQUMscUJBQXFCRixJQUFJRyxJQUFJLENBQUNLLElBQUk7Z0JBQzlDekMsNkJBQTZCaUMsSUFBSUcsSUFBSSxDQUFDSyxJQUFJO1lBQzVDLE9BQU87Z0JBQ0xQLFFBQVFRLEtBQUssQ0FBQyxxQkFBcUJULElBQUlVLEdBQUc7Z0JBQzFDMUcsaUpBQU9BLENBQUN5RyxLQUFLLENBQUNULElBQUlVLEdBQUcsSUFBSTtZQUMzQjtRQUNGLEVBQUUsT0FBT0QsT0FBTztZQUNkUixRQUFRUSxLQUFLLENBQUMscUJBQXFCQTtZQUNuQ3pHLGlKQUFPQSxDQUFDeUcsS0FBSyxDQUFDO1FBQ2hCLFNBQVU7WUFDUnZDLHNCQUFzQjtRQUN4QjtJQUNGO0lBRUEsd0JBQXdCO0lBQ3hCLE1BQU04SCx5QkFBeUIsT0FBT3BGO1FBQ3BDLElBQUk7Z0JBYXNCWjtZQVp4QkMsUUFBUUMsR0FBRyxDQUFDLG1DQUF5QlU7WUFDckNYLFFBQVFDLEdBQUcsQ0FBQztZQUVaLHFCQUFxQjtZQUNyQixNQUFNLEVBQUVDLE1BQU1ILEdBQUcsRUFBRSxHQUFHLE1BQU16RixzREFBU0EsQ0FBQ3NHLDBCQUEwQixDQUFDRCxVQUFVO2dCQUN6RVAsTUFBTTtnQkFDTkMsVUFBVTtnQkFDVm9ELFFBQVEsRUFBRyxhQUFhO1lBQzFCO1lBRUF6RCxRQUFRQyxHQUFHLENBQUMsMkJBQWlCRjtZQUU3QixJQUFJQSxJQUFJTyxJQUFJLEtBQUssU0FBT1AsWUFBQUEsSUFBSUcsSUFBSSxjQUFSSCxnQ0FBQUEsVUFBVVEsSUFBSSxHQUFFO2dCQUN0Q1AsUUFBUUMsR0FBRyxDQUFDLHVCQUF1QkYsSUFBSUcsSUFBSSxDQUFDSyxJQUFJLENBQUNxQyxNQUFNO2dCQUN2RDdFLDZCQUE2QmdDLElBQUlHLElBQUksQ0FBQ0ssSUFBSTtnQkFFMUMsSUFBSVIsSUFBSUcsSUFBSSxDQUFDSyxJQUFJLENBQUNxQyxNQUFNLEtBQUssR0FBRztvQkFDOUI3SSxpSkFBT0EsQ0FBQ2tJLElBQUksQ0FBQztnQkFDZjtZQUNGLE9BQU87Z0JBQ0xqQyxRQUFRQyxHQUFHLENBQUM7Z0JBQ1psQyw2QkFBNkIsRUFBRTtnQkFDL0JoRSxpSkFBT0EsQ0FBQ2tJLElBQUksQ0FBQztZQUNmO1FBRUYsRUFBRSxPQUFPekIsT0FBTztZQUNkUixRQUFRUSxLQUFLLENBQUMsb0JBQW9CQTtZQUNsQ3pHLGlKQUFPQSxDQUFDeUcsS0FBSyxDQUFDO1lBQ2R6Qyw2QkFBNkIsRUFBRTtRQUNqQztJQUNGO0lBRUEsZUFBZTtJQUNmLE1BQU1pSSw0QkFBNEIsT0FBT3JGO1FBQ3ZDWCxRQUFRQyxHQUFHLENBQUMsNEJBQWtCVTtRQUM5QlgsUUFBUUMsR0FBRyxDQUFDLHdCQUFjdEY7UUFFMUJ5Qyw0QkFBNEJ1RDtRQUM1QnBELDRCQUE0QkY7UUFDNUJVLDZCQUE2QixFQUFFO1FBRS9CLGFBQWE7UUFDYjZCLGtCQUFrQnFHLGNBQWMsQ0FBQztZQUFFbEMsVUFBVTFHO1FBQVU7UUFFdkQsYUFBYTtRQUNiLElBQUlzRCxVQUFVO1lBQ1pYLFFBQVFDLEdBQUcsQ0FBQztZQUNaaEMsc0JBQXNCO1lBRXRCLElBQUk7Z0JBQ0YsTUFBTThILHVCQUF1QnBGO2dCQUM3QlgsUUFBUUMsR0FBRyxDQUFDO1lBQ2QsRUFBRSxPQUFPTyxPQUFPO2dCQUNkUixRQUFRUSxLQUFLLENBQUMsY0FBY0E7WUFDOUIsU0FBVTtnQkFDUnZDLHNCQUFzQjtZQUN4QjtRQUNGO0lBQ0Y7SUFFQSxlQUFlO0lBQ2YsTUFBTWlJLDRCQUE0QixDQUFDbkM7UUFDakMvRCxRQUFRQyxHQUFHLENBQUMsNEJBQWtCOEQ7UUFDOUJ4Ryw0QkFBNEJ3RztJQUM5QjtJQUVBLGFBQWE7SUFDYixNQUFNb0MsMEJBQTBCO1FBQzlCckssK0JBQStCO1FBQy9Cc0IsNEJBQTRCQztRQUM1QkUsNEJBQTRCRjtRQUM1QlMsNkJBQTZCLEVBQUU7UUFDL0JDLDZCQUE2QixFQUFFO1FBQy9CNkIsa0JBQWtCc0QsV0FBVztJQUMvQjtJQUVBLFdBQVc7SUFDWCxNQUFNa0QseUJBQXlCO1FBQzdCdEssK0JBQStCO1FBQy9CLE1BQU1nSztJQUNSO0lBRUEsT0FBTztJQUNQLE1BQU1PLHNCQUFzQixPQUFPbkY7UUFDakMsSUFBSTtZQUNGLElBQUksQ0FBQzVELDRCQUE0QixDQUFDSCwwQkFBMEI7Z0JBQzFEcEQsaUpBQU9BLENBQUN5RyxLQUFLLENBQUM7Z0JBQ2Q7WUFDRjtZQUVBdkMsc0JBQXNCO1lBQ3RCK0IsUUFBUUMsR0FBRyxDQUFDLDJCQUFpQjNDO1lBQzdCMEMsUUFBUUMsR0FBRyxDQUFDLHNCQUFZOUM7WUFDeEI2QyxRQUFRQyxHQUFHLENBQUMsc0JBQVlpQjtZQUV4QixjQUFjO1lBQ2QsTUFBTW9GLGlCQUFpQjFMLDBCQUEwQjJMLElBQUksQ0FBQ3BFLENBQUFBLElBQUtBLEVBQUUwQixFQUFFLEtBQUt2RztZQUNwRSxJQUFJLENBQUNnSixnQkFBZ0I7Z0JBQ25Cdk0saUpBQU9BLENBQUN5RyxLQUFLLENBQUM7Z0JBQ2Q7WUFDRjtZQUVBUixRQUFRQyxHQUFHLENBQUMsd0JBQWNxRztZQUUxQixlQUFlO1lBQ2Z0RyxRQUFRQyxHQUFHLENBQUMsZ0NBQXNCM0M7WUFDbEMsTUFBTSxFQUFFNEMsTUFBTUgsR0FBRyxFQUFFLEdBQUcsTUFBTXpGLHNEQUFTQSxDQUFDa00sYUFBYSxDQUFDbEo7WUFFcEQsSUFBSXlDLElBQUlPLElBQUksS0FBSyxLQUFLO2dCQUNwQnZHLGlKQUFPQSxDQUFDa0osT0FBTyxDQUFDO2dCQUNoQmtEO2dCQUVBLFdBQVc7Z0JBQ1huRyxRQUFRQyxHQUFHLENBQUMsZ0JBQWdCRixJQUFJRyxJQUFJO2dCQUVwQyxTQUFTO2dCQUNULE1BQU1jO2dCQUVOLG9CQUFvQjtnQkFDcEIsSUFBSTdELDRCQUE0QkwsZUFBZXFILEdBQUcsQ0FBQ2hILDJCQUEyQjtvQkFDNUUsTUFBTXVELG1CQUFtQnZEO2dCQUMzQjtZQUNGLE9BQU87Z0JBQ0w2QyxRQUFRUSxLQUFLLENBQUMsYUFBYVQsSUFBSVUsR0FBRztnQkFDbEMxRyxpSkFBT0EsQ0FBQ3lHLEtBQUssQ0FBQ1QsSUFBSVUsR0FBRyxJQUFJO1lBQzNCO1FBQ0YsRUFBRSxPQUFPRCxPQUFZO2dCQUVNQTtZQUR6QlIsUUFBUVEsS0FBSyxDQUFDLGFBQWFBO1lBQzNCUixRQUFRUSxLQUFLLENBQUMsWUFBV0Esa0JBQUFBLE1BQU1nRCxRQUFRLGNBQWRoRCxzQ0FBQUEsZ0JBQWdCTixJQUFJO1lBQzdDbkcsaUpBQU9BLENBQUN5RyxLQUFLLENBQUM7UUFDaEIsU0FBVTtZQUNSdkMsc0JBQXNCO1FBQ3hCO0lBQ0Y7SUFFQSxXQUFXO0lBQ1gsTUFBTXdJLG9CQUFvQjtRQUN4QjNLLCtCQUErQjtRQUMvQnNCLDRCQUE0QkM7UUFDNUJFLDRCQUE0QkY7UUFDNUJJLHdCQUF3QixFQUFFO1FBQzFCSSx3QkFBd0IsRUFBRTtRQUMxQitCLGtCQUFrQnNELFdBQVc7SUFDL0I7SUFNQSxTQUFTO0lBQ1QsTUFBTXdELG9CQUFrRCxPQUFPQztRQUM3RCxNQUFNLEVBQUVyRSxJQUFJLEVBQUVzRSxTQUFTLEVBQUVDLE9BQU8sRUFBRSxHQUFHRjtRQUVyQyxJQUFJO1lBQ0YsZ0JBQWdCO1lBQ2hCLE1BQU1wRixNQUFNLE1BQU1oSCxzREFBU0EsQ0FBQ3VNLFdBQVcsQ0FBQ3hFO1lBQ3hDdEMsUUFBUUMsR0FBRyxDQUFDLG1CQUFtQnNCO1lBRS9CL0UsaUJBQWlCK0U7WUFDakJxRixzQkFBQUEsZ0NBQUFBLFVBQVk7Z0JBQUVyRixLQUFLQTtZQUFJO1lBQ3ZCeEgsaUpBQU9BLENBQUNrSixPQUFPLENBQUM7UUFDbEIsRUFBRSxPQUFPekMsT0FBWTtZQUNuQlIsUUFBUVEsS0FBSyxDQUFDLGVBQWVBO1lBQzdCekcsaUpBQU9BLENBQUN5RyxLQUFLLENBQUMsU0FBa0MsT0FBekJBLE1BQU16RyxPQUFPLElBQUk7WUFDeEM4TSxvQkFBQUEsOEJBQUFBLFFBQVVyRztRQUNaO0lBQ0Y7SUFFQSxTQUFTO0lBQ1QsTUFBTXVHLG9CQUFvQjtRQUN4QnZLLGlCQUFpQjtRQUNqQixPQUFPO0lBQ1Q7SUFFQSxhQUFhO0lBQ2IsTUFBTXdLLDBCQUF3RCxPQUFPTDtRQUNuRSxNQUFNLEVBQUVyRSxJQUFJLEVBQUVzRSxTQUFTLEVBQUVDLE9BQU8sRUFBRSxHQUFHRjtRQUVyQyxJQUFJO1lBQ0YsZ0JBQWdCO1lBQ2hCLE1BQU1wRixNQUFNLE1BQU1oSCxzREFBU0EsQ0FBQ3VNLFdBQVcsQ0FBQ3hFO1lBQ3hDdEMsUUFBUUMsR0FBRyxDQUFDLG1CQUFtQnNCO1lBRS9CbEQsdUJBQXVCa0Q7WUFDdkJxRixzQkFBQUEsZ0NBQUFBLFVBQVk7Z0JBQUVyRixLQUFLQTtZQUFJO1lBQ3ZCeEgsaUpBQU9BLENBQUNrSixPQUFPLENBQUM7UUFDbEIsRUFBRSxPQUFPekMsT0FBWTtZQUNuQlIsUUFBUVEsS0FBSyxDQUFDLGVBQWVBO1lBQzdCekcsaUpBQU9BLENBQUN5RyxLQUFLLENBQUMsU0FBa0MsT0FBekJBLE1BQU16RyxPQUFPLElBQUk7WUFDeEM4TSxvQkFBQUEsOEJBQUFBLFFBQVVyRztRQUNaO0lBQ0Y7SUFFQSxXQUFXO0lBQ1gsTUFBTXlHLDBCQUEwQjtRQUM5QjVJLHVCQUF1QjtRQUN2QixPQUFPO0lBQ1Q7SUFFQSxXQUFXO0lBQ1gsTUFBTTZJLGlDQUErRCxPQUFPUDtRQUMxRSxNQUFNLEVBQUVyRSxJQUFJLEVBQUVzRSxTQUFTLEVBQUVDLE9BQU8sRUFBRSxHQUFHRjtRQUVyQyxJQUFJO1lBQ0YsZ0JBQWdCO1lBQ2hCLE1BQU1wRixNQUFNLE1BQU1oSCxzREFBU0EsQ0FBQ3VNLFdBQVcsQ0FBQ3hFO1lBQ3hDdEMsUUFBUUMsR0FBRyxDQUFDLGlCQUFpQnNCO1lBRTdCaEQsbUJBQW1Cc0MsQ0FBQUEsT0FBUTt1QkFBSUE7b0JBQU1VO2lCQUFJO1lBQ3pDcUYsc0JBQUFBLGdDQUFBQSxVQUFZO2dCQUFFckYsS0FBS0E7Z0JBQUtDLE1BQU0sS0FBZUEsSUFBSTtZQUFDO1lBQ2xEekgsaUpBQU9BLENBQUNrSixPQUFPLENBQUMsTUFBMEIsT0FBcEIsS0FBZXpCLElBQUksRUFBQztRQUM1QyxFQUFFLE9BQU9oQixPQUFZO1lBQ25CUixRQUFRUSxLQUFLLENBQUMsYUFBYUE7WUFDM0J6RyxpSkFBT0EsQ0FBQ3lHLEtBQUssQ0FBQyxNQUFtQ0EsT0FBN0IsS0FBZWdCLElBQUksRUFBQyxXQUFrQyxPQUF6QmhCLE1BQU16RyxPQUFPLElBQUk7WUFDbEU4TSxvQkFBQUEsOEJBQUFBLFFBQVVyRztRQUNaO0lBQ0Y7SUFFQSxTQUFTO0lBQ1QsTUFBTTJHLGlDQUFpQyxPQUFPN0U7WUFDcEJBO1FBQXhCLE1BQU1mLE1BQU1lLEtBQUtmLEdBQUcsTUFBSWUsaUJBQUFBLEtBQUtrQixRQUFRLGNBQWJsQixxQ0FBQUEsZUFBZWYsR0FBRztRQUMxQ2hELG1CQUFtQnNDLENBQUFBLE9BQVFBLEtBQUttQixNQUFNLENBQUNvRixDQUFBQSxJQUFLQSxNQUFNN0Y7UUFDbEQsT0FBTztJQUNUO0lBQ0EsU0FBUztJQUNULE1BQU04RixvQkFBa0QsT0FBT1Y7UUFDN0QsTUFBTSxFQUFFckUsSUFBSSxFQUFFc0UsU0FBUyxFQUFFQyxPQUFPLEVBQUUsR0FBR0Y7UUFFckMsSUFBSTtZQUNGLE1BQU1wRixNQUFNLE1BQU1oSCxzREFBU0EsQ0FBQ3VNLFdBQVcsQ0FBQ3hFO1lBQ3hDdEMsUUFBUUMsR0FBRyxDQUFDLGlCQUFpQnNCO1lBRTdCOUMsa0JBQWtCOEM7WUFDbEI1QyxtQkFBbUIsS0FBZTZDLElBQUk7WUFFdEMsaUJBQWlCO1lBQ2pCLE1BQU04RixlQUFlN0YsU0FBUzhGLGFBQWEsQ0FBQztZQUM1Q0QsYUFBYUUsR0FBRyxHQUFHakc7WUFDbkIrRixhQUFhRyxnQkFBZ0IsR0FBRztnQkFDOUJ4SSxpQkFBaUJzRyxLQUFLbUMsS0FBSyxDQUFDSixhQUFhSyxRQUFRO1lBQ25EO1lBRUFmLHNCQUFBQSxnQ0FBQUEsVUFBWTtnQkFBRXJGLEtBQUtBO1lBQUk7WUFDdkJ4SCxpSkFBT0EsQ0FBQ2tKLE9BQU8sQ0FBQztRQUNsQixFQUFFLE9BQU96QyxPQUFZO1lBQ25CUixRQUFRUSxLQUFLLENBQUMsYUFBYUE7WUFDM0J6RyxpSkFBT0EsQ0FBQ3lHLEtBQUssQ0FBQyxXQUFvQyxPQUF6QkEsTUFBTXpHLE9BQU8sSUFBSTtZQUMxQzhNLG9CQUFBQSw4QkFBQUEsUUFBVXJHO1FBQ1o7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNb0gsb0JBQW9CO1FBQ3hCbkosa0JBQWtCO1FBQ2xCRSxtQkFBbUI7UUFDbkJNLGlCQUFpQjtRQUNqQixPQUFPO0lBQ1Q7SUFFQSxTQUFTO0lBQ1QsTUFBTTRJLHVCQUFxRCxPQUFPbEI7UUFDaEUsTUFBTSxFQUFFckUsSUFBSSxFQUFFc0UsU0FBUyxFQUFFQyxPQUFPLEVBQUUsR0FBR0Y7UUFFckMsSUFBSTtZQUNGLE1BQU1wRixNQUFNLE1BQU1oSCxzREFBU0EsQ0FBQ3VNLFdBQVcsQ0FBQ3hFO1lBQ3hDdEMsUUFBUUMsR0FBRyxDQUFDLGlCQUFpQnNCO1lBRTdCMUMscUJBQXFCMEM7WUFDckJ4QyxzQkFBc0IsS0FBZXlDLElBQUk7WUFDekNvRixzQkFBQUEsZ0NBQUFBLFVBQVk7Z0JBQUVyRixLQUFLQTtZQUFJO1lBQ3ZCeEgsaUpBQU9BLENBQUNrSixPQUFPLENBQUM7UUFDbEIsRUFBRSxPQUFPekMsT0FBWTtZQUNuQlIsUUFBUVEsS0FBSyxDQUFDLGFBQWFBO1lBQzNCekcsaUpBQU9BLENBQUN5RyxLQUFLLENBQUMsV0FBb0MsT0FBekJBLE1BQU16RyxPQUFPLElBQUk7WUFDMUM4TSxvQkFBQUEsOEJBQUFBLFFBQVVyRztRQUNaO0lBQ0Y7SUFFQSxTQUFTO0lBQ1QsTUFBTXNILHVCQUF1QjtRQUMzQmpKLHFCQUFxQjtRQUNyQkUsc0JBQXNCO1FBQ3RCLE9BQU87SUFDVDtJQUNBLFdBQVc7SUFDWCxNQUFNZ0osa0JBQWtCO1FBQ3RCNUksb0JBQW9CO2VBQUlEO1lBQWtCO2dCQUFFRSxPQUFPO2dCQUFJQyxTQUFTO29CQUFDO2lCQUFHO1lBQUM7U0FBRTtJQUN6RTtJQUVBLE1BQU0ySSxxQkFBcUIsQ0FBQ0M7UUFDMUIsSUFBSS9JLGlCQUFpQjBELE1BQU0sR0FBRyxHQUFHO1lBQy9CLE1BQU1zRixVQUFVaEosaUJBQWlCOEMsTUFBTSxDQUFDLENBQUNtRyxHQUFHQyxJQUFNQSxNQUFNSDtZQUN4RDlJLG9CQUFvQitJO1FBQ3RCO0lBQ0Y7SUFFQSxNQUFNRywwQkFBMEIsQ0FBQ0osT0FBZTdJO1FBQzlDLE1BQU04SSxVQUFVO2VBQUloSjtTQUFpQjtRQUNyQ2dKLE9BQU8sQ0FBQ0QsTUFBTSxDQUFDN0ksS0FBSyxHQUFHQTtRQUN2QkQsb0JBQW9CK0k7SUFDdEI7SUFFQSxNQUFNSSw0QkFBNEIsQ0FBQ0wsT0FBZU0sY0FBc0JsSjtRQUN0RSxNQUFNNkksVUFBVTtlQUFJaEo7U0FBaUI7UUFDckNnSixPQUFPLENBQUNELE1BQU0sQ0FBQzVJLE9BQU8sQ0FBQ2tKLGFBQWEsR0FBR2xKO1FBQ3ZDRixvQkFBb0IrSTtJQUN0QjtJQUVBLE1BQU1NLHlCQUF5QixDQUFDUDtRQUM5QixNQUFNQyxVQUFVO2VBQUloSjtTQUFpQjtRQUNyQ2dKLE9BQU8sQ0FBQ0QsTUFBTSxDQUFDNUksT0FBTyxDQUFDb0osSUFBSSxDQUFDO1FBQzVCdEosb0JBQW9CK0k7SUFDdEI7SUFFQSxNQUFNUSw0QkFBNEIsQ0FBQ1QsT0FBZU07UUFDaEQsTUFBTUwsVUFBVTtlQUFJaEo7U0FBaUI7UUFDckMsSUFBSWdKLE9BQU8sQ0FBQ0QsTUFBTSxDQUFDNUksT0FBTyxDQUFDdUQsTUFBTSxHQUFHLEdBQUc7WUFDckNzRixPQUFPLENBQUNELE1BQU0sQ0FBQzVJLE9BQU8sQ0FBQ3NKLE1BQU0sQ0FBQ0osY0FBYztZQUM1Q3BKLG9CQUFvQitJO1FBQ3RCO0lBQ0Y7SUFRQSxVQUFVO0lBQ1YsTUFBTVUsZ0JBQWdCLE9BQU9DO1FBQzNCN00saUJBQWlCNk07UUFDakJySixlQUFleUcsY0FBYyxDQUFDNEM7UUFDOUJ2Tiw0QkFBNEI7SUFDOUI7SUFFQSxTQUFTO0lBQ1QsTUFBTXdOLGtCQUFrQixDQUFDak8sY0FBYyxFQUFFLEVBQUVtSCxNQUFNLENBQUM2RyxDQUFBQSxTQUNoREEsT0FBT3JILElBQUksQ0FBQ3VILFdBQVcsR0FBR3hGLFFBQVEsQ0FBQ3RILGNBQWM4TSxXQUFXLE9BQzVERixPQUFPaEgsV0FBVyxDQUFDa0gsV0FBVyxHQUFHeEYsUUFBUSxDQUFDdEgsY0FBYzhNLFdBQVcsT0FDbkVGLE9BQU9HLFFBQVEsQ0FBQ0QsV0FBVyxHQUFHeEYsUUFBUSxDQUFDdEgsY0FBYzhNLFdBQVc7SUFHbEUsNEJBQTRCO0lBQzVCLE1BQU1FLG1CQUFtQjtRQUN2QixNQUFNQyxZQUFtQixFQUFFO1FBRTNCbEosUUFBUUMsR0FBRyxDQUFDO1FBQ1pELFFBQVFDLEdBQUcsQ0FBQyx3QkFBY3hEO1FBQzFCdUQsUUFBUUMsR0FBRyxDQUFDLHVCQUFha0osTUFBTUMsSUFBSSxDQUFDdE07UUFDcENrRCxRQUFRQyxHQUFHLENBQUMsdUJBQWF0RDtRQUV6QkYsV0FBVzRNLE9BQU8sQ0FBQzlFLENBQUFBO1lBQ2pCLFVBQVU7WUFDVjJFLFVBQVVULElBQUksQ0FBQztnQkFDYmEsS0FBSyxVQUFvQixPQUFWL0UsT0FBT1YsRUFBRTtnQkFDeEJBLElBQUlVLE9BQU9WLEVBQUU7Z0JBQ2J6RSxPQUFPbUYsT0FBT25GLEtBQUs7Z0JBQ25CcUUsUUFBUWMsT0FBT2QsTUFBTTtnQkFDckI4RixNQUFNO2dCQUNOQyxZQUFZMU0sZUFBZXFILEdBQUcsQ0FBQ0ksT0FBT1YsRUFBRTtnQkFDeENsRCxVQUFVNEQsT0FBT1YsRUFBRTtZQUNyQjtZQUVBLGlCQUFpQjtZQUNqQixJQUFJL0csZUFBZXFILEdBQUcsQ0FBQ0ksT0FBT1YsRUFBRSxHQUFHO2dCQUNqQyxNQUFNNEYsYUFBYTlNLGlCQUFpQitNLEdBQUcsQ0FBQ25GLE9BQU9WLEVBQUUsS0FBSyxFQUFFO2dCQUN4RDdELFFBQVFDLEdBQUcsQ0FBQyxtQkFBbUIsT0FBVnNFLE9BQU9WLEVBQUUsRUFBQyxXQUFTNEY7Z0JBRXhDQSxXQUFXSixPQUFPLENBQUNSLENBQUFBO29CQUNqQkssVUFBVVQsSUFBSSxDQUFDO3dCQUNiYSxLQUFLLFVBQW9CLE9BQVZULE9BQU9oRixFQUFFO3dCQUN4QkEsSUFBSWdGLE9BQU9oRixFQUFFO3dCQUNiekUsT0FBT3lKLE9BQU96SixLQUFLO3dCQUNuQnFFLFFBQVFvRixPQUFPcEYsTUFBTTt3QkFDckI4RixNQUFNO3dCQUNONUksVUFBVTRELE9BQU9WLEVBQUU7d0JBQ25COEYsbUJBQW1CcEYsT0FBT25GLEtBQUs7b0JBQ2pDO2dCQUNGO1lBQ0Y7UUFDRjtRQUVBWSxRQUFRQyxHQUFHLENBQUMsd0JBQWNpSjtRQUMxQixPQUFPQTtJQUNUO0lBRUEsUUFBUTtJQUNSLE1BQU1VLFVBQVU7UUFDZDtZQUNFeEssT0FBTztZQUNQeUssV0FBVztZQUNYUCxLQUFLO1lBQ0xRLE9BQU87UUFDVDtRQUNBO1lBQ0UxSyxPQUFPO1lBQ1B5SyxXQUFXO1lBQ1hQLEtBQUs7WUFDTFMsUUFBUSxDQUFDQyxNQUFjQztnQkFDckIsSUFBSUEsT0FBT1YsSUFBSSxLQUFLLFVBQVU7b0JBQzVCLHFCQUNFLDhEQUFDVzt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUM5USxpSkFBTUE7Z0NBQ0xrUSxNQUFLO2dDQUNMYSxNQUFLO2dDQUNMQyxTQUFTLElBQU1uRyxzQkFBc0IrRixPQUFPcEcsRUFBRTtnQ0FDOUNzRyxXQUFVO2dDQUNWRyxPQUFPO29DQUFFQyxVQUFVO29DQUFRQyxRQUFRO2dDQUFPOzBDQUV6Q1AsT0FBT1QsVUFBVSxHQUFHLE1BQU07Ozs7OzswQ0FFN0IsOERBQUNpQjtnQ0FBS04sV0FBVTswQ0FBdUNIOzs7Ozs7MENBQ3ZELDhEQUFDcFEsa0pBQUdBO2dDQUFDOFEsT0FBTTtnQ0FBT1AsV0FBVTswQ0FBVTs7Ozs7Ozs7Ozs7O2dCQUc1QyxPQUFPO29CQUNMLHFCQUNFLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNNO2dDQUFLTixXQUFVOzBDQUFnQjs7Ozs7OzBDQUNoQyw4REFBQ007Z0NBQUtOLFdBQVU7MENBQWlCSDs7Ozs7OzBDQUNqQyw4REFBQ3BRLGtKQUFHQTtnQ0FBQzhRLE9BQU07Z0NBQVFQLFdBQVU7MENBQVU7Ozs7Ozs7Ozs7OztnQkFHN0M7WUFDRjtRQUNGO1FBQ0E7WUFDRS9LLE9BQU87WUFDUHlLLFdBQVc7WUFDWFAsS0FBSztZQUNMUSxPQUFPO1lBQ1BDLFFBQVEsQ0FBQ3RHLFFBQWdCd0c7Z0JBQ3ZCLE1BQU1VLGtCQUFrQixDQUFDbEg7b0JBQ3ZCLE9BQVFBO3dCQUNOLEtBQUs7NEJBQUcsT0FBTztnQ0FBRWlILE9BQU87Z0NBQVNWLE1BQU07NEJBQU07d0JBQzdDLEtBQUs7NEJBQUcsT0FBTztnQ0FBRVUsT0FBTztnQ0FBVVYsTUFBTTs0QkFBSzt3QkFDN0MsS0FBSzs0QkFBRyxPQUFPO2dDQUFFVSxPQUFPO2dDQUFPVixNQUFNOzRCQUFNO3dCQUMzQzs0QkFBUyxPQUFPO2dDQUFFVSxPQUFPO2dDQUFRVixNQUFNOzRCQUFLO29CQUM5QztnQkFDRjtnQkFFQSxNQUFNWSxTQUFTRCxnQkFBZ0JsSDtnQkFDL0IscUJBQU8sOERBQUM3SixrSkFBR0E7b0JBQUM4USxPQUFPRSxPQUFPRixLQUFLOzhCQUFHRSxPQUFPWixJQUFJOzs7Ozs7WUFDL0M7UUFDRjtRQUNBO1lBQ0U1SyxPQUFPO1lBQ1BrSyxLQUFLO1lBQ0xRLE9BQU87WUFDUEMsUUFBUSxDQUFDRTtnQkFDUCxJQUFJQSxPQUFPVixJQUFJLEtBQUssVUFBVTtvQkFDNUIscUJBQ0UsOERBQUM1UCxrSkFBS0E7d0JBQUN5USxNQUFLO2tDQUNWLDRFQUFDL1EsaUpBQU1BOzRCQUNMa1EsTUFBSzs0QkFDTGEsTUFBSzs0QkFDTFMsb0JBQU0sOERBQUM1USxzSkFBWUE7Ozs7OzRCQUNuQm9RLFNBQVM7Z0NBQ1B0USxpSkFBT0EsQ0FBQ2tJLElBQUksQ0FBQzs0QkFDZjtzQ0FDRDs7Ozs7Ozs7Ozs7Z0JBS1AsT0FBTztvQkFDTCxxQkFDRSw4REFBQ3RJLGtKQUFLQTt3QkFBQ3lRLE1BQUs7OzBDQUNWLDhEQUFDL1EsaUpBQU1BO2dDQUNMa1EsTUFBSztnQ0FDTGEsTUFBSztnQ0FDTFMsb0JBQU0sOERBQUM1USxzSkFBWUE7Ozs7O2dDQUNuQm9RLFNBQVM7b0NBQ1B0USxpSkFBT0EsQ0FBQ2tJLElBQUksQ0FBQztnQ0FDZjtnQ0FDQWtJLFdBQVU7MENBQ1g7Ozs7OzswQ0FHRCw4REFBQ3RRLGtKQUFVQTtnQ0FDVHVGLHFCQUNFLDhEQUFDOEs7O3NEQUNDLDhEQUFDWTtzREFBRTs7Ozs7O3NEQUNILDhEQUFDQTs0Q0FBRVgsV0FBVTs7Z0RBQXdCO2dEQUFNRixPQUFPN0ssS0FBSzs7Ozs7OztzREFDdkQsOERBQUMwTDs0Q0FBRVgsV0FBVTs7Z0RBQXdCO2dEQUFNRixPQUFPTixpQkFBaUI7Ozs7Ozs7Ozs7Ozs7Z0NBR3ZFb0IsV0FBVztvQ0FDVC9LLFFBQVFDLEdBQUcsQ0FBQyw0QkFBa0JnSztvQ0FDOUJoRyxzQkFBc0JnRyxPQUFPcEcsRUFBRSxFQUFFb0csT0FBT3RKLFFBQVE7Z0NBQ2xEO2dDQUNBcUssUUFBTztnQ0FDUEMsWUFBVztnQ0FDWEMsUUFBTzswQ0FFUCw0RUFBQzdSLGlKQUFNQTtvQ0FDTGtRLE1BQUs7b0NBQ0xhLE1BQUs7b0NBQ0xlLE1BQU07b0NBQ05OLG9CQUFNLDhEQUFDM1Esc0pBQWNBOzs7OztvQ0FDckJpUSxXQUFVOzhDQUNYOzs7Ozs7Ozs7Ozs7Ozs7OztnQkFNVDtZQUNGO1FBQ0Y7S0FDRDtJQUVELFNBQVM7SUFDVCxzQ0FBc0M7SUFDdEMsVUFBVTtJQUNWLDJEQUEyRDtJQUMzRCw4QkFBOEI7SUFDOUIsK0JBQStCO0lBQy9CLDRDQUE0QztJQUM1QyxlQUFlO0lBQ2YsMENBQTBDO0lBQzFDLGtCQUFrQjtJQUNsQiwrQkFBK0I7SUFDL0IsOEhBQThIO0lBQzlILDJIQUEySDtJQUMzSCw2SEFBNkg7SUFDN0gsNkhBQTZIO0lBQzdILDRIQUE0SDtJQUM1SCw4SEFBOEg7SUFDOUgsV0FBVztJQUNYLG1DQUFtQztJQUNuQyxRQUFRO0lBQ1Isc0JBQXNCO0lBQ3RCLHlDQUF5QztJQUN6QyxnQkFBZ0I7SUFDaEIsNkJBQTZCO0lBQzdCLDRIQUE0SDtJQUM1SCx5SEFBeUg7SUFDekgsMkhBQTJIO0lBQzNILDJIQUEySDtJQUMzSCwwSEFBMEg7SUFDMUgsNEhBQTRIO0lBQzVILFNBQVM7SUFDVCxpQ0FBaUM7SUFDakMsOENBQThDO0lBQzlDLE1BQU07SUFDTixLQUFLO0lBRUwsdUJBQXVCO0lBQ3ZCLE1BQU1yRixrQkFBa0I7UUFDdEIsSUFBSTtZQUNGOUUsUUFBUUMsR0FBRyxDQUFDO1lBQ1osTUFBTSxFQUFFQyxNQUFNSCxHQUFHLEVBQUUsR0FBRyxNQUFNekYsc0RBQVNBLENBQUM4USxhQUFhLENBQUM7Z0JBQ2xEaEwsTUFBTTtnQkFDTkMsVUFBVTtnQkFDVm9ELFFBQVEsRUFBTyxXQUFXO1lBQzVCO1lBRUF6RCxRQUFRQyxHQUFHLENBQUMscUNBQTJCRjtZQUV2QyxJQUFJQSxJQUFJTyxJQUFJLEtBQUssT0FBT1AsSUFBSUcsSUFBSSxJQUFJSCxJQUFJRyxJQUFJLENBQUNLLElBQUksRUFBRTtnQkFDakQsTUFBTThLLE9BQU90TCxJQUFJRyxJQUFJLENBQUNLLElBQUksQ0FBQzhCLEdBQUcsQ0FBQyxDQUFDaUosTUFBYzt3QkFDNUN6SCxJQUFJeUgsSUFBSXpILEVBQUU7d0JBQ1ZyQyxNQUFNOEosSUFBSTlKLElBQUk7d0JBQ2RrSixPQUFPWSxJQUFJWixLQUFLO3dCQUNoQjFCLFVBQVVzQyxJQUFJdEMsUUFBUTt3QkFDdEJuSCxhQUFheUosSUFBSXpKLFdBQVcsSUFBSTtvQkFDbEM7Z0JBRUF2RixjQUFjK087Z0JBQ2RyTCxRQUFRQyxHQUFHLENBQUMsaUJBQWlCb0w7WUFDL0IsT0FBTztnQkFDTHJMLFFBQVF1TCxJQUFJLENBQUMsbUJBQW1CeEw7Z0JBQ2hDekQsY0FBYyxFQUFFO2dCQUNoQnVELGFBQWFzRCxPQUFPLENBQUM7WUFDdkI7UUFDRixFQUFFLE9BQU8zQyxPQUFPO1lBQ2RSLFFBQVFRLEtBQUssQ0FBQyxlQUFlQTtZQUM3QmxFLGNBQWMsRUFBRTtZQUNoQnVELGFBQWFXLEtBQUssQ0FBQztRQUNyQjtJQUNGO0lBRUEsdUJBQXVCO0lBQ3ZCLE1BQU1nTCxvQkFBb0I7UUFDeEIsSUFBSTtnQkFVRXpMLHNCQUFBQTtZQVRKQyxRQUFRQyxHQUFHLENBQUM7WUFDWixNQUFNLEVBQUVDLE1BQU1ILEdBQUcsRUFBRSxHQUFHLE1BQU16RixzREFBU0EsQ0FBQzZGLG9CQUFvQixDQUFDO2dCQUN6REMsTUFBTTtnQkFDTkMsVUFBVSxHQUFHLGdCQUFnQjtZQUMvQjtZQUVBTCxRQUFRQyxHQUFHLENBQUMsNENBQWtDRjtZQUU5QyxZQUFZO1lBQ1osSUFBSUEsRUFBQUEsWUFBQUEsSUFBSUcsSUFBSSxjQUFSSCxpQ0FBQUEsdUJBQUFBLFVBQVUwTCxVQUFVLGNBQXBCMUwsMkNBQUFBLHFCQUFzQjJMLEtBQUssSUFBRyxJQUFJO2dCQUNwQzFMLFFBQVFDLEdBQUcsQ0FBQyxhQUF1QyxPQUExQkYsSUFBSUcsSUFBSSxDQUFDdUwsVUFBVSxDQUFDQyxLQUFLLEVBQUM7WUFDckQ7WUFFQSxJQUFJM0wsSUFBSU8sSUFBSSxLQUFLLE9BQU9QLElBQUlHLElBQUksRUFBRTtnQkFDaENGLFFBQVFDLEdBQUcsQ0FBQyw4QkFBb0JGLElBQUlHLElBQUk7Z0JBRXhDLElBQUlILElBQUlHLElBQUksQ0FBQ0ssSUFBSSxJQUFJNEksTUFBTXdDLE9BQU8sQ0FBQzVMLElBQUlHLElBQUksQ0FBQ0ssSUFBSSxHQUFHO29CQUNqRFAsUUFBUUMsR0FBRyxDQUFDLG9CQUErQixPQUFyQkYsSUFBSUcsSUFBSSxDQUFDSyxJQUFJLENBQUNxQyxNQUFNLEVBQUM7b0JBRTNDLDBCQUEwQjtvQkFDMUIsTUFBTWdKLGtCQUFrQjdMLElBQUlHLElBQUksQ0FBQ0ssSUFBSSxDQUFDOEIsR0FBRyxDQUFDLENBQUN3SixNQUFXNUQ7NEJBZ0IxQzREO3dCQWZWN0wsUUFBUUMsR0FBRyxDQUFDLG9CQUFvQixPQUFWZ0ksUUFBUSxHQUFFLFVBQVE7NEJBQ3RDcEUsSUFBSWdJLEtBQUtoSSxFQUFFOzRCQUNYekUsT0FBT3lNLEtBQUt6TSxLQUFLOzRCQUNqQjRKLFVBQVU2QyxLQUFLN0MsUUFBUTs0QkFDdkI4QyxlQUFlRCxLQUFLQyxhQUFhOzRCQUNqQ1QsTUFBTVEsS0FBS1IsSUFBSTt3QkFDakI7d0JBRUEsT0FBTzs0QkFDTHhILElBQUlnSSxLQUFLaEksRUFBRTs0QkFDWHpFLE9BQU95TSxLQUFLek0sS0FBSzs0QkFDakJ5QyxhQUFhZ0ssS0FBS2hLLFdBQVc7NEJBQzdCQyxZQUFZK0osS0FBSy9KLFVBQVUsSUFBSTs0QkFDL0JrSCxVQUFVNkMsS0FBS0MsYUFBYSxJQUFLRCxDQUFBQSxLQUFLN0MsUUFBUSxLQUFLLElBQUksT0FBTyxJQUFHOzRCQUNqRStDLFlBQVksRUFBRTs0QkFDZEMsUUFBUUgsRUFBQUEsYUFBQUEsS0FBS1IsSUFBSSxjQUFUUSxpQ0FBQUEsV0FBV3hKLEdBQUcsQ0FBQyxDQUFDaUosTUFBYUEsSUFBSXpILEVBQUUsTUFBSyxFQUFFOzRCQUNsRG9JLFdBQVdKLEtBQUtJLFNBQVMsSUFBSSxJQUFJQyxPQUFPQyxXQUFXOzRCQUNuREMsV0FBV1AsS0FBS08sU0FBUyxJQUFJLElBQUlGLE9BQU9DLFdBQVc7d0JBQ3JEO29CQUNGO29CQUVBaE8sZ0JBQWdCeU47b0JBQ2hCNUwsUUFBUUMsR0FBRyxDQUFDLGlCQUFpQjJMO2dCQUMvQixPQUFPO29CQUNMNUwsUUFBUXVMLElBQUksQ0FBQyxpQ0FBaUN4TCxJQUFJRyxJQUFJO29CQUN0RC9CLGdCQUFnQixFQUFFO2dCQUNwQjtZQUNGLE9BQU87Z0JBQ0w2QixRQUFRdUwsSUFBSSxDQUFDLG1CQUFtQjtvQkFDOUJqTCxNQUFNUCxJQUFJTyxJQUFJO29CQUNkdkcsU0FBU2dHLElBQUloRyxPQUFPO29CQUNwQm1HLE1BQU1ILElBQUlHLElBQUk7Z0JBQ2hCO2dCQUNBL0IsZ0JBQWdCLEVBQUU7WUFDcEI7UUFDRixFQUFFLE9BQU9xQyxPQUFPO1lBQ2RSLFFBQVFRLEtBQUssQ0FBQyxlQUFlQTtZQUM3QnJDLGdCQUFnQixFQUFFO1lBQ2xCMEIsYUFBYVcsS0FBSyxDQUFDO1FBQ3JCO0lBQ0Y7SUFFQXJILGdEQUFTQSxDQUFDO1FBQ1I2SDtRQUVBOEQ7UUFDQTBHO0lBQ0YsR0FBRyxFQUFFO0lBRUwscUJBQ0U7OzBCQUNFLDhEQUFDcFMsa0pBQUlBO2dCQUNIZ0csT0FBTTtnQkFDTmlOLHFCQUFPLDhEQUFDaFQsaUpBQU1BO29CQUFDa1EsTUFBSztvQkFBVWMsU0FBUzt3QkFDckNySjt3QkFDQTlGLHdCQUF3QjtvQkFDMUI7OEJBQUc7Ozs7OztnQkFDSGlQLFdBQVU7MEJBRVYsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQzlRLGlKQUFNQTs0QkFBQ2lULEtBQUs7NEJBQUNqQyxTQUFTLElBQU1qUCwyQkFBMkI7c0NBQU87Ozs7OztzQ0FHL0QsOERBQUMvQixpSkFBTUE7NEJBQUNpVCxLQUFLOzRCQUFDakMsU0FBUyxJQUFNN08sMkJBQTJCO3NDQUFPOzs7Ozs7c0NBRy9ELDhEQUFDbkMsaUpBQU1BOzRCQUFDaVQsS0FBSzs0QkFBQ2pDLFNBQVMsSUFBTTNPLHdCQUF3Qjs0QkFBTzZOLE1BQUs7c0NBQVM7Ozs7OztzQ0FHMUUsOERBQUNsUSxpSkFBTUE7NEJBQUNpVCxLQUFLOzRCQUFDakMsU0FBU2pFOzRCQUF3QmtFLE9BQU87Z0NBQUVpQyxpQkFBaUI7Z0NBQVNDLGFBQWE7Z0NBQVc5QixPQUFPOzRCQUFZO3NDQUFHOzs7Ozs7c0NBR2hJLDhEQUFDclIsaUpBQU1BOzRCQUFDaVQsS0FBSzs0QkFBQ2pDLFNBQVMsSUFBTXpPLCtCQUErQjs0QkFBTzBPLE9BQU87Z0NBQUVpQyxpQkFBaUI7Z0NBQVNDLGFBQWE7Z0NBQVc5QixPQUFPOzRCQUFZO3NDQUFHOzs7Ozs7Ozs7Ozs7Ozs7OzswQkFPeEosOERBQUNuUixrSkFBS0E7Z0JBQ0o2RixPQUFNO2dCQUNOcU4sTUFBTXhSO2dCQUNOeVIsVUFBVSxJQUFNeFIsd0JBQXdCO2dCQUN4Q3lSLFFBQVE7Z0JBQ1I3QyxPQUFPOztrQ0FFUCw4REFBQ0k7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUMzUDt3Q0FDQ29TLGFBQVk7d0NBQ1pDLFVBQVU7d0NBQ1Z2QyxPQUFPOzRDQUFFUixPQUFPO3dDQUFJO3dDQUNwQmdELFVBQVU1UTt3Q0FDVjZRLFVBQVUsQ0FBQ0MsSUFBTTlRLGlCQUFpQjhRLEVBQUVDLE1BQU0sQ0FBQ0MsS0FBSzs7Ozs7O2tEQUVsRCw4REFBQ2hEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQzlRLGlKQUFNQTtnREFDTGtRLE1BQUs7Z0RBQ0xzQixvQkFBTSw4REFBQzdRLHNKQUFZQTs7Ozs7Z0RBQ25CcVEsU0FBUyxJQUFNalAsMkJBQTJCOzBEQUMzQzs7Ozs7OzBEQUdELDhEQUFDL0IsaUpBQU1BO2dEQUNMa1EsTUFBSztnREFDTHNCLG9CQUFNLDhEQUFDN1Esc0pBQVlBOzs7OztnREFDbkJxUSxTQUFTLElBQU03TywyQkFBMkI7MERBQzNDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBT0wsOERBQUMwTztnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ007O29EQUFLO2tFQUFRLDhEQUFDMEM7d0RBQU9oRCxXQUFVO2tFQUFpQjFOLFdBQVdtRyxNQUFNOzs7Ozs7Ozs7Ozs7MERBQ2xFLDhEQUFDNkg7O29EQUFLO2tFQUFPLDhEQUFDMEM7d0RBQU9oRCxXQUFVO2tFQUFrQnJOLGVBQWVzTixJQUFJOzs7Ozs7Ozs7Ozs7MERBQ3BFLDhEQUFDSzs7b0RBQUs7a0VBQVEsOERBQUMwQzt3REFBT2hELFdBQVU7a0VBQzdCaEIsTUFBTUMsSUFBSSxDQUFDek0saUJBQWlCdUUsTUFBTSxJQUFJa00sTUFBTSxDQUFDLENBQUMxQixPQUFPMkIsVUFBWTNCLFFBQVEyQixRQUFRekssTUFBTSxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSTlGLDhEQUFDc0g7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDOVEsaUpBQU1BO2dEQUNMK1EsTUFBSztnREFDTGIsTUFBSztnREFDTGMsU0FBUy9GO2dEQUNUZ0osVUFBVXJRO2dEQUNWa04sV0FBVTswREFDWDs7Ozs7OzBEQUdELDhEQUFDOVEsaUpBQU1BO2dEQUNMK1EsTUFBSztnREFDTGIsTUFBSztnREFDTGMsU0FBUzdGO2dEQUNUOEksVUFBVXJRO2dEQUNWa04sV0FBVTswREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU9QLDhEQUFDN1Esa0pBQUtBO3dCQUNKc1EsU0FBU0E7d0JBQ1QyRCxZQUFZdEU7d0JBQ1p1RSxRQUFPO3dCQUNQelMsU0FBU2tDO3dCQUNUd08sWUFBWTs0QkFDVnBMLFVBQVU7NEJBQ1ZvTixpQkFBaUI7NEJBQ2pCQyxXQUFXLENBQUNoQyxRQUFVLEtBQVcsT0FBTkEsT0FBTTt3QkFDbkM7d0JBQ0F0QixNQUFLOzs7Ozs7Ozs7Ozs7MEJBS1QsOERBQUM3USxrSkFBS0E7Z0JBQ0o2RixPQUFNO2dCQUNOcU4sTUFBTXRSO2dCQUNOdVIsVUFBVTtvQkFDUnRSLDJCQUEyQjtvQkFDM0JrRSxjQUFjNEQsV0FBVztvQkFDekI3RSx1QkFBdUI7b0JBQ3ZCRSxtQkFBbUIsRUFBRTtvQkFDckJFLGtCQUFrQjtvQkFDbEJFLG1CQUFtQjtvQkFDbkJFLHFCQUFxQjtvQkFDckJFLHNCQUFzQjtvQkFDdEJFLGlCQUFpQjtvQkFDakJFLG9CQUFvQjt3QkFBQzs0QkFBRUMsT0FBTzs0QkFBSUMsU0FBUztnQ0FBQzs2QkFBRzt3QkFBQztxQkFBRTtnQkFDcEQ7Z0JBQ0FzTyxNQUFNLElBQU1yTyxjQUFjc08sTUFBTTtnQkFDaEM1QyxRQUFPO2dCQUNQQyxZQUFXOzBCQUVYLDRFQUFDelIsaUpBQUlBO29CQUNIcVUsTUFBTXZPO29CQUNOd08sUUFBTztvQkFDUEMsVUFBVTlNOztzQ0FFViw4REFBQ3pILGlKQUFJQSxDQUFDd1UsSUFBSTs0QkFDUnhNLE1BQUs7NEJBQ0x5TSxPQUFNOzRCQUNOQyxPQUFPO2dDQUFDO29DQUFFQyxVQUFVO29DQUFNcFUsU0FBUztnQ0FBWTs2QkFBRTtzQ0FFakQsNEVBQUNMLGlKQUFNQTtnQ0FDTGtULGFBQVk7Z0NBQ1p3QixVQUFVO2dDQUNWQyxrQkFBaUI7Z0NBQ2pCL0QsT0FBTztvQ0FBRVIsT0FBTztnQ0FBTzswQ0FFdEI1TCxhQUFhbUUsR0FBRyxDQUFDa0MsQ0FBQUEsdUJBQ2hCLDhEQUFDOUo7d0NBQXVCeVMsT0FBTzNJLE9BQU9WLEVBQUU7d0NBQUV6RSxPQUFPLEdBQXFCbUYsT0FBbEJBLE9BQU9uRixLQUFLLEVBQUMsT0FBd0IsT0FBbkJtRixPQUFPMUMsV0FBVztrREFDdEYsNEVBQUNxSTs0Q0FBSUksT0FBTztnREFDVmdFLFVBQVU7Z0RBQ1ZDLGNBQWM7Z0RBQ2RDLFlBQVk7Z0RBQ1pDLFVBQVU7NENBQ1o7OzhEQUNFLDhEQUFDaEU7b0RBQUtILE9BQU87d0RBQUVvRSxZQUFZO29EQUFJOzhEQUFJbkssT0FBT25GLEtBQUs7Ozs7Ozs4REFDL0MsOERBQUNxTDtvREFBS0gsT0FBTzt3REFBRXFFLFVBQVU7d0RBQVFqRSxPQUFPO3dEQUFRa0UsWUFBWTtvREFBTTs7d0RBQUc7d0RBQ2pFckssT0FBT3lFLFFBQVE7d0RBQUM7Ozs7Ozs7Ozs7Ozs7dUNBVFh6RSxPQUFPVixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7c0NBaUI1Qiw4REFBQ3JLLGlKQUFJQSxDQUFDd1UsSUFBSTs0QkFDUnhNLE1BQUs7NEJBQ0x5TSxPQUFNOzRCQUNOQyxPQUFPO2dDQUFDO29DQUFFQyxVQUFVO29DQUFNcFUsU0FBUztnQ0FBVTs2QkFBRTtzQ0FFL0MsNEVBQUNOLGlKQUFLQTtnQ0FBQ21ULGFBQVk7Ozs7Ozs7Ozs7O3NDQUdyQiw4REFBQ3BULGlKQUFJQSxDQUFDd1UsSUFBSTs0QkFDUnhNLE1BQUs7NEJBQ0x5TSxPQUFNOzRCQUNOQyxPQUFPO2dDQUFDO29DQUFFQyxVQUFVO29DQUFNcFUsU0FBUztnQ0FBVTs2QkFBRTtzQ0FFL0MsNEVBQUNOLGlKQUFLQSxDQUFDb1YsUUFBUTtnQ0FDYkMsTUFBTTtnQ0FDTmxDLGFBQVk7Z0NBQ1ptQyxTQUFTO2dDQUNUQyxXQUFXOzs7Ozs7Ozs7OztzQ0FJZiw4REFBQ3hWLGlKQUFJQSxDQUFDd1UsSUFBSTs0QkFDUkMsT0FBTTs0QkFDTkMsT0FBTztnQ0FBQztvQ0FBRUMsVUFBVTtvQ0FBTXBVLFNBQVM7Z0NBQVU7NkJBQUU7c0NBRS9DLDRFQUFDRCxrSkFBTUEsQ0FBQ21WLE9BQU87Z0NBQ2J6TixNQUFLO2dDQUNMME4sZUFBZWxJO2dDQUNmbUksVUFBVWxJO2dDQUNWbUksUUFBTztnQ0FDUEMsVUFBVTtnQ0FDVkMsVUFBUzswQ0FFUmxSLG9DQUNDLDhEQUFDOEw7OENBQ0MsNEVBQUNxRjt3Q0FBSS9ILEtBQUtwSjt3Q0FBcUJvUixLQUFJO3dDQUFTbEYsT0FBTzs0Q0FBRVIsT0FBTzs0Q0FBUTJGLFdBQVc7NENBQVNDLFdBQVc7d0NBQVE7Ozs7Ozs7Ozs7OERBRzdHLDhEQUFDeEY7O3NEQUNDLDhEQUFDWTs0Q0FBRVgsV0FBVTtzREFDWCw0RUFBQy9QLHNKQUFhQTs7Ozs7Ozs7OztzREFFaEIsOERBQUMwUTs0Q0FBRVgsV0FBVTtzREFBa0I7Ozs7OztzREFDL0IsOERBQUNXOzRDQUFFWCxXQUFVO3NEQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FRdkMsOERBQUMzUSxpSkFBSUEsQ0FBQ3dVLElBQUk7NEJBQ1J4TSxNQUFLOzRCQUNMeU0sT0FBTTs0QkFDTkMsT0FBTztnQ0FDTDtvQ0FBRUMsVUFBVTtvQ0FBTXBVLFNBQVM7Z0NBQVU7Z0NBQ3JDO29DQUNFd1AsTUFBTTtvQ0FDTm9HLEtBQUs7b0NBQ0w1VixTQUFTO29DQUNUNlYsV0FBVyxDQUFDMUMsUUFBVTJDLE9BQU8zQztnQ0FDL0I7NkJBQ0Q7NEJBQ0Q0QyxTQUFRO3NDQUVSLDRFQUFDclcsaUpBQUtBO2dDQUFDOFAsTUFBSztnQ0FBU3FELGFBQVk7Z0NBQXFCK0MsS0FBSzs7Ozs7Ozs7Ozs7c0NBTTdELDhEQUFDblcsaUpBQUlBLENBQUN3VSxJQUFJOzRCQUNSQyxPQUFNOzRCQUNONkIsU0FBUTtzQ0FFUiw0RUFBQ2hXLGtKQUFNQSxDQUFDbVYsT0FBTztnQ0FDYnpOLE1BQUs7Z0NBQ0wwTixlQUFlN0g7Z0NBQ2Y4SCxVQUFVdkg7Z0NBQ1Z3SCxRQUFPO2dDQUNQQyxVQUFVO2dDQUNWQyxVQUFTOzBDQUVSOVEsK0JBQ0MsOERBQUMwTDs7c0RBQ0MsOERBQUM1STs0Q0FDQ2tHLEtBQUtoSjs0Q0FDTDhMLE9BQU87Z0RBQUVSLE9BQU87Z0RBQVEyRixXQUFXOzRDQUFROzRDQUMzQ00sUUFBUTs7Ozs7O3NEQUVWLDhEQUFDakY7NENBQUVSLE9BQU87Z0RBQUUwRixXQUFXO2dEQUFHdEYsT0FBTzs0Q0FBTztzREFDckNoTSxtQkFBbUI7Ozs7Ozs7Ozs7OzhEQUl4Qiw4REFBQ3dMOztzREFDQyw4REFBQ1k7NENBQUVYLFdBQVU7c0RBQ1gsNEVBQUMvUCxzSkFBYUE7Ozs7Ozs7Ozs7c0RBRWhCLDhEQUFDMFE7NENBQUVYLFdBQVU7c0RBQWtCOzs7Ozs7c0RBQy9CLDhEQUFDVzs0Q0FBRVgsV0FBVTtzREFBa0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBU3ZDLDhEQUFDM1EsaUpBQUlBLENBQUN3VSxJQUFJOzRCQUNSQyxPQUFNOzRCQUNONkIsU0FBUTtzQ0FFUiw0RUFBQ2hXLGtKQUFNQSxDQUFDbVYsT0FBTztnQ0FDYnpOLE1BQUs7Z0NBQ0wwTixlQUFlckg7Z0NBQ2ZzSCxVQUFVckg7Z0NBQ1ZzSCxRQUFPO2dDQUNQQyxVQUFVO2dDQUNWQyxVQUFTOzBDQUVSMVEsa0NBQ0MsOERBQUNzTDs4Q0FDQyw0RUFBQ0E7d0NBQUlJLE9BQU87NENBQUUyRixTQUFTOzRDQUFRQyxXQUFXO3dDQUFTOzswREFDakQsOERBQUM5VixzSkFBYUE7Z0RBQUNrUSxPQUFPO29EQUFFcUUsVUFBVTtvREFBUWpFLE9BQU87Z0RBQVU7Ozs7OzswREFDM0QsOERBQUNJO2dEQUFFUixPQUFPO29EQUFFMEYsV0FBVztvREFBR3RGLE9BQU87Z0RBQU87MERBQ3JDNUwsc0JBQXNCOzs7Ozs7Ozs7Ozs7Ozs7OzhEQUs3Qiw4REFBQ29MOztzREFDQyw4REFBQ1k7NENBQUVYLFdBQVU7c0RBQ1gsNEVBQUMvUCxzSkFBYUE7Ozs7Ozs7Ozs7c0RBRWhCLDhEQUFDMFE7NENBQUVYLFdBQVU7c0RBQWtCOzs7Ozs7c0RBQy9CLDhEQUFDVzs0Q0FBRVgsV0FBVTtzREFBa0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBVXZDLDhEQUFDM1EsaUpBQUlBLENBQUN3VSxJQUFJOzRCQUNSeE0sTUFBSzs0QkFDTHlNLE9BQU07NEJBQ042QixTQUFRO3NDQUVSLDRFQUFDcFcsaUpBQU1BO2dDQUNMeVcsTUFBSztnQ0FDTHZELGFBQVk7Z0NBQ1p0QyxPQUFPO29DQUFFUixPQUFPO2dDQUFPOzs7Ozs7Ozs7OztzQ0FJM0IsOERBQUN0USxpSkFBSUEsQ0FBQ3dVLElBQUk7NEJBQ1JDLE9BQU07NEJBQ042QixTQUFROzs4Q0FFUiw4REFBQ2hXLGtKQUFNQTtvQ0FDTDBILE1BQUs7b0NBQ0wwTixlQUFlaEk7b0NBQ2ZpSSxVQUFVaEk7b0NBQ1ZpSixRQUFRO29DQUNSaEIsUUFBTzs4Q0FFUCw0RUFBQy9WLGlKQUFNQTt3Q0FBQ3dSLG9CQUFNLDhEQUFDMVEsc0pBQWNBOzs7OztrREFBSzs7Ozs7Ozs7Ozs7OENBRXBDLDhEQUFDK1A7b0NBQUlJLE9BQU87d0NBQUVxRSxVQUFVO3dDQUFRakUsT0FBTzt3Q0FBUXNGLFdBQVc7b0NBQUU7OENBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVFyRSw4REFBQ3pXLGtKQUFLQTtnQkFDSjZGLE9BQU07Z0JBQ05xTixNQUFNcFI7Z0JBQ05xUixVQUFVO29CQUNScFIsNEJBQTRCO29CQUM1QlUsaUJBQWlCO29CQUNqQndELGVBQWUwRCxXQUFXO2dCQUM1QjtnQkFDQXlLLE1BQU0sSUFBTW5PLGVBQWVvTyxNQUFNO2dCQUNqQzVDLFFBQU87Z0JBQ1BDLFlBQVc7MEJBRVgsNEVBQUN6UixpSkFBSUE7b0JBQ0hxVSxNQUFNck87b0JBQ05zTyxRQUFPO29CQUNQQyxVQUFVcEs7O3NDQUVWLDhEQUFDbkssaUpBQUlBLENBQUN3VSxJQUFJOzRCQUNSeE0sTUFBSzs0QkFDTHlNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7b0NBQU1wVSxTQUFTO2dDQUFVOzZCQUFFO3NDQUUvQyw0RUFBQ04saUpBQUtBO2dDQUFDbVQsYUFBWTs7Ozs7Ozs7Ozs7c0NBR3JCLDhEQUFDcFQsaUpBQUlBLENBQUN3VSxJQUFJOzRCQUNSeE0sTUFBSzs0QkFDTHlNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7b0NBQU1wVSxTQUFTO2dDQUFVOzZCQUFFO3NDQUUvQyw0RUFBQ04saUpBQUtBLENBQUNvVixRQUFRO2dDQUFDQyxNQUFNO2dDQUFHbEMsYUFBWTs7Ozs7Ozs7Ozs7c0NBR3ZDLDhEQUFDcFQsaUpBQUlBLENBQUN3VSxJQUFJOzRCQUNSeE0sTUFBSzs0QkFDTHlNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7b0NBQU1wVSxTQUFTO2dDQUFVOzZCQUFFO3NDQUUvQyw0RUFBQ0wsaUpBQU1BO2dDQUFDa1QsYUFBWTs7a0RBQ2xCLDhEQUFDblM7d0NBQU95UyxPQUFNO2tEQUFPOzs7Ozs7a0RBQ3JCLDhEQUFDelM7d0NBQU95UyxPQUFNO2tEQUFPOzs7Ozs7a0RBQ3JCLDhEQUFDelM7d0NBQU95UyxPQUFNO2tEQUFPOzs7Ozs7a0RBQ3JCLDhEQUFDelM7d0NBQU95UyxPQUFNO2tEQUFPOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FJekIsOERBQUMxVCxpSkFBSUEsQ0FBQ3dVLElBQUk7NEJBQ1J4TSxNQUFLOzRCQUNMeU0sT0FBTTs0QkFDTkMsT0FBTztnQ0FBQztvQ0FBRUMsVUFBVTtvQ0FBTXBVLFNBQVM7Z0NBQVU7NkJBQUU7c0NBRS9DLDRFQUFDTCxpSkFBTUE7O2tEQUNMLDhEQUFDZTt3Q0FBT3lTLE9BQU07a0RBQVM7Ozs7OztrREFDdkIsOERBQUN6Uzt3Q0FBT3lTLE9BQU07a0RBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT2pDLDhEQUFDM1Qsa0pBQUtBO2dCQUNKNkYsT0FBTTtnQkFDTnFOLE1BQU1sUjtnQkFDTm1SLFVBQVU7b0JBQ1JsUiwyQkFBMkI7b0JBQzNCaUUsY0FBY3lELFdBQVc7b0JBQ3pCMUcsaUJBQWlCO2dCQUNuQjtnQkFDQW1SLE1BQU0sSUFBTWxPLGNBQWNtTyxNQUFNO2dCQUNoQzVDLFFBQU87Z0JBQ1BDLFlBQVc7Z0JBQ1huQixPQUFPOzBCQUVQLDRFQUFDdFEsaUpBQUlBO29CQUNIcVUsTUFBTXBPO29CQUNOcU8sUUFBTztvQkFDUEMsVUFBVXRKOztzQ0FFViw4REFBQ2pMLGlKQUFJQSxDQUFDd1UsSUFBSTs0QkFDUnhNLE1BQUs7NEJBQ0x5TSxPQUFNOzRCQUNOQyxPQUFPO2dDQUFDO29DQUFFQyxVQUFVO29DQUFNcFUsU0FBUztnQ0FBWTs2QkFBRTtzQ0FFakQsNEVBQUNOLGlKQUFLQTtnQ0FBQ21ULGFBQVk7Ozs7Ozs7Ozs7O3NDQUdyQiw4REFBQ3BULGlKQUFJQSxDQUFDd1UsSUFBSTs0QkFDUnhNLE1BQUs7NEJBQ0x5TSxPQUFNOzRCQUNOQyxPQUFPO2dDQUFDO29DQUFFQyxVQUFVO29DQUFNcFUsU0FBUztnQ0FBVTs2QkFBRTtzQ0FFL0MsNEVBQUNOLGlKQUFLQSxDQUFDb1YsUUFBUTtnQ0FDYkMsTUFBTTtnQ0FDTmxDLGFBQVk7Z0NBQ1ptQyxTQUFTO2dDQUNUQyxXQUFXOzs7Ozs7Ozs7OztzQ0FJZiw4REFBQ3hWLGlKQUFJQSxDQUFDd1UsSUFBSTs0QkFDUkMsT0FBTTs0QkFDTkMsT0FBTztnQ0FBQztvQ0FBRUMsVUFBVTtvQ0FBTXBVLFNBQVM7Z0NBQVU7NkJBQUU7c0NBRS9DLDRFQUFDRCxrSkFBTUEsQ0FBQ21WLE9BQU87Z0NBQ2J6TixNQUFLO2dDQUNMME4sZUFBZXhJO2dDQUNmeUksVUFBVXBJO2dDQUNWcUksUUFBTztnQ0FDUEMsVUFBVTtnQ0FDVkMsVUFBUzswQ0FFUi9TLDhCQUNDLDhEQUFDMk47OENBQ0MsNEVBQUNxRjt3Q0FBSS9ILEtBQUtqTDt3Q0FBZWlULEtBQUk7d0NBQU9sRixPQUFPOzRDQUFFUixPQUFPOzRDQUFRMkYsV0FBVzs0Q0FBU0MsV0FBVzt3Q0FBUTs7Ozs7Ozs7Ozs4REFHckcsOERBQUN4Rjs7c0RBQ0MsOERBQUNZOzRDQUFFWCxXQUFVO3NEQUNYLDRFQUFDL1Asc0pBQWFBOzs7Ozs7Ozs7O3NEQUVoQiw4REFBQzBROzRDQUFFWCxXQUFVO3NEQUFrQjs7Ozs7O3NEQUMvQiw4REFBQ1c7NENBQUVYLFdBQVU7c0RBQWtCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQVV2Qyw4REFBQzNRLGlKQUFJQSxDQUFDd1UsSUFBSTs0QkFDUnhNLE1BQUs7NEJBQ0x5TSxPQUFNOzRCQUNOQyxPQUFPO2dDQUFDO29DQUFFQyxVQUFVO29DQUFNcFUsU0FBUztnQ0FBZTs2QkFBRTs0QkFDcERzVyxjQUFjO3NDQUVkLDRFQUFDM1csaUpBQU1BO2dDQUFDa1QsYUFBWTs7a0RBQ2xCLDhEQUFDblM7d0NBQU95UyxPQUFPO2tEQUFHOzs7Ozs7a0RBQ2xCLDhEQUFDelM7d0NBQU95UyxPQUFPO2tEQUFHOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FJdEIsOERBQUMxVCxpSkFBSUEsQ0FBQ3dVLElBQUk7NEJBQ1J4TSxNQUFLOzRCQUNMeU0sT0FBTTs0QkFDTkMsT0FBTztnQ0FBQztvQ0FBRUMsVUFBVTtvQ0FBTXBVLFNBQVM7Z0NBQVU7NkJBQUU7c0NBRS9DLDRFQUFDTixpSkFBS0E7Z0NBQ0ptVCxhQUFZO2dDQUNabUMsU0FBUztnQ0FDVEMsV0FBVzs7Ozs7Ozs7Ozs7c0NBSWYsOERBQUN4VixpSkFBSUEsQ0FBQ3dVLElBQUk7NEJBQ1J4TSxNQUFLOzRCQUNMeU0sT0FBTTs0QkFDTkMsT0FBTztnQ0FBQztvQ0FBRUMsVUFBVTtvQ0FBTXBVLFNBQVM7Z0NBQVE7NkJBQUU7c0NBRTdDLDRFQUFDTCxpSkFBTUE7Z0NBQ0x5VyxNQUFLO2dDQUNMdkQsYUFBWTtnQ0FDWjBELGlCQUFnQjswQ0FFZmpVLFdBQVdnRyxHQUFHLENBQUNpSixDQUFBQSxvQkFDZCw4REFBQzdRO3dDQUFvQnlTLE9BQU81QixJQUFJekgsRUFBRTt3Q0FBRW9LLE9BQU8zQyxJQUFJOUosSUFBSTtrREFDakQsNEVBQUM1SCxrSkFBR0E7NENBQUM4USxPQUFPWSxJQUFJWixLQUFLO3NEQUFHWSxJQUFJOUosSUFBSTs7Ozs7O3VDQURyQjhKLElBQUl6SCxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFVN0IsOERBQUN0SyxrSkFBS0E7Z0JBQ0o2RixPQUFNO2dCQUNOcU4sTUFBTWhSO2dCQUNOaVIsVUFBVTtvQkFDUmhSLHdCQUF3QjtvQkFDeEJnRSxXQUFXd0QsV0FBVztnQkFDeEI7Z0JBQ0F5SyxNQUFNLElBQU1qTyxXQUFXa08sTUFBTTtnQkFDN0I1QyxRQUFPO2dCQUNQQyxZQUFXO2dCQUNYbkIsT0FBTzswQkFFUCw0RUFBQ3RRLGlKQUFJQTtvQkFDSHFVLE1BQU1uTztvQkFDTm9PLFFBQU87b0JBQ1BDLFVBQVVuSjs7c0NBRVYsOERBQUNwTCxpSkFBSUEsQ0FBQ3dVLElBQUk7NEJBQ1J4TSxNQUFLOzRCQUNMeU0sT0FBTTs0QkFDTkMsT0FBTztnQ0FDTDtvQ0FBRUMsVUFBVTtvQ0FBTXBVLFNBQVM7Z0NBQVU7Z0NBQ3JDO29DQUFFd1csS0FBSztvQ0FBSXhXLFNBQVM7Z0NBQWdCOzZCQUNyQztzQ0FFRCw0RUFBQ04saUpBQUtBO2dDQUFDbVQsYUFBWTs7Ozs7Ozs7Ozs7c0NBR3JCLDhEQUFDcFQsaUpBQUlBLENBQUN3VSxJQUFJOzRCQUNSeE0sTUFBSzs0QkFDTHlNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7b0NBQU1wVSxTQUFTO2dDQUFVOzZCQUFFOzRCQUMvQ3NXLGNBQWE7c0NBRWIsNEVBQUMzVyxpSkFBTUE7Z0NBQUNrVCxhQUFZOztrREFDbEIsOERBQUNuUzt3Q0FBT3lTLE9BQU07a0RBQ1osNEVBQUNoRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVO29EQUFrQkcsT0FBTzt3REFBRWlDLGlCQUFpQjtvREFBVTs7Ozs7O2dEQUFTOzs7Ozs7Ozs7Ozs7a0RBSWxGLDhEQUFDOVI7d0NBQU95UyxPQUFNO2tEQUNaLDRFQUFDaEQ7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTtvREFBa0JHLE9BQU87d0RBQUVpQyxpQkFBaUI7b0RBQVU7Ozs7OztnREFBUzs7Ozs7Ozs7Ozs7O2tEQUlsRiw4REFBQzlSO3dDQUFPeVMsT0FBTTtrREFDWiw0RUFBQ2hEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7b0RBQWtCRyxPQUFPO3dEQUFFaUMsaUJBQWlCO29EQUFVOzs7Ozs7Z0RBQVM7Ozs7Ozs7Ozs7OztrREFJbEYsOERBQUM5Ujt3Q0FBT3lTLE9BQU07a0RBQ1osNEVBQUNoRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVO29EQUFrQkcsT0FBTzt3REFBRWlDLGlCQUFpQjtvREFBVTs7Ozs7O2dEQUFTOzs7Ozs7Ozs7Ozs7a0RBSWxGLDhEQUFDOVI7d0NBQU95UyxPQUFNO2tEQUNaLDRFQUFDaEQ7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTtvREFBa0JHLE9BQU87d0RBQUVpQyxpQkFBaUI7b0RBQVU7Ozs7OztnREFBUzs7Ozs7Ozs7Ozs7O2tEQUlsRiw4REFBQzlSO3dDQUFPeVMsT0FBTTtrREFDWiw0RUFBQ2hEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7b0RBQWtCRyxPQUFPO3dEQUFFaUMsaUJBQWlCO29EQUFVOzs7Ozs7Z0RBQVM7Ozs7Ozs7Ozs7OztrREFJbEYsOERBQUM5Ujt3Q0FBT3lTLE9BQU07a0RBQ1osNEVBQUNoRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVO29EQUFrQkcsT0FBTzt3REFBRWlDLGlCQUFpQjtvREFBVTs7Ozs7O2dEQUFTOzs7Ozs7Ozs7Ozs7a0RBSWxGLDhEQUFDOVI7d0NBQU95UyxPQUFNO2tEQUNaLDRFQUFDaEQ7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTtvREFBa0JHLE9BQU87d0RBQUVpQyxpQkFBaUI7b0RBQVU7Ozs7OztnREFBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBT3RGLDhEQUFDL1MsaUpBQUlBLENBQUN3VSxJQUFJOzRCQUNSeE0sTUFBSzs0QkFDTHlNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7b0NBQU1wVSxTQUFTO2dDQUFVOzZCQUFFOzRCQUMvQ3NXLGNBQWM7c0NBRWQsNEVBQUMzVyxpSkFBTUE7Z0NBQUNrVCxhQUFZOztrREFDbEIsOERBQUNuUzt3Q0FBT3lTLE9BQU87a0RBQUc7Ozs7OztrREFDbEIsOERBQUN6Uzt3Q0FBT3lTLE9BQU87a0RBQUc7Ozs7OztrREFDbEIsOERBQUN6Uzt3Q0FBT3lTLE9BQU87a0RBQUc7Ozs7OztrREFDbEIsOERBQUN6Uzt3Q0FBT3lTLE9BQU87a0RBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUl0Qiw4REFBQzFULGlKQUFJQSxDQUFDd1UsSUFBSTs0QkFDUnhNLE1BQUs7NEJBQ0x5TSxPQUFNOzRCQUNOQyxPQUFPO2dDQUFDO29DQUFFcUMsS0FBSztvQ0FBS3hXLFNBQVM7Z0NBQWlCOzZCQUFFO3NDQUVoRCw0RUFBQ04saUpBQUtBLENBQUNvVixRQUFRO2dDQUNiQyxNQUFNO2dDQUNObEMsYUFBWTtnQ0FDWm1DLFNBQVM7Z0NBQ1RDLFdBQVc7Ozs7Ozs7Ozs7O3NDQUlmLDhEQUFDeFYsaUpBQUlBLENBQUN3VSxJQUFJOzRCQUNSeE0sTUFBSzs0QkFDTHlNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7b0NBQU1wVSxTQUFTO2dDQUFVOzZCQUFFOzRCQUMvQ3NXLGNBQWM7c0NBRWQsNEVBQUMzVyxpSkFBTUE7Z0NBQUNrVCxhQUFZOztrREFDbEIsOERBQUNuUzt3Q0FBT3lTLE9BQU87a0RBQUc7Ozs7OztrREFDbEIsOERBQUN6Uzt3Q0FBT3lTLE9BQU87a0RBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTzFCLDhEQUFDM1Qsa0pBQUtBO2dCQUNKNkYsT0FBTTtnQkFDTnFOLE1BQU05UTtnQkFDTitRLFVBQVU7b0JBQ1I5USwrQkFBK0I7b0JBQy9CK0Qsa0JBQWtCdUQsV0FBVztnQkFDL0I7Z0JBQ0F5SyxNQUFNLElBQU1oTyxrQkFBa0JpTyxNQUFNO2dCQUNwQzVDLFFBQU87Z0JBQ1BDLFlBQVc7Z0JBQ1huQixPQUFPOzBCQUVQLDRFQUFDdFEsaUpBQUlBO29CQUNIcVUsTUFBTWxPO29CQUNObU8sUUFBTztvQkFDUEMsVUFBVWhKOztzQ0FFViw4REFBQ3ZMLGlKQUFJQSxDQUFDd1UsSUFBSTs0QkFDUnhNLE1BQUs7NEJBQ0x5TSxPQUFNOzRCQUNOQyxPQUFPO2dDQUFDO29DQUFFQyxVQUFVO29DQUFNcFUsU0FBUztnQ0FBYzs2QkFBRTtzQ0FFbkQsNEVBQUNMLGlKQUFNQTtnQ0FDTGtULGFBQVk7Z0NBQ1p3QixVQUFVO2dDQUNWb0MsY0FBYyxDQUFDQyxPQUFPQzt3Q0FDbkJBOzJDQUFBQSxtQkFBQUEsOEJBQUFBLG1CQUFBQSxPQUFRQyxRQUFRLGNBQWhCRCx1Q0FBRCxpQkFBeUMzSCxXQUFXLEdBQUd4RixRQUFRLENBQUNrTixNQUFNMUgsV0FBVzs7MENBR2xGN0ssYUFBYW1FLEdBQUcsQ0FBQyxDQUFDa0MsdUJBQ2pCLDhEQUFDOUo7d0NBQXVCeVMsT0FBTzNJLE9BQU9WLEVBQUU7OzRDQUNyQ1UsT0FBT25GLEtBQUs7NENBQUM7NENBQUdtRixPQUFPeUUsUUFBUTs0Q0FBQzs7dUNBRHRCekUsT0FBT1YsRUFBRTs7Ozs7Ozs7Ozs7Ozs7O3NDQU81Qiw4REFBQ3JLLGlKQUFJQSxDQUFDd1UsSUFBSTs0QkFDUnhNLE1BQUs7NEJBQ0x5TSxPQUFNOzRCQUNOQyxPQUFPO2dDQUFDO29DQUFFQyxVQUFVO2dDQUFNOzZCQUFFO3NDQUU1Qiw0RUFBQzFVLGlKQUFLQSxDQUFDb1YsUUFBUTtnQ0FDYmpDLGFBQVk7Z0NBQ1prQyxNQUFNO2dDQUNORSxXQUFXO2dDQUNYRCxTQUFTOzs7Ozs7Ozs7OztzQ0FJYiw4REFBQzdFOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ3lHO29DQUFHekcsV0FBVTs4Q0FBeUM7Ozs7Ozs4Q0FDdkQsOERBQUMwRztvQ0FBRzFHLFdBQVU7O3NEQUNaLDhEQUFDMkc7c0RBQUc7Ozs7OztzREFDSiw4REFBQ0E7c0RBQUc7Ozs7OztzREFDSiw4REFBQ0E7c0RBQUc7Ozs7OztzREFDSiw4REFBQ0E7c0RBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU9aLDhEQUFDdlgsa0pBQUtBO2dCQUNKNkYsT0FBTTtnQkFDTnFOLE1BQU01UTtnQkFDTjZRLFVBQVV2RztnQkFDVndHLFFBQVE7Z0JBQ1I3QyxPQUFPO2dCQUNQaUgsY0FBYzswQkFFZCw0RUFBQ3ZYLGlKQUFJQTtvQkFDSHFVLE1BQU1qTztvQkFDTmtPLFFBQU87b0JBQ1BDLFVBQVUxSDtvQkFDVjhELFdBQVU7O3NDQUdWLDhEQUFDM1EsaUpBQUlBLENBQUN3VSxJQUFJOzRCQUNSeE0sTUFBSzs0QkFDTHlNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7b0NBQU1wVSxTQUFTO2dDQUFVOzZCQUFFO3NDQUUvQyw0RUFBQ0wsaUpBQU1BO2dDQUNMa1QsYUFBWTtnQ0FDWjdSLFNBQVNpRDtnQ0FDVCtPLFVBQVUvRztnQ0FDVm9JLFVBQVU7Z0NBQ1ZvQyxjQUFjLENBQUNDLE9BQU9DO3dDQUNuQkE7MkNBQUFBLG1CQUFBQSw4QkFBQUEsbUJBQUFBLE9BQVFDLFFBQVEsY0FBaEJELHVDQUFELGlCQUF5QzNILFdBQVcsR0FBR3hGLFFBQVEsQ0FBQ2tOLE1BQU0xSCxXQUFXOzswQ0FHbEZwTywwQkFBMEIwSCxHQUFHLENBQUNrQyxDQUFBQSx1QkFDN0IsOERBQUM5Sjt3Q0FBdUJ5UyxPQUFPM0ksT0FBT1YsRUFBRTs7NENBQ3JDVSxPQUFPbkYsS0FBSzs0Q0FBQzs0Q0FBT21GLE9BQU9WLEVBQUU7NENBQUM7O3VDQURwQlUsT0FBT1YsRUFBRTs7Ozs7Ozs7Ozs7Ozs7O3NDQVE1Qiw4REFBQ3JLLGlKQUFJQSxDQUFDd1UsSUFBSTs0QkFDUnhNLE1BQUs7NEJBQ0x5TSxPQUFNOzRCQUNOQyxPQUFPO2dDQUFDO29DQUFFQyxVQUFVO29DQUFNcFUsU0FBUztnQ0FBYTs2QkFBRTtzQ0FFbEQsNEVBQUNMLGlKQUFNQTtnQ0FDTGtULGFBQWF6UCwyQkFBMkIsZUFBZTtnQ0FDdkRtUSxVQUFVLENBQUNuUTtnQ0FDWHBDLFNBQVNpRCxzQkFBc0IsQ0FBQyxDQUFDYjtnQ0FDakM0UCxVQUFVN0c7Z0NBQ1ZrSSxVQUFVO2dDQUNWb0MsY0FBYyxDQUFDQyxPQUFPQzt3Q0FDbkJBOzJDQUFBQSxtQkFBQUEsOEJBQUFBLG1CQUFBQSxPQUFRQyxRQUFRLGNBQWhCRCx1Q0FBRCxpQkFBeUMzSCxXQUFXLEdBQUd4RixRQUFRLENBQUNrTixNQUFNMUgsV0FBVzs7Z0NBRW5GaUksaUJBQ0VoVCxzQkFBc0JiLDJCQUNsQixlQUNBQSwyQkFDRSxhQUNBOzBDQUdQdkMsMEJBQTBCZ0ksTUFBTSxHQUFHLElBQ2xDaEksMEJBQTBCeUgsR0FBRyxDQUFDd0csQ0FBQUEsdUJBQzVCLDhEQUFDcE87d0NBQXVCeVMsT0FBT3JFLE9BQU9oRixFQUFFO2tEQUN0Qyw0RUFBQ3FHOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ007OERBQU01QixPQUFPekosS0FBSzs7Ozs7OzhEQUNuQiw4REFBQzhLO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ3ZRLGtKQUFHQTs0REFBQzhRLE9BQU83QixPQUFPcEYsTUFBTSxLQUFLLElBQUksVUFBVW9GLE9BQU9wRixNQUFNLEtBQUssSUFBSSxXQUFXOzREQUFPMEcsV0FBVTtzRUFDM0Z0QixPQUFPcEYsTUFBTSxLQUFLLElBQUksUUFBUW9GLE9BQU9wRixNQUFNLEtBQUssSUFBSSxPQUFPOzs7Ozs7c0VBRTlELDhEQUFDZ0g7NERBQUtOLFdBQVU7O2dFQUF3QjtnRUFBS3RCLE9BQU9oRixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VDQVAvQ2dGLE9BQU9oRixFQUFFOzs7O3FEQWF4QjFHLDRCQUE0QixDQUFDYSxtQ0FDM0IsOERBQUN2RDtvQ0FBTzZTLFFBQVE7b0NBQUNKLE9BQU07OENBQWE7Ozs7O2dEQUdsQzs7Ozs7Ozs7Ozs7c0NBTVYsOERBQUNoRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUN5RztvQ0FBR3pHLFdBQVU7OENBQWlDOzs7Ozs7OENBQy9DLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNXOztnREFBRTtnREFBVTNOLDRCQUE0Qjs7Ozs7OztzREFDekMsOERBQUMyTjs7Z0RBQUU7Z0RBQVV4Tiw0QkFBNEI7Ozs7Ozs7c0RBQ3pDLDhEQUFDd047O2dEQUFFO2dEQUFTblEsMEJBQTBCaUksTUFBTTs7Ozs7OztzREFDNUMsOERBQUNrSTs7Z0RBQUU7Z0RBQVVsUSwwQkFBMEJnSSxNQUFNOzs7Ozs7O3NEQUM3Qyw4REFBQ2tJOztnREFBRTtnREFBTzlNLHFCQUFxQixRQUFROzs7Ozs7O3dDQUN0Q3BELDBCQUEwQmdJLE1BQU0sR0FBRyxtQkFDbEMsOERBQUNzSDs7OERBQ0MsOERBQUNZOzhEQUFFOzs7Ozs7OERBQ0gsOERBQUMrRjtvREFBRzFHLFdBQVU7OERBQ1h2UCwwQkFBMEJ5SCxHQUFHLENBQUN3RyxDQUFBQSx1QkFDN0IsOERBQUNpSTs7Z0VBQW1CO2dFQUFLakksT0FBT2hGLEVBQUU7Z0VBQUM7Z0VBQU9nRixPQUFPekosS0FBSzs7MkRBQTdDeUosT0FBT2hGLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7d0JBUzdCdkcsMENBQ0MsOERBQUM0TTs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUN5RztvQ0FBR3pHLFdBQVU7OENBQXlDOzs7Ozs7OENBQ3ZELDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNXOztnREFBRTtnREFBT3hOOzs7Ozs7O3NEQUNWLDhEQUFDd047O2dEQUFFO2lEQUFPblEsa0NBQUFBLDBCQUEwQjRMLElBQUksQ0FBQzBLLENBQUFBLElBQUtBLEVBQUVwTixFQUFFLEtBQUsxRyx1Q0FBN0N4QyxzREFBQUEsZ0NBQXdFeUUsS0FBSzs7Ozs7OztzREFDdkYsOERBQUMwTDs7Z0RBQUU7aURBQU9sUSxrQ0FBQUEsMEJBQTBCMkwsSUFBSSxDQUFDcEUsQ0FBQUEsSUFBS0EsRUFBRTBCLEVBQUUsS0FBS3ZHLHVDQUE3QzFDLHNEQUFBQSxnQ0FBd0V3RSxLQUFLOzs7Ozs7O3NEQUN2Riw4REFBQzBMOzRDQUFFWCxXQUFVOztnREFBaUM7Z0RBQTBEN007Z0RBQXlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUt2SSw4REFBQzRNOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ1o3TSx5Q0FDQyw4REFBQ21OO3dDQUFLTixXQUFVO2tEQUFpQjs7Ozs7a0VBRWpDLDhEQUFDTTtrREFBSzs7Ozs7Ozs7Ozs7OENBR1YsOERBQUNQO29DQUFJQyxXQUFVOztzREFDYiw4REFBQzlRLGlKQUFNQTs0Q0FBQ2dSLFNBQVNsRTtzREFBeUI7Ozs7OztzREFHMUMsOERBQUM5TSxpSkFBTUE7NENBQ0xrUSxNQUFLOzRDQUNMMkgsVUFBUzs0Q0FDVG5XLFNBQVNpRDs0Q0FDVHNQLFVBQVUsQ0FBQ2hROzRDQUNYNk0sV0FBVzdNLDJCQUEyQixxREFBcUQ7c0RBRTFGVSxxQkFBcUIsV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVFqRDtHQTMrRE10RDs7UUFpRG9CbEIsaUpBQUlBLENBQUMrRjtRQUNKL0YsaUpBQUlBLENBQUMrRjtRQUNOL0YsaUpBQUlBLENBQUMrRjtRQUNSL0YsaUpBQUlBLENBQUMrRjtRQUNFL0YsaUpBQUlBLENBQUMrRjtRQUNML0YsaUpBQUlBLENBQUMrRjs7O0tBdEQ3QjdFO0FBNitETiwrREFBZUEsZ0JBQWdCQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC9hZG1pbi1zcGFjZS9jb21wb25lbnRzL2NvdXJzZS1tYW5hZ2VtZW50LnRzeD82ZDcyIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQ2FyZCwgQnV0dG9uLCBUYWJsZSwgTW9kYWwsIEZvcm0sIElucHV0LCBTZWxlY3QsIFNwYWNlLCBUYWcsIFBvcGNvbmZpcm0sIFVwbG9hZCwgbWVzc2FnZSB9IGZyb20gJ2FudGQnO1xuaW1wb3J0IHsgUGx1c091dGxpbmVkLCBFZGl0T3V0bGluZWQsIERlbGV0ZU91dGxpbmVkLCBTZWFyY2hPdXRsaW5lZCwgVXBsb2FkT3V0bGluZWQsIEluYm94T3V0bGluZWQgfSBmcm9tICdAYW50LWRlc2lnbi9pY29ucyc7XG5pbXBvcnQgeyBHZXROb3RpZmljYXRpb24gfSBmcm9tICdsb2dpYy1jb21tb24vZGlzdC9jb21wb25lbnRzL05vdGlmaWNhdGlvbic7XG5pbXBvcnQgeyBjb3Vyc2VBcGksIENvdXJzZSwgQ3JlYXRlQ291cnNlUmVxdWVzdCwgVXBkYXRlQ291cnNlUmVxdWVzdCwgQ3JlYXRlQ291cnNlU2VyaWVzUmVxdWVzdCwgVGVhY2hlciwgQ291cnNlVGFnLCBDb3Vyc2VTZXJpZXMgfSBmcm9tICdAL2xpYi9hcGkvY291cnNlJztcbmltcG9ydCB7IHVwbG9hZEFwaSB9IGZyb20gJ0AvbGliL2FwaS91cGxvYWQnO1xuaW1wb3J0IHR5cGUgeyBVcGxvYWRGaWxlLCBVcGxvYWRQcm9wcyB9IGZyb20gJ2FudGQvZXMvdXBsb2FkL2ludGVyZmFjZSc7XG5cbmNvbnN0IHsgU2VhcmNoIH0gPSBJbnB1dDtcbmNvbnN0IHsgT3B0aW9uIH0gPSBTZWxlY3Q7XG5cbmludGVyZmFjZSBDb3Vyc2VNYW5hZ2VtZW50UHJvcHMge1xuICAvLyDlj6/ku6Xmt7vliqDpnIDopoHnmoRwcm9wc1xufVxuXG4vLyDor77nqIvooajljZXmlbDmja7nsbvlnotcbmludGVyZmFjZSBDb3Vyc2VGb3JtRGF0YSB7XG4gIG5hbWU6IHN0cmluZztcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgY2F0ZWdvcnk6IHN0cmluZztcbiAgc3RhdHVzOiAnYWN0aXZlJyB8ICdpbmFjdGl2ZSc7XG4gIGNvbnRlbnRDb25maWc/OiB7XG4gICAgZHVyYXRpb24/OiBudW1iZXI7XG4gICAgZGlmZmljdWx0eT86ICdiZWdpbm5lcicgfCAnaW50ZXJtZWRpYXRlJyB8ICdhZHZhbmNlZCc7XG4gICAgcHJlcmVxdWlzaXRlcz86IHN0cmluZ1tdO1xuICB9O1xuICB0ZWFjaGluZ0luZm8/OiB7XG4gICAgb2JqZWN0aXZlcz86IHN0cmluZ1tdO1xuICAgIG1ldGhvZHM/OiBzdHJpbmdbXTtcbiAgICBtYXRlcmlhbHM/OiBzdHJpbmdbXTtcbiAgfTtcbn1cblxuY29uc3QgQ291cnNlTWFuYWdlbWVudDogUmVhY3QuRkM8Q291cnNlTWFuYWdlbWVudFByb3BzPiA9ICgpID0+IHtcbiAgY29uc3QgW2NvdXJzZUxpc3QsIHNldENvdXJzZUxpc3RdID0gdXNlU3RhdGU8Q291cnNlW10+KFtdKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNDb3Vyc2VNb2RhbFZpc2libGUsIHNldElzQ291cnNlTW9kYWxWaXNpYmxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzQWRkQ291cnNlTW9kYWxWaXNpYmxlLCBzZXRJc0FkZENvdXJzZU1vZGFsVmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpc0VkaXRDb3Vyc2VNb2RhbFZpc2libGUsIHNldElzRWRpdENvdXJzZU1vZGFsVmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpc0FkZFNlcmllc01vZGFsVmlzaWJsZSwgc2V0SXNBZGRTZXJpZXNNb2RhbFZpc2libGVdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNBZGRUYWdNb2RhbFZpc2libGUsIHNldElzQWRkVGFnTW9kYWxWaXNpYmxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzUHVibGlzaFNlcmllc01vZGFsVmlzaWJsZSwgc2V0SXNQdWJsaXNoU2VyaWVzTW9kYWxWaXNpYmxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzUHVibGlzaENvdXJzZU1vZGFsVmlzaWJsZSwgc2V0SXNQdWJsaXNoQ291cnNlTW9kYWxWaXNpYmxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2VkaXRpbmdDb3Vyc2UsIHNldEVkaXRpbmdDb3Vyc2VdID0gdXNlU3RhdGU8Q291cnNlIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzZWFyY2hLZXl3b3JkLCBzZXRTZWFyY2hLZXl3b3JkXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3RlYWNoZXJzLCBzZXRUZWFjaGVyc10gPSB1c2VTdGF0ZTxUZWFjaGVyW10+KFtdKTtcbiAgY29uc3QgW2NvdXJzZVRhZ3MsIHNldENvdXJzZVRhZ3NdID0gdXNlU3RhdGU8Q291cnNlVGFnW10+KFtdKTtcbiAgY29uc3QgW2NvdmVySW1hZ2VVcmwsIHNldENvdmVySW1hZ2VVcmxdID0gdXNlU3RhdGU8c3RyaW5nPignJyk7XG5cbiAgLy8g5paw5aKe77ya57O75YiX6K++56iL5ZKM5a2Q6K++56iL566h55CG55u45YWz54q25oCBXG4gIGNvbnN0IFtzZXJpZXNMaXN0LCBzZXRTZXJpZXNMaXN0XSA9IHVzZVN0YXRlPGFueVtdPihbXSk7XG4gIGNvbnN0IFtzZXJpZXNDb3Vyc2VzTWFwLCBzZXRTZXJpZXNDb3Vyc2VzTWFwXSA9IHVzZVN0YXRlPE1hcDxudW1iZXIsIGFueVtdPj4obmV3IE1hcCgpKTtcbiAgY29uc3QgW2V4cGFuZGVkU2VyaWVzLCBzZXRFeHBhbmRlZFNlcmllc10gPSB1c2VTdGF0ZTxTZXQ8bnVtYmVyPj4obmV3IFNldCgpKTtcbiAgY29uc3QgW3Nlcmllc0xvYWRpbmcsIHNldFNlcmllc0xvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIOWPkeW4g+ivvueoi+ebuOWFs+eKtuaAgVxuICBjb25zdCBbc2VsZWN0ZWRTZXJpZXNGb3JQdWJsaXNoLCBzZXRTZWxlY3RlZFNlcmllc0ZvclB1Ymxpc2hdID0gdXNlU3RhdGU8bnVtYmVyIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xuICBjb25zdCBbc2VsZWN0ZWRDb3Vyc2VGb3JQdWJsaXNoLCBzZXRTZWxlY3RlZENvdXJzZUZvclB1Ymxpc2hdID0gdXNlU3RhdGU8bnVtYmVyIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xuICBjb25zdCBbcHVibGlzaFNlcmllc0NvdXJzZXMsIHNldFB1Ymxpc2hTZXJpZXNDb3Vyc2VzXSA9IHVzZVN0YXRlPGFueVtdPihbXSk7XG4gIGNvbnN0IFtwdWJsaXNoTG9hZGluZywgc2V0UHVibGlzaExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbcHVibGlzaFNlcmllc09wdGlvbnMsIHNldFB1Ymxpc2hTZXJpZXNPcHRpb25zXSA9IHVzZVN0YXRlPGFueVtdPihbXSk7XG5cbiAgLy8g5paw55qE5Y+R5biD6K++56iL54q25oCBXG4gIGNvbnN0IFtwdWJsaXNoU2VyaWVzTGlzdEZvck1vZGFsLCBzZXRQdWJsaXNoU2VyaWVzTGlzdEZvck1vZGFsXSA9IHVzZVN0YXRlPGFueVtdPihbXSk7XG4gIGNvbnN0IFtwdWJsaXNoQ291cnNlTGlzdEZvck1vZGFsLCBzZXRQdWJsaXNoQ291cnNlTGlzdEZvck1vZGFsXSA9IHVzZVN0YXRlPGFueVtdPihbXSk7XG4gIGNvbnN0IFtwdWJsaXNoRm9ybUxvYWRpbmcsIHNldFB1Ymxpc2hGb3JtTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgY29uc3QgW2NvdXJzZVNlcmllcywgc2V0Q291cnNlU2VyaWVzXSA9IHVzZVN0YXRlPENvdXJzZVNlcmllc1tdPihbXSk7XG4gIGNvbnN0IFtjb3Vyc2VDb3ZlckltYWdlVXJsLCBzZXRDb3Vyc2VDb3ZlckltYWdlVXJsXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xuICBjb25zdCBbYWRkaXRpb25hbEZpbGVzLCBzZXRBZGRpdGlvbmFsRmlsZXNdID0gdXNlU3RhdGU8c3RyaW5nW10+KFtdKTtcbiAgY29uc3QgW2NvdXJzZVZpZGVvVXJsLCBzZXRDb3Vyc2VWaWRlb1VybF0gPSB1c2VTdGF0ZTxzdHJpbmc+KCcnKTtcbiAgY29uc3QgW2NvdXJzZVZpZGVvTmFtZSwgc2V0Q291cnNlVmlkZW9OYW1lXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xuICBjb25zdCBbY291cnNlRG9jdW1lbnRVcmwsIHNldENvdXJzZURvY3VtZW50VXJsXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xuICBjb25zdCBbY291cnNlRG9jdW1lbnROYW1lLCBzZXRDb3Vyc2VEb2N1bWVudE5hbWVdID0gdXNlU3RhdGU8c3RyaW5nPignJyk7XG4gIGNvbnN0IFt2aWRlb0R1cmF0aW9uLCBzZXRWaWRlb0R1cmF0aW9uXSA9IHVzZVN0YXRlPG51bWJlcj4oMCk7XG5cbiAgLy8g5pWZ5a2m5L+h5oGv54q25oCBXG4gIGNvbnN0IFt0ZWFjaGluZ0luZm9MaXN0LCBzZXRUZWFjaGluZ0luZm9MaXN0XSA9IHVzZVN0YXRlPEFycmF5PHtcbiAgICB0aXRsZTogc3RyaW5nO1xuICAgIGNvbnRlbnQ6IHN0cmluZ1tdO1xuICB9Pj4oW3sgdGl0bGU6ICcnLCBjb250ZW50OiBbJyddIH1dKTtcblxuICBjb25zdCBbYWRkQ291cnNlRm9ybV0gPSBGb3JtLnVzZUZvcm0oKTtcbiAgY29uc3QgW2VkaXRDb3Vyc2VGb3JtXSA9IEZvcm0udXNlRm9ybSgpO1xuICBjb25zdCBbYWRkU2VyaWVzRm9ybV0gPSBGb3JtLnVzZUZvcm0oKTtcbiAgY29uc3QgW2FkZFRhZ0Zvcm1dID0gRm9ybS51c2VGb3JtKCk7XG4gIGNvbnN0IFtwdWJsaXNoU2VyaWVzRm9ybV0gPSBGb3JtLnVzZUZvcm0oKTtcbiAgY29uc3QgW3B1Ymxpc2hDb3Vyc2VGb3JtXSA9IEZvcm0udXNlRm9ybSgpO1xuICBjb25zdCBub3RpZmljYXRpb24gPSBHZXROb3RpZmljYXRpb24oKTtcblxuXG5cbiAgLy8g6I635Y+W57O75YiX6K++56iL5YiX6KGoXG4gIGNvbnN0IGZldGNoU2VyaWVzTGlzdCA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0U2VyaWVzTG9hZGluZyh0cnVlKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OdIOiOt+WPluezu+WIl+ivvueoi+WIl+ihqC4uLicpO1xuXG4gICAgICBjb25zdCB7IGRhdGE6IHJlcyB9ID0gYXdhaXQgY291cnNlQXBpLmdldE1hcmtldHBsYWNlU2VyaWVzKHtcbiAgICAgICAgcGFnZTogMSxcbiAgICAgICAgcGFnZVNpemU6IDUwXG4gICAgICB9KTtcblxuICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDAgJiYgcmVzLmRhdGE/Lmxpc3QpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDojrflj5bns7vliJfor77nqIvliJfooajmiJDlip86JywgcmVzLmRhdGEubGlzdCk7XG4gICAgICAgIHNldFNlcmllc0xpc3QocmVzLmRhdGEubGlzdCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg6I635Y+W57O75YiX6K++56iL5YiX6KGo5aSx6LSlOicsIHJlcy5tc2cpO1xuICAgICAgICBtZXNzYWdlLmVycm9yKHJlcy5tc2cgfHwgJ+iOt+WPluezu+WIl+ivvueoi+WIl+ihqOWksei0pScpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg6I635Y+W57O75YiX6K++56iL5YiX6KGo5byC5bi4OicsIGVycm9yKTtcbiAgICAgIG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluezu+WIl+ivvueoi+WIl+ihqOWksei0pe+8jOivt+mHjeivlScpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRTZXJpZXNMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g6I635Y+W5oyH5a6a57O75YiX5LiL55qE5a2Q6K++56iL5YiX6KGoXG4gIGNvbnN0IGZldGNoU2VyaWVzQ291cnNlcyA9IGFzeW5jIChzZXJpZXNJZDogbnVtYmVyKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OdIOiOt+WPluezu+WIl+WtkOivvueoi+WIl+ihqO+8jOezu+WIl0lEOicsIHNlcmllc0lkKTtcblxuICAgICAgLy8g5L2/55So6K++56iL566h55CGQVBJ6I635Y+W5omA5pyJ54q25oCB55qE6K++56iLXG4gICAgICBjb25zdCB7IGRhdGE6IHJlcyB9ID0gYXdhaXQgY291cnNlQXBpLmdldE1hbmFnZW1lbnRTZXJpZXNDb3Vyc2VzKHNlcmllc0lkLCB7XG4gICAgICAgIHBhZ2U6IDEsXG4gICAgICAgIHBhZ2VTaXplOiA1MFxuICAgICAgICAvLyDkuI3kvKBzdGF0dXPlj4LmlbDvvIzojrflj5bmiYDmnInnirbmgIHnmoTor77nqItcbiAgICAgIH0pO1xuXG4gICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCAmJiByZXMuZGF0YT8ubGlzdCkge1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOiOt+WPluezu+WIl+WtkOivvueoi+WIl+ihqOaIkOWKnzonLCByZXMuZGF0YS5saXN0KTtcbiAgICAgICAgc2V0U2VyaWVzQ291cnNlc01hcChwcmV2ID0+IG5ldyBNYXAocHJldi5zZXQoc2VyaWVzSWQsIHJlcy5kYXRhLmxpc3QpKSk7XG4gICAgICAgIHNldEV4cGFuZGVkU2VyaWVzKHByZXYgPT4gbmV3IFNldChwcmV2LmFkZChzZXJpZXNJZCkpKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDojrflj5bns7vliJflrZDor77nqIvliJfooajlpLHotKU6JywgcmVzLm1zZyk7XG4gICAgICAgIG1lc3NhZ2UuZXJyb3IocmVzLm1zZyB8fCAn6I635Y+W5a2Q6K++56iL5YiX6KGo5aSx6LSlJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDojrflj5bns7vliJflrZDor77nqIvliJfooajlvILluLg6JywgZXJyb3IpO1xuICAgICAgbWVzc2FnZS5lcnJvcign6I635Y+W5a2Q6K++56iL5YiX6KGo5aSx6LSl77yM6K+36YeN6K+VJyk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOiOt+WPluivvueoi+WIl+ihqO+8iOS/neeVmeWOn+acieWKn+iDve+8iVxuICBjb25zdCBmZXRjaENvdXJzZUxpc3QgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OdIOiOt+WPluivvueoi+WIl+ihqC4uLicpO1xuXG4gICAgICAvLyDojrflj5bns7vliJfor77nqIvliJfooahcbiAgICAgIGF3YWl0IGZldGNoU2VyaWVzTGlzdCgpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg6I635Y+W6K++56iL5YiX6KGo5aSx6LSlOicsIGVycm9yKTtcbiAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign6I635Y+W6K++56iL5YiX6KGo5aSx6LSl77yM6K+36YeN6K+VJyk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOa3u+WKoOivvueoi1xuICBjb25zdCBoYW5kbGVBZGRDb3Vyc2UgPSBhc3luYyAodmFsdWVzOiBhbnkpID0+IHtcbiAgICB0cnkge1xuICAgICAgLy8g5p6E5bu65YaF5a656YWN572u77yM5Y+q5YyF5ZCr5pyJ5pWI55qE5aqS5L2T5paH5Lu2XG4gICAgICBjb25zdCBjb250ZW50Q29uZmlnOiBhbnkgPSB7XG4gICAgICAgIGhhc1ZpZGVvOiBjb3Vyc2VWaWRlb1VybCA/IDEgOiAwLFxuICAgICAgICBoYXNEb2N1bWVudDogY291cnNlRG9jdW1lbnRVcmwgPyAxIDogMCxcbiAgICAgIH07XG5cbiAgICAgIGlmIChjb3Vyc2VWaWRlb1VybCkge1xuICAgICAgICBjb250ZW50Q29uZmlnLnZpZGVvID0ge1xuICAgICAgICAgIHVybDogY291cnNlVmlkZW9VcmwsXG4gICAgICAgICAgbmFtZTogY291cnNlVmlkZW9OYW1lIHx8ICfor77nqIvop4bpopEubXA0J1xuICAgICAgICB9O1xuICAgICAgfVxuXG4gICAgICBpZiAoY291cnNlRG9jdW1lbnRVcmwpIHtcbiAgICAgICAgY29udGVudENvbmZpZy5kb2N1bWVudCA9IHtcbiAgICAgICAgICB1cmw6IGNvdXJzZURvY3VtZW50VXJsLFxuICAgICAgICAgIG5hbWU6IGNvdXJzZURvY3VtZW50TmFtZSB8fCAn6K++56iL5paH5qGjLnBkZidcbiAgICAgICAgfTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgY291cnNlRGF0YSA9IHtcbiAgICAgICAgc2VyaWVzSWQ6IHBhcnNlSW50KHZhbHVlcy5zZXJpZXNJZCksXG4gICAgICAgIHRpdGxlOiB2YWx1ZXMudGl0bGUudHJpbSgpLFxuICAgICAgICBkZXNjcmlwdGlvbjogdmFsdWVzLmRlc2NyaXB0aW9uLnRyaW0oKSxcbiAgICAgICAgY292ZXJJbWFnZTogY291cnNlQ292ZXJJbWFnZVVybCxcbiAgICAgICAgaGFzVmlkZW86IGNvdXJzZVZpZGVvVXJsID8gMSA6IDAsXG4gICAgICAgIGhhc0RvY3VtZW50OiBjb3Vyc2VEb2N1bWVudFVybCA/IDEgOiAwLFxuICAgICAgICB2aWRlb0R1cmF0aW9uOiB2aWRlb0R1cmF0aW9uIHx8IDAsXG4gICAgICAgIGNvbnRlbnRDb25maWcsXG4gICAgICAgIHRlYWNoaW5nSW5mbzogdGVhY2hpbmdJbmZvTGlzdC5maWx0ZXIoaW5mbyA9PiBpbmZvLnRpdGxlLnRyaW0oKSAmJiBpbmZvLmNvbnRlbnQuc29tZShjID0+IGMudHJpbSgpKSksXG4gICAgICAgIGFkZGl0aW9uYWxSZXNvdXJjZXM6IGFkZGl0aW9uYWxGaWxlcy5tYXAoZmlsZSA9PiAoe1xuICAgICAgICAgIHRpdGxlOiBmaWxlLnNwbGl0KCcvJykucG9wKCkgfHwgJ2ZpbGUnLFxuICAgICAgICAgIHVybDogZmlsZSxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ+ivvueoi+mZhOS7tui1hOa6kCdcbiAgICAgICAgfSkpLFxuICAgICAgICBvcmRlckluZGV4OiBwYXJzZUludCh2YWx1ZXMub3JkZXJJbmRleCkgfHwgMFxuICAgICAgfTtcblxuICAgICAgLy8g6aqM6K+B5b+F6KaB5a2X5q61XG4gICAgICBpZiAoIWNvdXJzZURhdGEuc2VyaWVzSWQpIHtcbiAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfor7fpgInmi6nmiYDlsZ7ns7vliJfor77nqIsnKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgaWYgKCFjb3Vyc2VEYXRhLnRpdGxlKSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign6K+36L6T5YWl6K++56iL5ZCN56ewJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIGlmICghY291cnNlRGF0YS5jb3ZlckltYWdlKSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign6K+35LiK5Lyg6K++56iL5bCB6Z2iJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg5o+Q5Lqk6K++56iL5pWw5o2uOicsIGNvdXJzZURhdGEpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk4og5pWw5o2u5aSn5bCP5Lyw566XOicsIEpTT04uc3RyaW5naWZ5KGNvdXJzZURhdGEpLmxlbmd0aCwgJ+Wtl+espicpO1xuXG4gICAgICAvLyDmt7vliqDph43or5XmnLrliLZcbiAgICAgIGxldCByZXRyeUNvdW50ID0gMDtcbiAgICAgIGNvbnN0IG1heFJldHJpZXMgPSAyO1xuICAgICAgbGV0IGxhc3RFcnJvcjtcblxuICAgICAgd2hpbGUgKHJldHJ5Q291bnQgPD0gbWF4UmV0cmllcykge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IHsgZGF0YTogcmVzIH0gPSBhd2FpdCBjb3Vyc2VBcGkuY3JlYXRlQ291cnNlKGNvdXJzZURhdGEpO1xuXG4gICAgICAgICAgLy8g5aaC5p6c5oiQ5Yqf77yM6Lez5Ye66YeN6K+V5b6q546vXG4gICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHtcbiAgICAgICAgICAgIG5vdGlmaWNhdGlvbi5zdWNjZXNzKCfliJvlu7ror77nqIvmiJDlip8nKTtcbiAgICAgICAgICAgIGZldGNoQ291cnNlTGlzdCgpO1xuICAgICAgICAgICAgc2V0SXNBZGRDb3Vyc2VNb2RhbFZpc2libGUoZmFsc2UpO1xuICAgICAgICAgICAgYWRkQ291cnNlRm9ybS5yZXNldEZpZWxkcygpO1xuICAgICAgICAgICAgc2V0Q291cnNlQ292ZXJJbWFnZVVybCgnJyk7XG4gICAgICAgICAgICBzZXRBZGRpdGlvbmFsRmlsZXMoW10pO1xuICAgICAgICAgICAgc2V0Q291cnNlVmlkZW9VcmwoJycpO1xuICAgICAgICAgICAgc2V0Q291cnNlVmlkZW9OYW1lKCcnKTtcbiAgICAgICAgICAgIHNldENvdXJzZURvY3VtZW50VXJsKCcnKTtcbiAgICAgICAgICAgIHNldENvdXJzZURvY3VtZW50TmFtZSgnJyk7XG4gICAgICAgICAgICBzZXRWaWRlb0R1cmF0aW9uKDApO1xuICAgICAgICAgICAgc2V0VGVhY2hpbmdJbmZvTGlzdChbeyB0aXRsZTogJycsIGNvbnRlbnQ6IFsnJ10gfV0pO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBub3RpZmljYXRpb24uZXJyb3IocmVzLm1zZyB8fCAn5Yib5bu66K++56iL5aSx6LSlJyk7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgfVxuICAgICAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICAgICAgbGFzdEVycm9yID0gZXJyb3I7XG4gICAgICAgICAgcmV0cnlDb3VudCsrO1xuXG4gICAgICAgICAgaWYgKHJldHJ5Q291bnQgPD0gbWF4UmV0cmllcykge1xuICAgICAgICAgICAgY29uc29sZS5sb2coYPCflIQg56ysJHtyZXRyeUNvdW50feasoemHjeivlS4uLmApO1xuICAgICAgICAgICAgbm90aWZpY2F0aW9uLndhcm5pbmcoYOe9kee7nOW8guW4uO+8jOato+WcqOmHjeivlSAoJHtyZXRyeUNvdW50fS8ke21heFJldHJpZXN9KWApO1xuICAgICAgICAgICAgLy8g562J5b6FMeenkuWQjumHjeivlVxuICAgICAgICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDEwMDApKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8g5aaC5p6c5omA5pyJ6YeN6K+V6YO95aSx6LSl5LqG77yM5oqb5Ye65pyA5ZCO55qE6ZSZ6K+vXG4gICAgICB0aHJvdyBsYXN0RXJyb3I7XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOWIm+W7uuivvueoi+Wksei0pTonLCBlcnJvcik7XG5cbiAgICAgIC8vIOabtOivpue7hueahOmUmeivr+WkhOeQhlxuICAgICAgaWYgKGVycm9yLmNvZGUgPT09ICdFQ09OTlJFU0VUJyB8fCBlcnJvci5tZXNzYWdlPy5pbmNsdWRlcygnRUNPTk5SRVNFVCcpIHx8XG4gICAgICAgICAgKGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlICYmIGVycm9yLnJlc3BvbnNlLmRhdGEubWVzc2FnZS5pbmNsdWRlcygnRUNPTk5SRVNFVCcpKSkge1xuICAgICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+e9kee7nOi/nuaOpeS4reaWre+8jOWPr+iDveaYr+e9kee7nOS4jeeos+WumuaIluacjeWKoeWZqOe5geW/meOAguivt+eojeWQjumHjeivleaIluiBlOezu+euoeeQhuWRmOOAgicpO1xuICAgICAgfSBlbHNlIGlmIChlcnJvci5jb2RlID09PSAnTkVUV09SS19FUlJPUicpIHtcbiAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfnvZHnu5zplJnor6/vvIzor7fmo4Dmn6XnvZHnu5zov57mjqUnKTtcbiAgICAgIH0gZWxzZSBpZiAoZXJyb3IucmVzcG9uc2U/LnN0YXR1cyA9PT0gNDEzKSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign5LiK5Lyg5paH5Lu26L+H5aSn77yM6K+35Y6L57yp5ZCO6YeN6K+VJyk7XG4gICAgICB9IGVsc2UgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwMCkge1xuICAgICAgICBjb25zdCBlcnJvck1zZyA9IGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8IGVycm9yLm1lc3NhZ2U7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcihg6K+35rGC5Y+C5pWw6ZSZ6K+vOiAke2Vycm9yTXNnfWApO1xuICAgICAgfSBlbHNlIGlmIChlcnJvci5yZXNwb25zZT8uc3RhdHVzID09PSA1MDApIHtcbiAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfmnI3liqHlmajlhoXpg6jplJnor6/vvIzor7fogZTns7vnrqHnkIblkZgnKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcihg5Yib5bu66K++56iL5aSx6LSlOiAke2Vycm9yLm1lc3NhZ2UgfHwgJ+ivt+eojeWQjumHjeivlSd9YCk7XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5SNIOWujOaVtOmUmeivr+S/oeaBrzonLCB7XG4gICAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2UsXG4gICAgICAgIGNvZGU6IGVycm9yLmNvZGUsXG4gICAgICAgIHN0YXR1czogZXJyb3IucmVzcG9uc2U/LnN0YXR1cyxcbiAgICAgICAgZGF0YTogZXJyb3IucmVzcG9uc2U/LmRhdGFcbiAgICAgIH0pO1xuICAgIH1cbiAgfTtcblxuICAvLyDnvJbovpHor77nqItcbiAgY29uc3QgaGFuZGxlRWRpdENvdXJzZSA9IGFzeW5jICh2YWx1ZXM6IFVwZGF0ZUNvdXJzZVJlcXVlc3QpID0+IHtcbiAgICBpZiAoIWVkaXRpbmdDb3Vyc2UpIHJldHVybjtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCB7IGRhdGE6IHJlcyB9ID0gYXdhaXQgY291cnNlQXBpLnVwZGF0ZUNvdXJzZShlZGl0aW5nQ291cnNlLmlkLCB2YWx1ZXMpO1xuICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHtcbiAgICAgICAgbm90aWZpY2F0aW9uLnN1Y2Nlc3MoJ+abtOaWsOivvueoi+aIkOWKnycpO1xuICAgICAgICBmZXRjaENvdXJzZUxpc3QoKTtcbiAgICAgICAgc2V0SXNFZGl0Q291cnNlTW9kYWxWaXNpYmxlKGZhbHNlKTtcbiAgICAgICAgc2V0RWRpdGluZ0NvdXJzZShudWxsKTtcbiAgICAgICAgZWRpdENvdXJzZUZvcm0ucmVzZXRGaWVsZHMoKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcihyZXMubXNnIHx8ICfmm7TmlrDor77nqIvlpLHotKUnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOabtOaWsOivvueoi+Wksei0pTonLCBlcnJvcik7XG4gICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+abtOaWsOivvueoi+Wksei0pe+8jOivt+mHjeivlScpO1xuICAgIH1cbiAgfTtcblxuICAvLyDliKDpmaTor77nqItcbiAgY29uc3QgaGFuZGxlRGVsZXRlQ291cnNlID0gYXN5bmMgKGNvdXJzZUlkOiBudW1iZXIpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBkYXRhOiByZXMgfSA9IGF3YWl0IGNvdXJzZUFwaS5kZWxldGVDb3Vyc2UoY291cnNlSWQpO1xuICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHtcbiAgICAgICAgbm90aWZpY2F0aW9uLnN1Y2Nlc3MoJ+WIoOmZpOivvueoi+aIkOWKnycpO1xuICAgICAgICBmZXRjaENvdXJzZUxpc3QoKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcihyZXMubXNnIHx8ICfliKDpmaTor77nqIvlpLHotKUnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOWIoOmZpOivvueoi+Wksei0pTonLCBlcnJvcik7XG4gICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+WIoOmZpOivvueoi+Wksei0pe+8jOivt+mHjeivlScpO1xuICAgIH1cbiAgfTtcblxuICAvLyDliKDpmaTlrZDor77nqItcbiAgY29uc3QgaGFuZGxlRGVsZXRlU3ViQ291cnNlID0gYXN5bmMgKGNvdXJzZUlkOiBudW1iZXIsIHNlcmllc0lkOiBudW1iZXIpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ/Cfl5HvuI8g5Yig6Zmk5a2Q6K++56iL77yM6K++56iLSUQ6JywgY291cnNlSWQsICfns7vliJdJRDonLCBzZXJpZXNJZCk7XG5cbiAgICAgIGNvbnN0IHsgZGF0YTogcmVzIH0gPSBhd2FpdCBjb3Vyc2VBcGkuZGVsZXRlQ291cnNlKGNvdXJzZUlkKTtcblxuICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHtcbiAgICAgICAgbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTlrZDor77nqIvmiJDlip8nKTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDlrZDor77nqIvliKDpmaTmiJDlip/vvIzph43mlrDojrflj5bns7vliJflrZDor77nqIvliJfooagnKTtcblxuICAgICAgICAvLyDph43mlrDojrflj5bor6Xns7vliJfnmoTlrZDor77nqIvliJfooahcbiAgICAgICAgYXdhaXQgZmV0Y2hTZXJpZXNDb3Vyc2VzKHNlcmllc0lkKTtcblxuICAgICAgICBjb25zb2xlLmxvZygn8J+UhCDlrZDor77nqIvliJfooajlt7LliLfmlrAnKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDliKDpmaTlrZDor77nqIvlpLHotKU6JywgcmVzLm1zZyk7XG4gICAgICAgIG1lc3NhZ2UuZXJyb3IocmVzLm1zZyB8fCAn5Yig6Zmk5a2Q6K++56iL5aSx6LSlJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDliKDpmaTlrZDor77nqIvlvILluLg6JywgZXJyb3IpO1xuICAgICAgbWVzc2FnZS5lcnJvcign5Yig6Zmk5a2Q6K++56iL5aSx6LSl77yM6K+36YeN6K+VJyk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWIh+aNouezu+WIl+WxleW8gC/mlLbotbfnirbmgIFcbiAgY29uc3QgdG9nZ2xlU2VyaWVzRXhwYW5zaW9uID0gYXN5bmMgKHNlcmllc0lkOiBudW1iZXIpID0+IHtcbiAgICBjb25zb2xlLmxvZygn8J+UhCDliIfmjaLns7vliJflsZXlvIDnirbmgIHvvIzns7vliJdJRDonLCBzZXJpZXNJZCk7XG4gICAgY29uc29sZS5sb2coJ/Cfk4og5b2T5YmN5bGV5byA54q25oCBOicsIGV4cGFuZGVkU2VyaWVzLmhhcyhzZXJpZXNJZCkpO1xuXG4gICAgaWYgKGV4cGFuZGVkU2VyaWVzLmhhcyhzZXJpZXNJZCkpIHtcbiAgICAgIC8vIOaUtui1t1xuICAgICAgY29uc29sZS5sb2coJ/Cfk4Eg5pS26LW357O75YiXOicsIHNlcmllc0lkKTtcbiAgICAgIHNldEV4cGFuZGVkU2VyaWVzKHByZXYgPT4ge1xuICAgICAgICBjb25zdCBuZXdTZXQgPSBuZXcgU2V0KHByZXYpO1xuICAgICAgICBuZXdTZXQuZGVsZXRlKHNlcmllc0lkKTtcbiAgICAgICAgcmV0dXJuIG5ld1NldDtcbiAgICAgIH0pO1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyDlsZXlvIDvvIzpnIDopoHojrflj5blrZDor77nqIvmlbDmja5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5OCIOWxleW8gOezu+WIl++8jOiOt+WPluWtkOivvueoizonLCBzZXJpZXNJZCk7XG4gICAgICBhd2FpdCBmZXRjaFNlcmllc0NvdXJzZXMoc2VyaWVzSWQpO1xuICAgIH1cbiAgfTtcblxuICAvLyDlsZXlvIDmiYDmnInns7vliJdcbiAgY29uc3QgZXhwYW5kQWxsU2VyaWVzID0gYXN5bmMgKCkgPT4ge1xuICAgIGNvbnNvbGUubG9nKCfwn5OCIOWxleW8gOaJgOacieezu+WIl+ivvueoiycpO1xuICAgIGZvciAoY29uc3Qgc2VyaWVzIG9mIHNlcmllc0xpc3QpIHtcbiAgICAgIGlmICghZXhwYW5kZWRTZXJpZXMuaGFzKHNlcmllcy5pZCkpIHtcbiAgICAgICAgYXdhaXQgZmV0Y2hTZXJpZXNDb3Vyc2VzKHNlcmllcy5pZCk7XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIC8vIOaUtui1t+aJgOacieezu+WIl1xuICBjb25zdCBjb2xsYXBzZUFsbFNlcmllcyA9ICgpID0+IHtcbiAgICBjb25zb2xlLmxvZygn8J+TgSDmlLbotbfmiYDmnInns7vliJfor77nqIsnKTtcbiAgICBzZXRFeHBhbmRlZFNlcmllcyhuZXcgU2V0KCkpO1xuICB9O1xuXG4gIC8vIOa3u+WKoOezu+WIl+ivvueoi1xuICBjb25zdCBoYW5kbGVBZGRTZXJpZXMgPSBhc3luYyAodmFsdWVzOiBDcmVhdGVDb3Vyc2VTZXJpZXNSZXF1ZXN0KSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHNlcmllc0RhdGEgPSB7XG4gICAgICAgIC4uLnZhbHVlcyxcbiAgICAgICAgY292ZXJJbWFnZTogY292ZXJJbWFnZVVybFxuICAgICAgfTtcblxuICAgICAgY29uc29sZS5sb2coJ+WIm+W7uuezu+WIl+ivvueoi+aVsOaNrjonLCBzZXJpZXNEYXRhKTtcblxuICAgICAgY29uc3QgeyBkYXRhOiByZXMgfSA9IGF3YWl0IGNvdXJzZUFwaS5jcmVhdGVDb3Vyc2VTZXJpZXMoc2VyaWVzRGF0YSk7XG5cbiAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5zdWNjZXNzKCfliJvlu7rns7vliJfor77nqIvmiJDlip8nKTtcbiAgICAgICAgZmV0Y2hDb3Vyc2VMaXN0KCk7XG4gICAgICAgIHNldElzQWRkU2VyaWVzTW9kYWxWaXNpYmxlKGZhbHNlKTtcbiAgICAgICAgYWRkU2VyaWVzRm9ybS5yZXNldEZpZWxkcygpO1xuICAgICAgICBzZXRDb3ZlckltYWdlVXJsKCcnKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcihyZXMubXNnIHx8ICfliJvlu7rns7vliJfor77nqIvlpLHotKUnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOWIm+W7uuezu+WIl+ivvueoi+Wksei0pTonLCBlcnJvcik7XG4gICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+WIm+W7uuezu+WIl+ivvueoi+Wksei0pe+8jOivt+mHjeivlScpO1xuICAgIH1cbiAgfTtcblxuICAvLyDliJvlu7ror77nqIvmoIfnrb5cbiAgY29uc3QgaGFuZGxlQWRkVGFnID0gYXN5bmMgKHZhbHVlczogYW55KSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn4+377iPIOWIm+W7uuivvueoi+agh+etvuaVsOaNrjonLCB2YWx1ZXMpO1xuXG4gICAgICBjb25zdCB7IGRhdGE6IHJlcyB9ID0gYXdhaXQgY291cnNlQXBpLmNyZWF0ZUNvdXJzZVRhZyh2YWx1ZXMpO1xuXG4gICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkge1xuICAgICAgICBub3RpZmljYXRpb24uc3VjY2Vzcygn5Yib5bu65qCH562+5oiQ5YqfJyk7XG4gICAgICAgIHNldElzQWRkVGFnTW9kYWxWaXNpYmxlKGZhbHNlKTtcbiAgICAgICAgYWRkVGFnRm9ybS5yZXNldEZpZWxkcygpO1xuICAgICAgICAvLyDph43mlrDojrflj5bmoIfnrb7liJfooahcbiAgICAgICAgZmV0Y2hDb3Vyc2VUYWdzKCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBub3RpZmljYXRpb24uZXJyb3IocmVzLm1zZyB8fCAn5Yib5bu65qCH562+5aSx6LSlJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDliJvlu7rmoIfnrb7lpLHotKU6JywgZXJyb3IpO1xuICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfliJvlu7rmoIfnrb7lpLHotKXvvIzor7fph43or5UnKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5Y+R5biD57O75YiX6K++56iLXG4gIGNvbnN0IGhhbmRsZVB1Ymxpc2hTZXJpZXMgPSBhc3luYyAodmFsdWVzOiBhbnkpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ/Cfk6Ig5Y+R5biD57O75YiX6K++56iL5pWw5o2uOicsIHZhbHVlcyk7XG5cbiAgICAgIGNvbnN0IHsgZGF0YTogcmVzIH0gPSBhd2FpdCBjb3Vyc2VBcGkucHVibGlzaENvdXJzZVNlcmllcyh2YWx1ZXMuc2VyaWVzSWQpO1xuXG4gICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkge1xuICAgICAgICBub3RpZmljYXRpb24uc3VjY2Vzcygn5Y+R5biD57O75YiX6K++56iL5oiQ5YqfJyk7XG4gICAgICAgIHNldElzUHVibGlzaFNlcmllc01vZGFsVmlzaWJsZShmYWxzZSk7XG4gICAgICAgIHB1Ymxpc2hTZXJpZXNGb3JtLnJlc2V0RmllbGRzKCk7XG5cbiAgICAgICAgLy8g5pi+56S65Y+R5biD57uT5p6c5L+h5oGvXG4gICAgICAgIGNvbnN0IHB1Ymxpc2hEYXRhID0gcmVzLmRhdGE7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg5Y+R5biD5oiQ5Yqf77yM57O75YiX5L+h5oGvOicsIHB1Ymxpc2hEYXRhKTtcblxuICAgICAgICAvLyDlj6/ku6XpgInmi6nmmL7npLrlj5HluIPnu5/orqHkv6Hmga9cbiAgICAgICAgaWYgKHB1Ymxpc2hEYXRhLnB1Ymxpc2hTdGF0cykge1xuICAgICAgICAgIGNvbnN0IHN0YXRzID0gcHVibGlzaERhdGEucHVibGlzaFN0YXRzO1xuICAgICAgICAgIGNvbnN0IHN0YXRzTWVzc2FnZSA9IGDlt7Llj5HluIMgJHtwdWJsaXNoRGF0YS5wdWJsaXNoZWRDb3Vyc2VzfS8ke3B1Ymxpc2hEYXRhLnRvdGFsQ291cnNlc30g5Liq6K++56iL77yM5YyF5ZCrICR7c3RhdHMudmlkZW9Db3Vyc2VDb3VudH0g5Liq6KeG6aKR6K++56iL77yM5oC75pe26ZW/ICR7TWF0aC5yb3VuZChzdGF0cy50b3RhbFZpZGVvRHVyYXRpb24gLyA2MCl9IOWIhumSn2A7XG4gICAgICAgICAgbm90aWZpY2F0aW9uLmluZm8oc3RhdHNNZXNzYWdlKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKHJlcy5tc2cgfHwgJ+WPkeW4g+ezu+WIl+ivvueoi+Wksei0pScpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg5Y+R5biD57O75YiX6K++56iL5aSx6LSlOicsIGVycm9yKTtcbiAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign5Y+R5biD57O75YiX6K++56iL5aSx6LSl77yM6K+36YeN6K+VJyk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOiOt+WPluWPkeW4g+eUqOeahOezu+WIl+ivvueoi+WIl+ihqFxuICBjb25zdCBmZXRjaFNlcmllc0ZvclB1Ymxpc2ggPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldFB1Ymxpc2hMb2FkaW5nKHRydWUpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk50g6I635Y+W5Y+R5biD55So57O75YiX6K++56iL5YiX6KGoLi4uJyk7XG5cbiAgICAgIGNvbnN0IHsgZGF0YTogcmVzIH0gPSBhd2FpdCBjb3Vyc2VBcGkuZ2V0TWFya2V0cGxhY2VTZXJpZXMoe1xuICAgICAgICBwYWdlOiAxLFxuICAgICAgICBwYWdlU2l6ZTogNTBcbiAgICAgIH0pO1xuXG4gICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCAmJiByZXMuZGF0YT8ubGlzdCkge1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOiOt+WPluWPkeW4g+eUqOezu+WIl+ivvueoi+WIl+ihqOaIkOWKnzonLCByZXMuZGF0YS5saXN0KTtcbiAgICAgICAgcmV0dXJuIHJlcy5kYXRhLmxpc3Q7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg6I635Y+W5Y+R5biD55So57O75YiX6K++56iL5YiX6KGo5aSx6LSlOicsIHJlcy5tc2cpO1xuICAgICAgICBtZXNzYWdlLmVycm9yKHJlcy5tc2cgfHwgJ+iOt+WPluezu+WIl+ivvueoi+WIl+ihqOWksei0pScpO1xuICAgICAgICByZXR1cm4gW107XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDojrflj5blj5HluIPnlKjns7vliJfor77nqIvliJfooajlvILluLg6JywgZXJyb3IpO1xuICAgICAgbWVzc2FnZS5lcnJvcign6I635Y+W57O75YiX6K++56iL5YiX6KGo5aSx6LSl77yM6K+36YeN6K+VJyk7XG4gICAgICByZXR1cm4gW107XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldFB1Ymxpc2hMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g6I635Y+W5oyH5a6a57O75YiX55qE6K++56iL6K+m5oOFXG4gIGNvbnN0IGZldGNoU2VyaWVzRGV0YWlsRm9yUHVibGlzaCA9IGFzeW5jIChzZXJpZXNJZDogbnVtYmVyKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldFB1Ymxpc2hMb2FkaW5nKHRydWUpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk50g6I635Y+W57O75YiX6K++56iL6K+m5oOF77yM57O75YiXSUQ6Jywgc2VyaWVzSWQpO1xuXG4gICAgICBjb25zdCB7IGRhdGE6IHJlcyB9ID0gYXdhaXQgY291cnNlQXBpLmdldE1hcmtldHBsYWNlU2VyaWVzRGV0YWlsKHNlcmllc0lkKTtcblxuICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDAgJiYgcmVzLmRhdGEpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDojrflj5bns7vliJfor77nqIvor6bmg4XmiJDlip86JywgcmVzLmRhdGEpO1xuICAgICAgICByZXR1cm4gcmVzLmRhdGE7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg6I635Y+W57O75YiX6K++56iL6K+m5oOF5aSx6LSlOicsIHJlcy5tc2cpO1xuICAgICAgICBtZXNzYWdlLmVycm9yKHJlcy5tc2cgfHwgJ+iOt+WPluezu+WIl+ivvueoi+ivpuaDheWksei0pScpO1xuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOiOt+WPluezu+WIl+ivvueoi+ivpuaDheW8guW4uDonLCBlcnJvcik7XG4gICAgICBtZXNzYWdlLmVycm9yKCfojrflj5bns7vliJfor77nqIvor6bmg4XlpLHotKXvvIzor7fph43or5UnKTtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRQdWJsaXNoTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOiOt+WPluWPkeW4g+W8ueeql+eahOezu+WIl+ivvueoi+WIl+ihqFxuICBjb25zdCBmZXRjaFB1Ymxpc2hTZXJpZXNMaXN0ID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRQdWJsaXNoRm9ybUxvYWRpbmcodHJ1ZSk7XG4gICAgICBjb25zb2xlLmxvZygn8J+TnSDojrflj5blj5HluIPlvLnnqpfnmoTns7vliJfor77nqIvliJfooaguLi4nKTtcblxuICAgICAgY29uc3QgeyBkYXRhOiByZXMgfSA9IGF3YWl0IGNvdXJzZUFwaS5nZXRNYXJrZXRwbGFjZVNlcmllcyh7XG4gICAgICAgIHBhZ2U6IDEsXG4gICAgICAgIHBhZ2VTaXplOiA1MFxuICAgICAgfSk7XG5cbiAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwICYmIHJlcy5kYXRhPy5saXN0KSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg6I635Y+W5Y+R5biD5by556qX57O75YiX6K++56iL5YiX6KGo5oiQ5YqfOicsIHJlcy5kYXRhLmxpc3QpO1xuICAgICAgICBzZXRQdWJsaXNoU2VyaWVzTGlzdEZvck1vZGFsKHJlcy5kYXRhLmxpc3QpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOiOt+WPluWPkeW4g+W8ueeql+ezu+WIl+ivvueoi+WIl+ihqOWksei0pTonLCByZXMubXNnKTtcbiAgICAgICAgbWVzc2FnZS5lcnJvcihyZXMubXNnIHx8ICfojrflj5bns7vliJfor77nqIvliJfooajlpLHotKUnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOiOt+WPluWPkeW4g+W8ueeql+ezu+WIl+ivvueoi+WIl+ihqOW8guW4uDonLCBlcnJvcik7XG4gICAgICBtZXNzYWdlLmVycm9yKCfojrflj5bns7vliJfor77nqIvliJfooajlpLHotKXvvIzor7fph43or5UnKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0UHVibGlzaEZvcm1Mb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g6I635Y+W5oyH5a6a57O75YiX5LiL55qE5a2Q6K++56iL5YiX6KGo77yI55So5LqO5Y+R5biD5by556qX77yJXG4gIGNvbnN0IGZldGNoUHVibGlzaENvdXJzZUxpc3QgPSBhc3luYyAoc2VyaWVzSWQ6IG51bWJlcikgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+TnSDojrflj5blj5HluIPlvLnnqpfnmoTlrZDor77nqIvliJfooajvvIzns7vliJdJRDonLCBzZXJpZXNJZCk7XG4gICAgICBjb25zb2xlLmxvZygn8J+UhCDkvb/nlKjor77nqIvnrqHnkIZBUEnojrflj5bojYnnqL/nirbmgIHnmoTlrZDor77nqIsuLi4nKTtcblxuICAgICAgLy8g5L2/55So6K++56iL566h55CGQVBJ6I635Y+W6I2J56i/54q25oCB55qE6K++56iLXG4gICAgICBjb25zdCB7IGRhdGE6IHJlcyB9ID0gYXdhaXQgY291cnNlQXBpLmdldE1hbmFnZW1lbnRTZXJpZXNDb3Vyc2VzKHNlcmllc0lkLCB7XG4gICAgICAgIHBhZ2U6IDEsXG4gICAgICAgIHBhZ2VTaXplOiA1MCxcbiAgICAgICAgc3RhdHVzOiAwICAvLyDlj6rojrflj5bojYnnqL/nirbmgIHnmoTor77nqItcbiAgICAgIH0pO1xuXG4gICAgICBjb25zb2xlLmxvZygn8J+UjSDor77nqIvnrqHnkIZBUEnlk43lupQ6JywgcmVzKTtcblxuICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDAgJiYgcmVzLmRhdGE/Lmxpc3QpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDojrflj5bojYnnqL/nirbmgIHlrZDor77nqIvliJfooajmiJDlip/vvIzmlbDph486JywgcmVzLmRhdGEubGlzdC5sZW5ndGgpO1xuICAgICAgICBzZXRQdWJsaXNoQ291cnNlTGlzdEZvck1vZGFsKHJlcy5kYXRhLmxpc3QpO1xuXG4gICAgICAgIGlmIChyZXMuZGF0YS5saXN0Lmxlbmd0aCA9PT0gMCkge1xuICAgICAgICAgIG1lc3NhZ2UuaW5mbygn6K+l57O75YiX5pqC5peg6I2J56i/54q25oCB55qE6K++56iL5Y+v5Y+R5biDJyk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfimqDvuI8g6K+l57O75YiX5pqC5peg5a2Q6K++56iL5oiWQVBJ6LCD55So5aSx6LSlJyk7XG4gICAgICAgIHNldFB1Ymxpc2hDb3Vyc2VMaXN0Rm9yTW9kYWwoW10pO1xuICAgICAgICBtZXNzYWdlLmluZm8oJ+ivpeezu+WIl+aaguaXoOiNieeov+eKtuaAgeeahOivvueoi+WPr+WPkeW4gycpO1xuICAgICAgfVxuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDojrflj5blj5HluIPlvLnnqpflrZDor77nqIvliJfooajlvILluLg6JywgZXJyb3IpO1xuICAgICAgbWVzc2FnZS5lcnJvcign6I635Y+W5a2Q6K++56iL5YiX6KGo5aSx6LSl77yM6K+36YeN6K+VJyk7XG4gICAgICBzZXRQdWJsaXNoQ291cnNlTGlzdEZvck1vZGFsKFtdKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5aSE55CG57O75YiX6YCJ5oup77yI5Y+R5biD5by556qX77yJXG4gIGNvbnN0IGhhbmRsZVB1Ymxpc2hTZXJpZXNDaGFuZ2UgPSBhc3luYyAoc2VyaWVzSWQ6IG51bWJlcikgPT4ge1xuICAgIGNvbnNvbGUubG9nKCfwn5OaIOWPkeW4g+W8ueeql+mAieaLqeezu+WIl0lEOicsIHNlcmllc0lkKTtcbiAgICBjb25zb2xlLmxvZygn8J+TmiDlvZPliY3ns7vliJfliJfooag6JywgcHVibGlzaFNlcmllc0xpc3RGb3JNb2RhbCk7XG5cbiAgICBzZXRTZWxlY3RlZFNlcmllc0ZvclB1Ymxpc2goc2VyaWVzSWQpO1xuICAgIHNldFNlbGVjdGVkQ291cnNlRm9yUHVibGlzaCh1bmRlZmluZWQpO1xuICAgIHNldFB1Ymxpc2hDb3Vyc2VMaXN0Rm9yTW9kYWwoW10pO1xuXG4gICAgLy8g6YeN572u6KGo5Y2V5Lit55qE6K++56iL6YCJ5oupXG4gICAgcHVibGlzaENvdXJzZUZvcm0uc2V0RmllbGRzVmFsdWUoeyBjb3Vyc2VJZDogdW5kZWZpbmVkIH0pO1xuXG4gICAgLy8g6I635Y+W6K+l57O75YiX5LiL55qE5a2Q6K++56iLXG4gICAgaWYgKHNlcmllc0lkKSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+UhCDlvIDlp4vojrflj5bns7vliJflrZDor77nqIsuLi4nKTtcbiAgICAgIHNldFB1Ymxpc2hGb3JtTG9hZGluZyh0cnVlKTtcblxuICAgICAgdHJ5IHtcbiAgICAgICAgYXdhaXQgZmV0Y2hQdWJsaXNoQ291cnNlTGlzdChzZXJpZXNJZCk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg5a2Q6K++56iL6I635Y+W5a6M5oiQJyk7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg5a2Q6K++56iL6I635Y+W5aSx6LSlOicsIGVycm9yKTtcbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIHNldFB1Ymxpc2hGb3JtTG9hZGluZyhmYWxzZSk7XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIC8vIOWkhOeQhuivvueoi+mAieaLqe+8iOWPkeW4g+W8ueeql++8iVxuICBjb25zdCBoYW5kbGVQdWJsaXNoQ291cnNlQ2hhbmdlID0gKGNvdXJzZUlkOiBudW1iZXIpID0+IHtcbiAgICBjb25zb2xlLmxvZygn8J+TliDlj5HluIPlvLnnqpfpgInmi6nor77nqItJRDonLCBjb3Vyc2VJZCk7XG4gICAgc2V0U2VsZWN0ZWRDb3Vyc2VGb3JQdWJsaXNoKGNvdXJzZUlkKTtcbiAgfTtcblxuICAvLyDph43nva7lj5HluIPor77nqIvlvLnnqpfnirbmgIFcbiAgY29uc3QgcmVzZXRQdWJsaXNoQ291cnNlTW9kYWwgPSAoKSA9PiB7XG4gICAgc2V0SXNQdWJsaXNoQ291cnNlTW9kYWxWaXNpYmxlKGZhbHNlKTtcbiAgICBzZXRTZWxlY3RlZFNlcmllc0ZvclB1Ymxpc2godW5kZWZpbmVkKTtcbiAgICBzZXRTZWxlY3RlZENvdXJzZUZvclB1Ymxpc2godW5kZWZpbmVkKTtcbiAgICBzZXRQdWJsaXNoU2VyaWVzTGlzdEZvck1vZGFsKFtdKTtcbiAgICBzZXRQdWJsaXNoQ291cnNlTGlzdEZvck1vZGFsKFtdKTtcbiAgICBwdWJsaXNoQ291cnNlRm9ybS5yZXNldEZpZWxkcygpO1xuICB9O1xuXG4gIC8vIOaJk+W8gOWPkeW4g+ivvueoi+W8ueeql1xuICBjb25zdCBvcGVuUHVibGlzaENvdXJzZU1vZGFsID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldElzUHVibGlzaENvdXJzZU1vZGFsVmlzaWJsZSh0cnVlKTtcbiAgICBhd2FpdCBmZXRjaFB1Ymxpc2hTZXJpZXNMaXN0KCk7XG4gIH07XG5cbiAgLy8g5Y+R5biD6K++56iLXG4gIGNvbnN0IGhhbmRsZVB1Ymxpc2hDb3Vyc2UgPSBhc3luYyAodmFsdWVzOiBhbnkpID0+IHtcbiAgICB0cnkge1xuICAgICAgaWYgKCFzZWxlY3RlZENvdXJzZUZvclB1Ymxpc2ggfHwgIXNlbGVjdGVkU2VyaWVzRm9yUHVibGlzaCkge1xuICAgICAgICBtZXNzYWdlLmVycm9yKCfor7fpgInmi6nns7vliJfor77nqIvlkozlrZDor77nqIsnKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBzZXRQdWJsaXNoRm9ybUxvYWRpbmcodHJ1ZSk7XG4gICAgICBjb25zb2xlLmxvZygn8J+ToiDlj5HluIPor77nqIvvvIzor77nqItJRDonLCBzZWxlY3RlZENvdXJzZUZvclB1Ymxpc2gpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk6Ig57O75YiXSUQ6Jywgc2VsZWN0ZWRTZXJpZXNGb3JQdWJsaXNoKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OkIOihqOWNleaVsOaNrjonLCB2YWx1ZXMpO1xuXG4gICAgICAvLyDojrflj5blvZPliY3pgInkuK3nmoTor77nqIvkv6Hmga9cbiAgICAgIGNvbnN0IHNlbGVjdGVkQ291cnNlID0gcHVibGlzaENvdXJzZUxpc3RGb3JNb2RhbC5maW5kKGMgPT4gYy5pZCA9PT0gc2VsZWN0ZWRDb3Vyc2VGb3JQdWJsaXNoKTtcbiAgICAgIGlmICghc2VsZWN0ZWRDb3Vyc2UpIHtcbiAgICAgICAgbWVzc2FnZS5lcnJvcign5pyq5om+5Yiw6YCJ5Lit55qE6K++56iL5L+h5oGvJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coJ/Cfk5Yg5b2T5YmN6K++56iL5L+h5oGvOicsIHNlbGVjdGVkQ291cnNlKTtcblxuICAgICAgLy8g5L2/55So5LiT6Zeo55qE5Y+R5biD6K++56iLQVBJXG4gICAgICBjb25zb2xlLmxvZygn8J+TpCDosIPnlKjlj5HluIPor77nqItBUEnvvIzor77nqItJRDonLCBzZWxlY3RlZENvdXJzZUZvclB1Ymxpc2gpO1xuICAgICAgY29uc3QgeyBkYXRhOiByZXMgfSA9IGF3YWl0IGNvdXJzZUFwaS5wdWJsaXNoQ291cnNlKHNlbGVjdGVkQ291cnNlRm9yUHVibGlzaCk7XG5cbiAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7XG4gICAgICAgIG1lc3NhZ2Uuc3VjY2Vzcygn5Y+R5biD6K++56iL5oiQ5YqfJyk7XG4gICAgICAgIHJlc2V0UHVibGlzaENvdXJzZU1vZGFsKCk7XG5cbiAgICAgICAgLy8g5pi+56S65Y+R5biD57uT5p6c5L+h5oGvXG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg5Y+R5biD5oiQ5Yqf77yM6K++56iL5L+h5oGvOicsIHJlcy5kYXRhKTtcblxuICAgICAgICAvLyDliLfmlrDor77nqIvliJfooahcbiAgICAgICAgYXdhaXQgZmV0Y2hDb3Vyc2VMaXN0KCk7XG5cbiAgICAgICAgLy8g5aaC5p6c5b2T5YmN57O75YiX5bey5bGV5byA77yM5Yi35paw5a2Q6K++56iL5YiX6KGoXG4gICAgICAgIGlmIChzZWxlY3RlZFNlcmllc0ZvclB1Ymxpc2ggJiYgZXhwYW5kZWRTZXJpZXMuaGFzKHNlbGVjdGVkU2VyaWVzRm9yUHVibGlzaCkpIHtcbiAgICAgICAgICBhd2FpdCBmZXRjaFNlcmllc0NvdXJzZXMoc2VsZWN0ZWRTZXJpZXNGb3JQdWJsaXNoKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOWPkeW4g+ivvueoi+Wksei0pTonLCByZXMubXNnKTtcbiAgICAgICAgbWVzc2FnZS5lcnJvcihyZXMubXNnIHx8ICflj5HluIPor77nqIvlpLHotKUnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg5Y+R5biD6K++56iL5byC5bi4OicsIGVycm9yKTtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDplJnor6/or6bmg4U6JywgZXJyb3IucmVzcG9uc2U/LmRhdGEpO1xuICAgICAgbWVzc2FnZS5lcnJvcign5Y+R5biD6K++56iL5aSx6LSl77yM6K+36YeN6K+VJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldFB1Ymxpc2hGb3JtTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOmHjee9ruWPkeW4g+W8ueeql+eKtuaAgVxuICBjb25zdCByZXNldFB1Ymxpc2hNb2RhbCA9ICgpID0+IHtcbiAgICBzZXRJc1B1Ymxpc2hDb3Vyc2VNb2RhbFZpc2libGUoZmFsc2UpO1xuICAgIHNldFNlbGVjdGVkU2VyaWVzRm9yUHVibGlzaCh1bmRlZmluZWQpO1xuICAgIHNldFNlbGVjdGVkQ291cnNlRm9yUHVibGlzaCh1bmRlZmluZWQpO1xuICAgIHNldFB1Ymxpc2hTZXJpZXNDb3Vyc2VzKFtdKTtcbiAgICBzZXRQdWJsaXNoU2VyaWVzT3B0aW9ucyhbXSk7XG4gICAgcHVibGlzaENvdXJzZUZvcm0ucmVzZXRGaWVsZHMoKTtcbiAgfTtcblxuXG5cblxuXG4gIC8vIOWkhOeQhuWbvueJh+S4iuS8oFxuICBjb25zdCBoYW5kbGVJbWFnZVVwbG9hZDogVXBsb2FkUHJvcHNbJ2N1c3RvbVJlcXVlc3QnXSA9IGFzeW5jIChvcHRpb25zKSA9PiB7XG4gICAgY29uc3QgeyBmaWxlLCBvblN1Y2Nlc3MsIG9uRXJyb3IgfSA9IG9wdGlvbnM7XG5cbiAgICB0cnkge1xuICAgICAgLy8g6LCD55So5a6e6ZmF55qET1NT5LiK5LygQVBJXG4gICAgICBjb25zdCB1cmwgPSBhd2FpdCB1cGxvYWRBcGkudXBsb2FkVG9Pc3MoZmlsZSBhcyBGaWxlKTtcbiAgICAgIGNvbnNvbGUubG9nKCfns7vliJflsIHpnaLlm77niYfkuIrkvKDmiJDlip/vvIxVUkw6JywgdXJsKTtcblxuICAgICAgc2V0Q292ZXJJbWFnZVVybCh1cmwpO1xuICAgICAgb25TdWNjZXNzPy4oeyB1cmw6IHVybCB9KTtcbiAgICAgIG1lc3NhZ2Uuc3VjY2Vzcygn5Zu+54mH5LiK5Lyg5oiQ5YqfJyk7XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcign57O75YiX5bCB6Z2i5Zu+54mH5LiK5Lyg5aSx6LSlOicsIGVycm9yKTtcbiAgICAgIG1lc3NhZ2UuZXJyb3IoYOS4iuS8oOWksei0pTogJHtlcnJvci5tZXNzYWdlIHx8ICfor7fnqI3lkI7ph43or5UnfWApO1xuICAgICAgb25FcnJvcj8uKGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5aSE55CG5Zu+54mH5Yig6ZmkXG4gIGNvbnN0IGhhbmRsZUltYWdlUmVtb3ZlID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldENvdmVySW1hZ2VVcmwoJycpO1xuICAgIHJldHVybiB0cnVlO1xuICB9O1xuXG4gIC8vIOWkhOeQhuivvueoi+WwgemdouWbvueJh+S4iuS8oFxuICBjb25zdCBoYW5kbGVDb3Vyc2VDb3ZlclVwbG9hZDogVXBsb2FkUHJvcHNbJ2N1c3RvbVJlcXVlc3QnXSA9IGFzeW5jIChvcHRpb25zKSA9PiB7XG4gICAgY29uc3QgeyBmaWxlLCBvblN1Y2Nlc3MsIG9uRXJyb3IgfSA9IG9wdGlvbnM7XG5cbiAgICB0cnkge1xuICAgICAgLy8g6LCD55So5a6e6ZmF55qET1NT5LiK5LygQVBJXG4gICAgICBjb25zdCB1cmwgPSBhd2FpdCB1cGxvYWRBcGkudXBsb2FkVG9Pc3MoZmlsZSBhcyBGaWxlKTtcbiAgICAgIGNvbnNvbGUubG9nKCfor77nqIvlsIHpnaLlm77niYfkuIrkvKDmiJDlip/vvIxVUkw6JywgdXJsKTtcblxuICAgICAgc2V0Q291cnNlQ292ZXJJbWFnZVVybCh1cmwpO1xuICAgICAgb25TdWNjZXNzPy4oeyB1cmw6IHVybCB9KTtcbiAgICAgIG1lc3NhZ2Uuc3VjY2Vzcygn6K++56iL5bCB6Z2i5LiK5Lyg5oiQ5YqfJyk7XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcign6K++56iL5bCB6Z2i5Zu+54mH5LiK5Lyg5aSx6LSlOicsIGVycm9yKTtcbiAgICAgIG1lc3NhZ2UuZXJyb3IoYOS4iuS8oOWksei0pTogJHtlcnJvci5tZXNzYWdlIHx8ICfor7fnqI3lkI7ph43or5UnfWApO1xuICAgICAgb25FcnJvcj8uKGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5aSE55CG6K++56iL5bCB6Z2i5Yig6ZmkXG4gIGNvbnN0IGhhbmRsZUNvdXJzZUNvdmVyUmVtb3ZlID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldENvdXJzZUNvdmVySW1hZ2VVcmwoJycpO1xuICAgIHJldHVybiB0cnVlO1xuICB9O1xuXG4gIC8vIOWkhOeQhumZhOS7tui1hOa6kOS4iuS8oFxuICBjb25zdCBoYW5kbGVBZGRpdGlvbmFsUmVzb3VyY2VVcGxvYWQ6IFVwbG9hZFByb3BzWydjdXN0b21SZXF1ZXN0J10gPSBhc3luYyAob3B0aW9ucykgPT4ge1xuICAgIGNvbnN0IHsgZmlsZSwgb25TdWNjZXNzLCBvbkVycm9yIH0gPSBvcHRpb25zO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIOiwg+eUqOWunumZheeahE9TU+S4iuS8oEFQSVxuICAgICAgY29uc3QgdXJsID0gYXdhaXQgdXBsb2FkQXBpLnVwbG9hZFRvT3NzKGZpbGUgYXMgRmlsZSk7XG4gICAgICBjb25zb2xlLmxvZygn6ZmE5Lu26LWE5rqQ5LiK5Lyg5oiQ5Yqf77yMVVJMOicsIHVybCk7XG5cbiAgICAgIHNldEFkZGl0aW9uYWxGaWxlcyhwcmV2ID0+IFsuLi5wcmV2LCB1cmxdKTtcbiAgICAgIG9uU3VjY2Vzcz8uKHsgdXJsOiB1cmwsIG5hbWU6IChmaWxlIGFzIEZpbGUpLm5hbWUgfSk7XG4gICAgICBtZXNzYWdlLnN1Y2Nlc3MoYOmZhOS7tiAkeyhmaWxlIGFzIEZpbGUpLm5hbWV9IOS4iuS8oOaIkOWKn2ApO1xuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+mZhOS7tui1hOa6kOS4iuS8oOWksei0pTonLCBlcnJvcik7XG4gICAgICBtZXNzYWdlLmVycm9yKGDpmYTku7YgJHsoZmlsZSBhcyBGaWxlKS5uYW1lfSDkuIrkvKDlpLHotKU6ICR7ZXJyb3IubWVzc2FnZSB8fCAn6K+356iN5ZCO6YeN6K+VJ31gKTtcbiAgICAgIG9uRXJyb3I/LihlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWkhOeQhumZhOS7tuWIoOmZpFxuICBjb25zdCBoYW5kbGVBZGRpdGlvbmFsUmVzb3VyY2VSZW1vdmUgPSBhc3luYyAoZmlsZTogYW55KSA9PiB7XG4gICAgY29uc3QgdXJsID0gZmlsZS51cmwgfHwgZmlsZS5yZXNwb25zZT8udXJsO1xuICAgIHNldEFkZGl0aW9uYWxGaWxlcyhwcmV2ID0+IHByZXYuZmlsdGVyKGYgPT4gZiAhPT0gdXJsKSk7XG4gICAgcmV0dXJuIHRydWU7XG4gIH07XG4gIC8vIOWkhOeQhuinhumikeS4iuS8oFxuICBjb25zdCBoYW5kbGVWaWRlb1VwbG9hZDogVXBsb2FkUHJvcHNbJ2N1c3RvbVJlcXVlc3QnXSA9IGFzeW5jIChvcHRpb25zKSA9PiB7XG4gICAgY29uc3QgeyBmaWxlLCBvblN1Y2Nlc3MsIG9uRXJyb3IgfSA9IG9wdGlvbnM7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgdXJsID0gYXdhaXQgdXBsb2FkQXBpLnVwbG9hZFRvT3NzKGZpbGUgYXMgRmlsZSk7XG4gICAgICBjb25zb2xlLmxvZygn6K++56iL6KeG6aKR5LiK5Lyg5oiQ5Yqf77yMVVJMOicsIHVybCk7XG5cbiAgICAgIHNldENvdXJzZVZpZGVvVXJsKHVybCk7XG4gICAgICBzZXRDb3Vyc2VWaWRlb05hbWUoKGZpbGUgYXMgRmlsZSkubmFtZSk7XG5cbiAgICAgIC8vIOWmguaenOaYr+inhumikeaWh+S7tu+8jOWwneivleiOt+WPluaXtumVv1xuICAgICAgY29uc3QgdmlkZW9FbGVtZW50ID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgndmlkZW8nKTtcbiAgICAgIHZpZGVvRWxlbWVudC5zcmMgPSB1cmw7XG4gICAgICB2aWRlb0VsZW1lbnQub25sb2FkZWRtZXRhZGF0YSA9ICgpID0+IHtcbiAgICAgICAgc2V0VmlkZW9EdXJhdGlvbihNYXRoLmZsb29yKHZpZGVvRWxlbWVudC5kdXJhdGlvbikpO1xuICAgICAgfTtcblxuICAgICAgb25TdWNjZXNzPy4oeyB1cmw6IHVybCB9KTtcbiAgICAgIG1lc3NhZ2Uuc3VjY2Vzcygn6K++56iL6KeG6aKR5LiK5Lyg5oiQ5YqfJyk7XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcign6K++56iL6KeG6aKR5LiK5Lyg5aSx6LSlOicsIGVycm9yKTtcbiAgICAgIG1lc3NhZ2UuZXJyb3IoYOinhumikeS4iuS8oOWksei0pTogJHtlcnJvci5tZXNzYWdlIHx8ICfor7fnqI3lkI7ph43or5UnfWApO1xuICAgICAgb25FcnJvcj8uKGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5aSE55CG6KeG6aKR5Yig6ZmkXG4gIGNvbnN0IGhhbmRsZVZpZGVvUmVtb3ZlID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldENvdXJzZVZpZGVvVXJsKCcnKTtcbiAgICBzZXRDb3Vyc2VWaWRlb05hbWUoJycpO1xuICAgIHNldFZpZGVvRHVyYXRpb24oMCk7XG4gICAgcmV0dXJuIHRydWU7XG4gIH07XG5cbiAgLy8g5aSE55CG5paH5qGj5LiK5LygXG4gIGNvbnN0IGhhbmRsZURvY3VtZW50VXBsb2FkOiBVcGxvYWRQcm9wc1snY3VzdG9tUmVxdWVzdCddID0gYXN5bmMgKG9wdGlvbnMpID0+IHtcbiAgICBjb25zdCB7IGZpbGUsIG9uU3VjY2Vzcywgb25FcnJvciB9ID0gb3B0aW9ucztcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCB1cmwgPSBhd2FpdCB1cGxvYWRBcGkudXBsb2FkVG9Pc3MoZmlsZSBhcyBGaWxlKTtcbiAgICAgIGNvbnNvbGUubG9nKCfor77nqIvmlofmoaPkuIrkvKDmiJDlip/vvIxVUkw6JywgdXJsKTtcblxuICAgICAgc2V0Q291cnNlRG9jdW1lbnRVcmwodXJsKTtcbiAgICAgIHNldENvdXJzZURvY3VtZW50TmFtZSgoZmlsZSBhcyBGaWxlKS5uYW1lKTtcbiAgICAgIG9uU3VjY2Vzcz8uKHsgdXJsOiB1cmwgfSk7XG4gICAgICBtZXNzYWdlLnN1Y2Nlc3MoJ+ivvueoi+aWh+aho+S4iuS8oOaIkOWKnycpO1xuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+ivvueoi+aWh+aho+S4iuS8oOWksei0pTonLCBlcnJvcik7XG4gICAgICBtZXNzYWdlLmVycm9yKGDmlofmoaPkuIrkvKDlpLHotKU6ICR7ZXJyb3IubWVzc2FnZSB8fCAn6K+356iN5ZCO6YeN6K+VJ31gKTtcbiAgICAgIG9uRXJyb3I/LihlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWkhOeQhuaWh+aho+WIoOmZpFxuICBjb25zdCBoYW5kbGVEb2N1bWVudFJlbW92ZSA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRDb3Vyc2VEb2N1bWVudFVybCgnJyk7XG4gICAgc2V0Q291cnNlRG9jdW1lbnROYW1lKCcnKTtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfTtcbiAgLy8g5pWZ5a2m5L+h5oGv566h55CG5Ye95pWwXG4gIGNvbnN0IGFkZFRlYWNoaW5nSW5mbyA9ICgpID0+IHtcbiAgICBzZXRUZWFjaGluZ0luZm9MaXN0KFsuLi50ZWFjaGluZ0luZm9MaXN0LCB7IHRpdGxlOiAnJywgY29udGVudDogWycnXSB9XSk7XG4gIH07XG5cbiAgY29uc3QgcmVtb3ZlVGVhY2hpbmdJbmZvID0gKGluZGV4OiBudW1iZXIpID0+IHtcbiAgICBpZiAodGVhY2hpbmdJbmZvTGlzdC5sZW5ndGggPiAxKSB7XG4gICAgICBjb25zdCBuZXdMaXN0ID0gdGVhY2hpbmdJbmZvTGlzdC5maWx0ZXIoKF8sIGkpID0+IGkgIT09IGluZGV4KTtcbiAgICAgIHNldFRlYWNoaW5nSW5mb0xpc3QobmV3TGlzdCk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHVwZGF0ZVRlYWNoaW5nSW5mb1RpdGxlID0gKGluZGV4OiBudW1iZXIsIHRpdGxlOiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCBuZXdMaXN0ID0gWy4uLnRlYWNoaW5nSW5mb0xpc3RdO1xuICAgIG5ld0xpc3RbaW5kZXhdLnRpdGxlID0gdGl0bGU7XG4gICAgc2V0VGVhY2hpbmdJbmZvTGlzdChuZXdMaXN0KTtcbiAgfTtcblxuICBjb25zdCB1cGRhdGVUZWFjaGluZ0luZm9Db250ZW50ID0gKGluZGV4OiBudW1iZXIsIGNvbnRlbnRJbmRleDogbnVtYmVyLCBjb250ZW50OiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCBuZXdMaXN0ID0gWy4uLnRlYWNoaW5nSW5mb0xpc3RdO1xuICAgIG5ld0xpc3RbaW5kZXhdLmNvbnRlbnRbY29udGVudEluZGV4XSA9IGNvbnRlbnQ7XG4gICAgc2V0VGVhY2hpbmdJbmZvTGlzdChuZXdMaXN0KTtcbiAgfTtcblxuICBjb25zdCBhZGRUZWFjaGluZ0luZm9Db250ZW50ID0gKGluZGV4OiBudW1iZXIpID0+IHtcbiAgICBjb25zdCBuZXdMaXN0ID0gWy4uLnRlYWNoaW5nSW5mb0xpc3RdO1xuICAgIG5ld0xpc3RbaW5kZXhdLmNvbnRlbnQucHVzaCgnJyk7XG4gICAgc2V0VGVhY2hpbmdJbmZvTGlzdChuZXdMaXN0KTtcbiAgfTtcblxuICBjb25zdCByZW1vdmVUZWFjaGluZ0luZm9Db250ZW50ID0gKGluZGV4OiBudW1iZXIsIGNvbnRlbnRJbmRleDogbnVtYmVyKSA9PiB7XG4gICAgY29uc3QgbmV3TGlzdCA9IFsuLi50ZWFjaGluZ0luZm9MaXN0XTtcbiAgICBpZiAobmV3TGlzdFtpbmRleF0uY29udGVudC5sZW5ndGggPiAxKSB7XG4gICAgICBuZXdMaXN0W2luZGV4XS5jb250ZW50LnNwbGljZShjb250ZW50SW5kZXgsIDEpO1xuICAgICAgc2V0VGVhY2hpbmdJbmZvTGlzdChuZXdMaXN0KTtcbiAgICB9XG4gIH07XG5cblxuXG5cblxuXG5cbiAgLy8g5omT5byA57yW6L6R5qih5oCB5qGGXG4gIGNvbnN0IG9wZW5FZGl0TW9kYWwgPSBhc3luYyAoY291cnNlOiBDb3Vyc2UpID0+IHtcbiAgICBzZXRFZGl0aW5nQ291cnNlKGNvdXJzZSk7XG4gICAgZWRpdENvdXJzZUZvcm0uc2V0RmllbGRzVmFsdWUoY291cnNlKTtcbiAgICBzZXRJc0VkaXRDb3Vyc2VNb2RhbFZpc2libGUodHJ1ZSk7XG4gIH07XG5cbiAgLy8g6L+H5ruk6K++56iL5YiX6KGoXG4gIGNvbnN0IGZpbHRlcmVkQ291cnNlcyA9IChjb3Vyc2VMaXN0IHx8IFtdKS5maWx0ZXIoY291cnNlID0+XG4gICAgY291cnNlLm5hbWUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hLZXl3b3JkLnRvTG93ZXJDYXNlKCkpIHx8XG4gICAgY291cnNlLmRlc2NyaXB0aW9uLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoS2V5d29yZC50b0xvd2VyQ2FzZSgpKSB8fFxuICAgIGNvdXJzZS5jYXRlZ29yeS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaEtleXdvcmQudG9Mb3dlckNhc2UoKSlcbiAgKTtcblxuICAvLyDlh4blpIfooajmoLzmlbDmja7vvJrlsIbns7vliJfor77nqIvlkozlrZDor77nqIvlkIjlubbkuLrkuIDkuKrmiYHlubPliJfooahcbiAgY29uc3QgcHJlcGFyZVRhYmxlRGF0YSA9ICgpID0+IHtcbiAgICBjb25zdCB0YWJsZURhdGE6IGFueVtdID0gW107XG5cbiAgICBjb25zb2xlLmxvZygn8J+UhCDlh4blpIfooajmoLzmlbDmja4uLi4nKTtcbiAgICBjb25zb2xlLmxvZygn8J+TiiDns7vliJfor77nqIvliJfooag6Jywgc2VyaWVzTGlzdCk7XG4gICAgY29uc29sZS5sb2coJ/Cfk4og5bGV5byA55qE57O75YiXOicsIEFycmF5LmZyb20oZXhwYW5kZWRTZXJpZXMpKTtcbiAgICBjb25zb2xlLmxvZygn8J+TiiDlrZDor77nqIvmmKDlsIQ6Jywgc2VyaWVzQ291cnNlc01hcCk7XG5cbiAgICBzZXJpZXNMaXN0LmZvckVhY2goc2VyaWVzID0+IHtcbiAgICAgIC8vIOa3u+WKoOezu+WIl+ivvueoi+ihjFxuICAgICAgdGFibGVEYXRhLnB1c2goe1xuICAgICAgICBrZXk6IGBzZXJpZXMtJHtzZXJpZXMuaWR9YCxcbiAgICAgICAgaWQ6IHNlcmllcy5pZCxcbiAgICAgICAgdGl0bGU6IHNlcmllcy50aXRsZSxcbiAgICAgICAgc3RhdHVzOiBzZXJpZXMuc3RhdHVzLFxuICAgICAgICB0eXBlOiAnc2VyaWVzJyxcbiAgICAgICAgaXNFeHBhbmRlZDogZXhwYW5kZWRTZXJpZXMuaGFzKHNlcmllcy5pZCksXG4gICAgICAgIHNlcmllc0lkOiBzZXJpZXMuaWRcbiAgICAgIH0pO1xuXG4gICAgICAvLyDlpoLmnpzns7vliJflt7LlsZXlvIDvvIzmt7vliqDlrZDor77nqIvooYxcbiAgICAgIGlmIChleHBhbmRlZFNlcmllcy5oYXMoc2VyaWVzLmlkKSkge1xuICAgICAgICBjb25zdCBzdWJDb3Vyc2VzID0gc2VyaWVzQ291cnNlc01hcC5nZXQoc2VyaWVzLmlkKSB8fCBbXTtcbiAgICAgICAgY29uc29sZS5sb2coYPCfk5og57O75YiXICR7c2VyaWVzLmlkfSDnmoTlrZDor77nqIs6YCwgc3ViQ291cnNlcyk7XG5cbiAgICAgICAgc3ViQ291cnNlcy5mb3JFYWNoKGNvdXJzZSA9PiB7XG4gICAgICAgICAgdGFibGVEYXRhLnB1c2goe1xuICAgICAgICAgICAga2V5OiBgY291cnNlLSR7Y291cnNlLmlkfWAsXG4gICAgICAgICAgICBpZDogY291cnNlLmlkLFxuICAgICAgICAgICAgdGl0bGU6IGNvdXJzZS50aXRsZSxcbiAgICAgICAgICAgIHN0YXR1czogY291cnNlLnN0YXR1cyxcbiAgICAgICAgICAgIHR5cGU6ICdjb3Vyc2UnLFxuICAgICAgICAgICAgc2VyaWVzSWQ6IHNlcmllcy5pZCxcbiAgICAgICAgICAgIHBhcmVudFNlcmllc1RpdGxlOiBzZXJpZXMudGl0bGVcbiAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICBjb25zb2xlLmxvZygn8J+TiyDmnIDnu4jooajmoLzmlbDmja46JywgdGFibGVEYXRhKTtcbiAgICByZXR1cm4gdGFibGVEYXRhO1xuICB9O1xuXG4gIC8vIOihqOagvOWIl+WumuS5iVxuICBjb25zdCBjb2x1bW5zID0gW1xuICAgIHtcbiAgICAgIHRpdGxlOiAn57O75YiX6K++56iLSUQnLFxuICAgICAgZGF0YUluZGV4OiAnaWQnLFxuICAgICAga2V5OiAnaWQnLFxuICAgICAgd2lkdGg6IDEyMCxcbiAgICB9LFxuICAgIHtcbiAgICAgIHRpdGxlOiAn57O75YiX6K++56iLL+WtkOivvueoi+WQjeensCcsXG4gICAgICBkYXRhSW5kZXg6ICd0aXRsZScsXG4gICAgICBrZXk6ICd0aXRsZScsXG4gICAgICByZW5kZXI6ICh0ZXh0OiBzdHJpbmcsIHJlY29yZDogYW55KSA9PiB7XG4gICAgICAgIGlmIChyZWNvcmQudHlwZSA9PT0gJ3NlcmllcycpIHtcbiAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgIHNpemU9XCJzbWFsbFwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gdG9nZ2xlU2VyaWVzRXhwYW5zaW9uKHJlY29yZC5pZCl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0wIG1pbi13LTAgaG92ZXI6YmctYmx1ZS01MFwiXG4gICAgICAgICAgICAgICAgc3R5bGU9e3sgbWluV2lkdGg6ICcyMHB4JywgaGVpZ2h0OiAnMjBweCcgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtyZWNvcmQuaXNFeHBhbmRlZCA/ICfilrwnIDogJ+KWtid9XG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWJsdWUtNjAwIHRleHQtYmFzZVwiPnt0ZXh0fTwvc3Bhbj5cbiAgICAgICAgICAgICAgPFRhZyBjb2xvcj1cImJsdWVcIiBjbGFzc05hbWU9XCJ0ZXh0LXhzXCI+57O75YiXPC9UYWc+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTggZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPuKUlOKUgDwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMFwiPnt0ZXh0fTwvc3Bhbj5cbiAgICAgICAgICAgICAgPFRhZyBjb2xvcj1cImdyZWVuXCIgY2xhc3NOYW1lPVwidGV4dC14c1wiPuWtkOivvueoizwvVGFnPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKTtcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICB9LFxuICAgIHtcbiAgICAgIHRpdGxlOiAn5Y+R5biD54q25oCBJyxcbiAgICAgIGRhdGFJbmRleDogJ3N0YXR1cycsXG4gICAgICBrZXk6ICdzdGF0dXMnLFxuICAgICAgd2lkdGg6IDEwMCxcbiAgICAgIHJlbmRlcjogKHN0YXR1czogbnVtYmVyLCByZWNvcmQ6IGFueSkgPT4ge1xuICAgICAgICBjb25zdCBnZXRTdGF0dXNDb25maWcgPSAoc3RhdHVzOiBudW1iZXIpID0+IHtcbiAgICAgICAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgICAgICAgY2FzZSAxOiByZXR1cm4geyBjb2xvcjogJ2dyZWVuJywgdGV4dDogJ+W3suWPkeW4gycgfTtcbiAgICAgICAgICAgIGNhc2UgMDogcmV0dXJuIHsgY29sb3I6ICdvcmFuZ2UnLCB0ZXh0OiAn6I2J56i/JyB9O1xuICAgICAgICAgICAgY2FzZSAyOiByZXR1cm4geyBjb2xvcjogJ3JlZCcsIHRleHQ6ICflt7LlvZLmoaMnIH07XG4gICAgICAgICAgICBkZWZhdWx0OiByZXR1cm4geyBjb2xvcjogJ2dyYXknLCB0ZXh0OiAn5pyq55+lJyB9O1xuICAgICAgICAgIH1cbiAgICAgICAgfTtcblxuICAgICAgICBjb25zdCBjb25maWcgPSBnZXRTdGF0dXNDb25maWcoc3RhdHVzKTtcbiAgICAgICAgcmV0dXJuIDxUYWcgY29sb3I9e2NvbmZpZy5jb2xvcn0+e2NvbmZpZy50ZXh0fTwvVGFnPjtcbiAgICAgIH0sXG4gICAgfSxcbiAgICB7XG4gICAgICB0aXRsZTogJ+aTjeS9nCcsXG4gICAgICBrZXk6ICdhY3Rpb24nLFxuICAgICAgd2lkdGg6IDE1MCxcbiAgICAgIHJlbmRlcjogKHJlY29yZDogYW55KSA9PiB7XG4gICAgICAgIGlmIChyZWNvcmQudHlwZSA9PT0gJ3NlcmllcycpIHtcbiAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgPFNwYWNlIHNpemU9XCJzbWFsbFwiPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdHlwZT1cImxpbmtcIlxuICAgICAgICAgICAgICAgIHNpemU9XCJzbWFsbFwiXG4gICAgICAgICAgICAgICAgaWNvbj17PEVkaXRPdXRsaW5lZCAvPn1cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICBtZXNzYWdlLmluZm8oJ+ezu+WIl+ivvueoi+e8lui+keWKn+iDveW+heWunueOsCcpO1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDnvJbovpFcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L1NwYWNlPlxuICAgICAgICAgICk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgIDxTcGFjZSBzaXplPVwic21hbGxcIj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHR5cGU9XCJsaW5rXCJcbiAgICAgICAgICAgICAgICBzaXplPVwic21hbGxcIlxuICAgICAgICAgICAgICAgIGljb249ezxFZGl0T3V0bGluZWQgLz59XG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgbWVzc2FnZS5pbmZvKCflrZDor77nqIvnvJbovpHlip/og73lvoXlrp7njrAnKTtcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDAgaG92ZXI6dGV4dC1ibHVlLTgwMFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDnvJbovpFcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDxQb3Bjb25maXJtXG4gICAgICAgICAgICAgICAgdGl0bGU9e1xuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPHA+56Gu5a6a6KaB5Yig6Zmk6L+Z5Liq5a2Q6K++56iL5ZCX77yfPC9wPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIHRleHQtc21cIj7or77nqIvlkI3np7DvvJp7cmVjb3JkLnRpdGxlfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCB0ZXh0LXNtXCI+5omA5bGe57O75YiX77yae3JlY29yZC5wYXJlbnRTZXJpZXNUaXRsZX08L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgb25Db25maXJtPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+Xke+4jyDnlKjmiLfnoa7orqTliKDpmaTlrZDor77nqIs6JywgcmVjb3JkKTtcbiAgICAgICAgICAgICAgICAgIGhhbmRsZURlbGV0ZVN1YkNvdXJzZShyZWNvcmQuaWQsIHJlY29yZC5zZXJpZXNJZCk7XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICBva1RleHQ9XCLnoa7lrprliKDpmaRcIlxuICAgICAgICAgICAgICAgIGNhbmNlbFRleHQ9XCLlj5bmtohcIlxuICAgICAgICAgICAgICAgIG9rVHlwZT1cImRhbmdlclwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICB0eXBlPVwibGlua1wiXG4gICAgICAgICAgICAgICAgICBzaXplPVwic21hbGxcIlxuICAgICAgICAgICAgICAgICAgZGFuZ2VyXG4gICAgICAgICAgICAgICAgICBpY29uPXs8RGVsZXRlT3V0bGluZWQgLz59XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgaG92ZXI6dGV4dC1yZWQtODAwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICDliKDpmaRcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9Qb3Bjb25maXJtPlxuICAgICAgICAgICAgPC9TcGFjZT5cbiAgICAgICAgICApO1xuICAgICAgICB9XG4gICAgICB9LFxuICAgIH0sXG4gIF07XG5cbiAgLy8g6I635Y+W5pWZ5biI5YiX6KGoXG4gIC8vIGNvbnN0IGZldGNoVGVhY2hlcnMgPSBhc3luYyAoKSA9PiB7XG4gIC8vICAgdHJ5IHtcbiAgLy8gICAgIGNvbnN0IHsgZGF0YTogcmVzIH0gPSBhd2FpdCBjb3Vyc2VBcGkuZ2V0VGVhY2hlcnMoKTtcbiAgLy8gICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7XG4gIC8vICAgICAgIHNldFRlYWNoZXJzKHJlcy5kYXRhKTtcbiAgLy8gICAgICAgY29uc29sZS5sb2coJ+aIkOWKn+iOt+WPluaVmeW4iOWIl+ihqDonLCByZXMuZGF0YSk7XG4gIC8vICAgICB9IGVsc2Uge1xuICAvLyAgICAgICBjb25zb2xlLmxvZygnQVBJ6L+U5Zue5peg5pWw5o2u77yM5L2/55So5qih5ouf5pWZ5biI5pWw5o2uJyk7XG4gIC8vICAgICAgIC8vIOS9v+eUqOaooeaLn+aVsOaNrlxuICAvLyAgICAgICBjb25zdCBtb2NrVGVhY2hlcnMgPSBbXG4gIC8vICAgICAgICAgeyBpZDogMSwgbmFtZTogJ+W8oOiAgeW4iCcsIGVtYWlsOiAnemhhbmdAc2Nob29sLmNvbScsIHN1YmplY3Q6ICfmlbDlraYnLCBzY2hvb2w6ICflrp7pqozlsI/lraYnLCBhdmF0YXI6ICcnLCBwaG9uZTogJzEzODAwMTM4MDAxJyB9LFxuICAvLyAgICAgICAgIHsgaWQ6IDIsIG5hbWU6ICfmnY7ogIHluIgnLCBlbWFpbDogJ2xpQHNjaG9vbC5jb20nLCBzdWJqZWN0OiAn6K+t5paHJywgc2Nob29sOiAn5a6e6aqM5bCP5a2mJywgYXZhdGFyOiAnJywgcGhvbmU6ICcxMzgwMDEzODAwMicgfSxcbiAgLy8gICAgICAgICB7IGlkOiAzLCBuYW1lOiAn546L6ICB5biIJywgZW1haWw6ICd3YW5nQHNjaG9vbC5jb20nLCBzdWJqZWN0OiAn6Iux6K+tJywgc2Nob29sOiAn56ys5LqM5bCP5a2mJywgYXZhdGFyOiAnJywgcGhvbmU6ICcxMzgwMDEzODAwMycgfSxcbiAgLy8gICAgICAgICB7IGlkOiA0LCBuYW1lOiAn6LW16ICB5biIJywgZW1haWw6ICd6aGFvQHNjaG9vbC5jb20nLCBzdWJqZWN0OiAn56eR5a2mJywgc2Nob29sOiAn56ys5LqM5bCP5a2mJywgYXZhdGFyOiAnJywgcGhvbmU6ICcxMzgwMDEzODAwNCcgfSxcbiAgLy8gICAgICAgICB7IGlkOiA1LCBuYW1lOiAn5YiY6ICB5biIJywgZW1haWw6ICdsaXVAc2Nob29sLmNvbScsIHN1YmplY3Q6ICfnvJbnqIsnLCBzY2hvb2w6ICflrp7pqozkuK3lraYnLCBhdmF0YXI6ICcnLCBwaG9uZTogJzEzODAwMTM4MDA1JyB9LFxuICAvLyAgICAgICAgIHsgaWQ6IDYsIG5hbWU6ICfpmYjogIHluIgnLCBlbWFpbDogJ2NoZW5Ac2Nob29sLmNvbScsIHN1YmplY3Q6ICfkv6Hmga/mioDmnK8nLCBzY2hvb2w6ICflrp7pqozkuK3lraYnLCBhdmF0YXI6ICcnLCBwaG9uZTogJzEzODAwMTM4MDA2JyB9XG4gIC8vICAgICAgIF07XG4gIC8vICAgICAgIHNldFRlYWNoZXJzKG1vY2tUZWFjaGVycyk7XG4gIC8vICAgICB9XG4gIC8vICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgLy8gICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluaVmeW4iOWIl+ihqOWksei0pTonLCBlcnJvcik7XG4gIC8vICAgICAvLyDkvb/nlKjmqKHmi5/mlbDmja5cbiAgLy8gICAgIGNvbnN0IG1vY2tUZWFjaGVycyA9IFtcbiAgLy8gICAgICAgeyBpZDogMSwgbmFtZTogJ+W8oOiAgeW4iCcsIGVtYWlsOiAnemhhbmdAc2Nob29sLmNvbScsIHN1YmplY3Q6ICfmlbDlraYnLCBzY2hvb2w6ICflrp7pqozlsI/lraYnLCBhdmF0YXI6ICcnLCBwaG9uZTogJzEzODAwMTM4MDAxJyB9LFxuICAvLyAgICAgICB7IGlkOiAyLCBuYW1lOiAn5p2O6ICB5biIJywgZW1haWw6ICdsaUBzY2hvb2wuY29tJywgc3ViamVjdDogJ+ivreaWhycsIHNjaG9vbDogJ+WunumqjOWwj+WtpicsIGF2YXRhcjogJycsIHBob25lOiAnMTM4MDAxMzgwMDInIH0sXG4gIC8vICAgICAgIHsgaWQ6IDMsIG5hbWU6ICfnjovogIHluIgnLCBlbWFpbDogJ3dhbmdAc2Nob29sLmNvbScsIHN1YmplY3Q6ICfoi7Hor60nLCBzY2hvb2w6ICfnrKzkuozlsI/lraYnLCBhdmF0YXI6ICcnLCBwaG9uZTogJzEzODAwMTM4MDAzJyB9LFxuICAvLyAgICAgICB7IGlkOiA0LCBuYW1lOiAn6LW16ICB5biIJywgZW1haWw6ICd6aGFvQHNjaG9vbC5jb20nLCBzdWJqZWN0OiAn56eR5a2mJywgc2Nob29sOiAn56ys5LqM5bCP5a2mJywgYXZhdGFyOiAnJywgcGhvbmU6ICcxMzgwMDEzODAwNCcgfSxcbiAgLy8gICAgICAgeyBpZDogNSwgbmFtZTogJ+WImOiAgeW4iCcsIGVtYWlsOiAnbGl1QHNjaG9vbC5jb20nLCBzdWJqZWN0OiAn57yW56iLJywgc2Nob29sOiAn5a6e6aqM5Lit5a2mJywgYXZhdGFyOiAnJywgcGhvbmU6ICcxMzgwMDEzODAwNScgfSxcbiAgLy8gICAgICAgeyBpZDogNiwgbmFtZTogJ+mZiOiAgeW4iCcsIGVtYWlsOiAnY2hlbkBzY2hvb2wuY29tJywgc3ViamVjdDogJ+S/oeaBr+aKgOacrycsIHNjaG9vbDogJ+WunumqjOS4reWtpicsIGF2YXRhcjogJycsIHBob25lOiAnMTM4MDAxMzgwMDYnIH1cbiAgLy8gICAgIF07XG4gIC8vICAgICBzZXRUZWFjaGVycyhtb2NrVGVhY2hlcnMpO1xuICAvLyAgICAgY29uc29sZS5sb2coJ+S9v+eUqOaooeaLn+aVmeW4iOaVsOaNrjonLCBtb2NrVGVhY2hlcnMpO1xuICAvLyAgIH1cbiAgLy8gfTtcblxuICAvLyDojrflj5bor77nqIvmoIfnrb7liJfooaggLSDkvb/nlKjor77nqIvluILlnLpBUElcbiAgY29uc3QgZmV0Y2hDb3Vyc2VUYWdzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+Pt++4jyDlvIDlp4vojrflj5bor77nqIvmoIfnrb7liJfooaguLi4nKTtcbiAgICAgIGNvbnN0IHsgZGF0YTogcmVzIH0gPSBhd2FpdCBjb3Vyc2VBcGkuZ2V0Q291cnNlVGFncyh7XG4gICAgICAgIHBhZ2U6IDEsXG4gICAgICAgIHBhZ2VTaXplOiAxMDAsIC8vIOiOt+WPluabtOWkmuagh+etvueUqOS6jumAieaLqVxuICAgICAgICBzdGF0dXM6IDEgICAgICAvLyDlj6rojrflj5blkK/nlKjnmoTmoIfnrb5cbiAgICAgIH0pO1xuXG4gICAgICBjb25zb2xlLmxvZygn8J+TqCBnZXRDb3Vyc2VUYWdzIEFQSeWTjeW6lDonLCByZXMpO1xuXG4gICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCAmJiByZXMuZGF0YSAmJiByZXMuZGF0YS5saXN0KSB7XG4gICAgICAgIGNvbnN0IHRhZ3MgPSByZXMuZGF0YS5saXN0Lm1hcCgodGFnOiBhbnkpID0+ICh7XG4gICAgICAgICAgaWQ6IHRhZy5pZCxcbiAgICAgICAgICBuYW1lOiB0YWcubmFtZSxcbiAgICAgICAgICBjb2xvcjogdGFnLmNvbG9yLFxuICAgICAgICAgIGNhdGVnb3J5OiB0YWcuY2F0ZWdvcnksXG4gICAgICAgICAgZGVzY3JpcHRpb246IHRhZy5kZXNjcmlwdGlvbiB8fCAnJ1xuICAgICAgICB9KSk7XG5cbiAgICAgICAgc2V0Q291cnNlVGFncyh0YWdzKTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDmiJDlip/ojrflj5bor77nqIvmoIfnrb7liJfooag6JywgdGFncyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBBUEnov5Tlm57mlbDmja7moLzlvI/lvILluLg6JywgcmVzKTtcbiAgICAgICAgc2V0Q291cnNlVGFncyhbXSk7XG4gICAgICAgIG5vdGlmaWNhdGlvbi53YXJuaW5nKCfojrflj5bmoIfnrb7liJfooajlpLHotKXvvIzor7fmo4Dmn6XnvZHnu5zov57mjqUnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOiOt+WPluivvueoi+agh+etvuWksei0pTonLCBlcnJvcik7XG4gICAgICBzZXRDb3Vyc2VUYWdzKFtdKTtcbiAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign6I635Y+W5qCH562+5YiX6KGo5aSx6LSl77yM6K+36YeN6K+VJyk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOiOt+WPluivvueoi+ezu+WIl+WIl+ihqCAtIOS9v+eUqOivvueoi+W4guWcukFQSVxuICBjb25zdCBmZXRjaENvdXJzZVNlcmllcyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ/CflIQg5byA5aeL6I635Y+W6K++56iL5biC5Zy657O75YiX6K++56iL5YiX6KGoLi4uJyk7XG4gICAgICBjb25zdCB7IGRhdGE6IHJlcyB9ID0gYXdhaXQgY291cnNlQXBpLmdldE1hcmtldHBsYWNlU2VyaWVzKHtcbiAgICAgICAgcGFnZTogMSxcbiAgICAgICAgcGFnZVNpemU6IDUwIC8vIOivvueoi+W4guWcukFQSemZkOWItuacgOWkpzUwXG4gICAgICB9KTtcblxuICAgICAgY29uc29sZS5sb2coJ/Cfk6ggZ2V0TWFya2V0cGxhY2VTZXJpZXMgQVBJ5ZON5bqUOicsIHJlcyk7XG5cbiAgICAgIC8vIOajgOafpeaYr+WQpuacieabtOWkmuaVsOaNrlxuICAgICAgaWYgKHJlcy5kYXRhPy5wYWdpbmF0aW9uPy50b3RhbCA+IDUwKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKGDimqDvuI8g5rOo5oSP77ya5oC75YWx5pyJICR7cmVzLmRhdGEucGFnaW5hdGlvbi50b3RhbH0g5Liq57O75YiX6K++56iL77yM5b2T5YmN5Y+q5pi+56S65YmNNTDkuKpgKTtcbiAgICAgIH1cblxuICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDAgJiYgcmVzLmRhdGEpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfk4ogQVBJ6L+U5Zue55qE5a6M5pW05pWw5o2u57uT5p6EOicsIHJlcy5kYXRhKTtcblxuICAgICAgICBpZiAocmVzLmRhdGEubGlzdCAmJiBBcnJheS5pc0FycmF5KHJlcy5kYXRhLmxpc3QpKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coYPCfk4sg6I635Y+W5YiwICR7cmVzLmRhdGEubGlzdC5sZW5ndGh9IOS4quezu+WIl+ivvueoi2ApO1xuXG4gICAgICAgICAgLy8g5bCG6K++56iL5biC5Zy6QVBJ6L+U5Zue55qE5pWw5o2u6L2s5o2i5Li657uE5Lu26ZyA6KaB55qE5qC85byPXG4gICAgICAgICAgY29uc3QgZm9ybWF0dGVkU2VyaWVzID0gcmVzLmRhdGEubGlzdC5tYXAoKGl0ZW06IGFueSwgaW5kZXg6IG51bWJlcikgPT4ge1xuICAgICAgICAgICAgY29uc29sZS5sb2coYPCflI0g5aSE55CG56ysICR7aW5kZXggKyAxfSDkuKrns7vliJc6YCwge1xuICAgICAgICAgICAgICBpZDogaXRlbS5pZCxcbiAgICAgICAgICAgICAgdGl0bGU6IGl0ZW0udGl0bGUsXG4gICAgICAgICAgICAgIGNhdGVnb3J5OiBpdGVtLmNhdGVnb3J5LFxuICAgICAgICAgICAgICBjYXRlZ29yeUxhYmVsOiBpdGVtLmNhdGVnb3J5TGFiZWwsXG4gICAgICAgICAgICAgIHRhZ3M6IGl0ZW0udGFnc1xuICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgIGlkOiBpdGVtLmlkLFxuICAgICAgICAgICAgICB0aXRsZTogaXRlbS50aXRsZSxcbiAgICAgICAgICAgICAgZGVzY3JpcHRpb246IGl0ZW0uZGVzY3JpcHRpb24sXG4gICAgICAgICAgICAgIGNvdmVySW1hZ2U6IGl0ZW0uY292ZXJJbWFnZSB8fCAnJyxcbiAgICAgICAgICAgICAgY2F0ZWdvcnk6IGl0ZW0uY2F0ZWdvcnlMYWJlbCB8fCAoaXRlbS5jYXRlZ29yeSA9PT0gMCA/ICflrpjmlrknIDogJ+ekvuWMuicpLCAvLyDkvb/nlKhjYXRlZ29yeUxhYmVs5oiW6L2s5o2iY2F0ZWdvcnlcbiAgICAgICAgICAgICAgdGVhY2hlcklkczogW10sIC8vIOivvueoi+W4guWcukFQSeS4jei/lOWbnnRlYWNoZXJJZHNcbiAgICAgICAgICAgICAgdGFnSWRzOiBpdGVtLnRhZ3M/Lm1hcCgodGFnOiBhbnkpID0+IHRhZy5pZCkgfHwgW10sIC8vIOS7jnRhZ3PmlbDnu4TkuK3mj5Dlj5ZJRFxuICAgICAgICAgICAgICBjcmVhdGVkQXQ6IGl0ZW0uY3JlYXRlZEF0IHx8IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICAgICAgdXBkYXRlZEF0OiBpdGVtLnVwZGF0ZWRBdCB8fCBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgfSk7XG5cbiAgICAgICAgICBzZXRDb3Vyc2VTZXJpZXMoZm9ybWF0dGVkU2VyaWVzKTtcbiAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIOaIkOWKn+iOt+WPluezu+WIl+ivvueoi+WIl+ihqDonLCBmb3JtYXR0ZWRTZXJpZXMpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGNvbnNvbGUud2Fybign4pqg77iPIEFQSei/lOWbnuaVsOaNruS4reayoeaciWxpc3TlrZfmrrXmiJZsaXN05LiN5piv5pWw57uEOicsIHJlcy5kYXRhKTtcbiAgICAgICAgICBzZXRDb3Vyc2VTZXJpZXMoW10pO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBBUEnov5Tlm57mlbDmja7moLzlvI/lvILluLg6Jywge1xuICAgICAgICAgIGNvZGU6IHJlcy5jb2RlLFxuICAgICAgICAgIG1lc3NhZ2U6IHJlcy5tZXNzYWdlLFxuICAgICAgICAgIGRhdGE6IHJlcy5kYXRhXG4gICAgICAgIH0pO1xuICAgICAgICBzZXRDb3Vyc2VTZXJpZXMoW10pO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg6I635Y+W6K++56iL57O75YiX5aSx6LSlOicsIGVycm9yKTtcbiAgICAgIHNldENvdXJzZVNlcmllcyhbXSk7XG4gICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+iOt+WPluezu+WIl+ivvueoi+WIl+ihqOWksei0pe+8jOivt+mHjeivlScpO1xuICAgIH1cbiAgfTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGZldGNoQ291cnNlTGlzdCgpO1xuXG4gICAgZmV0Y2hDb3Vyc2VUYWdzKCk7XG4gICAgZmV0Y2hDb3Vyc2VTZXJpZXMoKTtcbiAgfSwgW10pO1xuXG4gIHJldHVybiAoXG4gICAgPD5cbiAgICAgIDxDYXJkXG4gICAgICAgIHRpdGxlPVwi6K++56iL566h55CGXCJcbiAgICAgICAgZXh0cmE9ezxCdXR0b24gdHlwZT1cInByaW1hcnlcIiBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgZmV0Y2hDb3Vyc2VMaXN0KCk7XG4gICAgICAgICAgc2V0SXNDb3Vyc2VNb2RhbFZpc2libGUodHJ1ZSk7XG4gICAgICAgIH19Puafpeeci+WFqOmDqDwvQnV0dG9uPn1cbiAgICAgICAgY2xhc3NOYW1lPVwic2hhZG93LXNtXCJcbiAgICAgID5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICA8QnV0dG9uIGJsb2NrIG9uQ2xpY2s9eygpID0+IHNldElzQWRkQ291cnNlTW9kYWxWaXNpYmxlKHRydWUpfT5cbiAgICAgICAgICAgIOa3u+WKoOivvueoi1xuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDxCdXR0b24gYmxvY2sgb25DbGljaz17KCkgPT4gc2V0SXNBZGRTZXJpZXNNb2RhbFZpc2libGUodHJ1ZSl9PlxuICAgICAgICAgICAg5re75Yqg57O75YiX6K++56iLXG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPEJ1dHRvbiBibG9jayBvbkNsaWNrPXsoKSA9PiBzZXRJc0FkZFRhZ01vZGFsVmlzaWJsZSh0cnVlKX0gdHlwZT1cImRhc2hlZFwiPlxuICAgICAgICAgICAg5re75Yqg6K++56iL5qCH562+XG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPEJ1dHRvbiBibG9jayBvbkNsaWNrPXtvcGVuUHVibGlzaENvdXJzZU1vZGFsfSBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICd3aGl0ZScsIGJvcmRlckNvbG9yOiAnI2Q5ZDlkOScsIGNvbG9yOiAnIzAwMDAwMGQ5JyB9fT5cbiAgICAgICAgICAgIOWPkeW4g+ivvueoi1xuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDxCdXR0b24gYmxvY2sgb25DbGljaz17KCkgPT4gc2V0SXNQdWJsaXNoU2VyaWVzTW9kYWxWaXNpYmxlKHRydWUpfSBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICd3aGl0ZScsIGJvcmRlckNvbG9yOiAnI2Q5ZDlkOScsIGNvbG9yOiAnIzAwMDAwMGQ5JyB9fT5cbiAgICAgICAgICAgIOWPkeW4g+ezu+WIl+ivvueoi1xuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvQ2FyZD5cblxuICAgICAgey8qIOivvueoi+euoeeQhuS4u+aooeaAgeahhiAqL31cbiAgICAgIDxNb2RhbFxuICAgICAgICB0aXRsZT1cIuivvueoi+euoeeQhlwiXG4gICAgICAgIG9wZW49e2lzQ291cnNlTW9kYWxWaXNpYmxlfVxuICAgICAgICBvbkNhbmNlbD17KCkgPT4gc2V0SXNDb3Vyc2VNb2RhbFZpc2libGUoZmFsc2UpfVxuICAgICAgICBmb290ZXI9e251bGx9XG4gICAgICAgIHdpZHRoPXsxMDAwfVxuICAgICAgPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBtYi0zXCI+XG4gICAgICAgICAgICA8U2VhcmNoXG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi5pCc57Si57O75YiX6K++56iL5ZCN56ewXCJcbiAgICAgICAgICAgICAgYWxsb3dDbGVhclxuICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogMzAwIH19XG4gICAgICAgICAgICAgIG9uU2VhcmNoPXtzZXRTZWFyY2hLZXl3b3JkfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlYXJjaEtleXdvcmQoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMlwiPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdHlwZT1cInByaW1hcnlcIlxuICAgICAgICAgICAgICAgIGljb249ezxQbHVzT3V0bGluZWQgLz59XG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNBZGRDb3Vyc2VNb2RhbFZpc2libGUodHJ1ZSl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDmt7vliqDor77nqItcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICB0eXBlPVwiZGVmYXVsdFwiXG4gICAgICAgICAgICAgICAgaWNvbj17PFBsdXNPdXRsaW5lZCAvPn1cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc0FkZFNlcmllc01vZGFsVmlzaWJsZSh0cnVlKX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIOa3u+WKoOezu+WIl+ivvueoi1xuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIOe7n+iuoeS/oeaBr+WSjOW/q+aNt+aTjeS9nCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBiZy1ncmF5LTUwIHAtMyByb3VuZGVkXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTQgdGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgIDxzcGFuPuezu+WIl+ivvueoi+aAu+aVsDogPHN0cm9uZyBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwXCI+e3Nlcmllc0xpc3QubGVuZ3RofTwvc3Ryb25nPjwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4+5bey5bGV5byA57O75YiXOiA8c3Ryb25nIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNjAwXCI+e2V4cGFuZGVkU2VyaWVzLnNpemV9PC9zdHJvbmc+PC9zcGFuPlxuICAgICAgICAgICAgICA8c3Bhbj7lt7LliqDovb3lrZDor77nqIs6IDxzdHJvbmcgY2xhc3NOYW1lPVwidGV4dC1vcmFuZ2UtNjAwXCI+XG4gICAgICAgICAgICAgICAge0FycmF5LmZyb20oc2VyaWVzQ291cnNlc01hcC52YWx1ZXMoKSkucmVkdWNlKCh0b3RhbCwgY291cnNlcykgPT4gdG90YWwgKyBjb3Vyc2VzLmxlbmd0aCwgMCl9XG4gICAgICAgICAgICAgIDwvc3Ryb25nPjwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTJcIj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHNpemU9XCJzbWFsbFwiXG4gICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2V4cGFuZEFsbFNlcmllc31cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17c2VyaWVzTG9hZGluZ31cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwIGhvdmVyOnRleHQtYmx1ZS04MDBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg5bGV5byA5omA5pyJXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgc2l6ZT1cInNtYWxsXCJcbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17Y29sbGFwc2VBbGxTZXJpZXN9XG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3Nlcmllc0xvYWRpbmd9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktODAwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIOaUtui1t+aJgOaciVxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8VGFibGVcbiAgICAgICAgICBjb2x1bW5zPXtjb2x1bW5zfVxuICAgICAgICAgIGRhdGFTb3VyY2U9e3ByZXBhcmVUYWJsZURhdGEoKX1cbiAgICAgICAgICByb3dLZXk9XCJrZXlcIlxuICAgICAgICAgIGxvYWRpbmc9e3Nlcmllc0xvYWRpbmd9XG4gICAgICAgICAgcGFnaW5hdGlvbj17e1xuICAgICAgICAgICAgcGFnZVNpemU6IDIwLFxuICAgICAgICAgICAgc2hvd1NpemVDaGFuZ2VyOiBmYWxzZSxcbiAgICAgICAgICAgIHNob3dUb3RhbDogKHRvdGFsKSA9PiBg5YWxICR7dG90YWx9IOadoeiusOW9lWAsXG4gICAgICAgICAgfX1cbiAgICAgICAgICBzaXplPVwic21hbGxcIlxuICAgICAgICAvPlxuICAgICAgPC9Nb2RhbD5cblxuICAgICAgey8qIOa3u+WKoOivvueoi+aooeaAgeahhiAqL31cbiAgICAgIDxNb2RhbFxuICAgICAgICB0aXRsZT1cIua3u+WKoOivvueoi1wiXG4gICAgICAgIG9wZW49e2lzQWRkQ291cnNlTW9kYWxWaXNpYmxlfVxuICAgICAgICBvbkNhbmNlbD17KCkgPT4ge1xuICAgICAgICAgIHNldElzQWRkQ291cnNlTW9kYWxWaXNpYmxlKGZhbHNlKTtcbiAgICAgICAgICBhZGRDb3Vyc2VGb3JtLnJlc2V0RmllbGRzKCk7XG4gICAgICAgICAgc2V0Q291cnNlQ292ZXJJbWFnZVVybCgnJyk7XG4gICAgICAgICAgc2V0QWRkaXRpb25hbEZpbGVzKFtdKTtcbiAgICAgICAgICBzZXRDb3Vyc2VWaWRlb1VybCgnJyk7XG4gICAgICAgICAgc2V0Q291cnNlVmlkZW9OYW1lKCcnKTtcbiAgICAgICAgICBzZXRDb3Vyc2VEb2N1bWVudFVybCgnJyk7XG4gICAgICAgICAgc2V0Q291cnNlRG9jdW1lbnROYW1lKCcnKTtcbiAgICAgICAgICBzZXRWaWRlb0R1cmF0aW9uKDApO1xuICAgICAgICAgIHNldFRlYWNoaW5nSW5mb0xpc3QoW3sgdGl0bGU6ICcnLCBjb250ZW50OiBbJyddIH1dKTtcbiAgICAgICAgfX1cbiAgICAgICAgb25Paz17KCkgPT4gYWRkQ291cnNlRm9ybS5zdWJtaXQoKX1cbiAgICAgICAgb2tUZXh0PVwi56Gu5a6aXCJcbiAgICAgICAgY2FuY2VsVGV4dD1cIuWPlua2iFwiXG4gICAgICA+XG4gICAgICAgIDxGb3JtXG4gICAgICAgICAgZm9ybT17YWRkQ291cnNlRm9ybX1cbiAgICAgICAgICBsYXlvdXQ9XCJ2ZXJ0aWNhbFwiXG4gICAgICAgICAgb25GaW5pc2g9e2hhbmRsZUFkZENvdXJzZX1cbiAgICAgICAgPlxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJzZXJpZXNJZFwiXG4gICAgICAgICAgICBsYWJlbD1cIuaJgOWxnuezu+WIl+ivvueoi1wiXG4gICAgICAgICAgICBydWxlcz17W3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nmiYDlsZ7ns7vliJfor77nqIsnIH1dfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTZWxlY3RcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fpgInmi6nns7vliJfor77nqItcIlxuICAgICAgICAgICAgICBzaG93U2VhcmNoXG4gICAgICAgICAgICAgIG9wdGlvbkZpbHRlclByb3A9XCJjaGlsZHJlblwiXG4gICAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiAnMTAwJScgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2NvdXJzZVNlcmllcy5tYXAoc2VyaWVzID0+IChcbiAgICAgICAgICAgICAgICA8T3B0aW9uIGtleT17c2VyaWVzLmlkfSB2YWx1ZT17c2VyaWVzLmlkfSB0aXRsZT17YCR7c2VyaWVzLnRpdGxlfSAtICR7c2VyaWVzLmRlc2NyaXB0aW9ufWB9PlxuICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICBvdmVyZmxvdzogJ2hpZGRlbicsXG4gICAgICAgICAgICAgICAgICAgIHRleHRPdmVyZmxvdzogJ2VsbGlwc2lzJyxcbiAgICAgICAgICAgICAgICAgICAgd2hpdGVTcGFjZTogJ25vd3JhcCcsXG4gICAgICAgICAgICAgICAgICAgIG1heFdpZHRoOiAnMTAwJSdcbiAgICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT17eyBmb250V2VpZ2h0OiA1MDAgfX0+e3Nlcmllcy50aXRsZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPXt7IGZvbnRTaXplOiAnMTJweCcsIGNvbG9yOiAnIzY2NicsIG1hcmdpbkxlZnQ6ICc4cHgnIH19PlxuICAgICAgICAgICAgICAgICAgICAgICh7c2VyaWVzLmNhdGVnb3J5fSlcbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9PcHRpb24+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9TZWxlY3Q+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwidGl0bGVcIlxuICAgICAgICAgICAgbGFiZWw9XCLor77nqIvlkI3np7BcIlxuICAgICAgICAgICAgcnVsZXM9e1t7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl6K++56iL5ZCN56ewJyB9XX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8SW5wdXQgcGxhY2Vob2xkZXI9XCLor7fovpPlhaXor77nqIvlkI3np7BcIiAvPlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbmFtZT1cImRlc2NyaXB0aW9uXCJcbiAgICAgICAgICAgIGxhYmVsPVwi6K++56iL5o+P6L+wXCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeivvueoi+aPj+i/sCcgfV19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPElucHV0LlRleHRBcmVhXG4gICAgICAgICAgICAgIHJvd3M9ezR9XG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36K+m57uG5o+P6L+w6K++56iL5YaF5a6544CB55uu5qCH5ZKM54m56ImyLi4uXCJcbiAgICAgICAgICAgICAgc2hvd0NvdW50XG4gICAgICAgICAgICAgIG1heExlbmd0aD17NTAwfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIGxhYmVsPVwi6K++56iL5bCB6Z2iXCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+S4iuS8oOivvueoi+WwgemdoicgfV19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFVwbG9hZC5EcmFnZ2VyXG4gICAgICAgICAgICAgIG5hbWU9XCJjb3Vyc2VDb3ZlclwiXG4gICAgICAgICAgICAgIGN1c3RvbVJlcXVlc3Q9e2hhbmRsZUNvdXJzZUNvdmVyVXBsb2FkfVxuICAgICAgICAgICAgICBvblJlbW92ZT17aGFuZGxlQ291cnNlQ292ZXJSZW1vdmV9XG4gICAgICAgICAgICAgIGFjY2VwdD1cImltYWdlLypcIlxuICAgICAgICAgICAgICBtYXhDb3VudD17MX1cbiAgICAgICAgICAgICAgbGlzdFR5cGU9XCJwaWN0dXJlXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2NvdXJzZUNvdmVySW1hZ2VVcmwgPyAoXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxpbWcgc3JjPXtjb3Vyc2VDb3ZlckltYWdlVXJsfSBhbHQ9XCLor77nqIvlsIHpnaLpooTop4hcIiBzdHlsZT17eyB3aWR0aDogJzEwMCUnLCBtYXhIZWlnaHQ6ICcyMDBweCcsIG9iamVjdEZpdDogJ2NvdmVyJyB9fSAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJhbnQtdXBsb2FkLWRyYWctaWNvblwiPlxuICAgICAgICAgICAgICAgICAgICA8SW5ib3hPdXRsaW5lZCAvPlxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiYW50LXVwbG9hZC10ZXh0XCI+54K55Ye75oiW5ouW5ou95paH5Lu25Yiw5q2k5Yy65Z+f5LiK5LygPC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiYW50LXVwbG9hZC1oaW50XCI+XG4gICAgICAgICAgICAgICAgICAgIOaUr+aMgeWNleS4quaWh+S7tuS4iuS8oO+8jOW7uuiuruS4iuS8oGpwZ+OAgXBuZ+agvOW8j+WbvueJh++8jOWkp+Wwj+S4jei2hei/hzJNQlxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9VcGxvYWQuRHJhZ2dlcj5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJvcmRlckluZGV4XCJcbiAgICAgICAgICAgIGxhYmVsPVwi6K++56iL5bqP5Y+3XCJcbiAgICAgICAgICAgIHJ1bGVzPXtbXG4gICAgICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXor77nqIvluo/lj7cnIH0sXG4gICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICB0eXBlOiAnbnVtYmVyJyxcbiAgICAgICAgICAgICAgICBtaW46IDAsXG4gICAgICAgICAgICAgICAgbWVzc2FnZTogJ+ivvueoi+W6j+WPt+W/hemhu+Wkp+S6juetieS6jjAnLFxuICAgICAgICAgICAgICAgIHRyYW5zZm9ybTogKHZhbHVlKSA9PiBOdW1iZXIodmFsdWUpXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIF19XG4gICAgICAgICAgICB0b29sdGlwPVwi5Zyo57O75YiX6K++56iL5Lit55qE5o6S5bqP5L2N572u77yM5pWw5a2X6LaK5bCP5o6S5bqP6LaK6Z2g5YmN77yM5LuOMOW8gOWni1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPElucHV0IHR5cGU9XCJudW1iZXJcIiBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpeivvueoi+WcqOezu+WIl+S4reeahOW6j+WPt++8iOS7jjDlvIDlp4vvvIlcIiBtaW49ezB9IC8+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cblxuXG4gICAgICAgICAgey8qIOinhumikeS4iuS8oCAqL31cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBsYWJlbD1cIuivvueoi+inhumikVwiXG4gICAgICAgICAgICB0b29sdGlwPVwi5LiK5Lyg6K++56iL6KeG6aKR5paH5Lu277yM57O757uf5bCG6Ieq5Yqo6K+G5Yir5pe26ZW/562J5L+h5oGvXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8VXBsb2FkLkRyYWdnZXJcbiAgICAgICAgICAgICAgbmFtZT1cImNvdXJzZVZpZGVvXCJcbiAgICAgICAgICAgICAgY3VzdG9tUmVxdWVzdD17aGFuZGxlVmlkZW9VcGxvYWR9XG4gICAgICAgICAgICAgIG9uUmVtb3ZlPXtoYW5kbGVWaWRlb1JlbW92ZX1cbiAgICAgICAgICAgICAgYWNjZXB0PVwidmlkZW8vKlwiXG4gICAgICAgICAgICAgIG1heENvdW50PXsxfVxuICAgICAgICAgICAgICBsaXN0VHlwZT1cInBpY3R1cmVcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7Y291cnNlVmlkZW9VcmwgPyAoXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDx2aWRlb1xuICAgICAgICAgICAgICAgICAgICBzcmM9e2NvdXJzZVZpZGVvVXJsfVxuICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogJzEwMCUnLCBtYXhIZWlnaHQ6ICcyMDBweCcgfX1cbiAgICAgICAgICAgICAgICAgICAgY29udHJvbHNcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8cCBzdHlsZT17eyBtYXJnaW5Ub3A6IDgsIGNvbG9yOiAnIzY2NicgfX0+XG4gICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VWaWRlb05hbWUgfHwgJ+ivvueoi+inhumikSd9XG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImFudC11cGxvYWQtZHJhZy1pY29uXCI+XG4gICAgICAgICAgICAgICAgICAgIDxJbmJveE91dGxpbmVkIC8+XG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJhbnQtdXBsb2FkLXRleHRcIj7ngrnlh7vmiJbmi5bmi73op4bpopHmlofku7bliLDmraTljLrln5/kuIrkvKA8L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJhbnQtdXBsb2FkLWhpbnRcIj5cbiAgICAgICAgICAgICAgICAgICAg5pSv5oyBTVA044CBQVZJ44CBTU9W562J5qC85byP77yM5aSn5bCP5LiN6LaF6L+HMTAwTUJcbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvVXBsb2FkLkRyYWdnZXI+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICB7Lyog5paH5qGj5LiK5LygICovfVxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIGxhYmVsPVwi6K++56iL5paH5qGjXCJcbiAgICAgICAgICAgIHRvb2x0aXA9XCLkuIrkvKDor77nqIvnm7jlhbPmlofmoaPvvIzlpoJQUFTjgIFQREbnrYlcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxVcGxvYWQuRHJhZ2dlclxuICAgICAgICAgICAgICBuYW1lPVwiY291cnNlRG9jdW1lbnRcIlxuICAgICAgICAgICAgICBjdXN0b21SZXF1ZXN0PXtoYW5kbGVEb2N1bWVudFVwbG9hZH1cbiAgICAgICAgICAgICAgb25SZW1vdmU9e2hhbmRsZURvY3VtZW50UmVtb3ZlfVxuICAgICAgICAgICAgICBhY2NlcHQ9XCIucGRmLC5kb2MsLmRvY3gsLnBwdCwucHB0eFwiXG4gICAgICAgICAgICAgIG1heENvdW50PXsxfVxuICAgICAgICAgICAgICBsaXN0VHlwZT1cInBpY3R1cmVcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7Y291cnNlRG9jdW1lbnRVcmwgPyAoXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgcGFkZGluZzogJzIwcHgnLCB0ZXh0QWxpZ246ICdjZW50ZXInIH19PlxuICAgICAgICAgICAgICAgICAgICA8SW5ib3hPdXRsaW5lZCBzdHlsZT17eyBmb250U2l6ZTogJzQ4cHgnLCBjb2xvcjogJyMxODkwZmYnIH19IC8+XG4gICAgICAgICAgICAgICAgICAgIDxwIHN0eWxlPXt7IG1hcmdpblRvcDogOCwgY29sb3I6ICcjNjY2JyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICB7Y291cnNlRG9jdW1lbnROYW1lIHx8ICfor77nqIvmlofmoaMnfVxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiYW50LXVwbG9hZC1kcmFnLWljb25cIj5cbiAgICAgICAgICAgICAgICAgICAgPEluYm94T3V0bGluZWQgLz5cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImFudC11cGxvYWQtdGV4dFwiPueCueWHu+aIluaLluaLveaWh+aho+aWh+S7tuWIsOatpOWMuuWfn+S4iuS8oDwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImFudC11cGxvYWQtaGludFwiPlxuICAgICAgICAgICAgICAgICAgICDmlK/mjIFQREbjgIFXb3Jk44CBUFBU5qC85byP77yM5aSn5bCP5LiN6LaF6L+HNTBNQlxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9VcGxvYWQuRHJhZ2dlcj5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuXG5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwidGVhY2hpbmdPYmplY3RpdmVzXCJcbiAgICAgICAgICAgIGxhYmVsPVwi5pWZ5a2m55uu5qCHXCJcbiAgICAgICAgICAgIHRvb2x0aXA9XCLlrablkZjlrozmiJDmnKzor77nqIvlkI7lupTor6Xovr7liLDnmoTlrabkuaDnm67moIdcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTZWxlY3RcbiAgICAgICAgICAgICAgbW9kZT1cInRhZ3NcIlxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIueQhuino05vZGUuanPnmoTln7rmnKzmpoLlv7XlkoznibnngrnvvIzmjozmj6FOb2RlLmpz55qE5a6J6KOF5ZKM546v5aKD6YWN572uXCJcbiAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6ICcxMDAlJyB9fVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIGxhYmVsPVwi6ZmE5Lu26LWE5rqQXCJcbiAgICAgICAgICAgIHRvb2x0aXA9XCLkuIrkvKDor77nqIvnm7jlhbPnmoTpmYTku7botYTmupDvvIzlpoJQUFTjgIHmlofmoaPjgIHku6PnoIHnrYlcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxVcGxvYWRcbiAgICAgICAgICAgICAgbmFtZT1cImFkZGl0aW9uYWxSZXNvdXJjZXNcIlxuICAgICAgICAgICAgICBjdXN0b21SZXF1ZXN0PXtoYW5kbGVBZGRpdGlvbmFsUmVzb3VyY2VVcGxvYWR9XG4gICAgICAgICAgICAgIG9uUmVtb3ZlPXtoYW5kbGVBZGRpdGlvbmFsUmVzb3VyY2VSZW1vdmV9XG4gICAgICAgICAgICAgIG11bHRpcGxlXG4gICAgICAgICAgICAgIGFjY2VwdD1cIi5wZGYsLmRvYywuZG9jeCwucHB0LC5wcHR4LC54bHMsLnhsc3gsLnppcCwucmFyLC50eHRcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8QnV0dG9uIGljb249ezxVcGxvYWRPdXRsaW5lZCAvPn0+5LiK5Lyg6ZmE5Lu26LWE5rqQPC9CdXR0b24+XG4gICAgICAgICAgICA8L1VwbG9hZD5cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZm9udFNpemU6ICcxMnB4JywgY29sb3I6ICcjNjY2JywgbWFyZ2luVG9wOiA0IH19PlxuICAgICAgICAgICAgICDmlK/mjIHkuIrkvKBQREbjgIFPZmZpY2XmlofmoaPjgIHljovnvKnljIXnrYnmoLzlvI/mlofku7bvvIzljZXkuKrmlofku7bkuI3otoXov4cxME1CXG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cbiAgICAgICAgPC9Gb3JtPlxuICAgICAgPC9Nb2RhbD5cblxuICAgICAgey8qIOe8lui+keivvueoi+aooeaAgeahhiAqL31cbiAgICAgIDxNb2RhbFxuICAgICAgICB0aXRsZT1cIue8lui+keivvueoi1wiXG4gICAgICAgIG9wZW49e2lzRWRpdENvdXJzZU1vZGFsVmlzaWJsZX1cbiAgICAgICAgb25DYW5jZWw9eygpID0+IHtcbiAgICAgICAgICBzZXRJc0VkaXRDb3Vyc2VNb2RhbFZpc2libGUoZmFsc2UpO1xuICAgICAgICAgIHNldEVkaXRpbmdDb3Vyc2UobnVsbCk7XG4gICAgICAgICAgZWRpdENvdXJzZUZvcm0ucmVzZXRGaWVsZHMoKTtcbiAgICAgICAgfX1cbiAgICAgICAgb25Paz17KCkgPT4gZWRpdENvdXJzZUZvcm0uc3VibWl0KCl9XG4gICAgICAgIG9rVGV4dD1cIuehruWumlwiXG4gICAgICAgIGNhbmNlbFRleHQ9XCLlj5bmtohcIlxuICAgICAgPlxuICAgICAgICA8Rm9ybVxuICAgICAgICAgIGZvcm09e2VkaXRDb3Vyc2VGb3JtfVxuICAgICAgICAgIGxheW91dD1cInZlcnRpY2FsXCJcbiAgICAgICAgICBvbkZpbmlzaD17aGFuZGxlRWRpdENvdXJzZX1cbiAgICAgICAgPlxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJuYW1lXCJcbiAgICAgICAgICAgIGxhYmVsPVwi6K++56iL5ZCN56ewXCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeivvueoi+WQjeensCcgfV19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPElucHV0IHBsYWNlaG9sZGVyPVwi6K+36L6T5YWl6K++56iL5ZCN56ewXCIgLz5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJkZXNjcmlwdGlvblwiXG4gICAgICAgICAgICBsYWJlbD1cIuivvueoi+aPj+i/sFwiXG4gICAgICAgICAgICBydWxlcz17W3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXor77nqIvmj4/ov7AnIH1dfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxJbnB1dC5UZXh0QXJlYSByb3dzPXszfSBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpeivvueoi+aPj+i/sFwiIC8+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwiY2F0ZWdvcnlcIlxuICAgICAgICAgICAgbGFiZWw9XCLor77nqIvliIbnsbtcIlxuICAgICAgICAgICAgcnVsZXM9e1t7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup6K++56iL5YiG57G7JyB9XX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8U2VsZWN0IHBsYWNlaG9sZGVyPVwi6K+36YCJ5oup6K++56iL5YiG57G7XCI+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9XCLnvJbnqIvln7rnoYBcIj7nvJbnqIvln7rnoYA8L09wdGlvbj5cbiAgICAgICAgICAgICAgPE9wdGlvbiB2YWx1ZT1cIue8lueoi+i/m+mYtlwiPue8lueoi+i/m+mYtjwvT3B0aW9uPlxuICAgICAgICAgICAgICA8T3B0aW9uIHZhbHVlPVwi566X5rOV5oCd57u0XCI+566X5rOV5oCd57u0PC9PcHRpb24+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9XCLpobnnm67lrp7miJhcIj7pobnnm67lrp7miJg8L09wdGlvbj5cbiAgICAgICAgICAgIDwvU2VsZWN0PlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbmFtZT1cInN0YXR1c1wiXG4gICAgICAgICAgICBsYWJlbD1cIuivvueoi+eKtuaAgVwiXG4gICAgICAgICAgICBydWxlcz17W3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nor77nqIvnirbmgIEnIH1dfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTZWxlY3Q+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9XCJhY3RpdmVcIj7lkK/nlKg8L09wdGlvbj5cbiAgICAgICAgICAgICAgPE9wdGlvbiB2YWx1ZT1cImluYWN0aXZlXCI+56aB55SoPC9PcHRpb24+XG4gICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cbiAgICAgICAgPC9Gb3JtPlxuICAgICAgPC9Nb2RhbD5cblxuICAgICAgey8qIOa3u+WKoOezu+WIl+ivvueoi+aooeaAgeahhiAqL31cbiAgICAgIDxNb2RhbFxuICAgICAgICB0aXRsZT1cIuWIm+W7uuezu+WIl+ivvueoi1wiXG4gICAgICAgIG9wZW49e2lzQWRkU2VyaWVzTW9kYWxWaXNpYmxlfVxuICAgICAgICBvbkNhbmNlbD17KCkgPT4ge1xuICAgICAgICAgIHNldElzQWRkU2VyaWVzTW9kYWxWaXNpYmxlKGZhbHNlKTtcbiAgICAgICAgICBhZGRTZXJpZXNGb3JtLnJlc2V0RmllbGRzKCk7XG4gICAgICAgICAgc2V0Q292ZXJJbWFnZVVybCgnJyk7XG4gICAgICAgIH19XG4gICAgICAgIG9uT2s9eygpID0+IGFkZFNlcmllc0Zvcm0uc3VibWl0KCl9XG4gICAgICAgIG9rVGV4dD1cIuWIm+W7uuezu+WIl+ivvueoi1wiXG4gICAgICAgIGNhbmNlbFRleHQ9XCLlj5bmtohcIlxuICAgICAgICB3aWR0aD17ODAwfVxuICAgICAgPlxuICAgICAgICA8Rm9ybVxuICAgICAgICAgIGZvcm09e2FkZFNlcmllc0Zvcm19XG4gICAgICAgICAgbGF5b3V0PVwidmVydGljYWxcIlxuICAgICAgICAgIG9uRmluaXNoPXtoYW5kbGVBZGRTZXJpZXN9XG4gICAgICAgID5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwidGl0bGVcIlxuICAgICAgICAgICAgbGFiZWw9XCLns7vliJfor77nqIvlkI3np7BcIlxuICAgICAgICAgICAgcnVsZXM9e1t7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl57O75YiX6K++56iL5ZCN56ewJyB9XX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8SW5wdXQgcGxhY2Vob2xkZXI9XCLkvovlpoLvvJpSZWFjdOWFqOagiOW8gOWPkeWunuaImFwiIC8+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwiZGVzY3JpcHRpb25cIlxuICAgICAgICAgICAgbGFiZWw9XCLor77nqIvku4vnu41cIlxuICAgICAgICAgICAgcnVsZXM9e1t7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl6K++56iL5LuL57uNJyB9XX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8SW5wdXQuVGV4dEFyZWFcbiAgICAgICAgICAgICAgcm93cz17NH1cbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7for6bnu4bmj4/ov7Dns7vliJfor77nqIvnmoTlhoXlrrnjgIHnm67moIflkoznibnoibIuLi5cIlxuICAgICAgICAgICAgICBzaG93Q291bnRcbiAgICAgICAgICAgICAgbWF4TGVuZ3RoPXs1MDB9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbGFiZWw9XCLlsIHpnaLlm77niYdcIlxuICAgICAgICAgICAgcnVsZXM9e1t7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+35LiK5Lyg5bCB6Z2i5Zu+54mHJyB9XX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8VXBsb2FkLkRyYWdnZXJcbiAgICAgICAgICAgICAgbmFtZT1cImNvdmVySW1hZ2VcIlxuICAgICAgICAgICAgICBjdXN0b21SZXF1ZXN0PXtoYW5kbGVJbWFnZVVwbG9hZH1cbiAgICAgICAgICAgICAgb25SZW1vdmU9e2hhbmRsZUltYWdlUmVtb3ZlfVxuICAgICAgICAgICAgICBhY2NlcHQ9XCJpbWFnZS8qXCJcbiAgICAgICAgICAgICAgbWF4Q291bnQ9ezF9XG4gICAgICAgICAgICAgIGxpc3RUeXBlPVwicGljdHVyZVwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtjb3ZlckltYWdlVXJsID8gKFxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aW1nIHNyYz17Y292ZXJJbWFnZVVybH0gYWx0PVwi5bCB6Z2i6aKE6KeIXCIgc3R5bGU9e3sgd2lkdGg6ICcxMDAlJywgbWF4SGVpZ2h0OiAnMjAwcHgnLCBvYmplY3RGaXQ6ICdjb3ZlcicgfX0gLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiYW50LXVwbG9hZC1kcmFnLWljb25cIj5cbiAgICAgICAgICAgICAgICAgICAgPEluYm94T3V0bGluZWQgLz5cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImFudC11cGxvYWQtdGV4dFwiPueCueWHu+aIluaLluaLveaWh+S7tuWIsOatpOWMuuWfn+S4iuS8oDwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImFudC11cGxvYWQtaGludFwiPlxuICAgICAgICAgICAgICAgICAgICDmlK/mjIHljZXkuKrmlofku7bkuIrkvKDvvIzlu7rorq7kuIrkvKBqcGfjgIFwbmfmoLzlvI/lm77niYfvvIzlpKflsI/kuI3otoXov4cyTUJcbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvVXBsb2FkLkRyYWdnZXI+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cblxuXG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbmFtZT1cImNhdGVnb3J5XCJcbiAgICAgICAgICAgIGxhYmVsPVwi5piv5ZCm5Li65a6Y5pa557O75YiX6K++56iLXCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqeaYr+WQpuS4uuWumOaWueezu+WIl+ivvueoiycgfV19XG4gICAgICAgICAgICBpbml0aWFsVmFsdWU9ezB9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFNlbGVjdCBwbGFjZWhvbGRlcj1cIuivt+mAieaLqVwiPlxuICAgICAgICAgICAgICA8T3B0aW9uIHZhbHVlPXsxfT7mmK/vvIjlrpjmlrnvvIk8L09wdGlvbj5cbiAgICAgICAgICAgICAgPE9wdGlvbiB2YWx1ZT17MH0+5ZCm77yI56S+5Yy677yJPC9PcHRpb24+XG4gICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJwcm9qZWN0TWVtYmVyc1wiXG4gICAgICAgICAgICBsYWJlbD1cIuivvueoi+aIkOWRmFwiXG4gICAgICAgICAgICBydWxlcz17W3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXor77nqIvmiJDlkZgnIH1dfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpeivvueoi+aIkOWRmO+8jOWmgu+8mueOi+iAgeW4iOOAgeadjuWKqeaVmeOAgeW8oOWQjOWtplwiXG4gICAgICAgICAgICAgIHNob3dDb3VudFxuICAgICAgICAgICAgICBtYXhMZW5ndGg9ezIwMH1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwidGFnSWRzXCJcbiAgICAgICAgICAgIGxhYmVsPVwi5qCH562+6YCJ5oupXCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqeagh+etvicgfV19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFNlbGVjdFxuICAgICAgICAgICAgICBtb2RlPVwibXVsdGlwbGVcIlxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuivt+mAieaLqeebuOWFs+agh+etvlwiXG4gICAgICAgICAgICAgIG9wdGlvbkxhYmVsUHJvcD1cImxhYmVsXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2NvdXJzZVRhZ3MubWFwKHRhZyA9PiAoXG4gICAgICAgICAgICAgICAgPE9wdGlvbiBrZXk9e3RhZy5pZH0gdmFsdWU9e3RhZy5pZH0gbGFiZWw9e3RhZy5uYW1lfT5cbiAgICAgICAgICAgICAgICAgIDxUYWcgY29sb3I9e3RhZy5jb2xvcn0+e3RhZy5uYW1lfTwvVGFnPlxuICAgICAgICAgICAgICAgIDwvT3B0aW9uPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvU2VsZWN0PlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuICAgICAgICA8L0Zvcm0+XG4gICAgICA8L01vZGFsPlxuXG4gICAgICB7Lyog5re75Yqg6K++56iL5qCH562+5qih5oCB5qGGICovfVxuICAgICAgPE1vZGFsXG4gICAgICAgIHRpdGxlPVwi5Yib5bu66K++56iL5qCH562+XCJcbiAgICAgICAgb3Blbj17aXNBZGRUYWdNb2RhbFZpc2libGV9XG4gICAgICAgIG9uQ2FuY2VsPXsoKSA9PiB7XG4gICAgICAgICAgc2V0SXNBZGRUYWdNb2RhbFZpc2libGUoZmFsc2UpO1xuICAgICAgICAgIGFkZFRhZ0Zvcm0ucmVzZXRGaWVsZHMoKTtcbiAgICAgICAgfX1cbiAgICAgICAgb25Paz17KCkgPT4gYWRkVGFnRm9ybS5zdWJtaXQoKX1cbiAgICAgICAgb2tUZXh0PVwi5Yib5bu65qCH562+XCJcbiAgICAgICAgY2FuY2VsVGV4dD1cIuWPlua2iFwiXG4gICAgICAgIHdpZHRoPXs2MDB9XG4gICAgICA+XG4gICAgICAgIDxGb3JtXG4gICAgICAgICAgZm9ybT17YWRkVGFnRm9ybX1cbiAgICAgICAgICBsYXlvdXQ9XCJ2ZXJ0aWNhbFwiXG4gICAgICAgICAgb25GaW5pc2g9e2hhbmRsZUFkZFRhZ31cbiAgICAgICAgPlxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJuYW1lXCJcbiAgICAgICAgICAgIGxhYmVsPVwi5qCH562+5ZCN56ewXCJcbiAgICAgICAgICAgIHJ1bGVzPXtbXG4gICAgICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXmoIfnrb7lkI3np7AnIH0sXG4gICAgICAgICAgICAgIHsgbWF4OiAyMCwgbWVzc2FnZTogJ+agh+etvuWQjeensOS4jeiDvei2hei/hzIw5Liq5a2X56ymJyB9XG4gICAgICAgICAgICBdfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxJbnB1dCBwbGFjZWhvbGRlcj1cIuS+i+Wmgu+8mumrmOe6p+OAgee8lueoi+OAgeWunuaImFwiIC8+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwiY29sb3JcIlxuICAgICAgICAgICAgbGFiZWw9XCLmoIfnrb7popzoibJcIlxuICAgICAgICAgICAgcnVsZXM9e1t7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup5qCH562+6aKc6ImyJyB9XX1cbiAgICAgICAgICAgIGluaXRpYWxWYWx1ZT1cIiMwMDdiZmZcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTZWxlY3QgcGxhY2Vob2xkZXI9XCLor7fpgInmi6nmoIfnrb7popzoibJcIj5cbiAgICAgICAgICAgICAgPE9wdGlvbiB2YWx1ZT1cIiMwMDdiZmZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNCBoLTQgcm91bmRlZFwiIHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogJyMwMDdiZmYnIH19PjwvZGl2PlxuICAgICAgICAgICAgICAgICAg6JOd6ImyICgjMDA3YmZmKVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L09wdGlvbj5cbiAgICAgICAgICAgICAgPE9wdGlvbiB2YWx1ZT1cIiMyOGE3NDVcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNCBoLTQgcm91bmRlZFwiIHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogJyMyOGE3NDUnIH19PjwvZGl2PlxuICAgICAgICAgICAgICAgICAg57u/6ImyICgjMjhhNzQ1KVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L09wdGlvbj5cbiAgICAgICAgICAgICAgPE9wdGlvbiB2YWx1ZT1cIiNkYzM1NDVcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNCBoLTQgcm91bmRlZFwiIHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogJyNkYzM1NDUnIH19PjwvZGl2PlxuICAgICAgICAgICAgICAgICAg57qi6ImyICgjZGMzNTQ1KVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L09wdGlvbj5cbiAgICAgICAgICAgICAgPE9wdGlvbiB2YWx1ZT1cIiNmZmMxMDdcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNCBoLTQgcm91bmRlZFwiIHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogJyNmZmMxMDcnIH19PjwvZGl2PlxuICAgICAgICAgICAgICAgICAg6buE6ImyICgjZmZjMTA3KVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L09wdGlvbj5cbiAgICAgICAgICAgICAgPE9wdGlvbiB2YWx1ZT1cIiM2ZjQyYzFcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNCBoLTQgcm91bmRlZFwiIHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogJyM2ZjQyYzEnIH19PjwvZGl2PlxuICAgICAgICAgICAgICAgICAg57Sr6ImyICgjNmY0MmMxKVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L09wdGlvbj5cbiAgICAgICAgICAgICAgPE9wdGlvbiB2YWx1ZT1cIiNmZDdlMTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNCBoLTQgcm91bmRlZFwiIHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogJyNmZDdlMTQnIH19PjwvZGl2PlxuICAgICAgICAgICAgICAgICAg5qmZ6ImyICgjZmQ3ZTE0KVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L09wdGlvbj5cbiAgICAgICAgICAgICAgPE9wdGlvbiB2YWx1ZT1cIiMyMGM5OTdcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNCBoLTQgcm91bmRlZFwiIHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogJyMyMGM5OTcnIH19PjwvZGl2PlxuICAgICAgICAgICAgICAgICAg6Z2S6ImyICgjMjBjOTk3KVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L09wdGlvbj5cbiAgICAgICAgICAgICAgPE9wdGlvbiB2YWx1ZT1cIiM2Yzc1N2RcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNCBoLTQgcm91bmRlZFwiIHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogJyM2Yzc1N2QnIH19PjwvZGl2PlxuICAgICAgICAgICAgICAgICAg54Gw6ImyICgjNmM3NTdkKVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L09wdGlvbj5cbiAgICAgICAgICAgIDwvU2VsZWN0PlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbmFtZT1cImNhdGVnb3J5XCJcbiAgICAgICAgICAgIGxhYmVsPVwi5qCH562+5YiG57G7XCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqeagh+etvuWIhuexuycgfV19XG4gICAgICAgICAgICBpbml0aWFsVmFsdWU9ezF9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFNlbGVjdCBwbGFjZWhvbGRlcj1cIuivt+mAieaLqeagh+etvuWIhuexu1wiPlxuICAgICAgICAgICAgICA8T3B0aW9uIHZhbHVlPXswfT7pmr7luqbmoIfnrb48L09wdGlvbj5cbiAgICAgICAgICAgICAgPE9wdGlvbiB2YWx1ZT17MX0+57G75Z6L5qCH562+PC9PcHRpb24+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9ezJ9PueJueiJsuagh+etvjwvT3B0aW9uPlxuICAgICAgICAgICAgICA8T3B0aW9uIHZhbHVlPXszfT7lhbbku5bmoIfnrb48L09wdGlvbj5cbiAgICAgICAgICAgIDwvU2VsZWN0PlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbmFtZT1cImRlc2NyaXB0aW9uXCJcbiAgICAgICAgICAgIGxhYmVsPVwi5qCH562+5o+P6L+wXCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyBtYXg6IDEwMCwgbWVzc2FnZTogJ+agh+etvuaPj+i/sOS4jeiDvei2hei/hzEwMOS4quWtl+espicgfV19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPElucHV0LlRleHRBcmVhXG4gICAgICAgICAgICAgIHJvd3M9ezN9XG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36L6T5YWl5qCH562+55qE6K+m57uG5o+P6L+wLi4uXCJcbiAgICAgICAgICAgICAgc2hvd0NvdW50XG4gICAgICAgICAgICAgIG1heExlbmd0aD17MTAwfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJzdGF0dXNcIlxuICAgICAgICAgICAgbGFiZWw9XCLmoIfnrb7nirbmgIFcIlxuICAgICAgICAgICAgcnVsZXM9e1t7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup5qCH562+54q25oCBJyB9XX1cbiAgICAgICAgICAgIGluaXRpYWxWYWx1ZT17MX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8U2VsZWN0IHBsYWNlaG9sZGVyPVwi6K+36YCJ5oup5qCH562+54q25oCBXCI+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9ezF9PuWQr+eUqDwvT3B0aW9uPlxuICAgICAgICAgICAgICA8T3B0aW9uIHZhbHVlPXswfT7npoHnlKg8L09wdGlvbj5cbiAgICAgICAgICAgIDwvU2VsZWN0PlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuICAgICAgICA8L0Zvcm0+XG4gICAgICA8L01vZGFsPlxuXG4gICAgICB7Lyog5Y+R5biD57O75YiX6K++56iL5qih5oCB5qGGICovfVxuICAgICAgPE1vZGFsXG4gICAgICAgIHRpdGxlPVwi5Y+R5biD57O75YiX6K++56iLXCJcbiAgICAgICAgb3Blbj17aXNQdWJsaXNoU2VyaWVzTW9kYWxWaXNpYmxlfVxuICAgICAgICBvbkNhbmNlbD17KCkgPT4ge1xuICAgICAgICAgIHNldElzUHVibGlzaFNlcmllc01vZGFsVmlzaWJsZShmYWxzZSk7XG4gICAgICAgICAgcHVibGlzaFNlcmllc0Zvcm0ucmVzZXRGaWVsZHMoKTtcbiAgICAgICAgfX1cbiAgICAgICAgb25Paz17KCkgPT4gcHVibGlzaFNlcmllc0Zvcm0uc3VibWl0KCl9XG4gICAgICAgIG9rVGV4dD1cIuWPkeW4g+ezu+WIl1wiXG4gICAgICAgIGNhbmNlbFRleHQ9XCLlj5bmtohcIlxuICAgICAgICB3aWR0aD17NjAwfVxuICAgICAgPlxuICAgICAgICA8Rm9ybVxuICAgICAgICAgIGZvcm09e3B1Ymxpc2hTZXJpZXNGb3JtfVxuICAgICAgICAgIGxheW91dD1cInZlcnRpY2FsXCJcbiAgICAgICAgICBvbkZpbmlzaD17aGFuZGxlUHVibGlzaFNlcmllc31cbiAgICAgICAgPlxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJzZXJpZXNJZFwiXG4gICAgICAgICAgICBsYWJlbD1cIumAieaLqeimgeWPkeW4g+eahOezu+WIl+ivvueoi1wiXG4gICAgICAgICAgICBydWxlcz17W3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nopoHlj5HluIPnmoTns7vliJfor77nqIsnIH1dfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTZWxlY3RcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fpgInmi6nns7vliJfor77nqItcIlxuICAgICAgICAgICAgICBzaG93U2VhcmNoXG4gICAgICAgICAgICAgIGZpbHRlck9wdGlvbj17KGlucHV0LCBvcHRpb24pID0+XG4gICAgICAgICAgICAgICAgKG9wdGlvbj8uY2hpbGRyZW4gYXMgdW5rbm93biBhcyBzdHJpbmcpPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGlucHV0LnRvTG93ZXJDYXNlKCkpXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2NvdXJzZVNlcmllcy5tYXAoKHNlcmllcykgPT4gKFxuICAgICAgICAgICAgICAgIDxPcHRpb24ga2V5PXtzZXJpZXMuaWR9IHZhbHVlPXtzZXJpZXMuaWR9PlxuICAgICAgICAgICAgICAgICAge3Nlcmllcy50aXRsZX0gKHtzZXJpZXMuY2F0ZWdvcnl9KVxuICAgICAgICAgICAgICAgIDwvT3B0aW9uPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvU2VsZWN0PlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbmFtZT1cInB1Ymxpc2hOb3RlXCJcbiAgICAgICAgICAgIGxhYmVsPVwi5Y+R5biD6K+05piOXCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogZmFsc2UgfV19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPElucHV0LlRleHRBcmVhXG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36L6T5YWl5Y+R5biD6K+05piO77yI5Y+v6YCJ77yJXCJcbiAgICAgICAgICAgICAgcm93cz17M31cbiAgICAgICAgICAgICAgbWF4TGVuZ3RoPXsyMDB9XG4gICAgICAgICAgICAgIHNob3dDb3VudFxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS01MCBwLTQgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+5Y+R5biD6K+05piO77yaPC9oND5cbiAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgc3BhY2UteS0xXCI+XG4gICAgICAgICAgICAgIDxsaT7igKIg5Y+R5biD5ZCO57O75YiX6K++56iL5bCG5Zyo6K++56iL5biC5Zy65Lit5YWs5byA5pi+56S6PC9saT5cbiAgICAgICAgICAgICAgPGxpPuKAoiDlj6rmnInlt7LlrozmiJDnmoTor77nqIvmiY3kvJrooqvlj5HluIM8L2xpPlxuICAgICAgICAgICAgICA8bGk+4oCiIOWPkeW4g+WQjuWPr+S7peafpeeci+ivpue7hueahOWPkeW4g+e7n+iuoeS/oeaBrzwvbGk+XG4gICAgICAgICAgICAgIDxsaT7igKIg5Y+R5biD54q25oCB5Y+v5Lul6ZqP5pe25L+u5pS5PC9saT5cbiAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvRm9ybT5cbiAgICAgIDwvTW9kYWw+XG5cbiAgICAgIHsvKiDlj5HluIPor77nqIvmqKHmgIHmoYYgKi99XG4gICAgICA8TW9kYWxcbiAgICAgICAgdGl0bGU9XCLlj5HluIPor77nqItcIlxuICAgICAgICBvcGVuPXtpc1B1Ymxpc2hDb3Vyc2VNb2RhbFZpc2libGV9XG4gICAgICAgIG9uQ2FuY2VsPXtyZXNldFB1Ymxpc2hDb3Vyc2VNb2RhbH1cbiAgICAgICAgZm9vdGVyPXtudWxsfVxuICAgICAgICB3aWR0aD17NzAwfVxuICAgICAgICBkZXN0cm95T25DbG9zZVxuICAgICAgPlxuICAgICAgICA8Rm9ybVxuICAgICAgICAgIGZvcm09e3B1Ymxpc2hDb3Vyc2VGb3JtfVxuICAgICAgICAgIGxheW91dD1cInZlcnRpY2FsXCJcbiAgICAgICAgICBvbkZpbmlzaD17aGFuZGxlUHVibGlzaENvdXJzZX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJtdC00XCJcbiAgICAgICAgPlxuICAgICAgICAgIHsvKiDnrKzkuIDkuKrkuIvmi4nmoYbvvJrpgInmi6nns7vliJfor77nqIsgKi99XG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbmFtZT1cInNlcmllc0lkXCJcbiAgICAgICAgICAgIGxhYmVsPVwi6YCJ5oup57O75YiX6K++56iLXCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqeezu+WIl+ivvueoiycgfV19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFNlbGVjdFxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuivt+mAieaLqeezu+WIl+ivvueoi1wiXG4gICAgICAgICAgICAgIGxvYWRpbmc9e3B1Ymxpc2hGb3JtTG9hZGluZ31cbiAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZVB1Ymxpc2hTZXJpZXNDaGFuZ2V9XG4gICAgICAgICAgICAgIHNob3dTZWFyY2hcbiAgICAgICAgICAgICAgZmlsdGVyT3B0aW9uPXsoaW5wdXQsIG9wdGlvbikgPT5cbiAgICAgICAgICAgICAgICAob3B0aW9uPy5jaGlsZHJlbiBhcyB1bmtub3duIGFzIHN0cmluZyk/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoaW5wdXQudG9Mb3dlckNhc2UoKSlcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7cHVibGlzaFNlcmllc0xpc3RGb3JNb2RhbC5tYXAoc2VyaWVzID0+IChcbiAgICAgICAgICAgICAgICA8T3B0aW9uIGtleT17c2VyaWVzLmlkfSB2YWx1ZT17c2VyaWVzLmlkfT5cbiAgICAgICAgICAgICAgICAgIHtzZXJpZXMudGl0bGV9IChJRDoge3Nlcmllcy5pZH0pXG4gICAgICAgICAgICAgICAgPC9PcHRpb24+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9TZWxlY3Q+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICB7Lyog56ys5LqM5Liq5LiL5ouJ5qGG77ya6YCJ5oup5a2Q6K++56iLICovfVxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJjb3Vyc2VJZFwiXG4gICAgICAgICAgICBsYWJlbD1cIumAieaLqeWtkOivvueoi1wiXG4gICAgICAgICAgICBydWxlcz17W3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nopoHlj5HluIPnmoTlrZDor77nqIsnIH1dfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTZWxlY3RcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9e3NlbGVjdGVkU2VyaWVzRm9yUHVibGlzaCA/IFwi6K+36YCJ5oup6KaB5Y+R5biD55qE5a2Q6K++56iLXCIgOiBcIuivt+WFiOmAieaLqeezu+WIl+ivvueoi1wifVxuICAgICAgICAgICAgICBkaXNhYmxlZD17IXNlbGVjdGVkU2VyaWVzRm9yUHVibGlzaH1cbiAgICAgICAgICAgICAgbG9hZGluZz17cHVibGlzaEZvcm1Mb2FkaW5nICYmICEhc2VsZWN0ZWRTZXJpZXNGb3JQdWJsaXNofVxuICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlUHVibGlzaENvdXJzZUNoYW5nZX1cbiAgICAgICAgICAgICAgc2hvd1NlYXJjaFxuICAgICAgICAgICAgICBmaWx0ZXJPcHRpb249eyhpbnB1dCwgb3B0aW9uKSA9PlxuICAgICAgICAgICAgICAgIChvcHRpb24/LmNoaWxkcmVuIGFzIHVua25vd24gYXMgc3RyaW5nKT8udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhpbnB1dC50b0xvd2VyQ2FzZSgpKVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIG5vdEZvdW5kQ29udGVudD17XG4gICAgICAgICAgICAgICAgcHVibGlzaEZvcm1Mb2FkaW5nICYmIHNlbGVjdGVkU2VyaWVzRm9yUHVibGlzaFxuICAgICAgICAgICAgICAgICAgPyBcIuato+WcqOWKoOi9veWtkOivvueoiy4uLlwiXG4gICAgICAgICAgICAgICAgICA6IHNlbGVjdGVkU2VyaWVzRm9yUHVibGlzaFxuICAgICAgICAgICAgICAgICAgICA/IFwi6K+l57O75YiX5pqC5peg5a2Q6K++56iLXCJcbiAgICAgICAgICAgICAgICAgICAgOiBcIuivt+WFiOmAieaLqeezu+WIl+ivvueoi1wiXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge3B1Ymxpc2hDb3Vyc2VMaXN0Rm9yTW9kYWwubGVuZ3RoID4gMCA/IChcbiAgICAgICAgICAgICAgICBwdWJsaXNoQ291cnNlTGlzdEZvck1vZGFsLm1hcChjb3Vyc2UgPT4gKFxuICAgICAgICAgICAgICAgICAgPE9wdGlvbiBrZXk9e2NvdXJzZS5pZH0gdmFsdWU9e2NvdXJzZS5pZH0+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+e2NvdXJzZS50aXRsZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8VGFnIGNvbG9yPXtjb3Vyc2Uuc3RhdHVzID09PSAxID8gJ2dyZWVuJyA6IGNvdXJzZS5zdGF0dXMgPT09IDAgPyAnb3JhbmdlJyA6ICdyZWQnfSBjbGFzc05hbWU9XCJ0ZXh0LXhzXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2Uuc3RhdHVzID09PSAxID8gJ+W3suWPkeW4gycgOiBjb3Vyc2Uuc3RhdHVzID09PSAwID8gJ+iNieeovycgOiAn5bey5b2S5qGjJ31cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFnPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LXhzXCI+SUQ6IHtjb3Vyc2UuaWR9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvT3B0aW9uPlxuICAgICAgICAgICAgICAgICkpXG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgc2VsZWN0ZWRTZXJpZXNGb3JQdWJsaXNoICYmICFwdWJsaXNoRm9ybUxvYWRpbmcgPyAoXG4gICAgICAgICAgICAgICAgICA8T3B0aW9uIGRpc2FibGVkIHZhbHVlPVwibm8tY291cnNlc1wiPlxuICAgICAgICAgICAgICAgICAgICDor6Xns7vliJfmmoLml6DlrZDor77nqItcbiAgICAgICAgICAgICAgICAgIDwvT3B0aW9uPlxuICAgICAgICAgICAgICAgICkgOiBudWxsXG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIHsvKiDosIPor5Xkv6Hmga/mmL7npLogKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIHAtMyByb3VuZGVkLWxnIG1iLTQgdGV4dC14c1wiPlxuICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPuiwg+ivleS/oeaBrzwvaDQ+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgIDxwPuW3sumAieaLqeezu+WIl0lEOiB7c2VsZWN0ZWRTZXJpZXNGb3JQdWJsaXNoIHx8ICfmnKrpgInmi6knfTwvcD5cbiAgICAgICAgICAgICAgPHA+5bey6YCJ5oup6K++56iLSUQ6IHtzZWxlY3RlZENvdXJzZUZvclB1Ymxpc2ggfHwgJ+acqumAieaLqSd9PC9wPlxuICAgICAgICAgICAgICA8cD7ns7vliJfliJfooajmlbDph486IHtwdWJsaXNoU2VyaWVzTGlzdEZvck1vZGFsLmxlbmd0aH08L3A+XG4gICAgICAgICAgICAgIDxwPuWtkOivvueoi+WIl+ihqOaVsOmHjzoge3B1Ymxpc2hDb3Vyc2VMaXN0Rm9yTW9kYWwubGVuZ3RofTwvcD5cbiAgICAgICAgICAgICAgPHA+5Yqg6L2954q25oCBOiB7cHVibGlzaEZvcm1Mb2FkaW5nID8gJ+WKoOi9veS4rScgOiAn56m66ZeyJ308L3A+XG4gICAgICAgICAgICAgIHtwdWJsaXNoQ291cnNlTGlzdEZvck1vZGFsLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8cD7lrZDor77nqIvliJfooag6PC9wPlxuICAgICAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cIm1sLTQgbGlzdC1kaXNjXCI+XG4gICAgICAgICAgICAgICAgICAgIHtwdWJsaXNoQ291cnNlTGlzdEZvck1vZGFsLm1hcChjb3Vyc2UgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxsaSBrZXk9e2NvdXJzZS5pZH0+SUQ6IHtjb3Vyc2UuaWR9LCDlkI3np7A6IHtjb3Vyc2UudGl0bGV9PC9saT5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Lyog6YCJ5Lit6K++56iL5L+h5oGv5pi+56S6ICovfVxuICAgICAgICAgIHtzZWxlY3RlZENvdXJzZUZvclB1Ymxpc2ggJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibHVlLTUwIHAtNCByb3VuZGVkLWxnIG1iLTRcIj5cbiAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ibHVlLTkwMCBtYi0yXCI+5Y2z5bCG5Y+R5biD55qE6K++56iLPC9oND5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS03MDBcIj5cbiAgICAgICAgICAgICAgICA8cD7or77nqItJRDoge3NlbGVjdGVkQ291cnNlRm9yUHVibGlzaH08L3A+XG4gICAgICAgICAgICAgICAgPHA+5omA5bGe57O75YiXOiB7cHVibGlzaFNlcmllc0xpc3RGb3JNb2RhbC5maW5kKHMgPT4gcy5pZCA9PT0gc2VsZWN0ZWRTZXJpZXNGb3JQdWJsaXNoKT8udGl0bGV9PC9wPlxuICAgICAgICAgICAgICAgIDxwPuivvueoi+WQjeensDoge3B1Ymxpc2hDb3Vyc2VMaXN0Rm9yTW9kYWwuZmluZChjID0+IGMuaWQgPT09IHNlbGVjdGVkQ291cnNlRm9yUHVibGlzaCk/LnRpdGxlfTwvcD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0yIHRleHQtYmx1ZS02MDAgZm9udC1tZWRpdW1cIj7ngrnlh7tcIuWPkeW4g+atpOivvueoi1wi5bCG6LCD55So5Y+R5biDQVBJ77yaUE9TVCAvYXBpL3YxL2NvdXJzZS1tYW5hZ2VtZW50L2NvdXJzZXMve3NlbGVjdGVkQ291cnNlRm9yUHVibGlzaH0vcHVibGlzaDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgcHQtNCBib3JkZXItdFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAge3NlbGVjdGVkQ291cnNlRm9yUHVibGlzaCA/IChcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTYwMFwiPuKckyDlt7LpgInmi6nor77nqIvvvIzlj6/ku6Xlj5HluIM8L3NwYW4+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPHNwYW4+6K+35YWI6YCJ5oup57O75YiX6K++56iL5ZKM5a2Q6K++56iLPC9zcGFuPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTJcIj5cbiAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtyZXNldFB1Ymxpc2hDb3Vyc2VNb2RhbH0+XG4gICAgICAgICAgICAgICAg5Y+W5raIXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdHlwZT1cInByaW1hcnlcIlxuICAgICAgICAgICAgICAgIGh0bWxUeXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgICAgICBsb2FkaW5nPXtwdWJsaXNoRm9ybUxvYWRpbmd9XG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFzZWxlY3RlZENvdXJzZUZvclB1Ymxpc2h9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtzZWxlY3RlZENvdXJzZUZvclB1Ymxpc2ggPyAnYmctZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTcwMCBib3JkZXItZ3JlZW4tNjAwJyA6ICcnfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge3B1Ymxpc2hGb3JtTG9hZGluZyA/ICflj5HluIPkuK0uLi4nIDogJ+WPkeW4g+atpOivvueoiyd9XG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvRm9ybT5cbiAgICAgIDwvTW9kYWw+XG4gICAgPC8+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBDb3Vyc2VNYW5hZ2VtZW50O1xuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiQ2FyZCIsIkJ1dHRvbiIsIlRhYmxlIiwiTW9kYWwiLCJGb3JtIiwiSW5wdXQiLCJTZWxlY3QiLCJTcGFjZSIsIlRhZyIsIlBvcGNvbmZpcm0iLCJVcGxvYWQiLCJtZXNzYWdlIiwiUGx1c091dGxpbmVkIiwiRWRpdE91dGxpbmVkIiwiRGVsZXRlT3V0bGluZWQiLCJVcGxvYWRPdXRsaW5lZCIsIkluYm94T3V0bGluZWQiLCJHZXROb3RpZmljYXRpb24iLCJjb3Vyc2VBcGkiLCJ1cGxvYWRBcGkiLCJTZWFyY2giLCJPcHRpb24iLCJDb3Vyc2VNYW5hZ2VtZW50IiwicHVibGlzaFNlcmllc0xpc3RGb3JNb2RhbCIsInB1Ymxpc2hDb3Vyc2VMaXN0Rm9yTW9kYWwiLCJjb3Vyc2VMaXN0Iiwic2V0Q291cnNlTGlzdCIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiaXNDb3Vyc2VNb2RhbFZpc2libGUiLCJzZXRJc0NvdXJzZU1vZGFsVmlzaWJsZSIsImlzQWRkQ291cnNlTW9kYWxWaXNpYmxlIiwic2V0SXNBZGRDb3Vyc2VNb2RhbFZpc2libGUiLCJpc0VkaXRDb3Vyc2VNb2RhbFZpc2libGUiLCJzZXRJc0VkaXRDb3Vyc2VNb2RhbFZpc2libGUiLCJpc0FkZFNlcmllc01vZGFsVmlzaWJsZSIsInNldElzQWRkU2VyaWVzTW9kYWxWaXNpYmxlIiwiaXNBZGRUYWdNb2RhbFZpc2libGUiLCJzZXRJc0FkZFRhZ01vZGFsVmlzaWJsZSIsImlzUHVibGlzaFNlcmllc01vZGFsVmlzaWJsZSIsInNldElzUHVibGlzaFNlcmllc01vZGFsVmlzaWJsZSIsImlzUHVibGlzaENvdXJzZU1vZGFsVmlzaWJsZSIsInNldElzUHVibGlzaENvdXJzZU1vZGFsVmlzaWJsZSIsImVkaXRpbmdDb3Vyc2UiLCJzZXRFZGl0aW5nQ291cnNlIiwic2VhcmNoS2V5d29yZCIsInNldFNlYXJjaEtleXdvcmQiLCJ0ZWFjaGVycyIsInNldFRlYWNoZXJzIiwiY291cnNlVGFncyIsInNldENvdXJzZVRhZ3MiLCJjb3ZlckltYWdlVXJsIiwic2V0Q292ZXJJbWFnZVVybCIsInNlcmllc0xpc3QiLCJzZXRTZXJpZXNMaXN0Iiwic2VyaWVzQ291cnNlc01hcCIsInNldFNlcmllc0NvdXJzZXNNYXAiLCJNYXAiLCJleHBhbmRlZFNlcmllcyIsInNldEV4cGFuZGVkU2VyaWVzIiwiU2V0Iiwic2VyaWVzTG9hZGluZyIsInNldFNlcmllc0xvYWRpbmciLCJzZWxlY3RlZFNlcmllc0ZvclB1Ymxpc2giLCJzZXRTZWxlY3RlZFNlcmllc0ZvclB1Ymxpc2giLCJ1bmRlZmluZWQiLCJzZWxlY3RlZENvdXJzZUZvclB1Ymxpc2giLCJzZXRTZWxlY3RlZENvdXJzZUZvclB1Ymxpc2giLCJwdWJsaXNoU2VyaWVzQ291cnNlcyIsInNldFB1Ymxpc2hTZXJpZXNDb3Vyc2VzIiwicHVibGlzaExvYWRpbmciLCJzZXRQdWJsaXNoTG9hZGluZyIsInB1Ymxpc2hTZXJpZXNPcHRpb25zIiwic2V0UHVibGlzaFNlcmllc09wdGlvbnMiLCJzZXRQdWJsaXNoU2VyaWVzTGlzdEZvck1vZGFsIiwic2V0UHVibGlzaENvdXJzZUxpc3RGb3JNb2RhbCIsInB1Ymxpc2hGb3JtTG9hZGluZyIsInNldFB1Ymxpc2hGb3JtTG9hZGluZyIsImNvdXJzZVNlcmllcyIsInNldENvdXJzZVNlcmllcyIsImNvdXJzZUNvdmVySW1hZ2VVcmwiLCJzZXRDb3Vyc2VDb3ZlckltYWdlVXJsIiwiYWRkaXRpb25hbEZpbGVzIiwic2V0QWRkaXRpb25hbEZpbGVzIiwiY291cnNlVmlkZW9VcmwiLCJzZXRDb3Vyc2VWaWRlb1VybCIsImNvdXJzZVZpZGVvTmFtZSIsInNldENvdXJzZVZpZGVvTmFtZSIsImNvdXJzZURvY3VtZW50VXJsIiwic2V0Q291cnNlRG9jdW1lbnRVcmwiLCJjb3Vyc2VEb2N1bWVudE5hbWUiLCJzZXRDb3Vyc2VEb2N1bWVudE5hbWUiLCJ2aWRlb0R1cmF0aW9uIiwic2V0VmlkZW9EdXJhdGlvbiIsInRlYWNoaW5nSW5mb0xpc3QiLCJzZXRUZWFjaGluZ0luZm9MaXN0IiwidGl0bGUiLCJjb250ZW50IiwiYWRkQ291cnNlRm9ybSIsInVzZUZvcm0iLCJlZGl0Q291cnNlRm9ybSIsImFkZFNlcmllc0Zvcm0iLCJhZGRUYWdGb3JtIiwicHVibGlzaFNlcmllc0Zvcm0iLCJwdWJsaXNoQ291cnNlRm9ybSIsIm5vdGlmaWNhdGlvbiIsImZldGNoU2VyaWVzTGlzdCIsInJlcyIsImNvbnNvbGUiLCJsb2ciLCJkYXRhIiwiZ2V0TWFya2V0cGxhY2VTZXJpZXMiLCJwYWdlIiwicGFnZVNpemUiLCJjb2RlIiwibGlzdCIsImVycm9yIiwibXNnIiwiZmV0Y2hTZXJpZXNDb3Vyc2VzIiwic2VyaWVzSWQiLCJnZXRNYW5hZ2VtZW50U2VyaWVzQ291cnNlcyIsInByZXYiLCJzZXQiLCJhZGQiLCJmZXRjaENvdXJzZUxpc3QiLCJoYW5kbGVBZGRDb3Vyc2UiLCJ2YWx1ZXMiLCJjb250ZW50Q29uZmlnIiwiaGFzVmlkZW8iLCJoYXNEb2N1bWVudCIsInZpZGVvIiwidXJsIiwibmFtZSIsImRvY3VtZW50IiwiY291cnNlRGF0YSIsInBhcnNlSW50IiwidHJpbSIsImRlc2NyaXB0aW9uIiwiY292ZXJJbWFnZSIsInRlYWNoaW5nSW5mbyIsImZpbHRlciIsImluZm8iLCJzb21lIiwiYyIsImFkZGl0aW9uYWxSZXNvdXJjZXMiLCJtYXAiLCJmaWxlIiwic3BsaXQiLCJwb3AiLCJvcmRlckluZGV4IiwiSlNPTiIsInN0cmluZ2lmeSIsImxlbmd0aCIsInJldHJ5Q291bnQiLCJtYXhSZXRyaWVzIiwibGFzdEVycm9yIiwiY3JlYXRlQ291cnNlIiwic3VjY2VzcyIsInJlc2V0RmllbGRzIiwid2FybmluZyIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0VGltZW91dCIsImluY2x1ZGVzIiwicmVzcG9uc2UiLCJzdGF0dXMiLCJlcnJvck1zZyIsImhhbmRsZUVkaXRDb3Vyc2UiLCJ1cGRhdGVDb3Vyc2UiLCJpZCIsImhhbmRsZURlbGV0ZUNvdXJzZSIsImNvdXJzZUlkIiwiZGVsZXRlQ291cnNlIiwiaGFuZGxlRGVsZXRlU3ViQ291cnNlIiwidG9nZ2xlU2VyaWVzRXhwYW5zaW9uIiwiaGFzIiwibmV3U2V0IiwiZGVsZXRlIiwiZXhwYW5kQWxsU2VyaWVzIiwic2VyaWVzIiwiY29sbGFwc2VBbGxTZXJpZXMiLCJoYW5kbGVBZGRTZXJpZXMiLCJzZXJpZXNEYXRhIiwiY3JlYXRlQ291cnNlU2VyaWVzIiwiaGFuZGxlQWRkVGFnIiwiY3JlYXRlQ291cnNlVGFnIiwiZmV0Y2hDb3Vyc2VUYWdzIiwiaGFuZGxlUHVibGlzaFNlcmllcyIsInB1Ymxpc2hDb3Vyc2VTZXJpZXMiLCJwdWJsaXNoRGF0YSIsInB1Ymxpc2hTdGF0cyIsInN0YXRzIiwic3RhdHNNZXNzYWdlIiwicHVibGlzaGVkQ291cnNlcyIsInRvdGFsQ291cnNlcyIsIk1hdGgiLCJ2aWRlb0NvdXJzZUNvdW50Iiwicm91bmQiLCJ0b3RhbFZpZGVvRHVyYXRpb24iLCJmZXRjaFNlcmllc0ZvclB1Ymxpc2giLCJmZXRjaFNlcmllc0RldGFpbEZvclB1Ymxpc2giLCJnZXRNYXJrZXRwbGFjZVNlcmllc0RldGFpbCIsImZldGNoUHVibGlzaFNlcmllc0xpc3QiLCJmZXRjaFB1Ymxpc2hDb3Vyc2VMaXN0IiwiaGFuZGxlUHVibGlzaFNlcmllc0NoYW5nZSIsInNldEZpZWxkc1ZhbHVlIiwiaGFuZGxlUHVibGlzaENvdXJzZUNoYW5nZSIsInJlc2V0UHVibGlzaENvdXJzZU1vZGFsIiwib3BlblB1Ymxpc2hDb3Vyc2VNb2RhbCIsImhhbmRsZVB1Ymxpc2hDb3Vyc2UiLCJzZWxlY3RlZENvdXJzZSIsImZpbmQiLCJwdWJsaXNoQ291cnNlIiwicmVzZXRQdWJsaXNoTW9kYWwiLCJoYW5kbGVJbWFnZVVwbG9hZCIsIm9wdGlvbnMiLCJvblN1Y2Nlc3MiLCJvbkVycm9yIiwidXBsb2FkVG9Pc3MiLCJoYW5kbGVJbWFnZVJlbW92ZSIsImhhbmRsZUNvdXJzZUNvdmVyVXBsb2FkIiwiaGFuZGxlQ291cnNlQ292ZXJSZW1vdmUiLCJoYW5kbGVBZGRpdGlvbmFsUmVzb3VyY2VVcGxvYWQiLCJoYW5kbGVBZGRpdGlvbmFsUmVzb3VyY2VSZW1vdmUiLCJmIiwiaGFuZGxlVmlkZW9VcGxvYWQiLCJ2aWRlb0VsZW1lbnQiLCJjcmVhdGVFbGVtZW50Iiwic3JjIiwib25sb2FkZWRtZXRhZGF0YSIsImZsb29yIiwiZHVyYXRpb24iLCJoYW5kbGVWaWRlb1JlbW92ZSIsImhhbmRsZURvY3VtZW50VXBsb2FkIiwiaGFuZGxlRG9jdW1lbnRSZW1vdmUiLCJhZGRUZWFjaGluZ0luZm8iLCJyZW1vdmVUZWFjaGluZ0luZm8iLCJpbmRleCIsIm5ld0xpc3QiLCJfIiwiaSIsInVwZGF0ZVRlYWNoaW5nSW5mb1RpdGxlIiwidXBkYXRlVGVhY2hpbmdJbmZvQ29udGVudCIsImNvbnRlbnRJbmRleCIsImFkZFRlYWNoaW5nSW5mb0NvbnRlbnQiLCJwdXNoIiwicmVtb3ZlVGVhY2hpbmdJbmZvQ29udGVudCIsInNwbGljZSIsIm9wZW5FZGl0TW9kYWwiLCJjb3Vyc2UiLCJmaWx0ZXJlZENvdXJzZXMiLCJ0b0xvd2VyQ2FzZSIsImNhdGVnb3J5IiwicHJlcGFyZVRhYmxlRGF0YSIsInRhYmxlRGF0YSIsIkFycmF5IiwiZnJvbSIsImZvckVhY2giLCJrZXkiLCJ0eXBlIiwiaXNFeHBhbmRlZCIsInN1YkNvdXJzZXMiLCJnZXQiLCJwYXJlbnRTZXJpZXNUaXRsZSIsImNvbHVtbnMiLCJkYXRhSW5kZXgiLCJ3aWR0aCIsInJlbmRlciIsInRleHQiLCJyZWNvcmQiLCJkaXYiLCJjbGFzc05hbWUiLCJzaXplIiwib25DbGljayIsInN0eWxlIiwibWluV2lkdGgiLCJoZWlnaHQiLCJzcGFuIiwiY29sb3IiLCJnZXRTdGF0dXNDb25maWciLCJjb25maWciLCJpY29uIiwicCIsIm9uQ29uZmlybSIsIm9rVGV4dCIsImNhbmNlbFRleHQiLCJva1R5cGUiLCJkYW5nZXIiLCJnZXRDb3Vyc2VUYWdzIiwidGFncyIsInRhZyIsIndhcm4iLCJmZXRjaENvdXJzZVNlcmllcyIsInBhZ2luYXRpb24iLCJ0b3RhbCIsImlzQXJyYXkiLCJmb3JtYXR0ZWRTZXJpZXMiLCJpdGVtIiwiY2F0ZWdvcnlMYWJlbCIsInRlYWNoZXJJZHMiLCJ0YWdJZHMiLCJjcmVhdGVkQXQiLCJEYXRlIiwidG9JU09TdHJpbmciLCJ1cGRhdGVkQXQiLCJleHRyYSIsImJsb2NrIiwiYmFja2dyb3VuZENvbG9yIiwiYm9yZGVyQ29sb3IiLCJvcGVuIiwib25DYW5jZWwiLCJmb290ZXIiLCJwbGFjZWhvbGRlciIsImFsbG93Q2xlYXIiLCJvblNlYXJjaCIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsInZhbHVlIiwic3Ryb25nIiwicmVkdWNlIiwiY291cnNlcyIsImRpc2FibGVkIiwiZGF0YVNvdXJjZSIsInJvd0tleSIsInNob3dTaXplQ2hhbmdlciIsInNob3dUb3RhbCIsIm9uT2siLCJzdWJtaXQiLCJmb3JtIiwibGF5b3V0Iiwib25GaW5pc2giLCJJdGVtIiwibGFiZWwiLCJydWxlcyIsInJlcXVpcmVkIiwic2hvd1NlYXJjaCIsIm9wdGlvbkZpbHRlclByb3AiLCJvdmVyZmxvdyIsInRleHRPdmVyZmxvdyIsIndoaXRlU3BhY2UiLCJtYXhXaWR0aCIsImZvbnRXZWlnaHQiLCJmb250U2l6ZSIsIm1hcmdpbkxlZnQiLCJUZXh0QXJlYSIsInJvd3MiLCJzaG93Q291bnQiLCJtYXhMZW5ndGgiLCJEcmFnZ2VyIiwiY3VzdG9tUmVxdWVzdCIsIm9uUmVtb3ZlIiwiYWNjZXB0IiwibWF4Q291bnQiLCJsaXN0VHlwZSIsImltZyIsImFsdCIsIm1heEhlaWdodCIsIm9iamVjdEZpdCIsIm1pbiIsInRyYW5zZm9ybSIsIk51bWJlciIsInRvb2x0aXAiLCJjb250cm9scyIsIm1hcmdpblRvcCIsInBhZGRpbmciLCJ0ZXh0QWxpZ24iLCJtb2RlIiwibXVsdGlwbGUiLCJpbml0aWFsVmFsdWUiLCJvcHRpb25MYWJlbFByb3AiLCJtYXgiLCJmaWx0ZXJPcHRpb24iLCJpbnB1dCIsIm9wdGlvbiIsImNoaWxkcmVuIiwiaDQiLCJ1bCIsImxpIiwiZGVzdHJveU9uQ2xvc2UiLCJub3RGb3VuZENvbnRlbnQiLCJzIiwiaHRtbFR5cGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin-space/components/course-management.tsx\n"));

/***/ })

});