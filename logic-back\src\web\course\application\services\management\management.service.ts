import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, In, DataSource } from 'typeorm';
import { CourseSeries } from '../../../domain/entities/management/course-series.entity';
import { Course } from '../../../domain/entities/management/course.entity';
import { TaskTemplate } from '../../../domain/entities/teaching/task-template.entity';
import { CourseSettings } from '../../../domain/entities/management/course-settings.entity';
import { CourseSeriesTag } from '../../../domain/entities/marketplace/course-series-tag.entity';
import { CourseTag } from '../../../domain/entities/marketplace/course-tag.entity';

@Injectable()
export class ManagementService {
  constructor(
    @InjectRepository(CourseSeries)
    private readonly courseSeriesRepository: Repository<CourseSeries>,
    @InjectRepository(Course)
    private readonly courseRepository: Repository<Course>,
    @InjectRepository(TaskTemplate)
    private readonly taskTemplateRepository: Repository<TaskTemplate>,
    @InjectRepository(CourseSettings)
    private readonly courseSettingsRepository: Repository<CourseSettings>,
    @InjectRepository(CourseSeriesTag)
    private readonly courseSeriesTagRepository: Repository<CourseSeriesTag>,
    @InjectRepository(CourseTag)
    private readonly courseTagRepository: Repository<CourseTag>,
    private readonly dataSource: DataSource
  ) { }

  /**
   * 获取当前用户创建的系列课程列表
   * 优化版：减少数据库查询次数，提高并发性能
   */
  async getMyCourseSeries(
    userId: number,
    page: number,
    pageSize: number,
    status?: number,
    keyword?: string
  ) {
    // 1.构建查询条件
    const skip = (page - 1) * pageSize;
    const where: any = { creatorId: userId };

    if (status !== undefined) {
      where.status = status;
    }

    if (keyword) {
      where.title = Like(`%${keyword}%`);
    }

    // 2.执行查询
    const [items, total] = await this.courseSeriesRepository.findAndCount({
      where,
      skip,
      take: pageSize,
      order: { createdAt: 'DESC' }
    });

    // 如果没有查询到数据，直接返回
    if (items.length === 0) {
      return {
        items: [],
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      };
    }

    // 3.获取所有系列ID
    const seriesIds = items.map(item => item.id);

    // 4.一次性查询所有系列的课程统计信息
    const courseStats = await this.courseRepository.createQueryBuilder('course')
      .select('course.series_id', 'seriesId')
      .addSelect('COUNT(*)', 'totalCourses')
      .addSelect('SUM(CASE WHEN course.has_video = 1 THEN 1 ELSE 0 END)', 'videoCourses')
      .addSelect('SUM(CASE WHEN course.has_document = 1 THEN 1 ELSE 0 END)', 'documentCourses')
      .addSelect('SUM(COALESCE(course.resources_count, 0))', 'resourcesCount')
      .addSelect('SUM(CASE WHEN course.status = 1 THEN 1 ELSE 0 END)', 'publishedCourses')
      .where('course.series_id IN (:...seriesIds)', { seriesIds })
      .groupBy('course.series_id')
      .getRawMany();

    // 5.转换为Map便于快速查找
    const statsMap = new Map(courseStats.map(stat => [Number(stat.seriesId), stat]));

    // 6.为每个系列添加统计信息
    for (const item of items) {
      const stats = statsMap.get(item.id) || {
        totalCourses: 0, videoCourses: 0, documentCourses: 0,
        resourcesCount: 0, publishedCourses: 0
      };

      const totalCourses = Number(stats.totalCourses) || 0;
      const publishedCourses = Number(stats.publishedCourses) || 0;
      const completionRate = totalCourses > 0 ? publishedCourses / totalCourses : 0;

      item['_contentSummary'] = {
        videoCourseCount: Number(stats.videoCourses) || 0,
        documentCourseCount: Number(stats.documentCourses) || 0,
        totalResourcesCount: Number(stats.resourcesCount) || 0,
        completionRate: parseFloat(completionRate.toFixed(2))
      };
    }

    // 7.返回分页及其系列下的课程统计信息
    return {
      items,
      page,
      pageSize,
      total,
      totalPages: Math.ceil(total / pageSize)
    };
  }

  /**
   * 根据ID获取课程系列
   */
  async findCourseSeriesById(id: number): Promise<CourseSeries> {
    const series = await this.courseSeriesRepository.findOne({ where: { id } });

    if (!series) {
      throw new NotFoundException(`系列ID为${id}的课程系列不存在`);
    }

    return series;
  }

  /**
   * 创建系列课程
   */
  async createCourseSeries(courseSeriesData: any): Promise<CourseSeries> {
    // 使用事务确保数据一致性
    return await this.dataSource.transaction(async manager => {
      try {
        // 1.数据验证
        if (!courseSeriesData.title || courseSeriesData.title.trim() === '') {
          throw new BadRequestException('系列名称不能为空');
        }

        // 2.如果提供了标签ID，验证标签是否存在数据库里
        if (courseSeriesData.tagIds && courseSeriesData.tagIds.length > 0) {
          const existingTags = await manager.find(CourseTag, {
            where: { id: In(courseSeriesData.tagIds) }
          });

          if (existingTags.length !== courseSeriesData.tagIds.length) {
            throw new BadRequestException('部分标签不存在');
          }
        }

        // 3.创建新系列
        const newSeries = manager.create(CourseSeries, {
          title: courseSeriesData.title,
          description: courseSeriesData.description,
          coverImage: courseSeriesData.coverImage,
          category: courseSeriesData.category || 0,
          status: 0, // 默认为草稿状态
          projectMembers: courseSeriesData.projectMembers,
          creatorId: courseSeriesData.creatorId,
          totalCourses: 0, // 初始化为0
          totalStudents: 0 // 初始化为0
        });

        // 4.保存系列到数据库
        const savedSeries = await manager.save(CourseSeries, newSeries);

        // 5.如果提供了标签ID，创建关联关系
        if (courseSeriesData.tagIds && courseSeriesData.tagIds.length > 0) {
          const seriesTagRelations = courseSeriesData.tagIds.map(tagId => ({
            seriesId: savedSeries.id,
            tagId: tagId
          }));

          await manager.save(CourseSeriesTag, seriesTagRelations);
        }

        return savedSeries;
      } catch (error) {
        if (error instanceof BadRequestException) {
          throw error;
        }
        throw new BadRequestException(`创建系列课程失败: ${error.message}`);
      }
    });
  }

  /**
   * 更新课程系列
   */
  async updateCourseSeries(id: number, updateData: any, userId?: number): Promise<CourseSeries> {
    // 使用事务确保数据一致性
    return await this.dataSource.transaction(async manager => {
      // 查找现有系列
      const series = await this.findCourseSeriesById(id);

      // 验证权限（如果提供了userId）
      if (userId && series.creatorId !== userId) {
        throw new ForbiddenException('无权限更新此课程系列');
      }

      // 提取标签ID并从更新数据中移除
      const tagIds = updateData.tagIds;
      delete updateData.tagIds;

      // 防止更新只读字段
      delete updateData.id;
      delete updateData.createdAt;
      delete updateData.updatedAt;
      delete updateData.creatorId; // 创建者不允许修改

      // 如果提供了标签ID，验证标签是否存在
      if (tagIds && tagIds.length > 0) {
        const existingTags = await manager.find(CourseTag, {
          where: { id: In(tagIds) }
        });

        if (existingTags.length !== tagIds.length) {
          throw new BadRequestException('部分标签不存在');
        }
      }

      // 应用更新
      Object.assign(series, updateData);

      // 保存系列更新
      const updatedSeries = await manager.save(CourseSeries, series);

      // 处理标签关联更新
      if (tagIds !== undefined) {
        // 删除现有的标签关联
        await manager.delete(CourseSeriesTag, { seriesId: id });

        // 如果提供了新的标签ID，创建新的关联关系
        if (tagIds && tagIds.length > 0) {
          const seriesTagRelations = tagIds.map((tagId: number) => ({
            seriesId: id,
            tagId: tagId
          }));

          await manager.save(CourseSeriesTag, seriesTagRelations);
        }
      }

      return updatedSeries;
    });
  }

  /**
   * 删除课程系列
   */
  async removeCourseSeries(id: number, userId?: number): Promise<{ success: boolean; message: string }> {
    // 查找现有系列
    const series = await this.findCourseSeriesById(id);

    // 验证权限（如果提供了userId）
    if (userId && series.creatorId !== userId) {
      throw new ForbiddenException('无权限删除此课程系列');
    }

    // 执行删除
    await this.courseSeriesRepository.remove(series);

    return {
      success: true,
      message: `ID为${id}的课程系列已成功删除`
    };
  }

  /**
   * 发布课程系列
   */
  async publishCourseSeries(seriesId: number, userId: number) {
    // 1.查找现有系列
    const series = await this.findCourseSeriesById(seriesId);

    // 2.验证权限，只有创造者才有权限发布
    if (series.creatorId !== userId) {
      throw new ForbiddenException('无权限发布此课程系列');
    }

    // 3.检查发布条件（例如：至少需要一个课程）
    const courseCount = await this.courseRepository.count({ where: { seriesId } });
    if (courseCount <= 0) {
      throw new BadRequestException('发布失败：课程系列中至少需要包含一个课程');
    }

    // 4.获取发布统计信息
    const publishStats = await this.getCourseSeriesPublishStats(seriesId);

    // 5.更新状态为已发布
    series.status = 1; // 已发布状态
    await this.courseSeriesRepository.save(series);

    // 6.返回结果给控制器
    return {
      success: true,
      message: '课程系列发布成功',
      data: series,
      publishStats
    };
  }

  /**
   * 发布单个课程
   */
  async publishCourse(courseId: number, userId: number) {
    // 1.查找现有课程
    const course = await this.findCourseById(courseId);

    // 2.验证权限，只有创造者才有权限发布
    if (course.creatorId !== userId) {
      throw new ForbiddenException('无权限发布此课程');
    }

    // 3.检查课程是否已经发布
    if (course.status === 1) {
      throw new BadRequestException('课程已经发布，无需重复发布');
    }

    // 4.更新状态为已发布
    course.status = 1; // 已发布状态
    await this.courseRepository.save(course);

    // 5.返回结果给控制器
    return {
      success: true,
      message: '课程发布成功',
      data: course
    };
  }

  /**
   * 获取课程系列的发布统计信息
   * 包含
   * 1.课程数量
   * 2.已发布课程数量
   * 3.视频课程数量
   * 4.文档课程数量
   * 5.总视频时长
   * 6.总资源数量
   */
  private async getCourseSeriesPublishStats(seriesId: number) {
    // 查询系列下的课程统计信息
    const courseStats = await this.courseRepository.createQueryBuilder('course')
      .select('COUNT(*)', 'totalCourses')
      .addSelect('SUM(CASE WHEN course.status = 1 THEN 1 ELSE 0 END)', 'publishedCourses')
      .addSelect('SUM(CASE WHEN course.has_video = 1 THEN 1 ELSE 0 END)', 'videoCourseCount')
      .addSelect('SUM(CASE WHEN course.has_document = 1 THEN 1 ELSE 0 END)', 'documentCourseCount')
      .addSelect('SUM(course.video_duration)', 'totalVideoDuration')
      .addSelect('SUM(COALESCE(JSON_LENGTH(course.additional_resources), 0))', 'totalResourcesCount')
      .where('course.series_id = :seriesId', { seriesId })
      .getRawOne();

    return {
      videoCourseCount: Number(courseStats.videoCourseCount) || 0,
      documentCourseCount: Number(courseStats.documentCourseCount) || 0,
      totalVideoDuration: Number(courseStats.totalVideoDuration) || 0,
      totalResourcesCount: Number(courseStats.totalResourcesCount) || 0,
      publishedCourses: Number(courseStats.publishedCourses) || 0,
      totalCourses: Number(courseStats.totalCourses) || 0
    };
  }

  /**
   * 获取系列下的课程列表
   */
  async getSeriesCourses(
    seriesId: number,
    status?: number,
    page: number = 1,
    pageSize: number = 20,
    userId?: number
  ) {
    // 检查系列是否存在
    const series = await this.findCourseSeriesById(seriesId);

    // 如果提供了userId，检查权限
    if (userId && series.creatorId !== userId) {
      throw new ForbiddenException('无权限查看此系列课程');
    }

    // 构建查询条件
    const where: any = { seriesId };

    if (status !== undefined) {
      where.status = status;
    }

    // 计算总数
    const total = await this.courseRepository.count({ where });

    // 分页查询
    const skip = (page - 1) * pageSize;
    const courses = await this.courseRepository.find({
      where,
      order: { orderIndex: 'ASC' },
      skip,
      take: pageSize
    });

    return {
      seriesId,
      courses,
      total,
      page,
      pageSize
    };
  }

  /**
   * 创建课程
   */
  async createCourse(courseData: any) {
    try {
      // 1.数据验证
      if (!courseData.title || courseData.title.trim() === '') {
        throw new BadRequestException('课程标题不能为空');
      }

      if (!courseData.seriesId) {
        throw new BadRequestException('必须指定所属系列ID');
      }

      // 2.检查系列是否存在
      await this.findCourseSeriesById(courseData.seriesId);

      // 3.获取课程序号
      // 获取课程的最大排序索引，为新添加的课程分配下一个排序号
      const maxOrderIndex = await this.courseRepository
        .createQueryBuilder('course')
        .select('MAX(course.order_index)', 'maxOrder')
        .where('course.series_id = :seriesId', { seriesId: courseData.seriesId })
        .getRawOne();

      const nextOrderIndex = (maxOrderIndex?.maxOrder || 0) + 1;

      // 4.使用事务处理多表操作
      return await this.courseRepository.manager.transaction(async manager => {
        // 4.1 创建新课程。create触发时间初始化， TypeORM 这是一个新实体，需要设置创建时间
        const newCourse = this.courseRepository.create({
          seriesId: courseData.seriesId,
          title: courseData.title,
          description: courseData.description,
          coverImage: courseData.coverImage,
          // 直接使用请求体中的字段，不再需要JSON解析
          hasVideo: courseData.hasVideo || 0,
          hasDocument: courseData.hasDocument || 0,
          hasAudio: courseData.hasAudio || 0,
          videoDuration: courseData.videoDuration || 0,
          contentConfig: courseData.contentConfig || {},
          teachingInfo: courseData.teachingInfo || [],
          additionalResources: courseData.additionalResources || [],
          // 双重保障，前端没传则后端查出来
          orderIndex: courseData.orderIndex || nextOrderIndex,
          status: 0, // 默认为草稿状态
          creatorId: courseData.creatorId
        });

        // 4.2 保存课程到数据库
        const savedCourse = await manager.save(Course, newCourse);

        // 4.3 更新系列课程数量
        await this.updateSeriesCourseCountWithManager(courseData.seriesId, manager);

        return savedCourse;
      });
    } catch (error) {
      if (error instanceof BadRequestException || error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`创建课程失败: ${error.message}`);
    }
  }

  /**
   * 更新系列的课程数量（原来没用事物用的他，现在不用了）
   */
  private async updateSeriesCourseCount(seriesId: number) {
    const courseCount = await this.courseRepository.count({ where: { seriesId } });
    await this.courseSeriesRepository.update(seriesId, { totalCourses: courseCount });
  }

  /**
   * 更新系列的课程数量（支持事务管理器）
   */
  private async updateSeriesCourseCountWithManager(seriesId: number, manager: any) {
    const courseCount = await manager.count(Course, { where: { seriesId } });
    await manager.update(CourseSeries, seriesId, { totalCourses: courseCount });
  }

  /**
   * 设置课程配置
   */
  async setCourseSettings(courseId: number, settingsData: any, userId: number) {
    // 1.查找课程
    const course = await this.courseRepository.findOne({ where: { id: courseId } });

    if (!course) {
      throw new NotFoundException(`课程ID为${courseId}的课程不存在`);
    }

    // 2.查找系列
    const series = await this.findCourseSeriesById(course.seriesId);

    // 3.验证权限，只有系列创建者才能设置课程配置
    if (series.creatorId !== userId) {
      throw new ForbiddenException('无权限设置此课程配置');
    }

    // 4.查找现有设置或创建新设置
    let settings = await this.courseSettingsRepository.findOne({ where: { courseId } });

    // 5.不存在就创建新设置，有就更新
    if (!settings) {
      settings = this.courseSettingsRepository.create({
        courseId,
        templateId: settingsData.templateId || null,
        requiredPoints: settingsData.requiredPoints || 0,
        autoCreateTasks: settingsData.autoCreateTasks !== undefined ? settingsData.autoCreateTasks : 0
      });
    } else {
      // 更新现有设置
      if (settingsData.templateId !== undefined) {
        settings.templateId = settingsData.templateId;
      }

      if (settingsData.requiredPoints !== undefined) {
        settings.requiredPoints = settingsData.requiredPoints;
      }

      if (settingsData.autoCreateTasks !== undefined) {
        settings.autoCreateTasks = settingsData.autoCreateTasks;
      }
    }

    try {
      // 6.保存到数据库
      await this.courseSettingsRepository.save(settings);

      return {
        success: true,
        message: '课程配置设置成功',
        settings
      };
    } catch (error) {
      // 捕获外键约束错误
      if (error.message && error.message.includes('foreign key constraint fails')) {
        throw new BadRequestException('设置失败：指定的模板ID不存在，请输入有效的模板ID');
      }
      throw error;
    }
  }

  /**
   * 添加任务模板
   */
  async addTaskTemplate(courseId: number, templateData: any, userId: number) {
    // 1.查找课程
    const course = await this.courseRepository.findOne({ where: { id: courseId } });

    if (!course) {
      throw new NotFoundException(`课程ID为${courseId}的课程不存在`);
    }

    // 2.查找系列
    const series = await this.findCourseSeriesById(course.seriesId);

    // 3.验证权限
    if (series.creatorId !== userId) {
      throw new ForbiddenException('无权限为此课程添加任务模板');
    }

    // 4.数据验证
    if (!templateData.taskName || templateData.taskName.trim() === '') {
      throw new BadRequestException('任务名称不能为空');
    }

    // 5.创建任务模板 - 不再手动设置生成列的值
    const newTemplate = this.taskTemplateRepository.create({
      courseId, // 使用路径参数中的courseId
      taskName: templateData.taskName,
      taskDescription: templateData.taskDescription,
      durationDays: templateData.durationDays || 7,
      attachments: templateData.attachments || [],
      workIdsStr: templateData.workIdsStr || '',
      selfAssessmentItems: templateData.selfAssessmentItems || [],
      status: 1 // 默认启用
    });

    // 保存到数据库
    return await this.taskTemplateRepository.save(newTemplate);
  }

  /**
   * 根据ID获取课程详情
   */
  async findCourseById(id: number, userId?: number): Promise<Course> {
    const course = await this.courseRepository.findOne({ where: { id } });

    if (!course) {
      throw new NotFoundException(`课程ID为${id}的课程不存在`);
    }

    // 如果提供了userId，检查权限
    if (userId) {
      const series = await this.findCourseSeriesById(course.seriesId);
      if (series.creatorId !== userId) {
        throw new ForbiddenException('无权限查看此课程');
      }
    }

    return course;
  }

  /**
   * 更新课程
   */
  async updateCourse(id: number, updateData: any, userId: number): Promise<Course> {
    // 查找现有课程
    const course = await this.findCourseById(id);

    // 查找系列并验证权限
    const series = await this.findCourseSeriesById(course.seriesId);
    if (series.creatorId !== userId) {
      throw new ForbiddenException('无权限更新此课程');
    }

    // 防止更新只读字段
    delete updateData.id;
    delete updateData.createdAt;
    delete updateData.updatedAt;
    delete updateData.creatorId;
    delete updateData.seriesId; // 不允许修改所属系列

    // 应用更新
    Object.assign(course, updateData);

    // 保存更新
    return await this.courseRepository.save(course);
  }

  /**
   * 删除课程
   */
  async removeCourse(id: number, userId: number): Promise<{ success: boolean; message: string }> {
    // 查找现有课程
    const course = await this.findCourseById(id);

    // 查找系列并验证权限
    const series = await this.findCourseSeriesById(course.seriesId);
    if (series.creatorId !== userId) {
      throw new ForbiddenException('无权限删除此课程');
    }

    // 使用事务处理删除操作
    return await this.courseRepository.manager.transaction(async manager => {
      // 删除课程
      await manager.remove(Course, course);

      // 更新系列课程数量
      await this.updateSeriesCourseCountWithManager(course.seriesId, manager);

      return {
        success: true,
        message: `ID为${id}的课程已成功删除`
      };
    });
  }

  /**
   * 调整课程排序
   */
  async updateCourseOrders(
    seriesId: number,
    courseOrders: Array<{ courseId: number; orderIndex: number }>,
    userId: number
  ): Promise<{ success: boolean; message: string }> {
    // 1.验证系列存在并检查权限
    const series = await this.findCourseSeriesById(seriesId);
    if (series.creatorId !== userId) {
      throw new ForbiddenException('无权限调整此系列课程排序');
    }

    // 2.验证所有课程ID是否属于该系列
    const courseIds = courseOrders.map(item => item.courseId);
    const courses = await this.courseRepository.find({
      where: { id: In(courseIds), seriesId }
    });

    if (courses.length !== courseIds.length) {
      throw new BadRequestException('部分课程ID不存在或不属于该系列');
    }

    // 3.使用事务批量更新排序
    await this.courseRepository.manager.transaction(async manager => {
      for (const order of courseOrders) {
        await manager.update(Course, { id: order.courseId }, { orderIndex: order.orderIndex });
      }
    });

    return {
      success: true,
      message: `系列${seriesId}的课程排序已成功更新`
    };
  }
}
