"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./lib/api/course-management.ts":
/*!**************************************!*\
  !*** ./lib/api/course-management.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   courseManagementApi: function() { return /* binding */ courseManagementApi; }\n/* harmony export */ });\n/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./request */ \"(app-pages-browser)/./lib/api/request.ts\");\n\n// 开发配置\nconst DEV_CONFIG = {\n    // 设置为true可以在开发环境中强制发送真实请求（即使API不存在也会在Network中看到请求）\n    FORCE_REAL_REQUESTS: true,\n    // 设置为true可以看到详细的调试日志\n    ENABLE_DEBUG_LOGS: true\n};\n// 课程管理API类\nclass CourseManagementApi {\n    // 创建系列课程\n    async createSeriesCourse(data) {\n        if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n            console.log(\"\\uD83D\\uDE80 开始创建系列课程\");\n            console.log(\"\\uD83D\\uDCDD 请求数据类型: JSON\");\n            console.log(\"\\uD83D\\uDCDD 请求数据:\", data);\n            console.log(\"\\uD83C\\uDF10 目标URL: /api/v1/course-management/series\");\n            console.log(\"⚙️ 环境:\", \"development\");\n        }\n        try {\n            // 只支持JSON格式\n            const config = {\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            };\n            console.log(\"\\uD83D\\uDCE4 发送请求配置:\", config);\n            // 发送真实请求（这样可以在Network面板中看到请求）\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.post(\"/api/v1/course-management/series\", data, config);\n            if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n                console.log(\"✅ 收到真实API响应:\", response.data);\n            }\n            return response.data;\n        } catch (error) {\n            if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n                console.error(\"❌ 创建系列课程请求失败:\", error);\n                if (error.response) {\n                    console.error(\"❌ 错误状态:\", error.response.status);\n                    console.error(\"❌ 错误数据:\", error.response.data);\n                    console.error(\"❌ 错误头:\", error.response.headers);\n                }\n            }\n            // 在开发环境中不返回模拟数据，让错误正常抛出\n            throw error;\n        }\n    }\n    // 获取我的系列课程列表\n    async getMySeries(params) {\n        if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n            console.log(\"\\uD83D\\uDE80 获取我的系列课程列表\");\n            console.log(\"\\uD83D\\uDCDD 请求参数:\", params);\n            console.log(\"\\uD83C\\uDF10 目标URL: /api/v1/course-management/my-series\");\n        }\n        try {\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.get(\"/api/v1/course-management/my-series\", {\n                params\n            });\n            if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n                console.log(\"✅ 收到系列课程列表响应:\", response.data);\n            }\n            return response.data;\n        } catch (error) {\n            if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n                console.error(\"❌ 获取系列课程列表失败:\", error);\n            }\n            throw error;\n        }\n    }\n    // 获取系列课程列表（保留原方法以兼容）\n    async getSeriesCourseList(params) {\n        return this.getMySeries({\n            page: params === null || params === void 0 ? void 0 : params.page,\n            pageSize: params === null || params === void 0 ? void 0 : params.size,\n            category: params === null || params === void 0 ? void 0 : params.category,\n            status: (params === null || params === void 0 ? void 0 : params.status) ? parseInt(params.status) : undefined\n        });\n    }\n    // 获取系列课程详情 - 使用课程市场API\n    async getSeriesCourseDetail(id) {\n        try {\n            // 使用课程市场API获取系列详情\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.get(\"/api/v1/course-marketplace/series/\".concat(id));\n            return response.data;\n        } catch (error) {\n            console.error(\"获取系列课程详情失败:\", error);\n            throw error;\n        }\n    }\n    // 更新系列课程\n    async updateSeriesCourse(id, data) {\n        try {\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.put(\"/api/v1/course-management/series/\".concat(id), data);\n            return response.data;\n        } catch (error) {\n            console.error(\"更新系列课程失败:\", error);\n            throw error;\n        }\n    }\n    // 删除系列课程\n    async deleteSeriesCourse(id) {\n        try {\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.delete(\"/api/v1/course-management/series/\".concat(id));\n            return response.data;\n        } catch (error) {\n            console.error(\"删除系列课程失败:\", error);\n            throw error;\n        }\n    }\n    // 获取系列下的课程列表 - 使用课程管理API\n    async getSeriesCourses(seriesId, params) {\n        if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n            console.log(\"\\uD83D\\uDE80 获取系列课程列表\");\n            console.log(\"\\uD83D\\uDCDD 系列ID:\", seriesId);\n            console.log(\"\\uD83D\\uDCDD 请求参数:\", params);\n            console.log(\"\\uD83C\\uDF10 目标URL: /api/v1/course-management/series/\" + seriesId + \"/courses\");\n        }\n        try {\n            // 使用课程管理API获取系列下的课程列表\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.get(\"/api/v1/course-management/series/\".concat(seriesId, \"/courses\"), {\n                params\n            });\n            if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n                console.log(\"✅ 收到系列课程列表响应:\", response.data);\n            }\n            return response.data;\n        } catch (error) {\n            if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n                console.error(\"❌ 获取系列课程列表失败:\", error);\n            }\n            throw error;\n        }\n    }\n}\n// 导出API实例\nconst courseManagementApi = new CourseManagementApi();\n/* harmony default export */ __webpack_exports__[\"default\"] = (courseManagementApi);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api/course-management.ts\n"));

/***/ })

});