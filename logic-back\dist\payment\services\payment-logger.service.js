"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PaymentLoggerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentLoggerService = void 0;
const common_1 = require("@nestjs/common");
const payment_log_service_1 = require("../../util/database/mysql/payment_log/payment-log.service");
const payment_log_dto_1 = require("../../util/database/mysql/payment_log/dto/payment-log.dto");
let PaymentLoggerService = PaymentLoggerService_1 = class PaymentLoggerService {
    paymentLogService;
    logger = new common_1.Logger(PaymentLoggerService_1.name);
    constructor(paymentLogService) {
        this.paymentLogService = paymentLogService;
    }
    async log(params) {
        try {
            const logDto = {
                logType: params.logType,
                operation: params.operation,
                orderNo: params.orderNo,
                refundNo: params.refundNo,
                paymentChannel: params.paymentChannel,
                operatorId: params.operatorId,
                clientIp: params.clientIp,
                requestData: params.requestData,
                responseData: params.responseData,
                status: params.status || payment_log_dto_1.LogStatus.SUCCESS,
                errorMessage: params.errorMessage,
                executionTime: params.executionTime,
            };
            await this.paymentLogService.create(logDto);
        }
        catch (error) {
            this.logger.error(`记录支付日志失败: ${error.message}`, error.stack);
        }
    }
    async logPaymentRequest(orderNo, channel, requestData, responseData, status, errorMessage, executionTime, operatorId, clientIp) {
        await this.log({
            logType: payment_log_dto_1.LogType.PAYMENT,
            operation: payment_log_dto_1.OperationType.CREATE,
            orderNo,
            paymentChannel: channel,
            operatorId,
            clientIp,
            requestData,
            responseData,
            status,
            errorMessage,
            executionTime,
        });
    }
    async logPaymentQuery(orderNo, channel, requestData, responseData, status, errorMessage, executionTime) {
        await this.log({
            logType: payment_log_dto_1.LogType.PAYMENT,
            operation: payment_log_dto_1.OperationType.QUERY,
            orderNo,
            paymentChannel: channel,
            requestData,
            responseData,
            status,
            errorMessage,
            executionTime,
        });
    }
    async logPaymentCallback(orderNo, channel, notifyData, result, status, errorMessage, executionTime, skipDetails = false) {
        await this.log({
            logType: payment_log_dto_1.LogType.PAYMENT,
            operation: payment_log_dto_1.OperationType.CALLBACK,
            orderNo,
            paymentChannel: channel,
            requestData: skipDetails ? { type: 'duplicate_notify_skipped' } : notifyData,
            responseData: skipDetails ? { skipped: true } : result,
            status,
            errorMessage,
            executionTime,
        });
    }
    async logRefundRequest(orderNo, refundNo, channel, requestData, responseData, status, errorMessage, executionTime) {
        await this.log({
            logType: payment_log_dto_1.LogType.REFUND,
            operation: payment_log_dto_1.OperationType.CREATE,
            orderNo,
            refundNo,
            paymentChannel: channel,
            requestData,
            responseData,
            status,
            errorMessage,
            executionTime,
        });
    }
    async logSystemOperation(operation, requestData, responseData, status, errorMessage, operatorId) {
        await this.log({
            logType: payment_log_dto_1.LogType.SYSTEM,
            operation: operation,
            operatorId,
            requestData,
            responseData,
            status,
            errorMessage,
        });
    }
};
exports.PaymentLoggerService = PaymentLoggerService;
exports.PaymentLoggerService = PaymentLoggerService = PaymentLoggerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [payment_log_service_1.PaymentLogService])
], PaymentLoggerService);
//# sourceMappingURL=payment-logger.service.js.map