<!DOCTYPE html>
<html>
<head>
    <title>课程保存功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-section { margin-bottom: 30px; padding: 20px; border: 1px solid #e8e8e8; border-radius: 8px; }
        .test-title { font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #333; }
        .course-form { display: grid; gap: 15px; }
        .form-group { display: flex; flex-direction: column; }
        .form-group label { margin-bottom: 5px; font-weight: 500; }
        .form-group input, .form-group textarea { padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; }
        .form-group textarea { min-height: 80px; resize: vertical; }
        .upload-section { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .upload-item { border: 2px dashed #d9d9d9; padding: 20px; text-align: center; border-radius: 6px; cursor: pointer; }
        .upload-item:hover { border-color: #1890ff; }
        .upload-item.uploading { border-color: #1890ff; background: #e6f7ff; }
        .upload-item.success { border-color: #52c41a; background: #f6ffed; }
        .upload-item.error { border-color: #ff4d4f; background: #fff2f0; }
        .file-info { margin-top: 10px; padding: 10px; background: #f5f5f5; border-radius: 4px; font-size: 12px; }
        .file-info.blob { background: #fff2f0; color: #ff4d4f; }
        .file-info.real { background: #f6ffed; color: #52c41a; }
        .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; font-size: 14px; }
        .btn-primary { background: #1890ff; color: white; }
        .btn-success { background: #52c41a; color: white; }
        .btn-danger { background: #ff4d4f; color: white; }
        .btn:disabled { background: #d9d9d9; cursor: not-allowed; }
        .status-bar { padding: 15px; background: #f8f9fa; border-radius: 6px; margin: 15px 0; }
        .status-item { display: inline-block; margin-right: 20px; }
        .status-badge { padding: 4px 8px; border-radius: 12px; font-size: 12px; margin-left: 5px; }
        .status-uploading { background: #e6f7ff; color: #1890ff; }
        .status-ready { background: #f6ffed; color: #52c41a; }
        .status-error { background: #fff2f0; color: #ff4d4f; }
        .log-area { background: #f5f5f5; padding: 15px; border-radius: 6px; font-family: monospace; font-size: 12px; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
        .api-response { background: #f8f9fa; padding: 15px; border-radius: 6px; margin-top: 15px; }
        .api-response.success { border-left: 4px solid #52c41a; }
        .api-response.error { border-left: 4px solid #ff4d4f; }
        .preview-image { max-width: 150px; max-height: 100px; border-radius: 4px; }
        .video-preview { width: 200px; height: 120px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>课程保存功能测试</h1>
        
        <div class="test-section">
            <div class="test-title">课程基本信息</div>
            <div class="course-form">
                <div class="form-group">
                    <label>课程标题</label>
                    <input type="text" id="courseTitle" placeholder="请输入课程标题" value="测试课程 - 新课时">
                </div>
                <div class="form-group">
                    <label>课程描述</label>
                    <textarea id="courseDescription" placeholder="请输入课程描述">这是一个测试课程的描述内容</textarea>
                </div>
                <div class="form-group">
                    <label>系列ID</label>
                    <input type="number" id="seriesId" value="123" readonly>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">文件上传</div>
            <div class="upload-section">
                <div class="upload-item" id="coverUpload" onclick="triggerCoverUpload()">
                    <div id="coverPreview">点击上传封面图片</div>
                    <div class="file-info" id="coverInfo" style="display: none;"></div>
                </div>
                <div class="upload-item" id="videoUpload" onclick="triggerVideoUpload()">
                    <div id="videoPreview">点击上传课程视频</div>
                    <div class="file-info" id="videoInfo" style="display: none;"></div>
                </div>
            </div>
            <input type="file" id="coverInput" accept="image/*" style="display: none;" onchange="handleCoverUpload(event)">
            <input type="file" id="videoInput" accept="video/*" style="display: none;" onchange="handleVideoUpload(event)">
        </div>
        
        <div class="test-section">
            <div class="test-title">保存状态</div>
            <div class="status-bar">
                <div class="status-item">
                    <span>上传状态:</span>
                    <span class="status-badge status-ready" id="uploadStatus">无上传任务</span>
                </div>
                <div class="status-item">
                    <span>课程状态:</span>
                    <span class="status-badge" id="courseStatus">待保存</span>
                </div>
            </div>
            <div>
                <button class="btn btn-success" id="saveBtn" onclick="handleSave()">保存课程</button>
                <button class="btn btn-primary" onclick="validateCourse()">验证数据</button>
                <button class="btn btn-danger" onclick="clearData()">清空数据</button>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">API响应</div>
            <div class="api-response" id="apiResponse" style="display: none;">
                <div id="responseContent"></div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">测试日志</div>
            <div class="log-area" id="testLog">等待操作...</div>
        </div>
    </div>

    <script>
        let uploadingFiles = new Set();
        let courseData = {
            title: '',
            description: '',
            coverImage: '',
            contentConfig: {
                video: { url: '', name: '' },
                document: { url: '', name: '' }
            },
            seriesId: 123
        };
        let testLogs = [];

        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            testLogs.push(`[${timestamp}] ${message}`);
            document.getElementById('testLog').textContent = testLogs.join('\n');
            console.log(message);
        }

        function updateUploadStatus() {
            const statusElement = document.getElementById('uploadStatus');
            const saveBtn = document.getElementById('saveBtn');
            
            if (uploadingFiles.size > 0) {
                statusElement.textContent = `上传中: ${Array.from(uploadingFiles).join(', ')}`;
                statusElement.className = 'status-badge status-uploading';
                saveBtn.disabled = true;
                saveBtn.textContent = '上传中...';
            } else {
                statusElement.textContent = '无上传任务';
                statusElement.className = 'status-badge status-ready';
                saveBtn.disabled = false;
                saveBtn.textContent = '保存课程';
            }
        }

        function updateCourseStatus(status, type = 'ready') {
            const statusElement = document.getElementById('courseStatus');
            statusElement.textContent = status;
            statusElement.className = `status-badge status-${type}`;
        }

        // 模拟OSS上传
        async function mockUploadToOss(file) {
            await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000));
            
            if (Math.random() < 0.1) {
                throw new Error('网络错误，上传失败');
            }
            
            const timestamp = Date.now();
            const randomStr = Math.random().toString(36).substring(7);
            const fileExtension = file.name.split('.').pop();
            return `https://logicleap.oss-cn-guangzhou.aliyuncs.com/uploads/${timestamp}-${randomStr}.${fileExtension}`;
        }

        // 检查blob URL
        function checkForBlobUrls() {
            const issues = [];
            
            if (courseData.coverImage && courseData.coverImage.startsWith('blob:')) {
                issues.push('课程封面图片');
            }
            
            if (courseData.contentConfig.video.url && courseData.contentConfig.video.url.startsWith('blob:')) {
                issues.push('课程视频');
            }
            
            return issues;
        }

        // 封面上传
        function triggerCoverUpload() {
            document.getElementById('coverInput').click();
        }

        async function handleCoverUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const uploadArea = document.getElementById('coverUpload');
            const preview = document.getElementById('coverPreview');
            const info = document.getElementById('coverInfo');

            try {
                addLog(`📤 开始上传封面图片: ${file.name}`);
                
                uploadingFiles.add('cover');
                updateUploadStatus();
                
                uploadArea.className = 'upload-item uploading';
                
                // 显示预览
                const previewUrl = URL.createObjectURL(file);
                preview.innerHTML = `<img src="${previewUrl}" class="preview-image" alt="封面预览">`;
                courseData.coverImage = previewUrl;
                
                info.style.display = 'block';
                info.className = 'file-info blob';
                info.textContent = `预览URL: ${previewUrl}`;
                
                // 上传到OSS
                const realUrl = await mockUploadToOss(file);
                
                // 更新为真实URL
                courseData.coverImage = realUrl;
                uploadArea.className = 'upload-item success';
                info.className = 'file-info real';
                info.textContent = `OSS URL: ${realUrl}`;
                
                addLog(`✅ 封面图片上传成功: ${realUrl}`);
                
            } catch (error) {
                addLog(`❌ 封面图片上传失败: ${error.message}`);
                uploadArea.className = 'upload-item error';
                info.className = 'file-info blob';
                info.textContent = `上传失败: ${error.message}`;
            } finally {
                uploadingFiles.delete('cover');
                updateUploadStatus();
            }
        }

        // 视频上传
        function triggerVideoUpload() {
            document.getElementById('videoInput').click();
        }

        async function handleVideoUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const uploadArea = document.getElementById('videoUpload');
            const preview = document.getElementById('videoPreview');
            const info = document.getElementById('videoInfo');

            try {
                addLog(`📤 开始上传视频文件: ${file.name}`);
                
                uploadingFiles.add('video');
                updateUploadStatus();
                
                uploadArea.className = 'upload-item uploading';
                
                // 显示预览
                const previewUrl = URL.createObjectURL(file);
                preview.innerHTML = `<video src="${previewUrl}" class="video-preview" controls></video>`;
                courseData.contentConfig.video = { url: previewUrl, name: file.name };
                
                info.style.display = 'block';
                info.className = 'file-info blob';
                info.textContent = `预览URL: ${previewUrl}`;
                
                // 上传到OSS
                const realUrl = await mockUploadToOss(file);
                
                // 更新为真实URL
                courseData.contentConfig.video = { url: realUrl, name: file.name };
                uploadArea.className = 'upload-item success';
                info.className = 'file-info real';
                info.textContent = `OSS URL: ${realUrl}`;
                
                addLog(`✅ 视频文件上传成功: ${realUrl}`);
                
            } catch (error) {
                addLog(`❌ 视频文件上传失败: ${error.message}`);
                uploadArea.className = 'upload-item error';
                info.className = 'file-info blob';
                info.textContent = `上传失败: ${error.message}`;
            } finally {
                uploadingFiles.delete('video');
                updateUploadStatus();
            }
        }

        // 验证课程数据
        function validateCourse() {
            addLog('🔍 开始验证课程数据...');
            
            // 更新课程数据
            courseData.title = document.getElementById('courseTitle').value;
            courseData.description = document.getElementById('courseDescription').value;
            courseData.seriesId = parseInt(document.getElementById('seriesId').value);
            
            addLog(`📝 课程标题: ${courseData.title}`);
            addLog(`📝 课程描述: ${courseData.description}`);
            addLog(`📝 系列ID: ${courseData.seriesId}`);
            addLog(`📝 封面图片: ${courseData.coverImage}`);
            addLog(`📝 视频文件: ${courseData.contentConfig.video.url}`);
            
            // 检查必填字段
            if (!courseData.title.trim()) {
                addLog('❌ 验证失败: 课程标题不能为空');
                updateCourseStatus('标题为空', 'error');
                return false;
            }
            
            // 检查blob URL
            const blobIssues = checkForBlobUrls();
            if (blobIssues.length > 0) {
                addLog(`❌ 验证失败: 以下文件尚未上传完成：${blobIssues.join('、')}`);
                updateCourseStatus('文件未上传', 'error');
                return false;
            }
            
            addLog('✅ 验证通过: 课程数据完整');
            updateCourseStatus('验证通过', 'ready');
            return true;
        }

        // 模拟API调用
        async function mockApiCall(data) {
            addLog('📤 发送POST请求到: /api/v1/course-management/courses');
            addLog(`📝 请求数据: ${JSON.stringify(data, null, 2)}`);
            
            // 模拟网络延迟
            await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
            
            // 模拟可能的API错误
            if (Math.random() < 0.1) {
                throw new Error('服务器内部错误');
            }
            
            // 模拟成功响应
            return {
                code: 200,
                message: '课程创建成功',
                data: {
                    id: 4291,
                    ...data,
                    status: 0,
                    statusLabel: '草稿',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                }
            };
        }

        // 保存课程
        async function handleSave() {
            try {
                // 检查上传状态
                if (uploadingFiles.size > 0) {
                    addLog(`❌ 保存失败: 有文件正在上传中 (${Array.from(uploadingFiles).join(', ')})`);
                    alert('有文件正在上传中，请等待上传完成后再保存');
                    return;
                }
                
                // 验证数据
                if (!validateCourse()) {
                    return;
                }
                
                addLog('💾 开始保存课程...');
                updateCourseStatus('保存中...', 'uploading');
                
                // 准备API数据
                const apiData = {
                    seriesId: courseData.seriesId,
                    title: courseData.title,
                    description: courseData.description,
                    coverImage: courseData.coverImage,
                    hasVideo: courseData.contentConfig.video.url ? 1 : 0,
                    hasDocument: 0,
                    hasAudio: 0,
                    videoDuration: 0,
                    contentConfig: courseData.contentConfig,
                    teachingInfo: [],
                    additionalResources: [],
                    orderIndex: 1
                };
                
                // 调用API
                const response = await mockApiCall(apiData);
                
                // 显示响应
                const responseDiv = document.getElementById('apiResponse');
                const responseContent = document.getElementById('responseContent');
                responseDiv.style.display = 'block';
                responseDiv.className = 'api-response success';
                responseContent.innerHTML = `
                    <h4>✅ 保存成功</h4>
                    <pre>${JSON.stringify(response, null, 2)}</pre>
                `;
                
                addLog(`✅ 课程保存成功: ID=${response.data.id}`);
                updateCourseStatus('保存成功', 'ready');
                
            } catch (error) {
                addLog(`❌ 保存失败: ${error.message}`);
                updateCourseStatus('保存失败', 'error');
                
                const responseDiv = document.getElementById('apiResponse');
                const responseContent = document.getElementById('responseContent');
                responseDiv.style.display = 'block';
                responseDiv.className = 'api-response error';
                responseContent.innerHTML = `
                    <h4>❌ 保存失败</h4>
                    <p>${error.message}</p>
                `;
            }
        }

        // 清空数据
        function clearData() {
            courseData = {
                title: '',
                description: '',
                coverImage: '',
                contentConfig: { video: { url: '', name: '' }, document: { url: '', name: '' } },
                seriesId: 123
            };
            
            document.getElementById('courseTitle').value = '';
            document.getElementById('courseDescription').value = '';
            document.getElementById('coverPreview').textContent = '点击上传封面图片';
            document.getElementById('videoPreview').textContent = '点击上传课程视频';
            document.getElementById('coverInfo').style.display = 'none';
            document.getElementById('videoInfo').style.display = 'none';
            document.getElementById('coverUpload').className = 'upload-item';
            document.getElementById('videoUpload').className = 'upload-item';
            document.getElementById('apiResponse').style.display = 'none';
            
            updateCourseStatus('待保存', 'ready');
            addLog('🗑️ 数据已清空');
        }

        // 初始化
        addLog('🎯 课程保存功能测试页面已加载');
        addLog('📋 测试流程:');
        addLog('  1. 填写课程基本信息');
        addLog('  2. 上传封面图片和视频文件');
        addLog('  3. 验证数据完整性');
        addLog('  4. 调用POST /api/v1/course-management/courses');
        updateUploadStatus();
        updateCourseStatus('待保存', 'ready');
    </script>
</body>
</html>
