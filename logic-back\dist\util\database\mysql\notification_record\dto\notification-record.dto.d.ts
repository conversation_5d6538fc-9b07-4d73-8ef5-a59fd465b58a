export declare enum NotificationType {
    PAYMENT_SUCCESS = "payment_success",
    PAYMENT_FAIL = "payment_fail",
    REFUND_SUCCESS = "refund_success",
    REFUND_FAIL = "refund_fail"
}
export declare enum NotificationStatus {
    PENDING = "pending",
    PROCESSING = "processing",
    SUCCESS = "success",
    FAILED = "failed",
    PROCESSED = "processed"
}
export declare class CreateNotificationRecordDto {
    notificationType: string;
    targetId: string;
    userId: string;
    content?: string;
    maxRetryCount?: number;
    status?: string;
}
export declare class UpdateNotificationRecordDto {
    status?: string;
    content?: string;
    errorMessage?: string;
    retryCount?: number;
    nextRetryTime?: Date;
}
export declare class QueryNotificationRecordDto {
    notificationType?: string;
    targetId?: string;
    userId?: string;
    status?: string;
}
