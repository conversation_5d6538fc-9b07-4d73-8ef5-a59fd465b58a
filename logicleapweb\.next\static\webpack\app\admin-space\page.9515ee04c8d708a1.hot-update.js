"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-space/page",{

/***/ "(app-pages-browser)/./app/admin-space/components/course-management.tsx":
/*!**********************************************************!*\
  !*** ./app/admin-space/components/course-management.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/popconfirm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/upload/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EditOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/InboxOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/UploadOutlined.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst { Search } = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nconst { Option } = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\nconst CourseManagement = ()=>{\n    var _publishSeriesListForModal_find, _publishCourseListForModal_find;\n    _s();\n    const [courseList, setCourseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCourseModalVisible, setIsCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddCourseModalVisible, setIsAddCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditCourseModalVisible, setIsEditCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddSeriesModalVisible, setIsAddSeriesModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddTagModalVisible, setIsAddTagModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishSeriesModalVisible, setIsPublishSeriesModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishCourseModalVisible, setIsPublishCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingCourse, setEditingCourse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchKeyword, setSearchKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [teachers, setTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseTags, setCourseTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coverImageUrl, setCoverImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 新增：系列课程和子课程管理相关状态\n    const [seriesList, setSeriesList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [seriesCoursesMap, setSeriesCoursesMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [expandedSeries, setExpandedSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [seriesLoading, setSeriesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 发布课程相关状态\n    const [selectedSeriesForPublish, setSelectedSeriesForPublish] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [selectedCourseForPublish, setSelectedCourseForPublish] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [publishSeriesCourses, setPublishSeriesCourses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [publishLoading, setPublishLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishSeriesOptions, setPublishSeriesOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 新的发布课程状态\n    const [publishSeriesListForModal, setPublishSeriesListForModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [publishCourseListForModal, setPublishCourseListForModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [publishFormLoading, setPublishFormLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [courseSeries, setCourseSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseCoverImageUrl, setCourseCoverImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [additionalFiles, setAdditionalFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseVideoUrl, setCourseVideoUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseVideoName, setCourseVideoName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseDocumentUrl, setCourseDocumentUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseDocumentName, setCourseDocumentName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoDuration, setVideoDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [addCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [editCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [addSeriesForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [addTagForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [publishSeriesForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [publishCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__.GetNotification)();\n    // 获取系列课程列表\n    const fetchSeriesList = async ()=>{\n        try {\n            var _res_data;\n            setSeriesLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取系列课程列表成功:\", res.data.list);\n                setSeriesList(res.data.list);\n            } else {\n                console.error(\"❌ 获取系列课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程列表失败，请重试\");\n        } finally{\n            setSeriesLoading(false);\n        }\n    };\n    // 获取指定系列下的子课程列表\n    const fetchSeriesCourses = async (seriesId)=>{\n        try {\n            var _res_data;\n            console.log(\"\\uD83D\\uDCDD 获取系列子课程列表，系列ID:\", seriesId);\n            // 使用课程管理API获取所有状态的课程\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getManagementSeriesCourses(seriesId, {\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取系列子课程列表成功:\", res.data.list);\n                setSeriesCoursesMap((prev)=>new Map(prev.set(seriesId, res.data.list)));\n                setExpandedSeries((prev)=>new Set(prev.add(seriesId)));\n            } else {\n                console.error(\"❌ 获取系列子课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取子课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列子课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取子课程列表失败，请重试\");\n        }\n    };\n    // 获取课程列表（保留原有功能）\n    const fetchCourseList = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDCDD 获取课程列表...\");\n            // 获取系列课程列表\n            await fetchSeriesList();\n        } catch (error) {\n            console.error(\"❌ 获取课程列表失败:\", error);\n            notification.error(\"获取课程列表失败，请重试\");\n        }\n    };\n    // 添加课程\n    const handleAddCourse = async (values)=>{\n        try {\n            // 构建内容配置，只包含有效的媒体文件\n            const contentConfig = {\n                hasVideo: courseVideoUrl ? 1 : 0,\n                hasDocument: courseDocumentUrl ? 1 : 0\n            };\n            if (courseVideoUrl) {\n                contentConfig.video = {\n                    url: courseVideoUrl,\n                    name: courseVideoName || \"课程视频.mp4\"\n                };\n            }\n            if (courseDocumentUrl) {\n                contentConfig.document = {\n                    url: courseDocumentUrl,\n                    name: courseDocumentName || \"课程文档.pdf\"\n                };\n            }\n            const courseData = {\n                seriesId: parseInt(values.seriesId),\n                title: values.title.trim(),\n                description: values.description.trim(),\n                coverImage: courseCoverImageUrl,\n                hasVideo: courseVideoUrl ? 1 : 0,\n                hasDocument: courseDocumentUrl ? 1 : 0,\n                hasAudio: courseAudioUrl ? 1 : 0,\n                videoDuration: videoDuration || 0,\n                contentConfig,\n                teachingInfo: values.teachingObjectives && values.teachingObjectives.length > 0 ? [\n                    {\n                        title: \"教学目标\",\n                        content: Array.isArray(values.teachingObjectives) ? values.teachingObjectives : [\n                            values.teachingObjectives\n                        ]\n                    }\n                ] : [],\n                additionalResources: additionalFiles.map((file)=>({\n                        title: file.split(\"/\").pop() || \"file\",\n                        url: file,\n                        description: \"课程附件资源\"\n                    })),\n                orderIndex: parseInt(values.orderIndex) || 0\n            };\n            // 验证必要字段\n            if (!courseData.seriesId) {\n                notification.error(\"请选择所属系列课程\");\n                return;\n            }\n            if (!courseData.title) {\n                notification.error(\"请输入课程名称\");\n                return;\n            }\n            if (!courseData.coverImage) {\n                notification.error(\"请上传课程封面\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDCE4 提交课程数据:\", courseData);\n            console.log(\"\\uD83D\\uDCCA 数据大小估算:\", JSON.stringify(courseData).length, \"字符\");\n            // 添加重试机制\n            let retryCount = 0;\n            const maxRetries = 2;\n            let lastError;\n            while(retryCount <= maxRetries){\n                try {\n                    const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourse(courseData);\n                    // 如果成功，跳出重试循环\n                    if (res.code === 200) {\n                        notification.success(\"创建课程成功\");\n                        fetchCourseList();\n                        setIsAddCourseModalVisible(false);\n                        addCourseForm.resetFields();\n                        setCourseCoverImageUrl(\"\");\n                        setAdditionalFiles([]);\n                        setCourseVideoUrl(\"\");\n                        setCourseVideoName(\"\");\n                        setCourseDocumentUrl(\"\");\n                        setCourseDocumentName(\"\");\n                        setCourseAudioUrl(\"\");\n                        setCourseAudioName(\"\");\n                        setVideoDuration(0);\n                        return;\n                    } else {\n                        notification.error(res.msg || \"创建课程失败\");\n                        return;\n                    }\n                } catch (error) {\n                    lastError = error;\n                    retryCount++;\n                    if (retryCount <= maxRetries) {\n                        console.log(\"\\uD83D\\uDD04 第\".concat(retryCount, \"次重试...\"));\n                        notification.warning(\"网络异常，正在重试 (\".concat(retryCount, \"/\").concat(maxRetries, \")\"));\n                        // 等待1秒后重试\n                        await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    }\n                }\n            }\n            // 如果所有重试都失败了，抛出最后的错误\n            throw lastError;\n        } catch (error) {\n            var _error_message, _error_response_data, _error_response, _error_response1, _error_response2, _error_response3, _error_response4, _error_response5;\n            console.error(\"❌ 创建课程失败:\", error);\n            // 更详细的错误处理\n            if (error.code === \"ECONNRESET\" || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"ECONNRESET\")) || ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) && error.response.data.message.includes(\"ECONNRESET\")) {\n                notification.error(\"网络连接中断，可能是网络不稳定或服务器繁忙。请稍后重试或联系管理员。\");\n            } else if (error.code === \"NETWORK_ERROR\") {\n                notification.error(\"网络错误，请检查网络连接\");\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 413) {\n                notification.error(\"上传文件过大，请压缩后重试\");\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 400) {\n                var _error_response_data1, _error_response6;\n                const errorMsg = ((_error_response6 = error.response) === null || _error_response6 === void 0 ? void 0 : (_error_response_data1 = _error_response6.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || error.message;\n                notification.error(\"请求参数错误: \".concat(errorMsg));\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 500) {\n                notification.error(\"服务器内部错误，请联系管理员\");\n            } else {\n                notification.error(\"创建课程失败: \".concat(error.message || \"请稍后重试\"));\n            }\n            console.log(\"\\uD83D\\uDD0D 完整错误信息:\", {\n                message: error.message,\n                code: error.code,\n                status: (_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status,\n                data: (_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.data\n            });\n        }\n    };\n    // 编辑课程\n    const handleEditCourse = async (values)=>{\n        if (!editingCourse) return;\n        try {\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.updateCourse(editingCourse.id, values);\n            if (res.code === 200) {\n                notification.success(\"更新课程成功\");\n                fetchCourseList();\n                setIsEditCourseModalVisible(false);\n                setEditingCourse(null);\n                editCourseForm.resetFields();\n            } else {\n                notification.error(res.msg || \"更新课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 更新课程失败:\", error);\n            notification.error(\"更新课程失败，请重试\");\n        }\n    };\n    // 删除课程\n    const handleDeleteCourse = async (courseId)=>{\n        try {\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.deleteCourse(courseId);\n            if (res.code === 200) {\n                notification.success(\"删除课程成功\");\n                fetchCourseList();\n            } else {\n                notification.error(res.msg || \"删除课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 删除课程失败:\", error);\n            notification.error(\"删除课程失败，请重试\");\n        }\n    };\n    // 删除子课程\n    const handleDeleteSubCourse = async (courseId, seriesId)=>{\n        try {\n            console.log(\"\\uD83D\\uDDD1️ 删除子课程，课程ID:\", courseId, \"系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.deleteCourse(courseId);\n            if (res.code === 200) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"删除子课程成功\");\n                console.log(\"✅ 子课程删除成功，重新获取系列子课程列表\");\n                // 重新获取该系列的子课程列表\n                await fetchSeriesCourses(seriesId);\n                console.log(\"\\uD83D\\uDD04 子课程列表已刷新\");\n            } else {\n                console.error(\"❌ 删除子课程失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"删除子课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 删除子课程异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"删除子课程失败，请重试\");\n        }\n    };\n    // 切换系列展开/收起状态\n    const toggleSeriesExpansion = async (seriesId)=>{\n        console.log(\"\\uD83D\\uDD04 切换系列展开状态，系列ID:\", seriesId);\n        console.log(\"\\uD83D\\uDCCA 当前展开状态:\", expandedSeries.has(seriesId));\n        if (expandedSeries.has(seriesId)) {\n            // 收起\n            console.log(\"\\uD83D\\uDCC1 收起系列:\", seriesId);\n            setExpandedSeries((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(seriesId);\n                return newSet;\n            });\n        } else {\n            // 展开，需要获取子课程数据\n            console.log(\"\\uD83D\\uDCC2 展开系列，获取子课程:\", seriesId);\n            await fetchSeriesCourses(seriesId);\n        }\n    };\n    // 展开所有系列\n    const expandAllSeries = async ()=>{\n        console.log(\"\\uD83D\\uDCC2 展开所有系列课程\");\n        for (const series of seriesList){\n            if (!expandedSeries.has(series.id)) {\n                await fetchSeriesCourses(series.id);\n            }\n        }\n    };\n    // 收起所有系列\n    const collapseAllSeries = ()=>{\n        console.log(\"\\uD83D\\uDCC1 收起所有系列课程\");\n        setExpandedSeries(new Set());\n    };\n    // 添加系列课程\n    const handleAddSeries = async (values)=>{\n        try {\n            const seriesData = {\n                ...values,\n                coverImage: coverImageUrl\n            };\n            console.log(\"创建系列课程数据:\", seriesData);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourseSeries(seriesData);\n            if (res.code === 200) {\n                notification.success(\"创建系列课程成功\");\n                fetchCourseList();\n                setIsAddSeriesModalVisible(false);\n                addSeriesForm.resetFields();\n                setCoverImageUrl(\"\");\n            } else {\n                notification.error(res.msg || \"创建系列课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 创建系列课程失败:\", error);\n            notification.error(\"创建系列课程失败，请重试\");\n        }\n    };\n    // 创建课程标签\n    const handleAddTag = async (values)=>{\n        try {\n            console.log(\"\\uD83C\\uDFF7️ 创建课程标签数据:\", values);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourseTag(values);\n            if (res.code === 200) {\n                notification.success(\"创建标签成功\");\n                setIsAddTagModalVisible(false);\n                addTagForm.resetFields();\n                // 重新获取标签列表\n                fetchCourseTags();\n            } else {\n                notification.error(res.msg || \"创建标签失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 创建标签失败:\", error);\n            notification.error(\"创建标签失败，请重试\");\n        }\n    };\n    // 发布系列课程\n    const handlePublishSeries = async (values)=>{\n        try {\n            console.log(\"\\uD83D\\uDCE2 发布系列课程数据:\", values);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourseSeries(values.seriesId);\n            if (res.code === 200) {\n                notification.success(\"发布系列课程成功\");\n                setIsPublishSeriesModalVisible(false);\n                publishSeriesForm.resetFields();\n                // 显示发布结果信息\n                const publishData = res.data;\n                console.log(\"✅ 发布成功，系列信息:\", publishData);\n                // 可以选择显示发布统计信息\n                if (publishData.publishStats) {\n                    const stats = publishData.publishStats;\n                    const statsMessage = \"已发布 \".concat(publishData.publishedCourses, \"/\").concat(publishData.totalCourses, \" 个课程，包含 \").concat(stats.videoCourseCount, \" 个视频课程，总时长 \").concat(Math.round(stats.totalVideoDuration / 60), \" 分钟\");\n                    notification.info(statsMessage);\n                }\n            } else {\n                notification.error(res.msg || \"发布系列课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 发布系列课程失败:\", error);\n            notification.error(\"发布系列课程失败，请重试\");\n        }\n    };\n    // 获取发布用的系列课程列表\n    const fetchSeriesForPublish = async ()=>{\n        try {\n            var _res_data;\n            setPublishLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取发布用系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取发布用系列课程列表成功:\", res.data.list);\n                return res.data.list;\n            } else {\n                console.error(\"❌ 获取发布用系列课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程列表失败\");\n                return [];\n            }\n        } catch (error) {\n            console.error(\"❌ 获取发布用系列课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程列表失败，请重试\");\n            return [];\n        } finally{\n            setPublishLoading(false);\n        }\n    };\n    // 获取指定系列的课程详情\n    const fetchSeriesDetailForPublish = async (seriesId)=>{\n        try {\n            setPublishLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取系列课程详情，系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeriesDetail(seriesId);\n            if (res.code === 200 && res.data) {\n                console.log(\"✅ 获取系列课程详情成功:\", res.data);\n                return res.data;\n            } else {\n                console.error(\"❌ 获取系列课程详情失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程详情失败\");\n                return null;\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列课程详情异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程详情失败，请重试\");\n            return null;\n        } finally{\n            setPublishLoading(false);\n        }\n    };\n    // 获取发布弹窗的系列课程列表\n    const fetchPublishSeriesList = async ()=>{\n        try {\n            var _res_data;\n            setPublishFormLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取发布弹窗的系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取发布弹窗系列课程列表成功:\", res.data.list);\n                setPublishSeriesListForModal(res.data.list);\n            } else {\n                console.error(\"❌ 获取发布弹窗系列课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取发布弹窗系列课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程列表失败，请重试\");\n        } finally{\n            setPublishFormLoading(false);\n        }\n    };\n    // 获取指定系列下的子课程列表（用于发布弹窗）\n    const fetchPublishCourseList = async (seriesId)=>{\n        try {\n            var _res_data;\n            console.log(\"\\uD83D\\uDCDD 获取发布弹窗的子课程列表，系列ID:\", seriesId);\n            console.log(\"\\uD83D\\uDD04 使用课程管理API获取草稿状态的子课程...\");\n            // 使用课程管理API获取草稿状态的课程\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getManagementSeriesCourses(seriesId, {\n                page: 1,\n                pageSize: 50,\n                status: 0 // 只获取草稿状态的课程\n            });\n            console.log(\"\\uD83D\\uDD0D 课程管理API响应:\", res);\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取草稿状态子课程列表成功，数量:\", res.data.list.length);\n                setPublishCourseListForModal(res.data.list);\n                if (res.data.list.length === 0) {\n                    _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"该系列暂无草稿状态的课程可发布\");\n                }\n            } else {\n                console.log(\"⚠️ 该系列暂无子课程或API调用失败\");\n                setPublishCourseListForModal([]);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"该系列暂无草稿状态的课程可发布\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取发布弹窗子课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取子课程列表失败，请重试\");\n            setPublishCourseListForModal([]);\n        }\n    };\n    // 处理系列选择（发布弹窗）\n    const handlePublishSeriesChange = async (seriesId)=>{\n        console.log(\"\\uD83D\\uDCDA 发布弹窗选择系列ID:\", seriesId);\n        console.log(\"\\uD83D\\uDCDA 当前系列列表:\", publishSeriesListForModal);\n        setSelectedSeriesForPublish(seriesId);\n        setSelectedCourseForPublish(undefined);\n        setPublishCourseListForModal([]);\n        // 重置表单中的课程选择\n        publishCourseForm.setFieldsValue({\n            courseId: undefined\n        });\n        // 获取该系列下的子课程\n        if (seriesId) {\n            console.log(\"\\uD83D\\uDD04 开始获取系列子课程...\");\n            setPublishFormLoading(true);\n            try {\n                await fetchPublishCourseList(seriesId);\n                console.log(\"✅ 子课程获取完成\");\n            } catch (error) {\n                console.error(\"❌ 子课程获取失败:\", error);\n            } finally{\n                setPublishFormLoading(false);\n            }\n        }\n    };\n    // 处理课程选择（发布弹窗）\n    const handlePublishCourseChange = (courseId)=>{\n        console.log(\"\\uD83D\\uDCD6 发布弹窗选择课程ID:\", courseId);\n        setSelectedCourseForPublish(courseId);\n    };\n    // 重置发布课程弹窗状态\n    const resetPublishCourseModal = ()=>{\n        setIsPublishCourseModalVisible(false);\n        setSelectedSeriesForPublish(undefined);\n        setSelectedCourseForPublish(undefined);\n        setPublishSeriesListForModal([]);\n        setPublishCourseListForModal([]);\n        publishCourseForm.resetFields();\n    };\n    // 打开发布课程弹窗\n    const openPublishCourseModal = async ()=>{\n        setIsPublishCourseModalVisible(true);\n        await fetchPublishSeriesList();\n    };\n    // 发布课程\n    const handlePublishCourse = async (values)=>{\n        try {\n            if (!selectedCourseForPublish || !selectedSeriesForPublish) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"请选择系列课程和子课程\");\n                return;\n            }\n            setPublishFormLoading(true);\n            console.log(\"\\uD83D\\uDCE2 发布课程，课程ID:\", selectedCourseForPublish);\n            console.log(\"\\uD83D\\uDCE2 系列ID:\", selectedSeriesForPublish);\n            console.log(\"\\uD83D\\uDCE4 表单数据:\", values);\n            // 获取当前选中的课程信息\n            const selectedCourse = publishCourseListForModal.find((c)=>c.id === selectedCourseForPublish);\n            if (!selectedCourse) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"未找到选中的课程信息\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDCD6 当前课程信息:\", selectedCourse);\n            // 使用专门的发布课程API\n            console.log(\"\\uD83D\\uDCE4 调用发布课程API，课程ID:\", selectedCourseForPublish);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourse(selectedCourseForPublish);\n            if (res.code === 200) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"发布课程成功\");\n                resetPublishCourseModal();\n                // 显示发布结果信息\n                console.log(\"✅ 发布成功，课程信息:\", res.data);\n                // 刷新课程列表\n                await fetchCourseList();\n                // 如果当前系列已展开，刷新子课程列表\n                if (selectedSeriesForPublish && expandedSeries.has(selectedSeriesForPublish)) {\n                    await fetchSeriesCourses(selectedSeriesForPublish);\n                }\n            } else {\n                console.error(\"❌ 发布课程失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"发布课程失败\");\n            }\n        } catch (error) {\n            var _error_response;\n            console.error(\"❌ 发布课程异常:\", error);\n            console.error(\"❌ 错误详情:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"发布课程失败，请重试\");\n        } finally{\n            setPublishFormLoading(false);\n        }\n    };\n    // 重置发布弹窗状态\n    const resetPublishModal = ()=>{\n        setIsPublishCourseModalVisible(false);\n        setSelectedSeriesForPublish(undefined);\n        setSelectedCourseForPublish(undefined);\n        setPublishSeriesCourses([]);\n        setPublishSeriesOptions([]);\n        publishCourseForm.resetFields();\n    };\n    // 处理图片上传\n    const handleImageUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"系列封面图片上传成功，URL:\", url);\n            setCoverImageUrl(url);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"图片上传成功\");\n        } catch (error) {\n            console.error(\"系列封面图片上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理图片删除\n    const handleImageRemove = async ()=>{\n        setCoverImageUrl(\"\");\n        return true;\n    };\n    // 处理课程封面图片上传\n    const handleCourseCoverUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程封面图片上传成功，URL:\", url);\n            setCourseCoverImageUrl(url);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程封面上传成功\");\n        } catch (error) {\n            console.error(\"课程封面图片上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理课程封面删除\n    const handleCourseCoverRemove = async ()=>{\n        setCourseCoverImageUrl(\"\");\n        return true;\n    };\n    // 处理附件资源上传\n    const handleAdditionalResourceUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"附件资源上传成功，URL:\", url);\n            setAdditionalFiles((prev)=>[\n                    ...prev,\n                    url\n                ]);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url,\n                name: file.name\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"附件 \".concat(file.name, \" 上传成功\"));\n        } catch (error) {\n            console.error(\"附件资源上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"附件 \".concat(file.name, \" 上传失败: \").concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理附件删除\n    const handleAdditionalResourceRemove = async (file)=>{\n        var _file_response;\n        const url = file.url || ((_file_response = file.response) === null || _file_response === void 0 ? void 0 : _file_response.url);\n        setAdditionalFiles((prev)=>prev.filter((f)=>f !== url));\n        return true;\n    };\n    // 处理视频上传\n    const handleVideoUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程视频上传成功，URL:\", url);\n            setCourseVideoUrl(url);\n            setCourseVideoName(file.name);\n            // 如果是视频文件，尝试获取时长\n            const videoElement = document.createElement(\"video\");\n            videoElement.src = url;\n            videoElement.onloadedmetadata = ()=>{\n                setVideoDuration(Math.floor(videoElement.duration));\n            };\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程视频上传成功\");\n        } catch (error) {\n            console.error(\"课程视频上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"视频上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理视频删除\n    const handleVideoRemove = async ()=>{\n        setCourseVideoUrl(\"\");\n        setCourseVideoName(\"\");\n        setVideoDuration(0);\n        return true;\n    };\n    // 处理文档上传\n    const handleDocumentUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程文档上传成功，URL:\", url);\n            setCourseDocumentUrl(url);\n            setCourseDocumentName(file.name);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程文档上传成功\");\n        } catch (error) {\n            console.error(\"课程文档上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"文档上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理文档删除\n    const handleDocumentRemove = async ()=>{\n        setCourseDocumentUrl(\"\");\n        setCourseDocumentName(\"\");\n        return true;\n    };\n    // 处理音频上传\n    const handleAudioUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程音频上传成功，URL:\", url);\n            setCourseAudioUrl(url);\n            setCourseAudioName(file.name);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程音频上传成功\");\n        } catch (error) {\n            console.error(\"课程音频上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"音频上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理音频删除\n    const handleAudioRemove = async ()=>{\n        setCourseAudioUrl(\"\");\n        setCourseAudioName(\"\");\n        return true;\n    };\n    // 打开编辑模态框\n    const openEditModal = async (course)=>{\n        setEditingCourse(course);\n        editCourseForm.setFieldsValue(course);\n        setIsEditCourseModalVisible(true);\n    };\n    // 过滤课程列表\n    const filteredCourses = (courseList || []).filter((course)=>course.name.toLowerCase().includes(searchKeyword.toLowerCase()) || course.description.toLowerCase().includes(searchKeyword.toLowerCase()) || course.category.toLowerCase().includes(searchKeyword.toLowerCase()));\n    // 准备表格数据：将系列课程和子课程合并为一个扁平列表\n    const prepareTableData = ()=>{\n        const tableData = [];\n        console.log(\"\\uD83D\\uDD04 准备表格数据...\");\n        console.log(\"\\uD83D\\uDCCA 系列课程列表:\", seriesList);\n        console.log(\"\\uD83D\\uDCCA 展开的系列:\", Array.from(expandedSeries));\n        console.log(\"\\uD83D\\uDCCA 子课程映射:\", seriesCoursesMap);\n        seriesList.forEach((series)=>{\n            // 添加系列课程行\n            tableData.push({\n                key: \"series-\".concat(series.id),\n                id: series.id,\n                title: series.title,\n                status: series.status,\n                type: \"series\",\n                isExpanded: expandedSeries.has(series.id),\n                seriesId: series.id\n            });\n            // 如果系列已展开，添加子课程行\n            if (expandedSeries.has(series.id)) {\n                const subCourses = seriesCoursesMap.get(series.id) || [];\n                console.log(\"\\uD83D\\uDCDA 系列 \".concat(series.id, \" 的子课程:\"), subCourses);\n                subCourses.forEach((course)=>{\n                    tableData.push({\n                        key: \"course-\".concat(course.id),\n                        id: course.id,\n                        title: course.title,\n                        status: course.status,\n                        type: \"course\",\n                        seriesId: series.id,\n                        parentSeriesTitle: series.title\n                    });\n                });\n            }\n        });\n        console.log(\"\\uD83D\\uDCCB 最终表格数据:\", tableData);\n        return tableData;\n    };\n    // 表格列定义\n    const columns = [\n        {\n            title: \"系列课程ID\",\n            dataIndex: \"id\",\n            key: \"id\",\n            width: 120\n        },\n        {\n            title: \"系列课程/子课程名称\",\n            dataIndex: \"title\",\n            key: \"title\",\n            render: (text, record)=>{\n                if (record.type === \"series\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                type: \"text\",\n                                size: \"small\",\n                                onClick: ()=>toggleSeriesExpansion(record.id),\n                                className: \"p-0 min-w-0 hover:bg-blue-50\",\n                                style: {\n                                    minWidth: \"20px\",\n                                    height: \"20px\"\n                                },\n                                children: record.isExpanded ? \"▼\" : \"▶\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 938,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-blue-600 text-base\",\n                                children: text\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 947,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                color: \"blue\",\n                                className: \"text-xs\",\n                                children: \"系列\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 948,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 937,\n                        columnNumber: 13\n                    }, undefined);\n                } else {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-8 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400\",\n                                children: \"└─\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 954,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-700\",\n                                children: text\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 955,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                color: \"green\",\n                                className: \"text-xs\",\n                                children: \"子课程\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 956,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 953,\n                        columnNumber: 13\n                    }, undefined);\n                }\n            }\n        },\n        {\n            title: \"发布状态\",\n            dataIndex: \"status\",\n            key: \"status\",\n            width: 100,\n            render: (status, record)=>{\n                const getStatusConfig = (status)=>{\n                    switch(status){\n                        case 1:\n                            return {\n                                color: \"green\",\n                                text: \"已发布\"\n                            };\n                        case 0:\n                            return {\n                                color: \"orange\",\n                                text: \"草稿\"\n                            };\n                        case 2:\n                            return {\n                                color: \"red\",\n                                text: \"已归档\"\n                            };\n                        default:\n                            return {\n                                color: \"gray\",\n                                text: \"未知\"\n                            };\n                    }\n                };\n                const config = getStatusConfig(status);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    color: config.color,\n                    children: config.text\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 978,\n                    columnNumber: 16\n                }, undefined);\n            }\n        },\n        {\n            title: \"操作\",\n            key: \"action\",\n            width: 150,\n            render: (record)=>{\n                if (record.type === \"series\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            type: \"link\",\n                            size: \"small\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 992,\n                                columnNumber: 23\n                            }, void 0),\n                            onClick: ()=>{\n                                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"系列课程编辑功能待实现\");\n                            },\n                            children: \"编辑\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 989,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 988,\n                        columnNumber: 13\n                    }, undefined);\n                } else {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        size: \"small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                type: \"link\",\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1007,\n                                    columnNumber: 23\n                                }, void 0),\n                                onClick: ()=>{\n                                    _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"子课程编辑功能待实现\");\n                                },\n                                className: \"text-blue-600 hover:text-blue-800\",\n                                children: \"编辑\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1004,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"确定要删除这个子课程吗？\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1018,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: [\n                                                \"课程名称：\",\n                                                record.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1019,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: [\n                                                \"所属系列：\",\n                                                record.parentSeriesTitle\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1020,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1017,\n                                    columnNumber: 19\n                                }, void 0),\n                                onConfirm: ()=>{\n                                    console.log(\"\\uD83D\\uDDD1️ 用户确认删除子课程:\", record);\n                                    handleDeleteSubCourse(record.id, record.seriesId);\n                                },\n                                okText: \"确定删除\",\n                                cancelText: \"取消\",\n                                okType: \"danger\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    type: \"link\",\n                                    size: \"small\",\n                                    danger: true,\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1035,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    className: \"text-red-600 hover:text-red-800\",\n                                    children: \"删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1031,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1015,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1003,\n                        columnNumber: 13\n                    }, undefined);\n                }\n            }\n        }\n    ];\n    // 获取教师列表\n    // const fetchTeachers = async () => {\n    //   try {\n    //     const { data: res } = await courseApi.getTeachers();\n    //     if (res.code === 200) {\n    //       setTeachers(res.data);\n    //       console.log('成功获取教师列表:', res.data);\n    //     } else {\n    //       console.log('API返回无数据，使用模拟教师数据');\n    //       // 使用模拟数据\n    //       const mockTeachers = [\n    //         { id: 1, name: '张老师', email: '<EMAIL>', subject: '数学', school: '实验小学', avatar: '', phone: '13800138001' },\n    //         { id: 2, name: '李老师', email: '<EMAIL>', subject: '语文', school: '实验小学', avatar: '', phone: '13800138002' },\n    //         { id: 3, name: '王老师', email: '<EMAIL>', subject: '英语', school: '第二小学', avatar: '', phone: '13800138003' },\n    //         { id: 4, name: '赵老师', email: '<EMAIL>', subject: '科学', school: '第二小学', avatar: '', phone: '13800138004' },\n    //         { id: 5, name: '刘老师', email: '<EMAIL>', subject: '编程', school: '实验中学', avatar: '', phone: '13800138005' },\n    //         { id: 6, name: '陈老师', email: '<EMAIL>', subject: '信息技术', school: '实验中学', avatar: '', phone: '13800138006' }\n    //       ];\n    //       setTeachers(mockTeachers);\n    //     }\n    //   } catch (error) {\n    //     console.error('获取教师列表失败:', error);\n    //     // 使用模拟数据\n    //     const mockTeachers = [\n    //       { id: 1, name: '张老师', email: '<EMAIL>', subject: '数学', school: '实验小学', avatar: '', phone: '13800138001' },\n    //       { id: 2, name: '李老师', email: '<EMAIL>', subject: '语文', school: '实验小学', avatar: '', phone: '13800138002' },\n    //       { id: 3, name: '王老师', email: '<EMAIL>', subject: '英语', school: '第二小学', avatar: '', phone: '13800138003' },\n    //       { id: 4, name: '赵老师', email: '<EMAIL>', subject: '科学', school: '第二小学', avatar: '', phone: '13800138004' },\n    //       { id: 5, name: '刘老师', email: '<EMAIL>', subject: '编程', school: '实验中学', avatar: '', phone: '13800138005' },\n    //       { id: 6, name: '陈老师', email: '<EMAIL>', subject: '信息技术', school: '实验中学', avatar: '', phone: '13800138006' }\n    //     ];\n    //     setTeachers(mockTeachers);\n    //     console.log('使用模拟教师数据:', mockTeachers);\n    //   }\n    // };\n    // 获取课程标签列表 - 使用课程市场API\n    const fetchCourseTags = async ()=>{\n        try {\n            console.log(\"\\uD83C\\uDFF7️ 开始获取课程标签列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseTags({\n                page: 1,\n                pageSize: 100,\n                status: 1 // 只获取启用的标签\n            });\n            console.log(\"\\uD83D\\uDCE8 getCourseTags API响应:\", res);\n            if (res.code === 200 && res.data && res.data.list) {\n                const tags = res.data.list.map((tag)=>({\n                        id: tag.id,\n                        name: tag.name,\n                        color: tag.color,\n                        category: tag.category,\n                        description: tag.description || \"\"\n                    }));\n                setCourseTags(tags);\n                console.log(\"✅ 成功获取课程标签列表:\", tags);\n            } else {\n                console.warn(\"⚠️ API返回数据格式异常:\", res);\n                setCourseTags([]);\n                notification.warning(\"获取标签列表失败，请检查网络连接\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取课程标签失败:\", error);\n            setCourseTags([]);\n            notification.error(\"获取标签列表失败，请重试\");\n        }\n    };\n    // 获取课程系列列表 - 使用课程市场API\n    const fetchCourseSeries = async ()=>{\n        try {\n            var _res_data_pagination, _res_data;\n            console.log(\"\\uD83D\\uDD04 开始获取课程市场系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50 // 课程市场API限制最大50\n            });\n            console.log(\"\\uD83D\\uDCE8 getMarketplaceSeries API响应:\", res);\n            // 检查是否有更多数据\n            if (((_res_data = res.data) === null || _res_data === void 0 ? void 0 : (_res_data_pagination = _res_data.pagination) === null || _res_data_pagination === void 0 ? void 0 : _res_data_pagination.total) > 50) {\n                console.log(\"⚠️ 注意：总共有 \".concat(res.data.pagination.total, \" 个系列课程，当前只显示前50个\"));\n            }\n            if (res.code === 200 && res.data) {\n                console.log(\"\\uD83D\\uDCCA API返回的完整数据结构:\", res.data);\n                if (res.data.list && Array.isArray(res.data.list)) {\n                    console.log(\"\\uD83D\\uDCCB 获取到 \".concat(res.data.list.length, \" 个系列课程\"));\n                    // 将课程市场API返回的数据转换为组件需要的格式\n                    const formattedSeries = res.data.list.map((item, index)=>{\n                        var _item_tags;\n                        console.log(\"\\uD83D\\uDD0D 处理第 \".concat(index + 1, \" 个系列:\"), {\n                            id: item.id,\n                            title: item.title,\n                            category: item.category,\n                            categoryLabel: item.categoryLabel,\n                            tags: item.tags\n                        });\n                        return {\n                            id: item.id,\n                            title: item.title,\n                            description: item.description,\n                            coverImage: item.coverImage || \"\",\n                            category: item.categoryLabel || (item.category === 0 ? \"官方\" : \"社区\"),\n                            teacherIds: [],\n                            tagIds: ((_item_tags = item.tags) === null || _item_tags === void 0 ? void 0 : _item_tags.map((tag)=>tag.id)) || [],\n                            createdAt: item.createdAt || new Date().toISOString(),\n                            updatedAt: item.updatedAt || new Date().toISOString()\n                        };\n                    });\n                    setCourseSeries(formattedSeries);\n                    console.log(\"✅ 成功获取系列课程列表:\", formattedSeries);\n                } else {\n                    console.warn(\"⚠️ API返回数据中没有list字段或list不是数组:\", res.data);\n                    setCourseSeries([]);\n                }\n            } else {\n                console.warn(\"⚠️ API返回数据格式异常:\", {\n                    code: res.code,\n                    message: res.message,\n                    data: res.data\n                });\n                setCourseSeries([]);\n            }\n        } catch (error) {\n            console.error(\"❌ 获取课程系列失败:\", error);\n            setCourseSeries([]);\n            notification.error(\"获取系列课程列表失败，请重试\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchCourseList();\n        fetchCourseTags();\n        fetchCourseSeries();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                title: \"课程管理\",\n                extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    type: \"primary\",\n                    onClick: ()=>{\n                        fetchCourseList();\n                        setIsCourseModalVisible(true);\n                    },\n                    children: \"查看全部\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1196,\n                    columnNumber: 16\n                }, void 0),\n                className: \"shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddCourseModalVisible(true),\n                            children: \"添加课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1203,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddSeriesModalVisible(true),\n                            children: \"添加系列课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1206,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddTagModalVisible(true),\n                            type: \"dashed\",\n                            children: \"添加课程标签\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1209,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: openPublishCourseModal,\n                            style: {\n                                backgroundColor: \"white\",\n                                borderColor: \"#d9d9d9\",\n                                color: \"#000000d9\"\n                            },\n                            children: \"发布课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1212,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsPublishSeriesModalVisible(true),\n                            style: {\n                                backgroundColor: \"white\",\n                                borderColor: \"#d9d9d9\",\n                                color: \"#000000d9\"\n                            },\n                            children: \"发布系列课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1215,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1202,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1194,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"课程管理\",\n                open: isCourseModalVisible,\n                onCancel: ()=>setIsCourseModalVisible(false),\n                footer: null,\n                width: 1000,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Search, {\n                                        placeholder: \"搜索系列课程名称\",\n                                        allowClear: true,\n                                        style: {\n                                            width: 300\n                                        },\n                                        onSearch: setSearchKeyword,\n                                        onChange: (e)=>setSearchKeyword(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1231,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                type: \"primary\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1241,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                onClick: ()=>setIsAddCourseModalVisible(true),\n                                                children: \"添加课程\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1239,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                type: \"default\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1248,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                onClick: ()=>setIsAddSeriesModalVisible(true),\n                                                children: \"添加系列课程\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1246,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1238,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1230,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center bg-gray-50 p-3 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-4 text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"系列课程总数: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"text-blue-600\",\n                                                        children: seriesList.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                        lineNumber: 1259,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1259,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"已展开系列: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"text-green-600\",\n                                                        children: expandedSeries.size\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                        lineNumber: 1260,\n                                                        columnNumber: 28\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1260,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"已加载子课程: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"text-orange-600\",\n                                                        children: Array.from(seriesCoursesMap.values()).reduce((total, courses)=>total + courses.length, 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                        lineNumber: 1261,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1261,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1258,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: \"small\",\n                                                type: \"text\",\n                                                onClick: expandAllSeries,\n                                                disabled: seriesLoading,\n                                                className: \"text-blue-600 hover:text-blue-800\",\n                                                children: \"展开所有\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1267,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: \"small\",\n                                                type: \"text\",\n                                                onClick: collapseAllSeries,\n                                                disabled: seriesLoading,\n                                                className: \"text-gray-600 hover:text-gray-800\",\n                                                children: \"收起所有\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1276,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1266,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1257,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1229,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        columns: columns,\n                        dataSource: prepareTableData(),\n                        rowKey: \"key\",\n                        loading: seriesLoading,\n                        pagination: {\n                            pageSize: 20,\n                            showSizeChanger: false,\n                            showTotal: (total)=>\"共 \".concat(total, \" 条记录\")\n                        },\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1289,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1222,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"添加课程\",\n                open: isAddCourseModalVisible,\n                onCancel: ()=>{\n                    setIsAddCourseModalVisible(false);\n                    addCourseForm.resetFields();\n                    setCourseCoverImageUrl(\"\");\n                    setAdditionalFiles([]);\n                    setCourseVideoUrl(\"\");\n                    setCourseVideoName(\"\");\n                    setCourseDocumentUrl(\"\");\n                    setCourseDocumentName(\"\");\n                    setCourseAudioUrl(\"\");\n                    setCourseAudioName(\"\");\n                    setVideoDuration(0);\n                },\n                onOk: ()=>addCourseForm.submit(),\n                okText: \"确定\",\n                cancelText: \"取消\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddCourse,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"所属系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择所属系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                showSearch: true,\n                                optionFilterProp: \"children\",\n                                style: {\n                                    width: \"100%\"\n                                },\n                                children: courseSeries.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        title: \"\".concat(series.title, \" - \").concat(series.description),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                overflow: \"hidden\",\n                                                textOverflow: \"ellipsis\",\n                                                whiteSpace: \"nowrap\",\n                                                maxWidth: \"100%\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontWeight: 500\n                                                    },\n                                                    children: series.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1348,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"12px\",\n                                                        color: \"#666\",\n                                                        marginLeft: \"8px\"\n                                                    },\n                                                    children: [\n                                                        \"(\",\n                                                        series.category,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1349,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1342,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, series.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1341,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1334,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1329,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"title\",\n                            label: \"课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程名称\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1363,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1358,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程描述\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程描述\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 4,\n                                placeholder: \"请详细描述课程内容、目标和特色...\",\n                                showCount: true,\n                                maxLength: 500\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1371,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1366,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程封面\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请上传课程封面\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseCover\",\n                                customRequest: handleCourseCoverUpload,\n                                onRemove: handleCourseCoverRemove,\n                                accept: \"image/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseCoverImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: courseCoverImageUrl,\n                                        alt: \"课程封面预览\",\n                                        style: {\n                                            width: \"100%\",\n                                            maxHeight: \"200px\",\n                                            objectFit: \"cover\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1393,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1392,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1398,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1397,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1400,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持单个文件上传，建议上传jpg、png格式图片，大小不超过2MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1401,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1396,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1383,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1379,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"orderIndex\",\n                            label: \"课程序号\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程序号\"\n                                },\n                                {\n                                    type: \"number\",\n                                    min: 0,\n                                    message: \"课程序号必须大于等于0\",\n                                    transform: (value)=>Number(value)\n                                }\n                            ],\n                            tooltip: \"在系列课程中的排序位置，数字越小排序越靠前，从0开始\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                type: \"number\",\n                                placeholder: \"请输入课程在系列中的序号（从0开始）\",\n                                min: 0\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1423,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1409,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程视频\",\n                            tooltip: \"上传课程视频文件，系统将自动识别时长等信息\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseVideo\",\n                                customRequest: handleVideoUpload,\n                                onRemove: handleVideoRemove,\n                                accept: \"video/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseVideoUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            src: courseVideoUrl,\n                                            style: {\n                                                width: \"100%\",\n                                                maxHeight: \"200px\"\n                                            },\n                                            controls: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1443,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                marginTop: 8,\n                                                color: \"#666\"\n                                            },\n                                            children: courseVideoName || \"课程视频\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1448,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1442,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1455,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1454,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽视频文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1457,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持MP4、AVI、MOV等格式，大小不超过100MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1458,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1453,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1433,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1429,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程文档\",\n                            tooltip: \"上传课程相关文档，如PPT、PDF等\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseDocument\",\n                                customRequest: handleDocumentUpload,\n                                onRemove: handleDocumentRemove,\n                                accept: \".pdf,.doc,.docx,.ppt,.pptx\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseDocumentUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"20px\",\n                                            textAlign: \"center\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                style: {\n                                                    fontSize: \"48px\",\n                                                    color: \"#1890ff\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1482,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    marginTop: 8,\n                                                    color: \"#666\"\n                                                },\n                                                children: courseDocumentName || \"课程文档\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1483,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1481,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1480,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1491,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1490,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文档文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1493,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持PDF、Word、PPT格式，大小不超过50MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1494,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1489,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1471,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1467,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程音频\",\n                            tooltip: \"上传课程音频文件\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseAudio\",\n                                customRequest: handleAudioUpload,\n                                onRemove: handleAudioRemove,\n                                accept: \"audio/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseAudioUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                                            src: courseAudioUrl,\n                                            style: {\n                                                width: \"100%\"\n                                            },\n                                            controls: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1517,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                marginTop: 8,\n                                                color: \"#666\"\n                                            },\n                                            children: courseAudioName || \"课程音频\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1522,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1516,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1529,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1528,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽音频文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1531,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持MP3、WAV、AAC等格式，大小不超过50MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1532,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1527,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1507,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1503,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"teachingObjectives\",\n                            label: \"教学目标\",\n                            tooltip: \"学员完成本课程后应该达到的学习目标\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                mode: \"tags\",\n                                placeholder: \"理解Node.js的基本概念和特点，掌握Node.js的安装和环境配置\",\n                                style: {\n                                    width: \"100%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1545,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1540,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"附件资源\",\n                            tooltip: \"上传课程相关的附件资源，如PPT、文档、代码等\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    name: \"additionalResources\",\n                                    customRequest: handleAdditionalResourceUpload,\n                                    onRemove: handleAdditionalResourceRemove,\n                                    multiple: true,\n                                    accept: \".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.zip,.rar,.txt\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1563,\n                                            columnNumber: 29\n                                        }, void 0),\n                                        children: \"上传附件资源\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1563,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1556,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: \"12px\",\n                                        color: \"#666\",\n                                        marginTop: 4\n                                    },\n                                    children: \"支持上传PDF、Office文档、压缩包等格式文件，单个文件不超过10MB\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1565,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1552,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1324,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1304,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"编辑课程\",\n                open: isEditCourseModalVisible,\n                onCancel: ()=>{\n                    setIsEditCourseModalVisible(false);\n                    setEditingCourse(null);\n                    editCourseForm.resetFields();\n                },\n                onOk: ()=>editCourseForm.submit(),\n                okText: \"确定\",\n                cancelText: \"取消\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: editCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handleEditCourse,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"name\",\n                            label: \"课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程名称\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1595,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1590,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程描述\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程描述\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 3,\n                                placeholder: \"请输入课程描述\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1603,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1598,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"课程分类\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择课程分类\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择课程分类\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"编程基础\",\n                                        children: \"编程基础\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1612,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"编程进阶\",\n                                        children: \"编程进阶\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1613,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"算法思维\",\n                                        children: \"算法思维\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1614,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"项目实战\",\n                                        children: \"项目实战\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1615,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1611,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1606,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"status\",\n                            label: \"课程状态\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择课程状态\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"active\",\n                                        children: \"启用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1625,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"inactive\",\n                                        children: \"禁用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1626,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1624,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1619,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1585,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1573,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"创建系列课程\",\n                open: isAddSeriesModalVisible,\n                onCancel: ()=>{\n                    setIsAddSeriesModalVisible(false);\n                    addSeriesForm.resetFields();\n                    setCoverImageUrl(\"\");\n                },\n                onOk: ()=>addSeriesForm.submit(),\n                okText: \"创建系列课程\",\n                cancelText: \"取消\",\n                width: 800,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addSeriesForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddSeries,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"title\",\n                            label: \"系列课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入系列课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"例如：React全栈开发实战\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1656,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1651,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程介绍\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程介绍\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 4,\n                                placeholder: \"请详细描述系列课程的内容、目标和特色...\",\n                                showCount: true,\n                                maxLength: 500\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1664,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1659,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"封面图片\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请上传封面图片\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"coverImage\",\n                                customRequest: handleImageUpload,\n                                onRemove: handleImageRemove,\n                                accept: \"image/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: coverImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: coverImageUrl,\n                                        alt: \"封面预览\",\n                                        style: {\n                                            width: \"100%\",\n                                            maxHeight: \"200px\",\n                                            objectFit: \"cover\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1686,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1685,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1691,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1690,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1693,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持单个文件上传，建议上传jpg、png格式图片，大小不超过2MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1694,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1689,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1676,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1672,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"是否为官方系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择是否为官方系列课程\"\n                                }\n                            ],\n                            initialValue: 0,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"是（官方）\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1711,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"否（社区）\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1712,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1710,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1704,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"projectMembers\",\n                            label: \"课程成员\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程成员\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程成员，如：王老师、李助教、张同学\",\n                                showCount: true,\n                                maxLength: 200\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1721,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1716,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"tagIds\",\n                            label: \"标签选择\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                mode: \"multiple\",\n                                placeholder: \"请选择相关标签\",\n                                optionLabelProp: \"label\",\n                                children: courseTags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: tag.id,\n                                        label: tag.name,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            color: tag.color,\n                                            children: tag.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1740,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, tag.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1739,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1733,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1728,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1646,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1633,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"创建课程标签\",\n                open: isAddTagModalVisible,\n                onCancel: ()=>{\n                    setIsAddTagModalVisible(false);\n                    addTagForm.resetFields();\n                },\n                onOk: ()=>addTagForm.submit(),\n                okText: \"创建标签\",\n                cancelText: \"取消\",\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addTagForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddTag,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"name\",\n                            label: \"标签名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入标签名称\"\n                                },\n                                {\n                                    max: 20,\n                                    message: \"标签名称不能超过20个字符\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"例如：高级、编程、实战\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1774,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1766,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"color\",\n                            label: \"标签颜色\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签颜色\"\n                                }\n                            ],\n                            initialValue: \"#007bff\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签颜色\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#007bff\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#007bff\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1786,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"蓝色 (#007bff)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1785,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1784,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#28a745\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#28a745\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1792,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"绿色 (#28a745)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1791,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1790,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#dc3545\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#dc3545\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1798,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"红色 (#dc3545)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1797,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1796,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#ffc107\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#ffc107\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1804,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"黄色 (#ffc107)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1803,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1802,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#6f42c1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#6f42c1\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1810,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"紫色 (#6f42c1)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1809,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1808,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#fd7e14\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#fd7e14\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1816,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"橙色 (#fd7e14)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1815,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1814,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#20c997\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#20c997\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1822,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"青色 (#20c997)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1821,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1820,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#6c757d\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#6c757d\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1828,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"灰色 (#6c757d)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1827,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1826,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1783,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1777,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"标签分类\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签分类\"\n                                }\n                            ],\n                            initialValue: 1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签分类\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"难度标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1842,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"类型标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1843,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 2,\n                                        children: \"特色标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1844,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 3,\n                                        children: \"其他标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1845,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1841,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1835,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"标签描述\",\n                            rules: [\n                                {\n                                    max: 100,\n                                    message: \"标签描述不能超过100个字符\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 3,\n                                placeholder: \"请输入标签的详细描述...\",\n                                showCount: true,\n                                maxLength: 100\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1854,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1849,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"status\",\n                            label: \"标签状态\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签状态\"\n                                }\n                            ],\n                            initialValue: 1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签状态\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"启用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1869,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"禁用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1870,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1868,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1862,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1761,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1749,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"发布系列课程\",\n                open: isPublishSeriesModalVisible,\n                onCancel: ()=>{\n                    setIsPublishSeriesModalVisible(false);\n                    publishSeriesForm.resetFields();\n                },\n                onOk: ()=>publishSeriesForm.submit(),\n                okText: \"发布系列\",\n                cancelText: \"取消\",\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: publishSeriesForm,\n                    layout: \"vertical\",\n                    onFinish: handlePublishSeries,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"选择要发布的系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择要发布的系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                showSearch: true,\n                                filterOption: (input, option)=>{\n                                    var _option_children;\n                                    return option === null || option === void 0 ? void 0 : (_option_children = option.children) === null || _option_children === void 0 ? void 0 : _option_children.toLowerCase().includes(input.toLowerCase());\n                                },\n                                children: courseSeries.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        children: [\n                                            series.title,\n                                            \" (\",\n                                            series.category,\n                                            \")\"\n                                        ]\n                                    }, series.id, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1907,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1899,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1894,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"publishNote\",\n                            label: \"发布说明\",\n                            rules: [\n                                {\n                                    required: false\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                placeholder: \"请输入发布说明（可选）\",\n                                rows: 3,\n                                maxLength: 200,\n                                showCount: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1919,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1914,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"发布说明：\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1928,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-gray-600 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布后系列课程将在课程市场中公开显示\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1930,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 只有已完成的课程才会被发布\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1931,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布后可以查看详细的发布统计信息\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1932,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布状态可以随时修改\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1933,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1929,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1927,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1889,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1877,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"发布课程\",\n                open: isPublishCourseModalVisible,\n                onCancel: resetPublishCourseModal,\n                footer: null,\n                width: 700,\n                destroyOnClose: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: publishCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handlePublishCourse,\n                    className: \"mt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"选择系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                loading: publishFormLoading,\n                                onChange: handlePublishSeriesChange,\n                                showSearch: true,\n                                filterOption: (input, option)=>{\n                                    var _option_children;\n                                    return option === null || option === void 0 ? void 0 : (_option_children = option.children) === null || _option_children === void 0 ? void 0 : _option_children.toLowerCase().includes(input.toLowerCase());\n                                },\n                                children: publishSeriesListForModal.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        children: [\n                                            series.title,\n                                            \" (ID: \",\n                                            series.id,\n                                            \")\"\n                                        ]\n                                    }, series.id, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1970,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1960,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1955,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"courseId\",\n                            label: \"选择子课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择要发布的子课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: selectedSeriesForPublish ? \"请选择要发布的子课程\" : \"请先选择系列课程\",\n                                disabled: !selectedSeriesForPublish,\n                                loading: publishFormLoading && !!selectedSeriesForPublish,\n                                onChange: handlePublishCourseChange,\n                                showSearch: true,\n                                filterOption: (input, option)=>{\n                                    var _option_children;\n                                    return option === null || option === void 0 ? void 0 : (_option_children = option.children) === null || _option_children === void 0 ? void 0 : _option_children.toLowerCase().includes(input.toLowerCase());\n                                },\n                                notFoundContent: publishFormLoading && selectedSeriesForPublish ? \"正在加载子课程...\" : selectedSeriesForPublish ? \"该系列暂无子课程\" : \"请先选择系列课程\",\n                                children: publishCourseListForModal.length > 0 ? publishCourseListForModal.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: course.id,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: course.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 2004,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            color: course.status === 1 ? \"green\" : course.status === 0 ? \"orange\" : \"red\",\n                                                            className: \"text-xs\",\n                                                            children: course.status === 1 ? \"已发布\" : course.status === 0 ? \"草稿\" : \"已归档\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 2006,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400 text-xs\",\n                                                            children: [\n                                                                \"ID: \",\n                                                                course.id\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 2009,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 2005,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2003,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, course.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2002,\n                                        columnNumber: 19\n                                    }, undefined)) : selectedSeriesForPublish && !publishFormLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                    disabled: true,\n                                    value: \"no-courses\",\n                                    children: \"该系列暂无子课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2016,\n                                    columnNumber: 19\n                                }, undefined) : null\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1983,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1978,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-3 rounded-lg mb-4 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-gray-700 mb-2\",\n                                    children: \"调试信息\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2026,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1 text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"已选择系列ID: \",\n                                                selectedSeriesForPublish || \"未选择\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2028,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"已选择课程ID: \",\n                                                selectedCourseForPublish || \"未选择\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2029,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"系列列表数量: \",\n                                                publishSeriesListForModal.length\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2030,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"子课程列表数量: \",\n                                                publishCourseListForModal.length\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2031,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"加载状态: \",\n                                                publishFormLoading ? \"加载中\" : \"空闲\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2032,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        publishCourseListForModal.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"子课程列表:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 2035,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"ml-4 list-disc\",\n                                                    children: publishCourseListForModal.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                \"ID: \",\n                                                                course.id,\n                                                                \", 名称: \",\n                                                                course.title\n                                                            ]\n                                                        }, course.id, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 2038,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 2036,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2034,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2027,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 2025,\n                            columnNumber: 11\n                        }, undefined),\n                        selectedCourseForPublish && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 p-4 rounded-lg mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-blue-900 mb-2\",\n                                    children: \"即将发布的课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2049,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"课程ID: \",\n                                                selectedCourseForPublish\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2051,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"所属系列: \",\n                                                (_publishSeriesListForModal_find = publishSeriesListForModal.find((s)=>s.id === selectedSeriesForPublish)) === null || _publishSeriesListForModal_find === void 0 ? void 0 : _publishSeriesListForModal_find.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2052,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"课程名称: \",\n                                                (_publishCourseListForModal_find = publishCourseListForModal.find((c)=>c.id === selectedCourseForPublish)) === null || _publishCourseListForModal_find === void 0 ? void 0 : _publishCourseListForModal_find.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2053,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-blue-600 font-medium\",\n                                            children: [\n                                                '点击\"发布此课程\"将调用发布API：POST /api/v1/course-management/courses/',\n                                                selectedCourseForPublish,\n                                                \"/publish\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2054,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2050,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 2048,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: selectedCourseForPublish ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-600\",\n                                        children: \"✓ 已选择课程，可以发布\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2062,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"请先选择系列课程和子课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2064,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2060,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            onClick: resetPublishCourseModal,\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2068,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            type: \"primary\",\n                                            htmlType: \"submit\",\n                                            loading: publishFormLoading,\n                                            disabled: !selectedCourseForPublish,\n                                            className: selectedCourseForPublish ? \"bg-green-600 hover:bg-green-700 border-green-600\" : \"\",\n                                            children: publishFormLoading ? \"发布中...\" : \"发布此课程\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2071,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2067,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 2059,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1948,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1940,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(CourseManagement, \"gaNKAxRGrpG508gyCSbEdjTD3QI=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm\n    ];\n});\n_c = CourseManagement;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseManagement);\nvar _c;\n$RefreshReg$(_c, \"CourseManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9hZG1pbi1zcGFjZS9jb21wb25lbnRzL2NvdXJzZS1tYW5hZ2VtZW50LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUU0QztBQUNvRTtBQUNjO0FBQ2xEO0FBQ2dGO0FBQy9HO0FBRzdDLE1BQU0sRUFBRXNCLE1BQU0sRUFBRSxHQUFHZixpSkFBS0E7QUFDeEIsTUFBTSxFQUFFZ0IsTUFBTSxFQUFFLEdBQUdmLGlKQUFNQTtBQXdCekIsTUFBTWdCLG1CQUFvRDtRQWcrRGhDQyxpQ0FDQUM7O0lBaCtEeEIsTUFBTSxDQUFDQyxZQUFZQyxjQUFjLEdBQUc1QiwrQ0FBUUEsQ0FBVyxFQUFFO0lBQ3pELE1BQU0sQ0FBQzZCLFNBQVNDLFdBQVcsR0FBRzlCLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQytCLHNCQUFzQkMsd0JBQXdCLEdBQUdoQywrQ0FBUUEsQ0FBQztJQUNqRSxNQUFNLENBQUNpQyx5QkFBeUJDLDJCQUEyQixHQUFHbEMsK0NBQVFBLENBQUM7SUFDdkUsTUFBTSxDQUFDbUMsMEJBQTBCQyw0QkFBNEIsR0FBR3BDLCtDQUFRQSxDQUFDO0lBQ3pFLE1BQU0sQ0FBQ3FDLHlCQUF5QkMsMkJBQTJCLEdBQUd0QywrQ0FBUUEsQ0FBQztJQUN2RSxNQUFNLENBQUN1QyxzQkFBc0JDLHdCQUF3QixHQUFHeEMsK0NBQVFBLENBQUM7SUFDakUsTUFBTSxDQUFDeUMsNkJBQTZCQywrQkFBK0IsR0FBRzFDLCtDQUFRQSxDQUFDO0lBQy9FLE1BQU0sQ0FBQzJDLDZCQUE2QkMsK0JBQStCLEdBQUc1QywrQ0FBUUEsQ0FBQztJQUMvRSxNQUFNLENBQUM2QyxlQUFlQyxpQkFBaUIsR0FBRzlDLCtDQUFRQSxDQUFnQjtJQUNsRSxNQUFNLENBQUMrQyxlQUFlQyxpQkFBaUIsR0FBR2hELCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ2lELFVBQVVDLFlBQVksR0FBR2xELCtDQUFRQSxDQUFZLEVBQUU7SUFDdEQsTUFBTSxDQUFDbUQsWUFBWUMsY0FBYyxHQUFHcEQsK0NBQVFBLENBQWMsRUFBRTtJQUM1RCxNQUFNLENBQUNxRCxlQUFlQyxpQkFBaUIsR0FBR3RELCtDQUFRQSxDQUFTO0lBRTNELG9CQUFvQjtJQUNwQixNQUFNLENBQUN1RCxZQUFZQyxjQUFjLEdBQUd4RCwrQ0FBUUEsQ0FBUSxFQUFFO0lBQ3RELE1BQU0sQ0FBQ3lELGtCQUFrQkMsb0JBQW9CLEdBQUcxRCwrQ0FBUUEsQ0FBcUIsSUFBSTJEO0lBQ2pGLE1BQU0sQ0FBQ0MsZ0JBQWdCQyxrQkFBa0IsR0FBRzdELCtDQUFRQSxDQUFjLElBQUk4RDtJQUN0RSxNQUFNLENBQUNDLGVBQWVDLGlCQUFpQixHQUFHaEUsK0NBQVFBLENBQUM7SUFFbkQsV0FBVztJQUNYLE1BQU0sQ0FBQ2lFLDBCQUEwQkMsNEJBQTRCLEdBQUdsRSwrQ0FBUUEsQ0FBcUJtRTtJQUM3RixNQUFNLENBQUNDLDBCQUEwQkMsNEJBQTRCLEdBQUdyRSwrQ0FBUUEsQ0FBcUJtRTtJQUM3RixNQUFNLENBQUNHLHNCQUFzQkMsd0JBQXdCLEdBQUd2RSwrQ0FBUUEsQ0FBUSxFQUFFO0lBQzFFLE1BQU0sQ0FBQ3dFLGdCQUFnQkMsa0JBQWtCLEdBQUd6RSwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUMwRSxzQkFBc0JDLHdCQUF3QixHQUFHM0UsK0NBQVFBLENBQVEsRUFBRTtJQUUxRSxXQUFXO0lBQ1gsTUFBTSxDQUFDeUIsMkJBQTJCbUQsNkJBQTZCLEdBQUc1RSwrQ0FBUUEsQ0FBUSxFQUFFO0lBQ3BGLE1BQU0sQ0FBQzBCLDJCQUEyQm1ELDZCQUE2QixHQUFHN0UsK0NBQVFBLENBQVEsRUFBRTtJQUNwRixNQUFNLENBQUM4RSxvQkFBb0JDLHNCQUFzQixHQUFHL0UsK0NBQVFBLENBQUM7SUFFN0QsTUFBTSxDQUFDZ0YsY0FBY0MsZ0JBQWdCLEdBQUdqRiwrQ0FBUUEsQ0FBaUIsRUFBRTtJQUNuRSxNQUFNLENBQUNrRixxQkFBcUJDLHVCQUF1QixHQUFHbkYsK0NBQVFBLENBQVM7SUFDdkUsTUFBTSxDQUFDb0YsaUJBQWlCQyxtQkFBbUIsR0FBR3JGLCtDQUFRQSxDQUFXLEVBQUU7SUFDbkUsTUFBTSxDQUFDc0YsZ0JBQWdCQyxrQkFBa0IsR0FBR3ZGLCtDQUFRQSxDQUFTO0lBQzdELE1BQU0sQ0FBQ3dGLGlCQUFpQkMsbUJBQW1CLEdBQUd6RiwrQ0FBUUEsQ0FBUztJQUMvRCxNQUFNLENBQUMwRixtQkFBbUJDLHFCQUFxQixHQUFHM0YsK0NBQVFBLENBQVM7SUFDbkUsTUFBTSxDQUFDNEYsb0JBQW9CQyxzQkFBc0IsR0FBRzdGLCtDQUFRQSxDQUFTO0lBQ3JFLE1BQU0sQ0FBQzhGLGVBQWVDLGlCQUFpQixHQUFHL0YsK0NBQVFBLENBQVM7SUFFM0QsTUFBTSxDQUFDZ0csY0FBYyxHQUFHMUYsaUpBQUlBLENBQUMyRixPQUFPO0lBQ3BDLE1BQU0sQ0FBQ0MsZUFBZSxHQUFHNUYsaUpBQUlBLENBQUMyRixPQUFPO0lBQ3JDLE1BQU0sQ0FBQ0UsY0FBYyxHQUFHN0YsaUpBQUlBLENBQUMyRixPQUFPO0lBQ3BDLE1BQU0sQ0FBQ0csV0FBVyxHQUFHOUYsaUpBQUlBLENBQUMyRixPQUFPO0lBQ2pDLE1BQU0sQ0FBQ0ksa0JBQWtCLEdBQUcvRixpSkFBSUEsQ0FBQzJGLE9BQU87SUFDeEMsTUFBTSxDQUFDSyxrQkFBa0IsR0FBR2hHLGlKQUFJQSxDQUFDMkYsT0FBTztJQUN4QyxNQUFNTSxlQUFlcEYsMEZBQWVBO0lBSXBDLFdBQVc7SUFDWCxNQUFNcUYsa0JBQWtCO1FBQ3RCLElBQUk7Z0JBU3NCQztZQVJ4QnpDLGlCQUFpQjtZQUNqQjBDLFFBQVFDLEdBQUcsQ0FBQztZQUVaLE1BQU0sRUFBRUMsTUFBTUgsR0FBRyxFQUFFLEdBQUcsTUFBTXJGLHNEQUFTQSxDQUFDeUYsb0JBQW9CLENBQUM7Z0JBQ3pEQyxNQUFNO2dCQUNOQyxVQUFVO1lBQ1o7WUFFQSxJQUFJTixJQUFJTyxJQUFJLEtBQUssU0FBT1AsWUFBQUEsSUFBSUcsSUFBSSxjQUFSSCxnQ0FBQUEsVUFBVVEsSUFBSSxHQUFFO2dCQUN0Q1AsUUFBUUMsR0FBRyxDQUFDLGlCQUFpQkYsSUFBSUcsSUFBSSxDQUFDSyxJQUFJO2dCQUMxQ3pELGNBQWNpRCxJQUFJRyxJQUFJLENBQUNLLElBQUk7WUFDN0IsT0FBTztnQkFDTFAsUUFBUVEsS0FBSyxDQUFDLGlCQUFpQlQsSUFBSVUsR0FBRztnQkFDdEN0RyxpSkFBT0EsQ0FBQ3FHLEtBQUssQ0FBQ1QsSUFBSVUsR0FBRyxJQUFJO1lBQzNCO1FBQ0YsRUFBRSxPQUFPRCxPQUFPO1lBQ2RSLFFBQVFRLEtBQUssQ0FBQyxpQkFBaUJBO1lBQy9CckcsaUpBQU9BLENBQUNxRyxLQUFLLENBQUM7UUFDaEIsU0FBVTtZQUNSbEQsaUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQSxnQkFBZ0I7SUFDaEIsTUFBTW9ELHFCQUFxQixPQUFPQztRQUNoQyxJQUFJO2dCQVVzQlo7WUFUeEJDLFFBQVFDLEdBQUcsQ0FBQyxnQ0FBc0JVO1lBRWxDLHFCQUFxQjtZQUNyQixNQUFNLEVBQUVULE1BQU1ILEdBQUcsRUFBRSxHQUFHLE1BQU1yRixzREFBU0EsQ0FBQ2tHLDBCQUEwQixDQUFDRCxVQUFVO2dCQUN6RVAsTUFBTTtnQkFDTkMsVUFBVTtZQUVaO1lBRUEsSUFBSU4sSUFBSU8sSUFBSSxLQUFLLFNBQU9QLFlBQUFBLElBQUlHLElBQUksY0FBUkgsZ0NBQUFBLFVBQVVRLElBQUksR0FBRTtnQkFDdENQLFFBQVFDLEdBQUcsQ0FBQyxrQkFBa0JGLElBQUlHLElBQUksQ0FBQ0ssSUFBSTtnQkFDM0N2RCxvQkFBb0I2RCxDQUFBQSxPQUFRLElBQUk1RCxJQUFJNEQsS0FBS0MsR0FBRyxDQUFDSCxVQUFVWixJQUFJRyxJQUFJLENBQUNLLElBQUk7Z0JBQ3BFcEQsa0JBQWtCMEQsQ0FBQUEsT0FBUSxJQUFJekQsSUFBSXlELEtBQUtFLEdBQUcsQ0FBQ0o7WUFDN0MsT0FBTztnQkFDTFgsUUFBUVEsS0FBSyxDQUFDLGtCQUFrQlQsSUFBSVUsR0FBRztnQkFDdkN0RyxpSkFBT0EsQ0FBQ3FHLEtBQUssQ0FBQ1QsSUFBSVUsR0FBRyxJQUFJO1lBQzNCO1FBQ0YsRUFBRSxPQUFPRCxPQUFPO1lBQ2RSLFFBQVFRLEtBQUssQ0FBQyxrQkFBa0JBO1lBQ2hDckcsaUpBQU9BLENBQUNxRyxLQUFLLENBQUM7UUFDaEI7SUFDRjtJQUVBLGlCQUFpQjtJQUNqQixNQUFNUSxrQkFBa0I7UUFDdEIsSUFBSTtZQUNGaEIsUUFBUUMsR0FBRyxDQUFDO1lBRVosV0FBVztZQUNYLE1BQU1IO1FBQ1IsRUFBRSxPQUFPVSxPQUFPO1lBQ2RSLFFBQVFRLEtBQUssQ0FBQyxlQUFlQTtZQUM3QlgsYUFBYVcsS0FBSyxDQUFDO1FBQ3JCO0lBQ0Y7SUFFQSxPQUFPO0lBQ1AsTUFBTVMsa0JBQWtCLE9BQU9DO1FBQzdCLElBQUk7WUFDRixvQkFBb0I7WUFDcEIsTUFBTUMsZ0JBQXFCO2dCQUN6QkMsVUFBVXhDLGlCQUFpQixJQUFJO2dCQUMvQnlDLGFBQWFyQyxvQkFBb0IsSUFBSTtZQUN2QztZQUVBLElBQUlKLGdCQUFnQjtnQkFDbEJ1QyxjQUFjRyxLQUFLLEdBQUc7b0JBQ3BCQyxLQUFLM0M7b0JBQ0w0QyxNQUFNMUMsbUJBQW1CO2dCQUMzQjtZQUNGO1lBRUEsSUFBSUUsbUJBQW1CO2dCQUNyQm1DLGNBQWNNLFFBQVEsR0FBRztvQkFDdkJGLEtBQUt2QztvQkFDTHdDLE1BQU10QyxzQkFBc0I7Z0JBQzlCO1lBQ0Y7WUFFQSxNQUFNd0MsYUFBYTtnQkFDakJmLFVBQVVnQixTQUFTVCxPQUFPUCxRQUFRO2dCQUNsQ2lCLE9BQU9WLE9BQU9VLEtBQUssQ0FBQ0MsSUFBSTtnQkFDeEJDLGFBQWFaLE9BQU9ZLFdBQVcsQ0FBQ0QsSUFBSTtnQkFDcENFLFlBQVl2RDtnQkFDWjRDLFVBQVV4QyxpQkFBaUIsSUFBSTtnQkFDL0J5QyxhQUFhckMsb0JBQW9CLElBQUk7Z0JBQ3JDZ0QsVUFBVUMsaUJBQWlCLElBQUk7Z0JBQy9CN0MsZUFBZUEsaUJBQWlCO2dCQUNoQytCO2dCQUNBZSxjQUFjaEIsT0FBT2lCLGtCQUFrQixJQUFJakIsT0FBT2lCLGtCQUFrQixDQUFDQyxNQUFNLEdBQUcsSUFBSTtvQkFBQzt3QkFDakZSLE9BQU87d0JBQ1BTLFNBQVNDLE1BQU1DLE9BQU8sQ0FBQ3JCLE9BQU9pQixrQkFBa0IsSUFBSWpCLE9BQU9pQixrQkFBa0IsR0FBRzs0QkFBQ2pCLE9BQU9pQixrQkFBa0I7eUJBQUM7b0JBQzdHO2lCQUFFLEdBQUcsRUFBRTtnQkFDUEsscUJBQXFCOUQsZ0JBQWdCK0QsR0FBRyxDQUFDQyxDQUFBQSxPQUFTO3dCQUNoRGQsT0FBT2MsS0FBS0MsS0FBSyxDQUFDLEtBQUtDLEdBQUcsTUFBTTt3QkFDaENyQixLQUFLbUI7d0JBQ0xaLGFBQWE7b0JBQ2Y7Z0JBQ0FlLFlBQVlsQixTQUFTVCxPQUFPMkIsVUFBVSxLQUFLO1lBQzdDO1lBRUEsU0FBUztZQUNULElBQUksQ0FBQ25CLFdBQVdmLFFBQVEsRUFBRTtnQkFDeEJkLGFBQWFXLEtBQUssQ0FBQztnQkFDbkI7WUFDRjtZQUNBLElBQUksQ0FBQ2tCLFdBQVdFLEtBQUssRUFBRTtnQkFDckIvQixhQUFhVyxLQUFLLENBQUM7Z0JBQ25CO1lBQ0Y7WUFDQSxJQUFJLENBQUNrQixXQUFXSyxVQUFVLEVBQUU7Z0JBQzFCbEMsYUFBYVcsS0FBSyxDQUFDO2dCQUNuQjtZQUNGO1lBRUFSLFFBQVFDLEdBQUcsQ0FBQyx3QkFBY3lCO1lBQzFCMUIsUUFBUUMsR0FBRyxDQUFDLHdCQUFjNkMsS0FBS0MsU0FBUyxDQUFDckIsWUFBWVUsTUFBTSxFQUFFO1lBRTdELFNBQVM7WUFDVCxJQUFJWSxhQUFhO1lBQ2pCLE1BQU1DLGFBQWE7WUFDbkIsSUFBSUM7WUFFSixNQUFPRixjQUFjQyxXQUFZO2dCQUMvQixJQUFJO29CQUNGLE1BQU0sRUFBRS9DLE1BQU1ILEdBQUcsRUFBRSxHQUFHLE1BQU1yRixzREFBU0EsQ0FBQ3lJLFlBQVksQ0FBQ3pCO29CQUVuRCxjQUFjO29CQUNkLElBQUkzQixJQUFJTyxJQUFJLEtBQUssS0FBSzt3QkFDcEJULGFBQWF1RCxPQUFPLENBQUM7d0JBQ3JCcEM7d0JBQ0F4RiwyQkFBMkI7d0JBQzNCOEQsY0FBYytELFdBQVc7d0JBQ3pCNUUsdUJBQXVCO3dCQUN2QkUsbUJBQW1CLEVBQUU7d0JBQ3JCRSxrQkFBa0I7d0JBQ2xCRSxtQkFBbUI7d0JBQ25CRSxxQkFBcUI7d0JBQ3JCRSxzQkFBc0I7d0JBQ3RCbUUsa0JBQWtCO3dCQUNsQkMsbUJBQW1CO3dCQUNuQmxFLGlCQUFpQjt3QkFDakI7b0JBQ0YsT0FBTzt3QkFDTFEsYUFBYVcsS0FBSyxDQUFDVCxJQUFJVSxHQUFHLElBQUk7d0JBQzlCO29CQUNGO2dCQUNGLEVBQUUsT0FBT0QsT0FBWTtvQkFDbkIwQyxZQUFZMUM7b0JBQ1p3QztvQkFFQSxJQUFJQSxjQUFjQyxZQUFZO3dCQUM1QmpELFFBQVFDLEdBQUcsQ0FBQyxpQkFBa0IsT0FBWCtDLFlBQVc7d0JBQzlCbkQsYUFBYTJELE9BQU8sQ0FBQyxjQUE0QlAsT0FBZEQsWUFBVyxLQUFjLE9BQVhDLFlBQVc7d0JBQzVELFVBQVU7d0JBQ1YsTUFBTSxJQUFJUSxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO29CQUNuRDtnQkFDRjtZQUNGO1lBRUEscUJBQXFCO1lBQ3JCLE1BQU1SO1FBQ1IsRUFBRSxPQUFPMUMsT0FBWTtnQkFJZ0JBLGdCQUM5QkEsc0JBQUFBLGlCQUlNQSxrQkFFQUEsa0JBR0FBLGtCQVNEQSxrQkFDRkE7WUF2QlJSLFFBQVFRLEtBQUssQ0FBQyxhQUFhQTtZQUUzQixXQUFXO1lBQ1gsSUFBSUEsTUFBTUYsSUFBSSxLQUFLLGtCQUFnQkUsaUJBQUFBLE1BQU1yRyxPQUFPLGNBQWJxRyxxQ0FBQUEsZUFBZW9ELFFBQVEsQ0FBQyxrQkFDdERwRCxFQUFBQSxrQkFBQUEsTUFBTXFELFFBQVEsY0FBZHJELHVDQUFBQSx1QkFBQUEsZ0JBQWdCTixJQUFJLGNBQXBCTSwyQ0FBQUEscUJBQXNCckcsT0FBTyxLQUFJcUcsTUFBTXFELFFBQVEsQ0FBQzNELElBQUksQ0FBQy9GLE9BQU8sQ0FBQ3lKLFFBQVEsQ0FBQyxlQUFnQjtnQkFDekYvRCxhQUFhVyxLQUFLLENBQUM7WUFDckIsT0FBTyxJQUFJQSxNQUFNRixJQUFJLEtBQUssaUJBQWlCO2dCQUN6Q1QsYUFBYVcsS0FBSyxDQUFDO1lBQ3JCLE9BQU8sSUFBSUEsRUFBQUEsbUJBQUFBLE1BQU1xRCxRQUFRLGNBQWRyRCx1Q0FBQUEsaUJBQWdCc0QsTUFBTSxNQUFLLEtBQUs7Z0JBQ3pDakUsYUFBYVcsS0FBSyxDQUFDO1lBQ3JCLE9BQU8sSUFBSUEsRUFBQUEsbUJBQUFBLE1BQU1xRCxRQUFRLGNBQWRyRCx1Q0FBQUEsaUJBQWdCc0QsTUFBTSxNQUFLLEtBQUs7b0JBQ3hCdEQsdUJBQUFBO2dCQUFqQixNQUFNdUQsV0FBV3ZELEVBQUFBLG1CQUFBQSxNQUFNcUQsUUFBUSxjQUFkckQsd0NBQUFBLHdCQUFBQSxpQkFBZ0JOLElBQUksY0FBcEJNLDRDQUFBQSxzQkFBc0JyRyxPQUFPLEtBQUlxRyxNQUFNckcsT0FBTztnQkFDL0QwRixhQUFhVyxLQUFLLENBQUMsV0FBb0IsT0FBVHVEO1lBQ2hDLE9BQU8sSUFBSXZELEVBQUFBLG1CQUFBQSxNQUFNcUQsUUFBUSxjQUFkckQsdUNBQUFBLGlCQUFnQnNELE1BQU0sTUFBSyxLQUFLO2dCQUN6Q2pFLGFBQWFXLEtBQUssQ0FBQztZQUNyQixPQUFPO2dCQUNMWCxhQUFhVyxLQUFLLENBQUMsV0FBb0MsT0FBekJBLE1BQU1yRyxPQUFPLElBQUk7WUFDakQ7WUFFQTZGLFFBQVFDLEdBQUcsQ0FBQyx3QkFBYztnQkFDeEI5RixTQUFTcUcsTUFBTXJHLE9BQU87Z0JBQ3RCbUcsTUFBTUUsTUFBTUYsSUFBSTtnQkFDaEJ3RCxNQUFNLEdBQUV0RCxtQkFBQUEsTUFBTXFELFFBQVEsY0FBZHJELHVDQUFBQSxpQkFBZ0JzRCxNQUFNO2dCQUM5QjVELElBQUksR0FBRU0sbUJBQUFBLE1BQU1xRCxRQUFRLGNBQWRyRCx1Q0FBQUEsaUJBQWdCTixJQUFJO1lBQzVCO1FBQ0Y7SUFDRjtJQUVBLE9BQU87SUFDUCxNQUFNOEQsbUJBQW1CLE9BQU85QztRQUM5QixJQUFJLENBQUMvRSxlQUFlO1FBRXBCLElBQUk7WUFDRixNQUFNLEVBQUUrRCxNQUFNSCxHQUFHLEVBQUUsR0FBRyxNQUFNckYsc0RBQVNBLENBQUN1SixZQUFZLENBQUM5SCxjQUFjK0gsRUFBRSxFQUFFaEQ7WUFDckUsSUFBSW5CLElBQUlPLElBQUksS0FBSyxLQUFLO2dCQUNwQlQsYUFBYXVELE9BQU8sQ0FBQztnQkFDckJwQztnQkFDQXRGLDRCQUE0QjtnQkFDNUJVLGlCQUFpQjtnQkFDakJvRCxlQUFlNkQsV0FBVztZQUM1QixPQUFPO2dCQUNMeEQsYUFBYVcsS0FBSyxDQUFDVCxJQUFJVSxHQUFHLElBQUk7WUFDaEM7UUFDRixFQUFFLE9BQU9ELE9BQU87WUFDZFIsUUFBUVEsS0FBSyxDQUFDLGFBQWFBO1lBQzNCWCxhQUFhVyxLQUFLLENBQUM7UUFDckI7SUFDRjtJQUVBLE9BQU87SUFDUCxNQUFNMkQscUJBQXFCLE9BQU9DO1FBQ2hDLElBQUk7WUFDRixNQUFNLEVBQUVsRSxNQUFNSCxHQUFHLEVBQUUsR0FBRyxNQUFNckYsc0RBQVNBLENBQUMySixZQUFZLENBQUNEO1lBQ25ELElBQUlyRSxJQUFJTyxJQUFJLEtBQUssS0FBSztnQkFDcEJULGFBQWF1RCxPQUFPLENBQUM7Z0JBQ3JCcEM7WUFDRixPQUFPO2dCQUNMbkIsYUFBYVcsS0FBSyxDQUFDVCxJQUFJVSxHQUFHLElBQUk7WUFDaEM7UUFDRixFQUFFLE9BQU9ELE9BQU87WUFDZFIsUUFBUVEsS0FBSyxDQUFDLGFBQWFBO1lBQzNCWCxhQUFhVyxLQUFLLENBQUM7UUFDckI7SUFDRjtJQUVBLFFBQVE7SUFDUixNQUFNOEQsd0JBQXdCLE9BQU9GLFVBQWtCekQ7UUFDckQsSUFBSTtZQUNGWCxRQUFRQyxHQUFHLENBQUMsNkJBQW1CbUUsVUFBVSxTQUFTekQ7WUFFbEQsTUFBTSxFQUFFVCxNQUFNSCxHQUFHLEVBQUUsR0FBRyxNQUFNckYsc0RBQVNBLENBQUMySixZQUFZLENBQUNEO1lBRW5ELElBQUlyRSxJQUFJTyxJQUFJLEtBQUssS0FBSztnQkFDcEJuRyxpSkFBT0EsQ0FBQ2lKLE9BQU8sQ0FBQztnQkFDaEJwRCxRQUFRQyxHQUFHLENBQUM7Z0JBRVosZ0JBQWdCO2dCQUNoQixNQUFNUyxtQkFBbUJDO2dCQUV6QlgsUUFBUUMsR0FBRyxDQUFDO1lBQ2QsT0FBTztnQkFDTEQsUUFBUVEsS0FBSyxDQUFDLGNBQWNULElBQUlVLEdBQUc7Z0JBQ25DdEcsaUpBQU9BLENBQUNxRyxLQUFLLENBQUNULElBQUlVLEdBQUcsSUFBSTtZQUMzQjtRQUNGLEVBQUUsT0FBT0QsT0FBTztZQUNkUixRQUFRUSxLQUFLLENBQUMsY0FBY0E7WUFDNUJyRyxpSkFBT0EsQ0FBQ3FHLEtBQUssQ0FBQztRQUNoQjtJQUNGO0lBRUEsY0FBYztJQUNkLE1BQU0rRCx3QkFBd0IsT0FBTzVEO1FBQ25DWCxRQUFRQyxHQUFHLENBQUMsK0JBQXFCVTtRQUNqQ1gsUUFBUUMsR0FBRyxDQUFDLHdCQUFjL0MsZUFBZXNILEdBQUcsQ0FBQzdEO1FBRTdDLElBQUl6RCxlQUFlc0gsR0FBRyxDQUFDN0QsV0FBVztZQUNoQyxLQUFLO1lBQ0xYLFFBQVFDLEdBQUcsQ0FBQyxzQkFBWVU7WUFDeEJ4RCxrQkFBa0IwRCxDQUFBQTtnQkFDaEIsTUFBTTRELFNBQVMsSUFBSXJILElBQUl5RDtnQkFDdkI0RCxPQUFPQyxNQUFNLENBQUMvRDtnQkFDZCxPQUFPOEQ7WUFDVDtRQUNGLE9BQU87WUFDTCxlQUFlO1lBQ2Z6RSxRQUFRQyxHQUFHLENBQUMsNEJBQWtCVTtZQUM5QixNQUFNRCxtQkFBbUJDO1FBQzNCO0lBQ0Y7SUFFQSxTQUFTO0lBQ1QsTUFBTWdFLGtCQUFrQjtRQUN0QjNFLFFBQVFDLEdBQUcsQ0FBQztRQUNaLEtBQUssTUFBTTJFLFVBQVUvSCxXQUFZO1lBQy9CLElBQUksQ0FBQ0ssZUFBZXNILEdBQUcsQ0FBQ0ksT0FBT1YsRUFBRSxHQUFHO2dCQUNsQyxNQUFNeEQsbUJBQW1Ca0UsT0FBT1YsRUFBRTtZQUNwQztRQUNGO0lBQ0Y7SUFFQSxTQUFTO0lBQ1QsTUFBTVcsb0JBQW9CO1FBQ3hCN0UsUUFBUUMsR0FBRyxDQUFDO1FBQ1o5QyxrQkFBa0IsSUFBSUM7SUFDeEI7SUFFQSxTQUFTO0lBQ1QsTUFBTTBILGtCQUFrQixPQUFPNUQ7UUFDN0IsSUFBSTtZQUNGLE1BQU02RCxhQUFhO2dCQUNqQixHQUFHN0QsTUFBTTtnQkFDVGEsWUFBWXBGO1lBQ2Q7WUFFQXFELFFBQVFDLEdBQUcsQ0FBQyxhQUFhOEU7WUFFekIsTUFBTSxFQUFFN0UsTUFBTUgsR0FBRyxFQUFFLEdBQUcsTUFBTXJGLHNEQUFTQSxDQUFDc0ssa0JBQWtCLENBQUNEO1lBRXpELElBQUloRixJQUFJTyxJQUFJLEtBQUssS0FBSztnQkFDcEJULGFBQWF1RCxPQUFPLENBQUM7Z0JBQ3JCcEM7Z0JBQ0FwRiwyQkFBMkI7Z0JBQzNCNkQsY0FBYzRELFdBQVc7Z0JBQ3pCekcsaUJBQWlCO1lBQ25CLE9BQU87Z0JBQ0xpRCxhQUFhVyxLQUFLLENBQUNULElBQUlVLEdBQUcsSUFBSTtZQUNoQztRQUNGLEVBQUUsT0FBT0QsT0FBTztZQUNkUixRQUFRUSxLQUFLLENBQUMsZUFBZUE7WUFDN0JYLGFBQWFXLEtBQUssQ0FBQztRQUNyQjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQU15RSxlQUFlLE9BQU8vRDtRQUMxQixJQUFJO1lBQ0ZsQixRQUFRQyxHQUFHLENBQUMsMkJBQWlCaUI7WUFFN0IsTUFBTSxFQUFFaEIsTUFBTUgsR0FBRyxFQUFFLEdBQUcsTUFBTXJGLHNEQUFTQSxDQUFDd0ssZUFBZSxDQUFDaEU7WUFFdEQsSUFBSW5CLElBQUlPLElBQUksS0FBSyxLQUFLO2dCQUNwQlQsYUFBYXVELE9BQU8sQ0FBQztnQkFDckJ0SCx3QkFBd0I7Z0JBQ3hCNEQsV0FBVzJELFdBQVc7Z0JBQ3RCLFdBQVc7Z0JBQ1g4QjtZQUNGLE9BQU87Z0JBQ0x0RixhQUFhVyxLQUFLLENBQUNULElBQUlVLEdBQUcsSUFBSTtZQUNoQztRQUNGLEVBQUUsT0FBT0QsT0FBTztZQUNkUixRQUFRUSxLQUFLLENBQUMsYUFBYUE7WUFDM0JYLGFBQWFXLEtBQUssQ0FBQztRQUNyQjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQU00RSxzQkFBc0IsT0FBT2xFO1FBQ2pDLElBQUk7WUFDRmxCLFFBQVFDLEdBQUcsQ0FBQywwQkFBZ0JpQjtZQUU1QixNQUFNLEVBQUVoQixNQUFNSCxHQUFHLEVBQUUsR0FBRyxNQUFNckYsc0RBQVNBLENBQUMySyxtQkFBbUIsQ0FBQ25FLE9BQU9QLFFBQVE7WUFFekUsSUFBSVosSUFBSU8sSUFBSSxLQUFLLEtBQUs7Z0JBQ3BCVCxhQUFhdUQsT0FBTyxDQUFDO2dCQUNyQnBILCtCQUErQjtnQkFDL0IyRCxrQkFBa0IwRCxXQUFXO2dCQUU3QixXQUFXO2dCQUNYLE1BQU1pQyxjQUFjdkYsSUFBSUcsSUFBSTtnQkFDNUJGLFFBQVFDLEdBQUcsQ0FBQyxnQkFBZ0JxRjtnQkFFNUIsZUFBZTtnQkFDZixJQUFJQSxZQUFZQyxZQUFZLEVBQUU7b0JBQzVCLE1BQU1DLFFBQVFGLFlBQVlDLFlBQVk7b0JBQ3RDLE1BQU1FLGVBQWUsT0FBdUNILE9BQWhDQSxZQUFZSSxnQkFBZ0IsRUFBQyxLQUFzQ0YsT0FBbkNGLFlBQVlLLFlBQVksRUFBQyxZQUE4Q0MsT0FBcENKLE1BQU1LLGdCQUFnQixFQUFDLGVBQXVELE9BQTFDRCxLQUFLRSxLQUFLLENBQUNOLE1BQU1PLGtCQUFrQixHQUFHLEtBQUk7b0JBQzdLbEcsYUFBYW1HLElBQUksQ0FBQ1A7Z0JBQ3BCO1lBQ0YsT0FBTztnQkFDTDVGLGFBQWFXLEtBQUssQ0FBQ1QsSUFBSVUsR0FBRyxJQUFJO1lBQ2hDO1FBQ0YsRUFBRSxPQUFPRCxPQUFPO1lBQ2RSLFFBQVFRLEtBQUssQ0FBQyxlQUFlQTtZQUM3QlgsYUFBYVcsS0FBSyxDQUFDO1FBQ3JCO0lBQ0Y7SUFFQSxlQUFlO0lBQ2YsTUFBTXlGLHdCQUF3QjtRQUM1QixJQUFJO2dCQVNzQmxHO1lBUnhCaEMsa0JBQWtCO1lBQ2xCaUMsUUFBUUMsR0FBRyxDQUFDO1lBRVosTUFBTSxFQUFFQyxNQUFNSCxHQUFHLEVBQUUsR0FBRyxNQUFNckYsc0RBQVNBLENBQUN5RixvQkFBb0IsQ0FBQztnQkFDekRDLE1BQU07Z0JBQ05DLFVBQVU7WUFDWjtZQUVBLElBQUlOLElBQUlPLElBQUksS0FBSyxTQUFPUCxZQUFBQSxJQUFJRyxJQUFJLGNBQVJILGdDQUFBQSxVQUFVUSxJQUFJLEdBQUU7Z0JBQ3RDUCxRQUFRQyxHQUFHLENBQUMsb0JBQW9CRixJQUFJRyxJQUFJLENBQUNLLElBQUk7Z0JBQzdDLE9BQU9SLElBQUlHLElBQUksQ0FBQ0ssSUFBSTtZQUN0QixPQUFPO2dCQUNMUCxRQUFRUSxLQUFLLENBQUMsb0JBQW9CVCxJQUFJVSxHQUFHO2dCQUN6Q3RHLGlKQUFPQSxDQUFDcUcsS0FBSyxDQUFDVCxJQUFJVSxHQUFHLElBQUk7Z0JBQ3pCLE9BQU8sRUFBRTtZQUNYO1FBQ0YsRUFBRSxPQUFPRCxPQUFPO1lBQ2RSLFFBQVFRLEtBQUssQ0FBQyxvQkFBb0JBO1lBQ2xDckcsaUpBQU9BLENBQUNxRyxLQUFLLENBQUM7WUFDZCxPQUFPLEVBQUU7UUFDWCxTQUFVO1lBQ1J6QyxrQkFBa0I7UUFDcEI7SUFDRjtJQUVBLGNBQWM7SUFDZCxNQUFNbUksOEJBQThCLE9BQU92RjtRQUN6QyxJQUFJO1lBQ0Y1QyxrQkFBa0I7WUFDbEJpQyxRQUFRQyxHQUFHLENBQUMsK0JBQXFCVTtZQUVqQyxNQUFNLEVBQUVULE1BQU1ILEdBQUcsRUFBRSxHQUFHLE1BQU1yRixzREFBU0EsQ0FBQ3lMLDBCQUEwQixDQUFDeEY7WUFFakUsSUFBSVosSUFBSU8sSUFBSSxLQUFLLE9BQU9QLElBQUlHLElBQUksRUFBRTtnQkFDaENGLFFBQVFDLEdBQUcsQ0FBQyxpQkFBaUJGLElBQUlHLElBQUk7Z0JBQ3JDLE9BQU9ILElBQUlHLElBQUk7WUFDakIsT0FBTztnQkFDTEYsUUFBUVEsS0FBSyxDQUFDLGlCQUFpQlQsSUFBSVUsR0FBRztnQkFDdEN0RyxpSkFBT0EsQ0FBQ3FHLEtBQUssQ0FBQ1QsSUFBSVUsR0FBRyxJQUFJO2dCQUN6QixPQUFPO1lBQ1Q7UUFDRixFQUFFLE9BQU9ELE9BQU87WUFDZFIsUUFBUVEsS0FBSyxDQUFDLGlCQUFpQkE7WUFDL0JyRyxpSkFBT0EsQ0FBQ3FHLEtBQUssQ0FBQztZQUNkLE9BQU87UUFDVCxTQUFVO1lBQ1J6QyxrQkFBa0I7UUFDcEI7SUFDRjtJQUVBLGdCQUFnQjtJQUNoQixNQUFNcUkseUJBQXlCO1FBQzdCLElBQUk7Z0JBU3NCckc7WUFSeEIxQixzQkFBc0I7WUFDdEIyQixRQUFRQyxHQUFHLENBQUM7WUFFWixNQUFNLEVBQUVDLE1BQU1ILEdBQUcsRUFBRSxHQUFHLE1BQU1yRixzREFBU0EsQ0FBQ3lGLG9CQUFvQixDQUFDO2dCQUN6REMsTUFBTTtnQkFDTkMsVUFBVTtZQUNaO1lBRUEsSUFBSU4sSUFBSU8sSUFBSSxLQUFLLFNBQU9QLFlBQUFBLElBQUlHLElBQUksY0FBUkgsZ0NBQUFBLFVBQVVRLElBQUksR0FBRTtnQkFDdENQLFFBQVFDLEdBQUcsQ0FBQyxxQkFBcUJGLElBQUlHLElBQUksQ0FBQ0ssSUFBSTtnQkFDOUNyQyw2QkFBNkI2QixJQUFJRyxJQUFJLENBQUNLLElBQUk7WUFDNUMsT0FBTztnQkFDTFAsUUFBUVEsS0FBSyxDQUFDLHFCQUFxQlQsSUFBSVUsR0FBRztnQkFDMUN0RyxpSkFBT0EsQ0FBQ3FHLEtBQUssQ0FBQ1QsSUFBSVUsR0FBRyxJQUFJO1lBQzNCO1FBQ0YsRUFBRSxPQUFPRCxPQUFPO1lBQ2RSLFFBQVFRLEtBQUssQ0FBQyxxQkFBcUJBO1lBQ25DckcsaUpBQU9BLENBQUNxRyxLQUFLLENBQUM7UUFDaEIsU0FBVTtZQUNSbkMsc0JBQXNCO1FBQ3hCO0lBQ0Y7SUFFQSx3QkFBd0I7SUFDeEIsTUFBTWdJLHlCQUF5QixPQUFPMUY7UUFDcEMsSUFBSTtnQkFhc0JaO1lBWnhCQyxRQUFRQyxHQUFHLENBQUMsbUNBQXlCVTtZQUNyQ1gsUUFBUUMsR0FBRyxDQUFDO1lBRVoscUJBQXFCO1lBQ3JCLE1BQU0sRUFBRUMsTUFBTUgsR0FBRyxFQUFFLEdBQUcsTUFBTXJGLHNEQUFTQSxDQUFDa0csMEJBQTBCLENBQUNELFVBQVU7Z0JBQ3pFUCxNQUFNO2dCQUNOQyxVQUFVO2dCQUNWeUQsUUFBUSxFQUFHLGFBQWE7WUFDMUI7WUFFQTlELFFBQVFDLEdBQUcsQ0FBQywyQkFBaUJGO1lBRTdCLElBQUlBLElBQUlPLElBQUksS0FBSyxTQUFPUCxZQUFBQSxJQUFJRyxJQUFJLGNBQVJILGdDQUFBQSxVQUFVUSxJQUFJLEdBQUU7Z0JBQ3RDUCxRQUFRQyxHQUFHLENBQUMsdUJBQXVCRixJQUFJRyxJQUFJLENBQUNLLElBQUksQ0FBQzZCLE1BQU07Z0JBQ3ZEakUsNkJBQTZCNEIsSUFBSUcsSUFBSSxDQUFDSyxJQUFJO2dCQUUxQyxJQUFJUixJQUFJRyxJQUFJLENBQUNLLElBQUksQ0FBQzZCLE1BQU0sS0FBSyxHQUFHO29CQUM5QmpJLGlKQUFPQSxDQUFDNkwsSUFBSSxDQUFDO2dCQUNmO1lBQ0YsT0FBTztnQkFDTGhHLFFBQVFDLEdBQUcsQ0FBQztnQkFDWjlCLDZCQUE2QixFQUFFO2dCQUMvQmhFLGlKQUFPQSxDQUFDNkwsSUFBSSxDQUFDO1lBQ2Y7UUFFRixFQUFFLE9BQU94RixPQUFPO1lBQ2RSLFFBQVFRLEtBQUssQ0FBQyxvQkFBb0JBO1lBQ2xDckcsaUpBQU9BLENBQUNxRyxLQUFLLENBQUM7WUFDZHJDLDZCQUE2QixFQUFFO1FBQ2pDO0lBQ0Y7SUFFQSxlQUFlO0lBQ2YsTUFBTW1JLDRCQUE0QixPQUFPM0Y7UUFDdkNYLFFBQVFDLEdBQUcsQ0FBQyw0QkFBa0JVO1FBQzlCWCxRQUFRQyxHQUFHLENBQUMsd0JBQWNsRjtRQUUxQnlDLDRCQUE0Qm1EO1FBQzVCaEQsNEJBQTRCRjtRQUM1QlUsNkJBQTZCLEVBQUU7UUFFL0IsYUFBYTtRQUNieUIsa0JBQWtCMkcsY0FBYyxDQUFDO1lBQUVuQyxVQUFVM0c7UUFBVTtRQUV2RCxhQUFhO1FBQ2IsSUFBSWtELFVBQVU7WUFDWlgsUUFBUUMsR0FBRyxDQUFDO1lBQ1o1QixzQkFBc0I7WUFFdEIsSUFBSTtnQkFDRixNQUFNZ0ksdUJBQXVCMUY7Z0JBQzdCWCxRQUFRQyxHQUFHLENBQUM7WUFDZCxFQUFFLE9BQU9PLE9BQU87Z0JBQ2RSLFFBQVFRLEtBQUssQ0FBQyxjQUFjQTtZQUM5QixTQUFVO2dCQUNSbkMsc0JBQXNCO1lBQ3hCO1FBQ0Y7SUFDRjtJQUVBLGVBQWU7SUFDZixNQUFNbUksNEJBQTRCLENBQUNwQztRQUNqQ3BFLFFBQVFDLEdBQUcsQ0FBQyw0QkFBa0JtRTtRQUM5QnpHLDRCQUE0QnlHO0lBQzlCO0lBRUEsYUFBYTtJQUNiLE1BQU1xQywwQkFBMEI7UUFDOUJ2SywrQkFBK0I7UUFDL0JzQiw0QkFBNEJDO1FBQzVCRSw0QkFBNEJGO1FBQzVCUyw2QkFBNkIsRUFBRTtRQUMvQkMsNkJBQTZCLEVBQUU7UUFDL0J5QixrQkFBa0J5RCxXQUFXO0lBQy9CO0lBRUEsV0FBVztJQUNYLE1BQU1xRCx5QkFBeUI7UUFDN0J4SywrQkFBK0I7UUFDL0IsTUFBTWtLO0lBQ1I7SUFFQSxPQUFPO0lBQ1AsTUFBTU8sc0JBQXNCLE9BQU96RjtRQUNqQyxJQUFJO1lBQ0YsSUFBSSxDQUFDeEQsNEJBQTRCLENBQUNILDBCQUEwQjtnQkFDMURwRCxpSkFBT0EsQ0FBQ3FHLEtBQUssQ0FBQztnQkFDZDtZQUNGO1lBRUFuQyxzQkFBc0I7WUFDdEIyQixRQUFRQyxHQUFHLENBQUMsMkJBQWlCdkM7WUFDN0JzQyxRQUFRQyxHQUFHLENBQUMsc0JBQVkxQztZQUN4QnlDLFFBQVFDLEdBQUcsQ0FBQyxzQkFBWWlCO1lBRXhCLGNBQWM7WUFDZCxNQUFNMEYsaUJBQWlCNUwsMEJBQTBCNkwsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFNUMsRUFBRSxLQUFLeEc7WUFDcEUsSUFBSSxDQUFDa0osZ0JBQWdCO2dCQUNuQnpNLGlKQUFPQSxDQUFDcUcsS0FBSyxDQUFDO2dCQUNkO1lBQ0Y7WUFFQVIsUUFBUUMsR0FBRyxDQUFDLHdCQUFjMkc7WUFFMUIsZUFBZTtZQUNmNUcsUUFBUUMsR0FBRyxDQUFDLGdDQUFzQnZDO1lBQ2xDLE1BQU0sRUFBRXdDLE1BQU1ILEdBQUcsRUFBRSxHQUFHLE1BQU1yRixzREFBU0EsQ0FBQ3FNLGFBQWEsQ0FBQ3JKO1lBRXBELElBQUlxQyxJQUFJTyxJQUFJLEtBQUssS0FBSztnQkFDcEJuRyxpSkFBT0EsQ0FBQ2lKLE9BQU8sQ0FBQztnQkFDaEJxRDtnQkFFQSxXQUFXO2dCQUNYekcsUUFBUUMsR0FBRyxDQUFDLGdCQUFnQkYsSUFBSUcsSUFBSTtnQkFFcEMsU0FBUztnQkFDVCxNQUFNYztnQkFFTixvQkFBb0I7Z0JBQ3BCLElBQUl6RCw0QkFBNEJMLGVBQWVzSCxHQUFHLENBQUNqSCwyQkFBMkI7b0JBQzVFLE1BQU1tRCxtQkFBbUJuRDtnQkFDM0I7WUFDRixPQUFPO2dCQUNMeUMsUUFBUVEsS0FBSyxDQUFDLGFBQWFULElBQUlVLEdBQUc7Z0JBQ2xDdEcsaUpBQU9BLENBQUNxRyxLQUFLLENBQUNULElBQUlVLEdBQUcsSUFBSTtZQUMzQjtRQUNGLEVBQUUsT0FBT0QsT0FBWTtnQkFFTUE7WUFEekJSLFFBQVFRLEtBQUssQ0FBQyxhQUFhQTtZQUMzQlIsUUFBUVEsS0FBSyxDQUFDLFlBQVdBLGtCQUFBQSxNQUFNcUQsUUFBUSxjQUFkckQsc0NBQUFBLGdCQUFnQk4sSUFBSTtZQUM3Qy9GLGlKQUFPQSxDQUFDcUcsS0FBSyxDQUFDO1FBQ2hCLFNBQVU7WUFDUm5DLHNCQUFzQjtRQUN4QjtJQUNGO0lBRUEsV0FBVztJQUNYLE1BQU0ySSxvQkFBb0I7UUFDeEI5SywrQkFBK0I7UUFDL0JzQiw0QkFBNEJDO1FBQzVCRSw0QkFBNEJGO1FBQzVCSSx3QkFBd0IsRUFBRTtRQUMxQkksd0JBQXdCLEVBQUU7UUFDMUIyQixrQkFBa0J5RCxXQUFXO0lBQy9CO0lBTUEsU0FBUztJQUNULE1BQU00RCxvQkFBa0QsT0FBT0M7UUFDN0QsTUFBTSxFQUFFeEUsSUFBSSxFQUFFeUUsU0FBUyxFQUFFQyxPQUFPLEVBQUUsR0FBR0Y7UUFFckMsSUFBSTtZQUNGLGdCQUFnQjtZQUNoQixNQUFNM0YsTUFBTSxNQUFNNUcsc0RBQVNBLENBQUMwTSxXQUFXLENBQUMzRTtZQUN4QzFDLFFBQVFDLEdBQUcsQ0FBQyxtQkFBbUJzQjtZQUUvQjNFLGlCQUFpQjJFO1lBQ2pCNEYsc0JBQUFBLGdDQUFBQSxVQUFZO2dCQUFFNUYsS0FBS0E7WUFBSTtZQUN2QnBILGlKQUFPQSxDQUFDaUosT0FBTyxDQUFDO1FBQ2xCLEVBQUUsT0FBTzVDLE9BQVk7WUFDbkJSLFFBQVFRLEtBQUssQ0FBQyxlQUFlQTtZQUM3QnJHLGlKQUFPQSxDQUFDcUcsS0FBSyxDQUFDLFNBQWtDLE9BQXpCQSxNQUFNckcsT0FBTyxJQUFJO1lBQ3hDaU4sb0JBQUFBLDhCQUFBQSxRQUFVNUc7UUFDWjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQU04RyxvQkFBb0I7UUFDeEIxSyxpQkFBaUI7UUFDakIsT0FBTztJQUNUO0lBRUEsYUFBYTtJQUNiLE1BQU0ySywwQkFBd0QsT0FBT0w7UUFDbkUsTUFBTSxFQUFFeEUsSUFBSSxFQUFFeUUsU0FBUyxFQUFFQyxPQUFPLEVBQUUsR0FBR0Y7UUFFckMsSUFBSTtZQUNGLGdCQUFnQjtZQUNoQixNQUFNM0YsTUFBTSxNQUFNNUcsc0RBQVNBLENBQUMwTSxXQUFXLENBQUMzRTtZQUN4QzFDLFFBQVFDLEdBQUcsQ0FBQyxtQkFBbUJzQjtZQUUvQjlDLHVCQUF1QjhDO1lBQ3ZCNEYsc0JBQUFBLGdDQUFBQSxVQUFZO2dCQUFFNUYsS0FBS0E7WUFBSTtZQUN2QnBILGlKQUFPQSxDQUFDaUosT0FBTyxDQUFDO1FBQ2xCLEVBQUUsT0FBTzVDLE9BQVk7WUFDbkJSLFFBQVFRLEtBQUssQ0FBQyxlQUFlQTtZQUM3QnJHLGlKQUFPQSxDQUFDcUcsS0FBSyxDQUFDLFNBQWtDLE9BQXpCQSxNQUFNckcsT0FBTyxJQUFJO1lBQ3hDaU4sb0JBQUFBLDhCQUFBQSxRQUFVNUc7UUFDWjtJQUNGO0lBRUEsV0FBVztJQUNYLE1BQU1nSCwwQkFBMEI7UUFDOUIvSSx1QkFBdUI7UUFDdkIsT0FBTztJQUNUO0lBRUEsV0FBVztJQUNYLE1BQU1nSixpQ0FBK0QsT0FBT1A7UUFDMUUsTUFBTSxFQUFFeEUsSUFBSSxFQUFFeUUsU0FBUyxFQUFFQyxPQUFPLEVBQUUsR0FBR0Y7UUFFckMsSUFBSTtZQUNGLGdCQUFnQjtZQUNoQixNQUFNM0YsTUFBTSxNQUFNNUcsc0RBQVNBLENBQUMwTSxXQUFXLENBQUMzRTtZQUN4QzFDLFFBQVFDLEdBQUcsQ0FBQyxpQkFBaUJzQjtZQUU3QjVDLG1CQUFtQmtDLENBQUFBLE9BQVE7dUJBQUlBO29CQUFNVTtpQkFBSTtZQUN6QzRGLHNCQUFBQSxnQ0FBQUEsVUFBWTtnQkFBRTVGLEtBQUtBO2dCQUFLQyxNQUFNLEtBQWVBLElBQUk7WUFBQztZQUNsRHJILGlKQUFPQSxDQUFDaUosT0FBTyxDQUFDLE1BQTBCLE9BQXBCLEtBQWU1QixJQUFJLEVBQUM7UUFDNUMsRUFBRSxPQUFPaEIsT0FBWTtZQUNuQlIsUUFBUVEsS0FBSyxDQUFDLGFBQWFBO1lBQzNCckcsaUpBQU9BLENBQUNxRyxLQUFLLENBQUMsTUFBbUNBLE9BQTdCLEtBQWVnQixJQUFJLEVBQUMsV0FBa0MsT0FBekJoQixNQUFNckcsT0FBTyxJQUFJO1lBQ2xFaU4sb0JBQUFBLDhCQUFBQSxRQUFVNUc7UUFDWjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQU1rSCxpQ0FBaUMsT0FBT2hGO1lBQ3BCQTtRQUF4QixNQUFNbkIsTUFBTW1CLEtBQUtuQixHQUFHLE1BQUltQixpQkFBQUEsS0FBS21CLFFBQVEsY0FBYm5CLHFDQUFBQSxlQUFlbkIsR0FBRztRQUMxQzVDLG1CQUFtQmtDLENBQUFBLE9BQVFBLEtBQUs4RyxNQUFNLENBQUNDLENBQUFBLElBQUtBLE1BQU1yRztRQUNsRCxPQUFPO0lBQ1Q7SUFDQSxTQUFTO0lBQ1QsTUFBTXNHLG9CQUFrRCxPQUFPWDtRQUM3RCxNQUFNLEVBQUV4RSxJQUFJLEVBQUV5RSxTQUFTLEVBQUVDLE9BQU8sRUFBRSxHQUFHRjtRQUVyQyxJQUFJO1lBQ0YsTUFBTTNGLE1BQU0sTUFBTTVHLHNEQUFTQSxDQUFDME0sV0FBVyxDQUFDM0U7WUFDeEMxQyxRQUFRQyxHQUFHLENBQUMsaUJBQWlCc0I7WUFFN0IxQyxrQkFBa0IwQztZQUNsQnhDLG1CQUFtQixLQUFleUMsSUFBSTtZQUV0QyxpQkFBaUI7WUFDakIsTUFBTXNHLGVBQWVyRyxTQUFTc0csYUFBYSxDQUFDO1lBQzVDRCxhQUFhRSxHQUFHLEdBQUd6RztZQUNuQnVHLGFBQWFHLGdCQUFnQixHQUFHO2dCQUM5QjVJLGlCQUFpQnVHLEtBQUtzQyxLQUFLLENBQUNKLGFBQWFLLFFBQVE7WUFDbkQ7WUFFQWhCLHNCQUFBQSxnQ0FBQUEsVUFBWTtnQkFBRTVGLEtBQUtBO1lBQUk7WUFDdkJwSCxpSkFBT0EsQ0FBQ2lKLE9BQU8sQ0FBQztRQUNsQixFQUFFLE9BQU81QyxPQUFZO1lBQ25CUixRQUFRUSxLQUFLLENBQUMsYUFBYUE7WUFDM0JyRyxpSkFBT0EsQ0FBQ3FHLEtBQUssQ0FBQyxXQUFvQyxPQUF6QkEsTUFBTXJHLE9BQU8sSUFBSTtZQUMxQ2lOLG9CQUFBQSw4QkFBQUEsUUFBVTVHO1FBQ1o7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNNEgsb0JBQW9CO1FBQ3hCdkosa0JBQWtCO1FBQ2xCRSxtQkFBbUI7UUFDbkJNLGlCQUFpQjtRQUNqQixPQUFPO0lBQ1Q7SUFFQSxTQUFTO0lBQ1QsTUFBTWdKLHVCQUFxRCxPQUFPbkI7UUFDaEUsTUFBTSxFQUFFeEUsSUFBSSxFQUFFeUUsU0FBUyxFQUFFQyxPQUFPLEVBQUUsR0FBR0Y7UUFFckMsSUFBSTtZQUNGLE1BQU0zRixNQUFNLE1BQU01RyxzREFBU0EsQ0FBQzBNLFdBQVcsQ0FBQzNFO1lBQ3hDMUMsUUFBUUMsR0FBRyxDQUFDLGlCQUFpQnNCO1lBRTdCdEMscUJBQXFCc0M7WUFDckJwQyxzQkFBc0IsS0FBZXFDLElBQUk7WUFDekMyRixzQkFBQUEsZ0NBQUFBLFVBQVk7Z0JBQUU1RixLQUFLQTtZQUFJO1lBQ3ZCcEgsaUpBQU9BLENBQUNpSixPQUFPLENBQUM7UUFDbEIsRUFBRSxPQUFPNUMsT0FBWTtZQUNuQlIsUUFBUVEsS0FBSyxDQUFDLGFBQWFBO1lBQzNCckcsaUpBQU9BLENBQUNxRyxLQUFLLENBQUMsV0FBb0MsT0FBekJBLE1BQU1yRyxPQUFPLElBQUk7WUFDMUNpTixvQkFBQUEsOEJBQUFBLFFBQVU1RztRQUNaO0lBQ0Y7SUFFQSxTQUFTO0lBQ1QsTUFBTThILHVCQUF1QjtRQUMzQnJKLHFCQUFxQjtRQUNyQkUsc0JBQXNCO1FBQ3RCLE9BQU87SUFDVDtJQUVBLFNBQVM7SUFDVCxNQUFNb0osb0JBQWtELE9BQU9yQjtRQUM3RCxNQUFNLEVBQUV4RSxJQUFJLEVBQUV5RSxTQUFTLEVBQUVDLE9BQU8sRUFBRSxHQUFHRjtRQUVyQyxJQUFJO1lBQ0YsTUFBTTNGLE1BQU0sTUFBTTVHLHNEQUFTQSxDQUFDME0sV0FBVyxDQUFDM0U7WUFDeEMxQyxRQUFRQyxHQUFHLENBQUMsaUJBQWlCc0I7WUFFN0IrQixrQkFBa0IvQjtZQUNsQmdDLG1CQUFtQixLQUFlL0IsSUFBSTtZQUN0QzJGLHNCQUFBQSxnQ0FBQUEsVUFBWTtnQkFBRTVGLEtBQUtBO1lBQUk7WUFDdkJwSCxpSkFBT0EsQ0FBQ2lKLE9BQU8sQ0FBQztRQUNsQixFQUFFLE9BQU81QyxPQUFZO1lBQ25CUixRQUFRUSxLQUFLLENBQUMsYUFBYUE7WUFDM0JyRyxpSkFBT0EsQ0FBQ3FHLEtBQUssQ0FBQyxXQUFvQyxPQUF6QkEsTUFBTXJHLE9BQU8sSUFBSTtZQUMxQ2lOLG9CQUFBQSw4QkFBQUEsUUFBVTVHO1FBQ1o7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNZ0ksb0JBQW9CO1FBQ3hCbEYsa0JBQWtCO1FBQ2xCQyxtQkFBbUI7UUFDbkIsT0FBTztJQUNUO0lBTUEsVUFBVTtJQUNWLE1BQU1rRixnQkFBZ0IsT0FBT0M7UUFDM0J0TSxpQkFBaUJzTTtRQUNqQmxKLGVBQWUrRyxjQUFjLENBQUNtQztRQUM5QmhOLDRCQUE0QjtJQUM5QjtJQUVBLFNBQVM7SUFDVCxNQUFNaU4sa0JBQWtCLENBQUMxTixjQUFjLEVBQUUsRUFBRTBNLE1BQU0sQ0FBQ2UsQ0FBQUEsU0FDaERBLE9BQU9sSCxJQUFJLENBQUNvSCxXQUFXLEdBQUdoRixRQUFRLENBQUN2SCxjQUFjdU0sV0FBVyxPQUM1REYsT0FBTzVHLFdBQVcsQ0FBQzhHLFdBQVcsR0FBR2hGLFFBQVEsQ0FBQ3ZILGNBQWN1TSxXQUFXLE9BQ25FRixPQUFPRyxRQUFRLENBQUNELFdBQVcsR0FBR2hGLFFBQVEsQ0FBQ3ZILGNBQWN1TSxXQUFXO0lBR2xFLDRCQUE0QjtJQUM1QixNQUFNRSxtQkFBbUI7UUFDdkIsTUFBTUMsWUFBbUIsRUFBRTtRQUUzQi9JLFFBQVFDLEdBQUcsQ0FBQztRQUNaRCxRQUFRQyxHQUFHLENBQUMsd0JBQWNwRDtRQUMxQm1ELFFBQVFDLEdBQUcsQ0FBQyx1QkFBYXFDLE1BQU0wRyxJQUFJLENBQUM5TDtRQUNwQzhDLFFBQVFDLEdBQUcsQ0FBQyx1QkFBYWxEO1FBRXpCRixXQUFXb00sT0FBTyxDQUFDckUsQ0FBQUE7WUFDakIsVUFBVTtZQUNWbUUsVUFBVUcsSUFBSSxDQUFDO2dCQUNiQyxLQUFLLFVBQW9CLE9BQVZ2RSxPQUFPVixFQUFFO2dCQUN4QkEsSUFBSVUsT0FBT1YsRUFBRTtnQkFDYnRDLE9BQU9nRCxPQUFPaEQsS0FBSztnQkFDbkJrQyxRQUFRYyxPQUFPZCxNQUFNO2dCQUNyQnNGLE1BQU07Z0JBQ05DLFlBQVluTSxlQUFlc0gsR0FBRyxDQUFDSSxPQUFPVixFQUFFO2dCQUN4Q3ZELFVBQVVpRSxPQUFPVixFQUFFO1lBQ3JCO1lBRUEsaUJBQWlCO1lBQ2pCLElBQUloSCxlQUFlc0gsR0FBRyxDQUFDSSxPQUFPVixFQUFFLEdBQUc7Z0JBQ2pDLE1BQU1vRixhQUFhdk0saUJBQWlCd00sR0FBRyxDQUFDM0UsT0FBT1YsRUFBRSxLQUFLLEVBQUU7Z0JBQ3hEbEUsUUFBUUMsR0FBRyxDQUFDLG1CQUFtQixPQUFWMkUsT0FBT1YsRUFBRSxFQUFDLFdBQVNvRjtnQkFFeENBLFdBQVdMLE9BQU8sQ0FBQ1AsQ0FBQUE7b0JBQ2pCSyxVQUFVRyxJQUFJLENBQUM7d0JBQ2JDLEtBQUssVUFBb0IsT0FBVlQsT0FBT3hFLEVBQUU7d0JBQ3hCQSxJQUFJd0UsT0FBT3hFLEVBQUU7d0JBQ2J0QyxPQUFPOEcsT0FBTzlHLEtBQUs7d0JBQ25Ca0MsUUFBUTRFLE9BQU81RSxNQUFNO3dCQUNyQnNGLE1BQU07d0JBQ056SSxVQUFVaUUsT0FBT1YsRUFBRTt3QkFDbkJzRixtQkFBbUI1RSxPQUFPaEQsS0FBSztvQkFDakM7Z0JBQ0Y7WUFDRjtRQUNGO1FBRUE1QixRQUFRQyxHQUFHLENBQUMsd0JBQWM4STtRQUMxQixPQUFPQTtJQUNUO0lBRUEsUUFBUTtJQUNSLE1BQU1VLFVBQVU7UUFDZDtZQUNFN0gsT0FBTztZQUNQOEgsV0FBVztZQUNYUCxLQUFLO1lBQ0xRLE9BQU87UUFDVDtRQUNBO1lBQ0UvSCxPQUFPO1lBQ1A4SCxXQUFXO1lBQ1hQLEtBQUs7WUFDTFMsUUFBUSxDQUFDQyxNQUFjQztnQkFDckIsSUFBSUEsT0FBT1YsSUFBSSxLQUFLLFVBQVU7b0JBQzVCLHFCQUNFLDhEQUFDVzt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUN2USxpSkFBTUE7Z0NBQ0wyUCxNQUFLO2dDQUNMYSxNQUFLO2dDQUNMQyxTQUFTLElBQU0zRixzQkFBc0J1RixPQUFPNUYsRUFBRTtnQ0FDOUM4RixXQUFVO2dDQUNWRyxPQUFPO29DQUFFQyxVQUFVO29DQUFRQyxRQUFRO2dDQUFPOzBDQUV6Q1AsT0FBT1QsVUFBVSxHQUFHLE1BQU07Ozs7OzswQ0FFN0IsOERBQUNpQjtnQ0FBS04sV0FBVTswQ0FBdUNIOzs7Ozs7MENBQ3ZELDhEQUFDN1Asa0pBQUdBO2dDQUFDdVEsT0FBTTtnQ0FBT1AsV0FBVTswQ0FBVTs7Ozs7Ozs7Ozs7O2dCQUc1QyxPQUFPO29CQUNMLHFCQUNFLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNNO2dDQUFLTixXQUFVOzBDQUFnQjs7Ozs7OzBDQUNoQyw4REFBQ007Z0NBQUtOLFdBQVU7MENBQWlCSDs7Ozs7OzBDQUNqQyw4REFBQzdQLGtKQUFHQTtnQ0FBQ3VRLE9BQU07Z0NBQVFQLFdBQVU7MENBQVU7Ozs7Ozs7Ozs7OztnQkFHN0M7WUFDRjtRQUNGO1FBQ0E7WUFDRXBJLE9BQU87WUFDUDhILFdBQVc7WUFDWFAsS0FBSztZQUNMUSxPQUFPO1lBQ1BDLFFBQVEsQ0FBQzlGLFFBQWdCZ0c7Z0JBQ3ZCLE1BQU1VLGtCQUFrQixDQUFDMUc7b0JBQ3ZCLE9BQVFBO3dCQUNOLEtBQUs7NEJBQUcsT0FBTztnQ0FBRXlHLE9BQU87Z0NBQVNWLE1BQU07NEJBQU07d0JBQzdDLEtBQUs7NEJBQUcsT0FBTztnQ0FBRVUsT0FBTztnQ0FBVVYsTUFBTTs0QkFBSzt3QkFDN0MsS0FBSzs0QkFBRyxPQUFPO2dDQUFFVSxPQUFPO2dDQUFPVixNQUFNOzRCQUFNO3dCQUMzQzs0QkFBUyxPQUFPO2dDQUFFVSxPQUFPO2dDQUFRVixNQUFNOzRCQUFLO29CQUM5QztnQkFDRjtnQkFFQSxNQUFNWSxTQUFTRCxnQkFBZ0IxRztnQkFDL0IscUJBQU8sOERBQUM5SixrSkFBR0E7b0JBQUN1USxPQUFPRSxPQUFPRixLQUFLOzhCQUFHRSxPQUFPWixJQUFJOzs7Ozs7WUFDL0M7UUFDRjtRQUNBO1lBQ0VqSSxPQUFPO1lBQ1B1SCxLQUFLO1lBQ0xRLE9BQU87WUFDUEMsUUFBUSxDQUFDRTtnQkFDUCxJQUFJQSxPQUFPVixJQUFJLEtBQUssVUFBVTtvQkFDNUIscUJBQ0UsOERBQUNyUCxrSkFBS0E7d0JBQUNrUSxNQUFLO2tDQUNWLDRFQUFDeFEsaUpBQU1BOzRCQUNMMlAsTUFBSzs0QkFDTGEsTUFBSzs0QkFDTFMsb0JBQU0sOERBQUNyUSxzSkFBWUE7Ozs7OzRCQUNuQjZQLFNBQVM7Z0NBQ1AvUCxpSkFBT0EsQ0FBQzZMLElBQUksQ0FBQzs0QkFDZjtzQ0FDRDs7Ozs7Ozs7Ozs7Z0JBS1AsT0FBTztvQkFDTCxxQkFDRSw4REFBQ2pNLGtKQUFLQTt3QkFBQ2tRLE1BQUs7OzBDQUNWLDhEQUFDeFEsaUpBQU1BO2dDQUNMMlAsTUFBSztnQ0FDTGEsTUFBSztnQ0FDTFMsb0JBQU0sOERBQUNyUSxzSkFBWUE7Ozs7O2dDQUNuQjZQLFNBQVM7b0NBQ1AvUCxpSkFBT0EsQ0FBQzZMLElBQUksQ0FBQztnQ0FDZjtnQ0FDQWdFLFdBQVU7MENBQ1g7Ozs7OzswQ0FHRCw4REFBQy9QLGtKQUFVQTtnQ0FDVDJILHFCQUNFLDhEQUFDbUk7O3NEQUNDLDhEQUFDWTtzREFBRTs7Ozs7O3NEQUNILDhEQUFDQTs0Q0FBRVgsV0FBVTs7Z0RBQXdCO2dEQUFNRixPQUFPbEksS0FBSzs7Ozs7OztzREFDdkQsOERBQUMrSTs0Q0FBRVgsV0FBVTs7Z0RBQXdCO2dEQUFNRixPQUFPTixpQkFBaUI7Ozs7Ozs7Ozs7Ozs7Z0NBR3ZFb0IsV0FBVztvQ0FDVDVLLFFBQVFDLEdBQUcsQ0FBQyw0QkFBa0I2SjtvQ0FDOUJ4RixzQkFBc0J3RixPQUFPNUYsRUFBRSxFQUFFNEYsT0FBT25KLFFBQVE7Z0NBQ2xEO2dDQUNBa0ssUUFBTztnQ0FDUEMsWUFBVztnQ0FDWEMsUUFBTzswQ0FFUCw0RUFBQ3RSLGlKQUFNQTtvQ0FDTDJQLE1BQUs7b0NBQ0xhLE1BQUs7b0NBQ0xlLE1BQU07b0NBQ05OLG9CQUFNLDhEQUFDcFEsc0pBQWNBOzs7OztvQ0FDckIwUCxXQUFVOzhDQUNYOzs7Ozs7Ozs7Ozs7Ozs7OztnQkFNVDtZQUNGO1FBQ0Y7S0FDRDtJQUVELFNBQVM7SUFDVCxzQ0FBc0M7SUFDdEMsVUFBVTtJQUNWLDJEQUEyRDtJQUMzRCw4QkFBOEI7SUFDOUIsK0JBQStCO0lBQy9CLDRDQUE0QztJQUM1QyxlQUFlO0lBQ2YsMENBQTBDO0lBQzFDLGtCQUFrQjtJQUNsQiwrQkFBK0I7SUFDL0IsOEhBQThIO0lBQzlILDJIQUEySDtJQUMzSCw2SEFBNkg7SUFDN0gsNkhBQTZIO0lBQzdILDRIQUE0SDtJQUM1SCw4SEFBOEg7SUFDOUgsV0FBVztJQUNYLG1DQUFtQztJQUNuQyxRQUFRO0lBQ1Isc0JBQXNCO0lBQ3RCLHlDQUF5QztJQUN6QyxnQkFBZ0I7SUFDaEIsNkJBQTZCO0lBQzdCLDRIQUE0SDtJQUM1SCx5SEFBeUg7SUFDekgsMkhBQTJIO0lBQzNILDJIQUEySDtJQUMzSCwwSEFBMEg7SUFDMUgsNEhBQTRIO0lBQzVILFNBQVM7SUFDVCxpQ0FBaUM7SUFDakMsOENBQThDO0lBQzlDLE1BQU07SUFDTixLQUFLO0lBRUwsdUJBQXVCO0lBQ3ZCLE1BQU03RSxrQkFBa0I7UUFDdEIsSUFBSTtZQUNGbkYsUUFBUUMsR0FBRyxDQUFDO1lBQ1osTUFBTSxFQUFFQyxNQUFNSCxHQUFHLEVBQUUsR0FBRyxNQUFNckYsc0RBQVNBLENBQUN1USxhQUFhLENBQUM7Z0JBQ2xEN0ssTUFBTTtnQkFDTkMsVUFBVTtnQkFDVnlELFFBQVEsRUFBTyxXQUFXO1lBQzVCO1lBRUE5RCxRQUFRQyxHQUFHLENBQUMscUNBQTJCRjtZQUV2QyxJQUFJQSxJQUFJTyxJQUFJLEtBQUssT0FBT1AsSUFBSUcsSUFBSSxJQUFJSCxJQUFJRyxJQUFJLENBQUNLLElBQUksRUFBRTtnQkFDakQsTUFBTTJLLE9BQU9uTCxJQUFJRyxJQUFJLENBQUNLLElBQUksQ0FBQ2tDLEdBQUcsQ0FBQyxDQUFDMEksTUFBYzt3QkFDNUNqSCxJQUFJaUgsSUFBSWpILEVBQUU7d0JBQ1YxQyxNQUFNMkosSUFBSTNKLElBQUk7d0JBQ2QrSSxPQUFPWSxJQUFJWixLQUFLO3dCQUNoQjFCLFVBQVVzQyxJQUFJdEMsUUFBUTt3QkFDdEIvRyxhQUFhcUosSUFBSXJKLFdBQVcsSUFBSTtvQkFDbEM7Z0JBRUFwRixjQUFjd087Z0JBQ2RsTCxRQUFRQyxHQUFHLENBQUMsaUJBQWlCaUw7WUFDL0IsT0FBTztnQkFDTGxMLFFBQVFvTCxJQUFJLENBQUMsbUJBQW1Cckw7Z0JBQ2hDckQsY0FBYyxFQUFFO2dCQUNoQm1ELGFBQWEyRCxPQUFPLENBQUM7WUFDdkI7UUFDRixFQUFFLE9BQU9oRCxPQUFPO1lBQ2RSLFFBQVFRLEtBQUssQ0FBQyxlQUFlQTtZQUM3QjlELGNBQWMsRUFBRTtZQUNoQm1ELGFBQWFXLEtBQUssQ0FBQztRQUNyQjtJQUNGO0lBRUEsdUJBQXVCO0lBQ3ZCLE1BQU02SyxvQkFBb0I7UUFDeEIsSUFBSTtnQkFVRXRMLHNCQUFBQTtZQVRKQyxRQUFRQyxHQUFHLENBQUM7WUFDWixNQUFNLEVBQUVDLE1BQU1ILEdBQUcsRUFBRSxHQUFHLE1BQU1yRixzREFBU0EsQ0FBQ3lGLG9CQUFvQixDQUFDO2dCQUN6REMsTUFBTTtnQkFDTkMsVUFBVSxHQUFHLGdCQUFnQjtZQUMvQjtZQUVBTCxRQUFRQyxHQUFHLENBQUMsNENBQWtDRjtZQUU5QyxZQUFZO1lBQ1osSUFBSUEsRUFBQUEsWUFBQUEsSUFBSUcsSUFBSSxjQUFSSCxpQ0FBQUEsdUJBQUFBLFVBQVV1TCxVQUFVLGNBQXBCdkwsMkNBQUFBLHFCQUFzQndMLEtBQUssSUFBRyxJQUFJO2dCQUNwQ3ZMLFFBQVFDLEdBQUcsQ0FBQyxhQUF1QyxPQUExQkYsSUFBSUcsSUFBSSxDQUFDb0wsVUFBVSxDQUFDQyxLQUFLLEVBQUM7WUFDckQ7WUFFQSxJQUFJeEwsSUFBSU8sSUFBSSxLQUFLLE9BQU9QLElBQUlHLElBQUksRUFBRTtnQkFDaENGLFFBQVFDLEdBQUcsQ0FBQyw4QkFBb0JGLElBQUlHLElBQUk7Z0JBRXhDLElBQUlILElBQUlHLElBQUksQ0FBQ0ssSUFBSSxJQUFJK0IsTUFBTUMsT0FBTyxDQUFDeEMsSUFBSUcsSUFBSSxDQUFDSyxJQUFJLEdBQUc7b0JBQ2pEUCxRQUFRQyxHQUFHLENBQUMsb0JBQStCLE9BQXJCRixJQUFJRyxJQUFJLENBQUNLLElBQUksQ0FBQzZCLE1BQU0sRUFBQztvQkFFM0MsMEJBQTBCO29CQUMxQixNQUFNb0osa0JBQWtCekwsSUFBSUcsSUFBSSxDQUFDSyxJQUFJLENBQUNrQyxHQUFHLENBQUMsQ0FBQ2dKLE1BQVdDOzRCQWdCMUNEO3dCQWZWekwsUUFBUUMsR0FBRyxDQUFDLG9CQUFvQixPQUFWeUwsUUFBUSxHQUFFLFVBQVE7NEJBQ3RDeEgsSUFBSXVILEtBQUt2SCxFQUFFOzRCQUNYdEMsT0FBTzZKLEtBQUs3SixLQUFLOzRCQUNqQmlILFVBQVU0QyxLQUFLNUMsUUFBUTs0QkFDdkI4QyxlQUFlRixLQUFLRSxhQUFhOzRCQUNqQ1QsTUFBTU8sS0FBS1AsSUFBSTt3QkFDakI7d0JBRUEsT0FBTzs0QkFDTGhILElBQUl1SCxLQUFLdkgsRUFBRTs0QkFDWHRDLE9BQU82SixLQUFLN0osS0FBSzs0QkFDakJFLGFBQWEySixLQUFLM0osV0FBVzs0QkFDN0JDLFlBQVkwSixLQUFLMUosVUFBVSxJQUFJOzRCQUMvQjhHLFVBQVU0QyxLQUFLRSxhQUFhLElBQUtGLENBQUFBLEtBQUs1QyxRQUFRLEtBQUssSUFBSSxPQUFPLElBQUc7NEJBQ2pFK0MsWUFBWSxFQUFFOzRCQUNkQyxRQUFRSixFQUFBQSxhQUFBQSxLQUFLUCxJQUFJLGNBQVRPLGlDQUFBQSxXQUFXaEosR0FBRyxDQUFDLENBQUMwSSxNQUFhQSxJQUFJakgsRUFBRSxNQUFLLEVBQUU7NEJBQ2xENEgsV0FBV0wsS0FBS0ssU0FBUyxJQUFJLElBQUlDLE9BQU9DLFdBQVc7NEJBQ25EQyxXQUFXUixLQUFLUSxTQUFTLElBQUksSUFBSUYsT0FBT0MsV0FBVzt3QkFDckQ7b0JBQ0Y7b0JBRUF6TixnQkFBZ0JpTjtvQkFDaEJ4TCxRQUFRQyxHQUFHLENBQUMsaUJBQWlCdUw7Z0JBQy9CLE9BQU87b0JBQ0x4TCxRQUFRb0wsSUFBSSxDQUFDLGlDQUFpQ3JMLElBQUlHLElBQUk7b0JBQ3REM0IsZ0JBQWdCLEVBQUU7Z0JBQ3BCO1lBQ0YsT0FBTztnQkFDTHlCLFFBQVFvTCxJQUFJLENBQUMsbUJBQW1CO29CQUM5QjlLLE1BQU1QLElBQUlPLElBQUk7b0JBQ2RuRyxTQUFTNEYsSUFBSTVGLE9BQU87b0JBQ3BCK0YsTUFBTUgsSUFBSUcsSUFBSTtnQkFDaEI7Z0JBQ0EzQixnQkFBZ0IsRUFBRTtZQUNwQjtRQUNGLEVBQUUsT0FBT2lDLE9BQU87WUFDZFIsUUFBUVEsS0FBSyxDQUFDLGVBQWVBO1lBQzdCakMsZ0JBQWdCLEVBQUU7WUFDbEJzQixhQUFhVyxLQUFLLENBQUM7UUFDckI7SUFDRjtJQUVBakgsZ0RBQVNBLENBQUM7UUFDUnlIO1FBRUFtRTtRQUNBa0c7SUFDRixHQUFHLEVBQUU7SUFFTCxxQkFDRTs7MEJBQ0UsOERBQUM3UixrSkFBSUE7Z0JBQ0hvSSxPQUFNO2dCQUNOc0sscUJBQU8sOERBQUN6UyxpSkFBTUE7b0JBQUMyUCxNQUFLO29CQUFVYyxTQUFTO3dCQUNyQ2xKO3dCQUNBMUYsd0JBQXdCO29CQUMxQjs4QkFBRzs7Ozs7O2dCQUNIME8sV0FBVTswQkFFViw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDdlEsaUpBQU1BOzRCQUFDMFMsS0FBSzs0QkFBQ2pDLFNBQVMsSUFBTTFPLDJCQUEyQjtzQ0FBTzs7Ozs7O3NDQUcvRCw4REFBQy9CLGlKQUFNQTs0QkFBQzBTLEtBQUs7NEJBQUNqQyxTQUFTLElBQU10TywyQkFBMkI7c0NBQU87Ozs7OztzQ0FHL0QsOERBQUNuQyxpSkFBTUE7NEJBQUMwUyxLQUFLOzRCQUFDakMsU0FBUyxJQUFNcE8sd0JBQXdCOzRCQUFPc04sTUFBSztzQ0FBUzs7Ozs7O3NDQUcxRSw4REFBQzNQLGlKQUFNQTs0QkFBQzBTLEtBQUs7NEJBQUNqQyxTQUFTeEQ7NEJBQXdCeUQsT0FBTztnQ0FBRWlDLGlCQUFpQjtnQ0FBU0MsYUFBYTtnQ0FBVzlCLE9BQU87NEJBQVk7c0NBQUc7Ozs7OztzQ0FHaEksOERBQUM5USxpSkFBTUE7NEJBQUMwUyxLQUFLOzRCQUFDakMsU0FBUyxJQUFNbE8sK0JBQStCOzRCQUFPbU8sT0FBTztnQ0FBRWlDLGlCQUFpQjtnQ0FBU0MsYUFBYTtnQ0FBVzlCLE9BQU87NEJBQVk7c0NBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU94Siw4REFBQzVRLGtKQUFLQTtnQkFDSmlJLE9BQU07Z0JBQ04wSyxNQUFNalI7Z0JBQ05rUixVQUFVLElBQU1qUix3QkFBd0I7Z0JBQ3hDa1IsUUFBUTtnQkFDUjdDLE9BQU87O2tDQUVQLDhEQUFDSTt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3BQO3dDQUNDNlIsYUFBWTt3Q0FDWkMsVUFBVTt3Q0FDVnZDLE9BQU87NENBQUVSLE9BQU87d0NBQUk7d0NBQ3BCZ0QsVUFBVXJRO3dDQUNWc1EsVUFBVSxDQUFDQyxJQUFNdlEsaUJBQWlCdVEsRUFBRUMsTUFBTSxDQUFDQyxLQUFLOzs7Ozs7a0RBRWxELDhEQUFDaEQ7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDdlEsaUpBQU1BO2dEQUNMMlAsTUFBSztnREFDTHNCLG9CQUFNLDhEQUFDdFEsc0pBQVlBOzs7OztnREFDbkI4UCxTQUFTLElBQU0xTywyQkFBMkI7MERBQzNDOzs7Ozs7MERBR0QsOERBQUMvQixpSkFBTUE7Z0RBQ0wyUCxNQUFLO2dEQUNMc0Isb0JBQU0sOERBQUN0USxzSkFBWUE7Ozs7O2dEQUNuQjhQLFNBQVMsSUFBTXRPLDJCQUEyQjswREFDM0M7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FPTCw4REFBQ21PO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDTTs7b0RBQUs7a0VBQVEsOERBQUMwQzt3REFBT2hELFdBQVU7a0VBQWlCbk4sV0FBV3VGLE1BQU07Ozs7Ozs7Ozs7OzswREFDbEUsOERBQUNrSTs7b0RBQUs7a0VBQU8sOERBQUMwQzt3REFBT2hELFdBQVU7a0VBQWtCOU0sZUFBZStNLElBQUk7Ozs7Ozs7Ozs7OzswREFDcEUsOERBQUNLOztvREFBSztrRUFBUSw4REFBQzBDO3dEQUFPaEQsV0FBVTtrRUFDN0IxSCxNQUFNMEcsSUFBSSxDQUFDak0saUJBQWlCbUUsTUFBTSxJQUFJK0wsTUFBTSxDQUFDLENBQUMxQixPQUFPMkIsVUFBWTNCLFFBQVEyQixRQUFROUssTUFBTSxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSTlGLDhEQUFDMkg7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDdlEsaUpBQU1BO2dEQUNMd1EsTUFBSztnREFDTGIsTUFBSztnREFDTGMsU0FBU3ZGO2dEQUNUd0ksVUFBVTlQO2dEQUNWMk0sV0FBVTswREFDWDs7Ozs7OzBEQUdELDhEQUFDdlEsaUpBQU1BO2dEQUNMd1EsTUFBSztnREFDTGIsTUFBSztnREFDTGMsU0FBU3JGO2dEQUNUc0ksVUFBVTlQO2dEQUNWMk0sV0FBVTswREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU9QLDhEQUFDdFEsa0pBQUtBO3dCQUNKK1AsU0FBU0E7d0JBQ1QyRCxZQUFZdEU7d0JBQ1p1RSxRQUFPO3dCQUNQbFMsU0FBU2tDO3dCQUNUaU8sWUFBWTs0QkFDVmpMLFVBQVU7NEJBQ1ZpTixpQkFBaUI7NEJBQ2pCQyxXQUFXLENBQUNoQyxRQUFVLEtBQVcsT0FBTkEsT0FBTTt3QkFDbkM7d0JBQ0F0QixNQUFLOzs7Ozs7Ozs7Ozs7MEJBS1QsOERBQUN0USxrSkFBS0E7Z0JBQ0ppSSxPQUFNO2dCQUNOMEssTUFBTS9RO2dCQUNOZ1IsVUFBVTtvQkFDUi9RLDJCQUEyQjtvQkFDM0I4RCxjQUFjK0QsV0FBVztvQkFDekI1RSx1QkFBdUI7b0JBQ3ZCRSxtQkFBbUIsRUFBRTtvQkFDckJFLGtCQUFrQjtvQkFDbEJFLG1CQUFtQjtvQkFDbkJFLHFCQUFxQjtvQkFDckJFLHNCQUFzQjtvQkFDdEJtRSxrQkFBa0I7b0JBQ2xCQyxtQkFBbUI7b0JBQ25CbEUsaUJBQWlCO2dCQUNuQjtnQkFDQW1PLE1BQU0sSUFBTWxPLGNBQWNtTyxNQUFNO2dCQUNoQzVDLFFBQU87Z0JBQ1BDLFlBQVc7MEJBRVgsNEVBQUNsUixpSkFBSUE7b0JBQ0g4VCxNQUFNcE87b0JBQ05xTyxRQUFPO29CQUNQQyxVQUFVM007O3NDQUVWLDhEQUFDckgsaUpBQUlBLENBQUNpVSxJQUFJOzRCQUNSck0sTUFBSzs0QkFDTHNNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7b0NBQU03VCxTQUFTO2dDQUFZOzZCQUFFO3NDQUVqRCw0RUFBQ0wsaUpBQU1BO2dDQUNMMlMsYUFBWTtnQ0FDWndCLFVBQVU7Z0NBQ1ZDLGtCQUFpQjtnQ0FDakIvRCxPQUFPO29DQUFFUixPQUFPO2dDQUFPOzBDQUV0QnJMLGFBQWFtRSxHQUFHLENBQUNtQyxDQUFBQSx1QkFDaEIsOERBQUMvSjt3Q0FBdUJrUyxPQUFPbkksT0FBT1YsRUFBRTt3Q0FBRXRDLE9BQU8sR0FBcUJnRCxPQUFsQkEsT0FBT2hELEtBQUssRUFBQyxPQUF3QixPQUFuQmdELE9BQU85QyxXQUFXO2tEQUN0Riw0RUFBQ2lJOzRDQUFJSSxPQUFPO2dEQUNWZ0UsVUFBVTtnREFDVkMsY0FBYztnREFDZEMsWUFBWTtnREFDWkMsVUFBVTs0Q0FDWjs7OERBQ0UsOERBQUNoRTtvREFBS0gsT0FBTzt3REFBRW9FLFlBQVk7b0RBQUk7OERBQUkzSixPQUFPaEQsS0FBSzs7Ozs7OzhEQUMvQyw4REFBQzBJO29EQUFLSCxPQUFPO3dEQUFFcUUsVUFBVTt3REFBUWpFLE9BQU87d0RBQVFrRSxZQUFZO29EQUFNOzt3REFBRzt3REFDakU3SixPQUFPaUUsUUFBUTt3REFBQzs7Ozs7Ozs7Ozs7Ozt1Q0FUWGpFLE9BQU9WLEVBQUU7Ozs7Ozs7Ozs7Ozs7OztzQ0FpQjVCLDhEQUFDdEssaUpBQUlBLENBQUNpVSxJQUFJOzRCQUNSck0sTUFBSzs0QkFDTHNNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7b0NBQU03VCxTQUFTO2dDQUFVOzZCQUFFO3NDQUUvQyw0RUFBQ04saUpBQUtBO2dDQUFDNFMsYUFBWTs7Ozs7Ozs7Ozs7c0NBR3JCLDhEQUFDN1MsaUpBQUlBLENBQUNpVSxJQUFJOzRCQUNSck0sTUFBSzs0QkFDTHNNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7b0NBQU03VCxTQUFTO2dDQUFVOzZCQUFFO3NDQUUvQyw0RUFBQ04saUpBQUtBLENBQUM2VSxRQUFRO2dDQUNiQyxNQUFNO2dDQUNObEMsYUFBWTtnQ0FDWm1DLFNBQVM7Z0NBQ1RDLFdBQVc7Ozs7Ozs7Ozs7O3NDQUlmLDhEQUFDalYsaUpBQUlBLENBQUNpVSxJQUFJOzRCQUNSQyxPQUFNOzRCQUNOQyxPQUFPO2dDQUFDO29DQUFFQyxVQUFVO29DQUFNN1QsU0FBUztnQ0FBVTs2QkFBRTtzQ0FFL0MsNEVBQUNELGtKQUFNQSxDQUFDNFUsT0FBTztnQ0FDYnROLE1BQUs7Z0NBQ0x1TixlQUFleEg7Z0NBQ2Z5SCxVQUFVeEg7Z0NBQ1Z5SCxRQUFPO2dDQUNQQyxVQUFVO2dDQUNWQyxVQUFTOzBDQUVSM1Esb0NBQ0MsOERBQUN1TDs4Q0FDQyw0RUFBQ3FGO3dDQUFJcEgsS0FBS3hKO3dDQUFxQjZRLEtBQUk7d0NBQVNsRixPQUFPOzRDQUFFUixPQUFPOzRDQUFRMkYsV0FBVzs0Q0FBU0MsV0FBVzt3Q0FBUTs7Ozs7Ozs7Ozs4REFHN0csOERBQUN4Rjs7c0RBQ0MsOERBQUNZOzRDQUFFWCxXQUFVO3NEQUNYLDRFQUFDeFAsc0pBQWFBOzs7Ozs7Ozs7O3NEQUVoQiw4REFBQ21ROzRDQUFFWCxXQUFVO3NEQUFrQjs7Ozs7O3NEQUMvQiw4REFBQ1c7NENBQUVYLFdBQVU7c0RBQWtCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQVF2Qyw4REFBQ3BRLGlKQUFJQSxDQUFDaVUsSUFBSTs0QkFDUnJNLE1BQUs7NEJBQ0xzTSxPQUFNOzRCQUNOQyxPQUFPO2dDQUNMO29DQUFFQyxVQUFVO29DQUFNN1QsU0FBUztnQ0FBVTtnQ0FDckM7b0NBQ0VpUCxNQUFNO29DQUNOb0csS0FBSztvQ0FDTHJWLFNBQVM7b0NBQ1RzVixXQUFXLENBQUMxQyxRQUFVMkMsT0FBTzNDO2dDQUMvQjs2QkFDRDs0QkFDRDRDLFNBQVE7c0NBRVIsNEVBQUM5VixpSkFBS0E7Z0NBQUN1UCxNQUFLO2dDQUFTcUQsYUFBWTtnQ0FBcUIrQyxLQUFLOzs7Ozs7Ozs7OztzQ0FNN0QsOERBQUM1VixpSkFBSUEsQ0FBQ2lVLElBQUk7NEJBQ1JDLE9BQU07NEJBQ042QixTQUFRO3NDQUVSLDRFQUFDelYsa0pBQU1BLENBQUM0VSxPQUFPO2dDQUNidE4sTUFBSztnQ0FDTHVOLGVBQWVsSDtnQ0FDZm1ILFVBQVU1RztnQ0FDVjZHLFFBQU87Z0NBQ1BDLFVBQVU7Z0NBQ1ZDLFVBQVM7MENBRVJ2USwrQkFDQyw4REFBQ21MOztzREFDQyw4REFBQ3pJOzRDQUNDMEcsS0FBS3BKOzRDQUNMdUwsT0FBTztnREFBRVIsT0FBTztnREFBUTJGLFdBQVc7NENBQVE7NENBQzNDTSxRQUFROzs7Ozs7c0RBRVYsOERBQUNqRjs0Q0FBRVIsT0FBTztnREFBRTBGLFdBQVc7Z0RBQUd0RixPQUFPOzRDQUFPO3NEQUNyQ3pMLG1CQUFtQjs7Ozs7Ozs7Ozs7OERBSXhCLDhEQUFDaUw7O3NEQUNDLDhEQUFDWTs0Q0FBRVgsV0FBVTtzREFDWCw0RUFBQ3hQLHNKQUFhQTs7Ozs7Ozs7OztzREFFaEIsOERBQUNtUTs0Q0FBRVgsV0FBVTtzREFBa0I7Ozs7OztzREFDL0IsOERBQUNXOzRDQUFFWCxXQUFVO3NEQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FTdkMsOERBQUNwUSxpSkFBSUEsQ0FBQ2lVLElBQUk7NEJBQ1JDLE9BQU07NEJBQ042QixTQUFRO3NDQUVSLDRFQUFDelYsa0pBQU1BLENBQUM0VSxPQUFPO2dDQUNidE4sTUFBSztnQ0FDTHVOLGVBQWUxRztnQ0FDZjJHLFVBQVUxRztnQ0FDVjJHLFFBQU87Z0NBQ1BDLFVBQVU7Z0NBQ1ZDLFVBQVM7MENBRVJuUSxrQ0FDQyw4REFBQytLOzhDQUNDLDRFQUFDQTt3Q0FBSUksT0FBTzs0Q0FBRTJGLFNBQVM7NENBQVFDLFdBQVc7d0NBQVM7OzBEQUNqRCw4REFBQ3ZWLHNKQUFhQTtnREFBQzJQLE9BQU87b0RBQUVxRSxVQUFVO29EQUFRakUsT0FBTztnREFBVTs7Ozs7OzBEQUMzRCw4REFBQ0k7Z0RBQUVSLE9BQU87b0RBQUUwRixXQUFXO29EQUFHdEYsT0FBTztnREFBTzswREFDckNyTCxzQkFBc0I7Ozs7Ozs7Ozs7Ozs7Ozs7OERBSzdCLDhEQUFDNks7O3NEQUNDLDhEQUFDWTs0Q0FBRVgsV0FBVTtzREFDWCw0RUFBQ3hQLHNKQUFhQTs7Ozs7Ozs7OztzREFFaEIsOERBQUNtUTs0Q0FBRVgsV0FBVTtzREFBa0I7Ozs7OztzREFDL0IsOERBQUNXOzRDQUFFWCxXQUFVO3NEQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FTdkMsOERBQUNwUSxpSkFBSUEsQ0FBQ2lVLElBQUk7NEJBQ1JDLE9BQU07NEJBQ042QixTQUFRO3NDQUVSLDRFQUFDelYsa0pBQU1BLENBQUM0VSxPQUFPO2dDQUNidE4sTUFBSztnQ0FDTHVOLGVBQWV4RztnQ0FDZnlHLFVBQVV4RztnQ0FDVnlHLFFBQU87Z0NBQ1BDLFVBQVU7Z0NBQ1ZDLFVBQVM7MENBRVJsTiwrQkFDQyw4REFBQzhIOztzREFDQyw4REFBQ2lHOzRDQUNDaEksS0FBSy9GOzRDQUNMa0ksT0FBTztnREFBRVIsT0FBTzs0Q0FBTzs0Q0FDdkJpRyxRQUFROzs7Ozs7c0RBRVYsOERBQUNqRjs0Q0FBRVIsT0FBTztnREFBRTBGLFdBQVc7Z0RBQUd0RixPQUFPOzRDQUFPO3NEQUNyQzBGLG1CQUFtQjs7Ozs7Ozs7Ozs7OERBSXhCLDhEQUFDbEc7O3NEQUNDLDhEQUFDWTs0Q0FBRVgsV0FBVTtzREFDWCw0RUFBQ3hQLHNKQUFhQTs7Ozs7Ozs7OztzREFFaEIsOERBQUNtUTs0Q0FBRVgsV0FBVTtzREFBa0I7Ozs7OztzREFDL0IsOERBQUNXOzRDQUFFWCxXQUFVO3NEQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FRdkMsOERBQUNwUSxpSkFBSUEsQ0FBQ2lVLElBQUk7NEJBQ1JyTSxNQUFLOzRCQUNMc00sT0FBTTs0QkFDTjZCLFNBQVE7c0NBRVIsNEVBQUM3VixpSkFBTUE7Z0NBQ0xvVyxNQUFLO2dDQUNMekQsYUFBWTtnQ0FDWnRDLE9BQU87b0NBQUVSLE9BQU87Z0NBQU87Ozs7Ozs7Ozs7O3NDQUkzQiw4REFBQy9QLGlKQUFJQSxDQUFDaVUsSUFBSTs0QkFDUkMsT0FBTTs0QkFDTjZCLFNBQVE7OzhDQUVSLDhEQUFDelYsa0pBQU1BO29DQUNMc0gsTUFBSztvQ0FDTHVOLGVBQWV0SDtvQ0FDZnVILFVBQVV0SDtvQ0FDVnlJLFFBQVE7b0NBQ1JsQixRQUFPOzhDQUVQLDRFQUFDeFYsaUpBQU1BO3dDQUFDaVIsb0JBQU0sOERBQUNuUSxzSkFBY0E7Ozs7O2tEQUFLOzs7Ozs7Ozs7Ozs4Q0FFcEMsOERBQUN3UDtvQ0FBSUksT0FBTzt3Q0FBRXFFLFVBQVU7d0NBQVFqRSxPQUFPO3dDQUFRc0YsV0FBVztvQ0FBRTs4Q0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUXJFLDhEQUFDbFcsa0pBQUtBO2dCQUNKaUksT0FBTTtnQkFDTjBLLE1BQU03UTtnQkFDTjhRLFVBQVU7b0JBQ1I3USw0QkFBNEI7b0JBQzVCVSxpQkFBaUI7b0JBQ2pCb0QsZUFBZTZELFdBQVc7Z0JBQzVCO2dCQUNBbUssTUFBTSxJQUFNaE8sZUFBZWlPLE1BQU07Z0JBQ2pDNUMsUUFBTztnQkFDUEMsWUFBVzswQkFFWCw0RUFBQ2xSLGlKQUFJQTtvQkFDSDhULE1BQU1sTztvQkFDTm1PLFFBQU87b0JBQ1BDLFVBQVU1Sjs7c0NBRVYsOERBQUNwSyxpSkFBSUEsQ0FBQ2lVLElBQUk7NEJBQ1JyTSxNQUFLOzRCQUNMc00sT0FBTTs0QkFDTkMsT0FBTztnQ0FBQztvQ0FBRUMsVUFBVTtvQ0FBTTdULFNBQVM7Z0NBQVU7NkJBQUU7c0NBRS9DLDRFQUFDTixpSkFBS0E7Z0NBQUM0UyxhQUFZOzs7Ozs7Ozs7OztzQ0FHckIsOERBQUM3UyxpSkFBSUEsQ0FBQ2lVLElBQUk7NEJBQ1JyTSxNQUFLOzRCQUNMc00sT0FBTTs0QkFDTkMsT0FBTztnQ0FBQztvQ0FBRUMsVUFBVTtvQ0FBTTdULFNBQVM7Z0NBQVU7NkJBQUU7c0NBRS9DLDRFQUFDTixpSkFBS0EsQ0FBQzZVLFFBQVE7Z0NBQUNDLE1BQU07Z0NBQUdsQyxhQUFZOzs7Ozs7Ozs7OztzQ0FHdkMsOERBQUM3UyxpSkFBSUEsQ0FBQ2lVLElBQUk7NEJBQ1JyTSxNQUFLOzRCQUNMc00sT0FBTTs0QkFDTkMsT0FBTztnQ0FBQztvQ0FBRUMsVUFBVTtvQ0FBTTdULFNBQVM7Z0NBQVU7NkJBQUU7c0NBRS9DLDRFQUFDTCxpSkFBTUE7Z0NBQUMyUyxhQUFZOztrREFDbEIsOERBQUM1Ujt3Q0FBT2tTLE9BQU07a0RBQU87Ozs7OztrREFDckIsOERBQUNsUzt3Q0FBT2tTLE9BQU07a0RBQU87Ozs7OztrREFDckIsOERBQUNsUzt3Q0FBT2tTLE9BQU07a0RBQU87Ozs7OztrREFDckIsOERBQUNsUzt3Q0FBT2tTLE9BQU07a0RBQU87Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUl6Qiw4REFBQ25ULGlKQUFJQSxDQUFDaVUsSUFBSTs0QkFDUnJNLE1BQUs7NEJBQ0xzTSxPQUFNOzRCQUNOQyxPQUFPO2dDQUFDO29DQUFFQyxVQUFVO29DQUFNN1QsU0FBUztnQ0FBVTs2QkFBRTtzQ0FFL0MsNEVBQUNMLGlKQUFNQTs7a0RBQ0wsOERBQUNlO3dDQUFPa1MsT0FBTTtrREFBUzs7Ozs7O2tEQUN2Qiw4REFBQ2xTO3dDQUFPa1MsT0FBTTtrREFBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPakMsOERBQUNwVCxrSkFBS0E7Z0JBQ0ppSSxPQUFNO2dCQUNOMEssTUFBTTNRO2dCQUNONFEsVUFBVTtvQkFDUjNRLDJCQUEyQjtvQkFDM0I2RCxjQUFjNEQsV0FBVztvQkFDekJ6RyxpQkFBaUI7Z0JBQ25CO2dCQUNBNFEsTUFBTSxJQUFNL04sY0FBY2dPLE1BQU07Z0JBQ2hDNUMsUUFBTztnQkFDUEMsWUFBVztnQkFDWG5CLE9BQU87MEJBRVAsNEVBQUMvUCxpSkFBSUE7b0JBQ0g4VCxNQUFNak87b0JBQ05rTyxRQUFPO29CQUNQQyxVQUFVOUk7O3NDQUVWLDhEQUFDbEwsaUpBQUlBLENBQUNpVSxJQUFJOzRCQUNSck0sTUFBSzs0QkFDTHNNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7b0NBQU03VCxTQUFTO2dDQUFZOzZCQUFFO3NDQUVqRCw0RUFBQ04saUpBQUtBO2dDQUFDNFMsYUFBWTs7Ozs7Ozs7Ozs7c0NBR3JCLDhEQUFDN1MsaUpBQUlBLENBQUNpVSxJQUFJOzRCQUNSck0sTUFBSzs0QkFDTHNNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7b0NBQU03VCxTQUFTO2dDQUFVOzZCQUFFO3NDQUUvQyw0RUFBQ04saUpBQUtBLENBQUM2VSxRQUFRO2dDQUNiQyxNQUFNO2dDQUNObEMsYUFBWTtnQ0FDWm1DLFNBQVM7Z0NBQ1RDLFdBQVc7Ozs7Ozs7Ozs7O3NDQUlmLDhEQUFDalYsaUpBQUlBLENBQUNpVSxJQUFJOzRCQUNSQyxPQUFNOzRCQUNOQyxPQUFPO2dDQUFDO29DQUFFQyxVQUFVO29DQUFNN1QsU0FBUztnQ0FBVTs2QkFBRTtzQ0FFL0MsNEVBQUNELGtKQUFNQSxDQUFDNFUsT0FBTztnQ0FDYnROLE1BQUs7Z0NBQ0x1TixlQUFlOUg7Z0NBQ2YrSCxVQUFVMUg7Z0NBQ1YySCxRQUFPO2dDQUNQQyxVQUFVO2dDQUNWQyxVQUFTOzBDQUVSeFMsOEJBQ0MsOERBQUNvTjs4Q0FDQyw0RUFBQ3FGO3dDQUFJcEgsS0FBS3JMO3dDQUFlMFMsS0FBSTt3Q0FBT2xGLE9BQU87NENBQUVSLE9BQU87NENBQVEyRixXQUFXOzRDQUFTQyxXQUFXO3dDQUFROzs7Ozs7Ozs7OzhEQUdyRyw4REFBQ3hGOztzREFDQyw4REFBQ1k7NENBQUVYLFdBQVU7c0RBQ1gsNEVBQUN4UCxzSkFBYUE7Ozs7Ozs7Ozs7c0RBRWhCLDhEQUFDbVE7NENBQUVYLFdBQVU7c0RBQWtCOzs7Ozs7c0RBQy9CLDhEQUFDVzs0Q0FBRVgsV0FBVTtzREFBa0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBVXZDLDhEQUFDcFEsaUpBQUlBLENBQUNpVSxJQUFJOzRCQUNSck0sTUFBSzs0QkFDTHNNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7b0NBQU03VCxTQUFTO2dDQUFlOzZCQUFFOzRCQUNwRGlXLGNBQWM7c0NBRWQsNEVBQUN0VyxpSkFBTUE7Z0NBQUMyUyxhQUFZOztrREFDbEIsOERBQUM1Ujt3Q0FBT2tTLE9BQU87a0RBQUc7Ozs7OztrREFDbEIsOERBQUNsUzt3Q0FBT2tTLE9BQU87a0RBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUl0Qiw4REFBQ25ULGlKQUFJQSxDQUFDaVUsSUFBSTs0QkFDUnJNLE1BQUs7NEJBQ0xzTSxPQUFNOzRCQUNOQyxPQUFPO2dDQUFDO29DQUFFQyxVQUFVO29DQUFNN1QsU0FBUztnQ0FBVTs2QkFBRTtzQ0FFL0MsNEVBQUNOLGlKQUFLQTtnQ0FDSjRTLGFBQVk7Z0NBQ1ptQyxTQUFTO2dDQUNUQyxXQUFXOzs7Ozs7Ozs7OztzQ0FJZiw4REFBQ2pWLGlKQUFJQSxDQUFDaVUsSUFBSTs0QkFDUnJNLE1BQUs7NEJBQ0xzTSxPQUFNOzRCQUNOQyxPQUFPO2dDQUFDO29DQUFFQyxVQUFVO29DQUFNN1QsU0FBUztnQ0FBUTs2QkFBRTtzQ0FFN0MsNEVBQUNMLGlKQUFNQTtnQ0FDTG9XLE1BQUs7Z0NBQ0x6RCxhQUFZO2dDQUNaNEQsaUJBQWdCOzBDQUVmNVQsV0FBV2dHLEdBQUcsQ0FBQzBJLENBQUFBLG9CQUNkLDhEQUFDdFE7d0NBQW9Ca1MsT0FBTzVCLElBQUlqSCxFQUFFO3dDQUFFNEosT0FBTzNDLElBQUkzSixJQUFJO2tEQUNqRCw0RUFBQ3hILGtKQUFHQTs0Q0FBQ3VRLE9BQU9ZLElBQUlaLEtBQUs7c0RBQUdZLElBQUkzSixJQUFJOzs7Ozs7dUNBRHJCMkosSUFBSWpILEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVU3Qiw4REFBQ3ZLLGtKQUFLQTtnQkFDSmlJLE9BQU07Z0JBQ04wSyxNQUFNelE7Z0JBQ04wUSxVQUFVO29CQUNSelEsd0JBQXdCO29CQUN4QjRELFdBQVcyRCxXQUFXO2dCQUN4QjtnQkFDQW1LLE1BQU0sSUFBTTlOLFdBQVcrTixNQUFNO2dCQUM3QjVDLFFBQU87Z0JBQ1BDLFlBQVc7Z0JBQ1huQixPQUFPOzBCQUVQLDRFQUFDL1AsaUpBQUlBO29CQUNIOFQsTUFBTWhPO29CQUNOaU8sUUFBTztvQkFDUEMsVUFBVTNJOztzQ0FFViw4REFBQ3JMLGlKQUFJQSxDQUFDaVUsSUFBSTs0QkFDUnJNLE1BQUs7NEJBQ0xzTSxPQUFNOzRCQUNOQyxPQUFPO2dDQUNMO29DQUFFQyxVQUFVO29DQUFNN1QsU0FBUztnQ0FBVTtnQ0FDckM7b0NBQUVtVyxLQUFLO29DQUFJblcsU0FBUztnQ0FBZ0I7NkJBQ3JDO3NDQUVELDRFQUFDTixpSkFBS0E7Z0NBQUM0UyxhQUFZOzs7Ozs7Ozs7OztzQ0FHckIsOERBQUM3UyxpSkFBSUEsQ0FBQ2lVLElBQUk7NEJBQ1JyTSxNQUFLOzRCQUNMc00sT0FBTTs0QkFDTkMsT0FBTztnQ0FBQztvQ0FBRUMsVUFBVTtvQ0FBTTdULFNBQVM7Z0NBQVU7NkJBQUU7NEJBQy9DaVcsY0FBYTtzQ0FFYiw0RUFBQ3RXLGlKQUFNQTtnQ0FBQzJTLGFBQVk7O2tEQUNsQiw4REFBQzVSO3dDQUFPa1MsT0FBTTtrREFDWiw0RUFBQ2hEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7b0RBQWtCRyxPQUFPO3dEQUFFaUMsaUJBQWlCO29EQUFVOzs7Ozs7Z0RBQVM7Ozs7Ozs7Ozs7OztrREFJbEYsOERBQUN2Ujt3Q0FBT2tTLE9BQU07a0RBQ1osNEVBQUNoRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVO29EQUFrQkcsT0FBTzt3REFBRWlDLGlCQUFpQjtvREFBVTs7Ozs7O2dEQUFTOzs7Ozs7Ozs7Ozs7a0RBSWxGLDhEQUFDdlI7d0NBQU9rUyxPQUFNO2tEQUNaLDRFQUFDaEQ7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTtvREFBa0JHLE9BQU87d0RBQUVpQyxpQkFBaUI7b0RBQVU7Ozs7OztnREFBUzs7Ozs7Ozs7Ozs7O2tEQUlsRiw4REFBQ3ZSO3dDQUFPa1MsT0FBTTtrREFDWiw0RUFBQ2hEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7b0RBQWtCRyxPQUFPO3dEQUFFaUMsaUJBQWlCO29EQUFVOzs7Ozs7Z0RBQVM7Ozs7Ozs7Ozs7OztrREFJbEYsOERBQUN2Ujt3Q0FBT2tTLE9BQU07a0RBQ1osNEVBQUNoRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVO29EQUFrQkcsT0FBTzt3REFBRWlDLGlCQUFpQjtvREFBVTs7Ozs7O2dEQUFTOzs7Ozs7Ozs7Ozs7a0RBSWxGLDhEQUFDdlI7d0NBQU9rUyxPQUFNO2tEQUNaLDRFQUFDaEQ7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTtvREFBa0JHLE9BQU87d0RBQUVpQyxpQkFBaUI7b0RBQVU7Ozs7OztnREFBUzs7Ozs7Ozs7Ozs7O2tEQUlsRiw4REFBQ3ZSO3dDQUFPa1MsT0FBTTtrREFDWiw0RUFBQ2hEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7b0RBQWtCRyxPQUFPO3dEQUFFaUMsaUJBQWlCO29EQUFVOzs7Ozs7Z0RBQVM7Ozs7Ozs7Ozs7OztrREFJbEYsOERBQUN2Ujt3Q0FBT2tTLE9BQU07a0RBQ1osNEVBQUNoRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVO29EQUFrQkcsT0FBTzt3REFBRWlDLGlCQUFpQjtvREFBVTs7Ozs7O2dEQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FPdEYsOERBQUN4UyxpSkFBSUEsQ0FBQ2lVLElBQUk7NEJBQ1JyTSxNQUFLOzRCQUNMc00sT0FBTTs0QkFDTkMsT0FBTztnQ0FBQztvQ0FBRUMsVUFBVTtvQ0FBTTdULFNBQVM7Z0NBQVU7NkJBQUU7NEJBQy9DaVcsY0FBYztzQ0FFZCw0RUFBQ3RXLGlKQUFNQTtnQ0FBQzJTLGFBQVk7O2tEQUNsQiw4REFBQzVSO3dDQUFPa1MsT0FBTztrREFBRzs7Ozs7O2tEQUNsQiw4REFBQ2xTO3dDQUFPa1MsT0FBTztrREFBRzs7Ozs7O2tEQUNsQiw4REFBQ2xTO3dDQUFPa1MsT0FBTztrREFBRzs7Ozs7O2tEQUNsQiw4REFBQ2xTO3dDQUFPa1MsT0FBTztrREFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSXRCLDhEQUFDblQsaUpBQUlBLENBQUNpVSxJQUFJOzRCQUNSck0sTUFBSzs0QkFDTHNNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUV1QyxLQUFLO29DQUFLblcsU0FBUztnQ0FBaUI7NkJBQUU7c0NBRWhELDRFQUFDTixpSkFBS0EsQ0FBQzZVLFFBQVE7Z0NBQ2JDLE1BQU07Z0NBQ05sQyxhQUFZO2dDQUNabUMsU0FBUztnQ0FDVEMsV0FBVzs7Ozs7Ozs7Ozs7c0NBSWYsOERBQUNqVixpSkFBSUEsQ0FBQ2lVLElBQUk7NEJBQ1JyTSxNQUFLOzRCQUNMc00sT0FBTTs0QkFDTkMsT0FBTztnQ0FBQztvQ0FBRUMsVUFBVTtvQ0FBTTdULFNBQVM7Z0NBQVU7NkJBQUU7NEJBQy9DaVcsY0FBYztzQ0FFZCw0RUFBQ3RXLGlKQUFNQTtnQ0FBQzJTLGFBQVk7O2tEQUNsQiw4REFBQzVSO3dDQUFPa1MsT0FBTztrREFBRzs7Ozs7O2tEQUNsQiw4REFBQ2xTO3dDQUFPa1MsT0FBTztrREFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPMUIsOERBQUNwVCxrSkFBS0E7Z0JBQ0ppSSxPQUFNO2dCQUNOMEssTUFBTXZRO2dCQUNOd1EsVUFBVTtvQkFDUnZRLCtCQUErQjtvQkFDL0IyRCxrQkFBa0IwRCxXQUFXO2dCQUMvQjtnQkFDQW1LLE1BQU0sSUFBTTdOLGtCQUFrQjhOLE1BQU07Z0JBQ3BDNUMsUUFBTztnQkFDUEMsWUFBVztnQkFDWG5CLE9BQU87MEJBRVAsNEVBQUMvUCxpSkFBSUE7b0JBQ0g4VCxNQUFNL047b0JBQ05nTyxRQUFPO29CQUNQQyxVQUFVeEk7O3NDQUVWLDhEQUFDeEwsaUpBQUlBLENBQUNpVSxJQUFJOzRCQUNSck0sTUFBSzs0QkFDTHNNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7b0NBQU03VCxTQUFTO2dDQUFjOzZCQUFFO3NDQUVuRCw0RUFBQ0wsaUpBQU1BO2dDQUNMMlMsYUFBWTtnQ0FDWndCLFVBQVU7Z0NBQ1ZzQyxjQUFjLENBQUNDLE9BQU9DO3dDQUNuQkE7MkNBQUFBLG1CQUFBQSw4QkFBQUEsbUJBQUFBLE9BQVFDLFFBQVEsY0FBaEJELHVDQUFELGlCQUF5QzdILFdBQVcsR0FBR2hGLFFBQVEsQ0FBQzRNLE1BQU01SCxXQUFXOzswQ0FHbEZ0SyxhQUFhbUUsR0FBRyxDQUFDLENBQUNtQyx1QkFDakIsOERBQUMvSjt3Q0FBdUJrUyxPQUFPbkksT0FBT1YsRUFBRTs7NENBQ3JDVSxPQUFPaEQsS0FBSzs0Q0FBQzs0Q0FBR2dELE9BQU9pRSxRQUFROzRDQUFDOzt1Q0FEdEJqRSxPQUFPVixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7c0NBTzVCLDhEQUFDdEssaUpBQUlBLENBQUNpVSxJQUFJOzRCQUNSck0sTUFBSzs0QkFDTHNNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7Z0NBQU07NkJBQUU7c0NBRTVCLDRFQUFDblUsaUpBQUtBLENBQUM2VSxRQUFRO2dDQUNiakMsYUFBWTtnQ0FDWmtDLE1BQU07Z0NBQ05FLFdBQVc7Z0NBQ1hELFNBQVM7Ozs7Ozs7Ozs7O3NDQUliLDhEQUFDN0U7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDMkc7b0NBQUczRyxXQUFVOzhDQUF5Qzs7Ozs7OzhDQUN2RCw4REFBQzRHO29DQUFHNUcsV0FBVTs7c0RBQ1osOERBQUM2RztzREFBRzs7Ozs7O3NEQUNKLDhEQUFDQTtzREFBRzs7Ozs7O3NEQUNKLDhEQUFDQTtzREFBRzs7Ozs7O3NEQUNKLDhEQUFDQTtzREFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT1osOERBQUNsWCxrSkFBS0E7Z0JBQ0ppSSxPQUFNO2dCQUNOMEssTUFBTXJRO2dCQUNOc1EsVUFBVTlGO2dCQUNWK0YsUUFBUTtnQkFDUjdDLE9BQU87Z0JBQ1BtSCxjQUFjOzBCQUVkLDRFQUFDbFgsaUpBQUlBO29CQUNIOFQsTUFBTTlOO29CQUNOK04sUUFBTztvQkFDUEMsVUFBVWpIO29CQUNWcUQsV0FBVTs7c0NBR1YsOERBQUNwUSxpSkFBSUEsQ0FBQ2lVLElBQUk7NEJBQ1JyTSxNQUFLOzRCQUNMc00sT0FBTTs0QkFDTkMsT0FBTztnQ0FBQztvQ0FBRUMsVUFBVTtvQ0FBTTdULFNBQVM7Z0NBQVU7NkJBQUU7c0NBRS9DLDRFQUFDTCxpSkFBTUE7Z0NBQ0wyUyxhQUFZO2dDQUNadFIsU0FBU2lEO2dDQUNUd08sVUFBVXRHO2dDQUNWMkgsVUFBVTtnQ0FDVnNDLGNBQWMsQ0FBQ0MsT0FBT0M7d0NBQ25CQTsyQ0FBQUEsbUJBQUFBLDhCQUFBQSxtQkFBQUEsT0FBUUMsUUFBUSxjQUFoQkQsdUNBQUQsaUJBQXlDN0gsV0FBVyxHQUFHaEYsUUFBUSxDQUFDNE0sTUFBTTVILFdBQVc7OzBDQUdsRjdOLDBCQUEwQjBILEdBQUcsQ0FBQ21DLENBQUFBLHVCQUM3Qiw4REFBQy9KO3dDQUF1QmtTLE9BQU9uSSxPQUFPVixFQUFFOzs0Q0FDckNVLE9BQU9oRCxLQUFLOzRDQUFDOzRDQUFPZ0QsT0FBT1YsRUFBRTs0Q0FBQzs7dUNBRHBCVSxPQUFPVixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7c0NBUTVCLDhEQUFDdEssaUpBQUlBLENBQUNpVSxJQUFJOzRCQUNSck0sTUFBSzs0QkFDTHNNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7b0NBQU03VCxTQUFTO2dDQUFhOzZCQUFFO3NDQUVsRCw0RUFBQ0wsaUpBQU1BO2dDQUNMMlMsYUFBYWxQLDJCQUEyQixlQUFlO2dDQUN2RDRQLFVBQVUsQ0FBQzVQO2dDQUNYcEMsU0FBU2lELHNCQUFzQixDQUFDLENBQUNiO2dDQUNqQ3FQLFVBQVVwRztnQ0FDVnlILFVBQVU7Z0NBQ1ZzQyxjQUFjLENBQUNDLE9BQU9DO3dDQUNuQkE7MkNBQUFBLG1CQUFBQSw4QkFBQUEsbUJBQUFBLE9BQVFDLFFBQVEsY0FBaEJELHVDQUFELGlCQUF5QzdILFdBQVcsR0FBR2hGLFFBQVEsQ0FBQzRNLE1BQU01SCxXQUFXOztnQ0FFbkZtSSxpQkFDRTNTLHNCQUFzQmIsMkJBQ2xCLGVBQ0FBLDJCQUNFLGFBQ0E7MENBR1B2QywwQkFBMEJvSCxNQUFNLEdBQUcsSUFDbENwSCwwQkFBMEJ5SCxHQUFHLENBQUNpRyxDQUFBQSx1QkFDNUIsOERBQUM3Tjt3Q0FBdUJrUyxPQUFPckUsT0FBT3hFLEVBQUU7a0RBQ3RDLDRFQUFDNkY7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDTTs4REFBTTVCLE9BQU85RyxLQUFLOzs7Ozs7OERBQ25CLDhEQUFDbUk7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDaFEsa0pBQUdBOzREQUFDdVEsT0FBTzdCLE9BQU81RSxNQUFNLEtBQUssSUFBSSxVQUFVNEUsT0FBTzVFLE1BQU0sS0FBSyxJQUFJLFdBQVc7NERBQU9rRyxXQUFVO3NFQUMzRnRCLE9BQU81RSxNQUFNLEtBQUssSUFBSSxRQUFRNEUsT0FBTzVFLE1BQU0sS0FBSyxJQUFJLE9BQU87Ozs7OztzRUFFOUQsOERBQUN3Rzs0REFBS04sV0FBVTs7Z0VBQXdCO2dFQUFLdEIsT0FBT3hFLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7dUNBUC9Dd0UsT0FBT3hFLEVBQUU7Ozs7cURBYXhCM0csNEJBQTRCLENBQUNhLG1DQUMzQiw4REFBQ3ZEO29DQUFPc1MsUUFBUTtvQ0FBQ0osT0FBTTs4Q0FBYTs7Ozs7Z0RBR2xDOzs7Ozs7Ozs7OztzQ0FNViw4REFBQ2hEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQzJHO29DQUFHM0csV0FBVTs4Q0FBaUM7Ozs7Ozs4Q0FDL0MsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ1c7O2dEQUFFO2dEQUFVcE4sNEJBQTRCOzs7Ozs7O3NEQUN6Qyw4REFBQ29OOztnREFBRTtnREFBVWpOLDRCQUE0Qjs7Ozs7OztzREFDekMsOERBQUNpTjs7Z0RBQUU7Z0RBQVM1UCwwQkFBMEJxSCxNQUFNOzs7Ozs7O3NEQUM1Qyw4REFBQ3VJOztnREFBRTtnREFBVTNQLDBCQUEwQm9ILE1BQU07Ozs7Ozs7c0RBQzdDLDhEQUFDdUk7O2dEQUFFO2dEQUFPdk0scUJBQXFCLFFBQVE7Ozs7Ozs7d0NBQ3RDcEQsMEJBQTBCb0gsTUFBTSxHQUFHLG1CQUNsQyw4REFBQzJIOzs4REFDQyw4REFBQ1k7OERBQUU7Ozs7Ozs4REFDSCw4REFBQ2lHO29EQUFHNUcsV0FBVTs4REFDWGhQLDBCQUEwQnlILEdBQUcsQ0FBQ2lHLENBQUFBLHVCQUM3Qiw4REFBQ21JOztnRUFBbUI7Z0VBQUtuSSxPQUFPeEUsRUFBRTtnRUFBQztnRUFBT3dFLE9BQU85RyxLQUFLOzsyREFBN0M4RyxPQUFPeEUsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt3QkFTN0J4RywwQ0FDQyw4REFBQ3FNOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQzJHO29DQUFHM0csV0FBVTs4Q0FBeUM7Ozs7Ozs4Q0FDdkQsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ1c7O2dEQUFFO2dEQUFPak47Ozs7Ozs7c0RBQ1YsOERBQUNpTjs7Z0RBQUU7aURBQU81UCxrQ0FBQUEsMEJBQTBCOEwsSUFBSSxDQUFDbUssQ0FBQUEsSUFBS0EsRUFBRTlNLEVBQUUsS0FBSzNHLHVDQUE3Q3hDLHNEQUFBQSxnQ0FBd0U2RyxLQUFLOzs7Ozs7O3NEQUN2Riw4REFBQytJOztnREFBRTtpREFBTzNQLGtDQUFBQSwwQkFBMEI2TCxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUU1QyxFQUFFLEtBQUt4Ryx1Q0FBN0MxQyxzREFBQUEsZ0NBQXdFNEcsS0FBSzs7Ozs7OztzREFDdkYsOERBQUMrSTs0Q0FBRVgsV0FBVTs7Z0RBQWlDO2dEQUEwRHRNO2dEQUF5Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLdkksOERBQUNxTTs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNadE0seUNBQ0MsOERBQUM0TTt3Q0FBS04sV0FBVTtrREFBaUI7Ozs7O2tFQUVqQyw4REFBQ007a0RBQUs7Ozs7Ozs7Ozs7OzhDQUdWLDhEQUFDUDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUN2USxpSkFBTUE7NENBQUN5USxTQUFTekQ7c0RBQXlCOzs7Ozs7c0RBRzFDLDhEQUFDaE4saUpBQU1BOzRDQUNMMlAsTUFBSzs0Q0FDTDZILFVBQVM7NENBQ1Q5VixTQUFTaUQ7NENBQ1QrTyxVQUFVLENBQUN6UDs0Q0FDWHNNLFdBQVd0TSwyQkFBMkIscURBQXFEO3NEQUUxRlUscUJBQXFCLFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRakQ7R0FsZ0VNdEQ7O1FBMkNvQmxCLGlKQUFJQSxDQUFDMkY7UUFDSjNGLGlKQUFJQSxDQUFDMkY7UUFDTjNGLGlKQUFJQSxDQUFDMkY7UUFDUjNGLGlKQUFJQSxDQUFDMkY7UUFDRTNGLGlKQUFJQSxDQUFDMkY7UUFDTDNGLGlKQUFJQSxDQUFDMkY7OztLQWhEN0J6RTtBQW9nRU4sK0RBQWVBLGdCQUFnQkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvYWRtaW4tc3BhY2UvY29tcG9uZW50cy9jb3Vyc2UtbWFuYWdlbWVudC50c3g/NmQ3MiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IENhcmQsIEJ1dHRvbiwgVGFibGUsIE1vZGFsLCBGb3JtLCBJbnB1dCwgU2VsZWN0LCBTcGFjZSwgVGFnLCBQb3Bjb25maXJtLCBVcGxvYWQsIG1lc3NhZ2UgfSBmcm9tICdhbnRkJztcbmltcG9ydCB7IFBsdXNPdXRsaW5lZCwgRWRpdE91dGxpbmVkLCBEZWxldGVPdXRsaW5lZCwgU2VhcmNoT3V0bGluZWQsIFVwbG9hZE91dGxpbmVkLCBJbmJveE91dGxpbmVkIH0gZnJvbSAnQGFudC1kZXNpZ24vaWNvbnMnO1xuaW1wb3J0IHsgR2V0Tm90aWZpY2F0aW9uIH0gZnJvbSAnbG9naWMtY29tbW9uL2Rpc3QvY29tcG9uZW50cy9Ob3RpZmljYXRpb24nO1xuaW1wb3J0IHsgY291cnNlQXBpLCBDb3Vyc2UsIENyZWF0ZUNvdXJzZVJlcXVlc3QsIFVwZGF0ZUNvdXJzZVJlcXVlc3QsIENyZWF0ZUNvdXJzZVNlcmllc1JlcXVlc3QsIFRlYWNoZXIsIENvdXJzZVRhZywgQ291cnNlU2VyaWVzIH0gZnJvbSAnQC9saWIvYXBpL2NvdXJzZSc7XG5pbXBvcnQgeyB1cGxvYWRBcGkgfSBmcm9tICdAL2xpYi9hcGkvdXBsb2FkJztcbmltcG9ydCB0eXBlIHsgVXBsb2FkRmlsZSwgVXBsb2FkUHJvcHMgfSBmcm9tICdhbnRkL2VzL3VwbG9hZC9pbnRlcmZhY2UnO1xuXG5jb25zdCB7IFNlYXJjaCB9ID0gSW5wdXQ7XG5jb25zdCB7IE9wdGlvbiB9ID0gU2VsZWN0O1xuXG5pbnRlcmZhY2UgQ291cnNlTWFuYWdlbWVudFByb3BzIHtcbiAgLy8g5Y+v5Lul5re75Yqg6ZyA6KaB55qEcHJvcHNcbn1cblxuLy8g6K++56iL6KGo5Y2V5pWw5o2u57G75Z6LXG5pbnRlcmZhY2UgQ291cnNlRm9ybURhdGEge1xuICBuYW1lOiBzdHJpbmc7XG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XG4gIGNhdGVnb3J5OiBzdHJpbmc7XG4gIHN0YXR1czogJ2FjdGl2ZScgfCAnaW5hY3RpdmUnO1xuICBjb250ZW50Q29uZmlnPzoge1xuICAgIGR1cmF0aW9uPzogbnVtYmVyO1xuICAgIGRpZmZpY3VsdHk/OiAnYmVnaW5uZXInIHwgJ2ludGVybWVkaWF0ZScgfCAnYWR2YW5jZWQnO1xuICAgIHByZXJlcXVpc2l0ZXM/OiBzdHJpbmdbXTtcbiAgfTtcbiAgdGVhY2hpbmdJbmZvPzoge1xuICAgIG9iamVjdGl2ZXM/OiBzdHJpbmdbXTtcbiAgICBtZXRob2RzPzogc3RyaW5nW107XG4gICAgbWF0ZXJpYWxzPzogc3RyaW5nW107XG4gIH07XG59XG5cbmNvbnN0IENvdXJzZU1hbmFnZW1lbnQ6IFJlYWN0LkZDPENvdXJzZU1hbmFnZW1lbnRQcm9wcz4gPSAoKSA9PiB7XG4gIGNvbnN0IFtjb3Vyc2VMaXN0LCBzZXRDb3Vyc2VMaXN0XSA9IHVzZVN0YXRlPENvdXJzZVtdPihbXSk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzQ291cnNlTW9kYWxWaXNpYmxlLCBzZXRJc0NvdXJzZU1vZGFsVmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpc0FkZENvdXJzZU1vZGFsVmlzaWJsZSwgc2V0SXNBZGRDb3Vyc2VNb2RhbFZpc2libGVdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNFZGl0Q291cnNlTW9kYWxWaXNpYmxlLCBzZXRJc0VkaXRDb3Vyc2VNb2RhbFZpc2libGVdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNBZGRTZXJpZXNNb2RhbFZpc2libGUsIHNldElzQWRkU2VyaWVzTW9kYWxWaXNpYmxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzQWRkVGFnTW9kYWxWaXNpYmxlLCBzZXRJc0FkZFRhZ01vZGFsVmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpc1B1Ymxpc2hTZXJpZXNNb2RhbFZpc2libGUsIHNldElzUHVibGlzaFNlcmllc01vZGFsVmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpc1B1Ymxpc2hDb3Vyc2VNb2RhbFZpc2libGUsIHNldElzUHVibGlzaENvdXJzZU1vZGFsVmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtlZGl0aW5nQ291cnNlLCBzZXRFZGl0aW5nQ291cnNlXSA9IHVzZVN0YXRlPENvdXJzZSB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbc2VhcmNoS2V5d29yZCwgc2V0U2VhcmNoS2V5d29yZF0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFt0ZWFjaGVycywgc2V0VGVhY2hlcnNdID0gdXNlU3RhdGU8VGVhY2hlcltdPihbXSk7XG4gIGNvbnN0IFtjb3Vyc2VUYWdzLCBzZXRDb3Vyc2VUYWdzXSA9IHVzZVN0YXRlPENvdXJzZVRhZ1tdPihbXSk7XG4gIGNvbnN0IFtjb3ZlckltYWdlVXJsLCBzZXRDb3ZlckltYWdlVXJsXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xuXG4gIC8vIOaWsOWinu+8muezu+WIl+ivvueoi+WSjOWtkOivvueoi+euoeeQhuebuOWFs+eKtuaAgVxuICBjb25zdCBbc2VyaWVzTGlzdCwgc2V0U2VyaWVzTGlzdF0gPSB1c2VTdGF0ZTxhbnlbXT4oW10pO1xuICBjb25zdCBbc2VyaWVzQ291cnNlc01hcCwgc2V0U2VyaWVzQ291cnNlc01hcF0gPSB1c2VTdGF0ZTxNYXA8bnVtYmVyLCBhbnlbXT4+KG5ldyBNYXAoKSk7XG4gIGNvbnN0IFtleHBhbmRlZFNlcmllcywgc2V0RXhwYW5kZWRTZXJpZXNdID0gdXNlU3RhdGU8U2V0PG51bWJlcj4+KG5ldyBTZXQoKSk7XG4gIGNvbnN0IFtzZXJpZXNMb2FkaW5nLCBzZXRTZXJpZXNMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICAvLyDlj5HluIPor77nqIvnm7jlhbPnirbmgIFcbiAgY29uc3QgW3NlbGVjdGVkU2VyaWVzRm9yUHVibGlzaCwgc2V0U2VsZWN0ZWRTZXJpZXNGb3JQdWJsaXNoXSA9IHVzZVN0YXRlPG51bWJlciB8IHVuZGVmaW5lZD4odW5kZWZpbmVkKTtcbiAgY29uc3QgW3NlbGVjdGVkQ291cnNlRm9yUHVibGlzaCwgc2V0U2VsZWN0ZWRDb3Vyc2VGb3JQdWJsaXNoXSA9IHVzZVN0YXRlPG51bWJlciB8IHVuZGVmaW5lZD4odW5kZWZpbmVkKTtcbiAgY29uc3QgW3B1Ymxpc2hTZXJpZXNDb3Vyc2VzLCBzZXRQdWJsaXNoU2VyaWVzQ291cnNlc10gPSB1c2VTdGF0ZTxhbnlbXT4oW10pO1xuICBjb25zdCBbcHVibGlzaExvYWRpbmcsIHNldFB1Ymxpc2hMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3B1Ymxpc2hTZXJpZXNPcHRpb25zLCBzZXRQdWJsaXNoU2VyaWVzT3B0aW9uc10gPSB1c2VTdGF0ZTxhbnlbXT4oW10pO1xuXG4gIC8vIOaWsOeahOWPkeW4g+ivvueoi+eKtuaAgVxuICBjb25zdCBbcHVibGlzaFNlcmllc0xpc3RGb3JNb2RhbCwgc2V0UHVibGlzaFNlcmllc0xpc3RGb3JNb2RhbF0gPSB1c2VTdGF0ZTxhbnlbXT4oW10pO1xuICBjb25zdCBbcHVibGlzaENvdXJzZUxpc3RGb3JNb2RhbCwgc2V0UHVibGlzaENvdXJzZUxpc3RGb3JNb2RhbF0gPSB1c2VTdGF0ZTxhbnlbXT4oW10pO1xuICBjb25zdCBbcHVibGlzaEZvcm1Mb2FkaW5nLCBzZXRQdWJsaXNoRm9ybUxvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIGNvbnN0IFtjb3Vyc2VTZXJpZXMsIHNldENvdXJzZVNlcmllc10gPSB1c2VTdGF0ZTxDb3Vyc2VTZXJpZXNbXT4oW10pO1xuICBjb25zdCBbY291cnNlQ292ZXJJbWFnZVVybCwgc2V0Q291cnNlQ292ZXJJbWFnZVVybF0gPSB1c2VTdGF0ZTxzdHJpbmc+KCcnKTtcbiAgY29uc3QgW2FkZGl0aW9uYWxGaWxlcywgc2V0QWRkaXRpb25hbEZpbGVzXSA9IHVzZVN0YXRlPHN0cmluZ1tdPihbXSk7XG4gIGNvbnN0IFtjb3Vyc2VWaWRlb1VybCwgc2V0Q291cnNlVmlkZW9VcmxdID0gdXNlU3RhdGU8c3RyaW5nPignJyk7XG4gIGNvbnN0IFtjb3Vyc2VWaWRlb05hbWUsIHNldENvdXJzZVZpZGVvTmFtZV0gPSB1c2VTdGF0ZTxzdHJpbmc+KCcnKTtcbiAgY29uc3QgW2NvdXJzZURvY3VtZW50VXJsLCBzZXRDb3Vyc2VEb2N1bWVudFVybF0gPSB1c2VTdGF0ZTxzdHJpbmc+KCcnKTtcbiAgY29uc3QgW2NvdXJzZURvY3VtZW50TmFtZSwgc2V0Q291cnNlRG9jdW1lbnROYW1lXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xuICBjb25zdCBbdmlkZW9EdXJhdGlvbiwgc2V0VmlkZW9EdXJhdGlvbl0gPSB1c2VTdGF0ZTxudW1iZXI+KDApO1xuXG4gIGNvbnN0IFthZGRDb3Vyc2VGb3JtXSA9IEZvcm0udXNlRm9ybSgpO1xuICBjb25zdCBbZWRpdENvdXJzZUZvcm1dID0gRm9ybS51c2VGb3JtKCk7XG4gIGNvbnN0IFthZGRTZXJpZXNGb3JtXSA9IEZvcm0udXNlRm9ybSgpO1xuICBjb25zdCBbYWRkVGFnRm9ybV0gPSBGb3JtLnVzZUZvcm0oKTtcbiAgY29uc3QgW3B1Ymxpc2hTZXJpZXNGb3JtXSA9IEZvcm0udXNlRm9ybSgpO1xuICBjb25zdCBbcHVibGlzaENvdXJzZUZvcm1dID0gRm9ybS51c2VGb3JtKCk7XG4gIGNvbnN0IG5vdGlmaWNhdGlvbiA9IEdldE5vdGlmaWNhdGlvbigpO1xuXG5cblxuICAvLyDojrflj5bns7vliJfor77nqIvliJfooahcbiAgY29uc3QgZmV0Y2hTZXJpZXNMaXN0ID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRTZXJpZXNMb2FkaW5nKHRydWUpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk50g6I635Y+W57O75YiX6K++56iL5YiX6KGoLi4uJyk7XG5cbiAgICAgIGNvbnN0IHsgZGF0YTogcmVzIH0gPSBhd2FpdCBjb3Vyc2VBcGkuZ2V0TWFya2V0cGxhY2VTZXJpZXMoe1xuICAgICAgICBwYWdlOiAxLFxuICAgICAgICBwYWdlU2l6ZTogNTBcbiAgICAgIH0pO1xuXG4gICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCAmJiByZXMuZGF0YT8ubGlzdCkge1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOiOt+WPluezu+WIl+ivvueoi+WIl+ihqOaIkOWKnzonLCByZXMuZGF0YS5saXN0KTtcbiAgICAgICAgc2V0U2VyaWVzTGlzdChyZXMuZGF0YS5saXN0KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDojrflj5bns7vliJfor77nqIvliJfooajlpLHotKU6JywgcmVzLm1zZyk7XG4gICAgICAgIG1lc3NhZ2UuZXJyb3IocmVzLm1zZyB8fCAn6I635Y+W57O75YiX6K++56iL5YiX6KGo5aSx6LSlJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDojrflj5bns7vliJfor77nqIvliJfooajlvILluLg6JywgZXJyb3IpO1xuICAgICAgbWVzc2FnZS5lcnJvcign6I635Y+W57O75YiX6K++56iL5YiX6KGo5aSx6LSl77yM6K+36YeN6K+VJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldFNlcmllc0xvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyDojrflj5bmjIflrprns7vliJfkuIvnmoTlrZDor77nqIvliJfooahcbiAgY29uc3QgZmV0Y2hTZXJpZXNDb3Vyc2VzID0gYXN5bmMgKHNlcmllc0lkOiBudW1iZXIpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ/Cfk50g6I635Y+W57O75YiX5a2Q6K++56iL5YiX6KGo77yM57O75YiXSUQ6Jywgc2VyaWVzSWQpO1xuXG4gICAgICAvLyDkvb/nlKjor77nqIvnrqHnkIZBUEnojrflj5bmiYDmnInnirbmgIHnmoTor77nqItcbiAgICAgIGNvbnN0IHsgZGF0YTogcmVzIH0gPSBhd2FpdCBjb3Vyc2VBcGkuZ2V0TWFuYWdlbWVudFNlcmllc0NvdXJzZXMoc2VyaWVzSWQsIHtcbiAgICAgICAgcGFnZTogMSxcbiAgICAgICAgcGFnZVNpemU6IDUwXG4gICAgICAgIC8vIOS4jeS8oHN0YXR1c+WPguaVsO+8jOiOt+WPluaJgOacieeKtuaAgeeahOivvueoi1xuICAgICAgfSk7XG5cbiAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwICYmIHJlcy5kYXRhPy5saXN0KSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg6I635Y+W57O75YiX5a2Q6K++56iL5YiX6KGo5oiQ5YqfOicsIHJlcy5kYXRhLmxpc3QpO1xuICAgICAgICBzZXRTZXJpZXNDb3Vyc2VzTWFwKHByZXYgPT4gbmV3IE1hcChwcmV2LnNldChzZXJpZXNJZCwgcmVzLmRhdGEubGlzdCkpKTtcbiAgICAgICAgc2V0RXhwYW5kZWRTZXJpZXMocHJldiA9PiBuZXcgU2V0KHByZXYuYWRkKHNlcmllc0lkKSkpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOiOt+WPluezu+WIl+WtkOivvueoi+WIl+ihqOWksei0pTonLCByZXMubXNnKTtcbiAgICAgICAgbWVzc2FnZS5lcnJvcihyZXMubXNnIHx8ICfojrflj5blrZDor77nqIvliJfooajlpLHotKUnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOiOt+WPluezu+WIl+WtkOivvueoi+WIl+ihqOW8guW4uDonLCBlcnJvcik7XG4gICAgICBtZXNzYWdlLmVycm9yKCfojrflj5blrZDor77nqIvliJfooajlpLHotKXvvIzor7fph43or5UnKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g6I635Y+W6K++56iL5YiX6KGo77yI5L+d55WZ5Y6f5pyJ5Yqf6IO977yJXG4gIGNvbnN0IGZldGNoQ291cnNlTGlzdCA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ/Cfk50g6I635Y+W6K++56iL5YiX6KGoLi4uJyk7XG5cbiAgICAgIC8vIOiOt+WPluezu+WIl+ivvueoi+WIl+ihqFxuICAgICAgYXdhaXQgZmV0Y2hTZXJpZXNMaXN0KCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDojrflj5bor77nqIvliJfooajlpLHotKU6JywgZXJyb3IpO1xuICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfojrflj5bor77nqIvliJfooajlpLHotKXvvIzor7fph43or5UnKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5re75Yqg6K++56iLXG4gIGNvbnN0IGhhbmRsZUFkZENvdXJzZSA9IGFzeW5jICh2YWx1ZXM6IGFueSkgPT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyDmnoTlu7rlhoXlrrnphY3nva7vvIzlj6rljIXlkKvmnInmlYjnmoTlqpLkvZPmlofku7ZcbiAgICAgIGNvbnN0IGNvbnRlbnRDb25maWc6IGFueSA9IHtcbiAgICAgICAgaGFzVmlkZW86IGNvdXJzZVZpZGVvVXJsID8gMSA6IDAsXG4gICAgICAgIGhhc0RvY3VtZW50OiBjb3Vyc2VEb2N1bWVudFVybCA/IDEgOiAwLFxuICAgICAgfTtcblxuICAgICAgaWYgKGNvdXJzZVZpZGVvVXJsKSB7XG4gICAgICAgIGNvbnRlbnRDb25maWcudmlkZW8gPSB7XG4gICAgICAgICAgdXJsOiBjb3Vyc2VWaWRlb1VybCxcbiAgICAgICAgICBuYW1lOiBjb3Vyc2VWaWRlb05hbWUgfHwgJ+ivvueoi+inhumikS5tcDQnXG4gICAgICAgIH07XG4gICAgICB9XG5cbiAgICAgIGlmIChjb3Vyc2VEb2N1bWVudFVybCkge1xuICAgICAgICBjb250ZW50Q29uZmlnLmRvY3VtZW50ID0ge1xuICAgICAgICAgIHVybDogY291cnNlRG9jdW1lbnRVcmwsXG4gICAgICAgICAgbmFtZTogY291cnNlRG9jdW1lbnROYW1lIHx8ICfor77nqIvmlofmoaMucGRmJ1xuICAgICAgICB9O1xuICAgICAgfVxuXG4gICAgICBjb25zdCBjb3Vyc2VEYXRhID0ge1xuICAgICAgICBzZXJpZXNJZDogcGFyc2VJbnQodmFsdWVzLnNlcmllc0lkKSxcbiAgICAgICAgdGl0bGU6IHZhbHVlcy50aXRsZS50cmltKCksXG4gICAgICAgIGRlc2NyaXB0aW9uOiB2YWx1ZXMuZGVzY3JpcHRpb24udHJpbSgpLFxuICAgICAgICBjb3ZlckltYWdlOiBjb3Vyc2VDb3ZlckltYWdlVXJsLFxuICAgICAgICBoYXNWaWRlbzogY291cnNlVmlkZW9VcmwgPyAxIDogMCxcbiAgICAgICAgaGFzRG9jdW1lbnQ6IGNvdXJzZURvY3VtZW50VXJsID8gMSA6IDAsXG4gICAgICAgIGhhc0F1ZGlvOiBjb3Vyc2VBdWRpb1VybCA/IDEgOiAwLFxuICAgICAgICB2aWRlb0R1cmF0aW9uOiB2aWRlb0R1cmF0aW9uIHx8IDAsXG4gICAgICAgIGNvbnRlbnRDb25maWcsXG4gICAgICAgIHRlYWNoaW5nSW5mbzogdmFsdWVzLnRlYWNoaW5nT2JqZWN0aXZlcyAmJiB2YWx1ZXMudGVhY2hpbmdPYmplY3RpdmVzLmxlbmd0aCA+IDAgPyBbe1xuICAgICAgICAgIHRpdGxlOiBcIuaVmeWtpuebruagh1wiLFxuICAgICAgICAgIGNvbnRlbnQ6IEFycmF5LmlzQXJyYXkodmFsdWVzLnRlYWNoaW5nT2JqZWN0aXZlcykgPyB2YWx1ZXMudGVhY2hpbmdPYmplY3RpdmVzIDogW3ZhbHVlcy50ZWFjaGluZ09iamVjdGl2ZXNdXG4gICAgICAgIH1dIDogW10sXG4gICAgICAgIGFkZGl0aW9uYWxSZXNvdXJjZXM6IGFkZGl0aW9uYWxGaWxlcy5tYXAoZmlsZSA9PiAoe1xuICAgICAgICAgIHRpdGxlOiBmaWxlLnNwbGl0KCcvJykucG9wKCkgfHwgJ2ZpbGUnLFxuICAgICAgICAgIHVybDogZmlsZSxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ+ivvueoi+mZhOS7tui1hOa6kCdcbiAgICAgICAgfSkpLFxuICAgICAgICBvcmRlckluZGV4OiBwYXJzZUludCh2YWx1ZXMub3JkZXJJbmRleCkgfHwgMFxuICAgICAgfTtcblxuICAgICAgLy8g6aqM6K+B5b+F6KaB5a2X5q61XG4gICAgICBpZiAoIWNvdXJzZURhdGEuc2VyaWVzSWQpIHtcbiAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfor7fpgInmi6nmiYDlsZ7ns7vliJfor77nqIsnKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgaWYgKCFjb3Vyc2VEYXRhLnRpdGxlKSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign6K+36L6T5YWl6K++56iL5ZCN56ewJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIGlmICghY291cnNlRGF0YS5jb3ZlckltYWdlKSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign6K+35LiK5Lyg6K++56iL5bCB6Z2iJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg5o+Q5Lqk6K++56iL5pWw5o2uOicsIGNvdXJzZURhdGEpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk4og5pWw5o2u5aSn5bCP5Lyw566XOicsIEpTT04uc3RyaW5naWZ5KGNvdXJzZURhdGEpLmxlbmd0aCwgJ+Wtl+espicpO1xuXG4gICAgICAvLyDmt7vliqDph43or5XmnLrliLZcbiAgICAgIGxldCByZXRyeUNvdW50ID0gMDtcbiAgICAgIGNvbnN0IG1heFJldHJpZXMgPSAyO1xuICAgICAgbGV0IGxhc3RFcnJvcjtcblxuICAgICAgd2hpbGUgKHJldHJ5Q291bnQgPD0gbWF4UmV0cmllcykge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IHsgZGF0YTogcmVzIH0gPSBhd2FpdCBjb3Vyc2VBcGkuY3JlYXRlQ291cnNlKGNvdXJzZURhdGEpO1xuXG4gICAgICAgICAgLy8g5aaC5p6c5oiQ5Yqf77yM6Lez5Ye66YeN6K+V5b6q546vXG4gICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHtcbiAgICAgICAgICAgIG5vdGlmaWNhdGlvbi5zdWNjZXNzKCfliJvlu7ror77nqIvmiJDlip8nKTtcbiAgICAgICAgICAgIGZldGNoQ291cnNlTGlzdCgpO1xuICAgICAgICAgICAgc2V0SXNBZGRDb3Vyc2VNb2RhbFZpc2libGUoZmFsc2UpO1xuICAgICAgICAgICAgYWRkQ291cnNlRm9ybS5yZXNldEZpZWxkcygpO1xuICAgICAgICAgICAgc2V0Q291cnNlQ292ZXJJbWFnZVVybCgnJyk7XG4gICAgICAgICAgICBzZXRBZGRpdGlvbmFsRmlsZXMoW10pO1xuICAgICAgICAgICAgc2V0Q291cnNlVmlkZW9VcmwoJycpO1xuICAgICAgICAgICAgc2V0Q291cnNlVmlkZW9OYW1lKCcnKTtcbiAgICAgICAgICAgIHNldENvdXJzZURvY3VtZW50VXJsKCcnKTtcbiAgICAgICAgICAgIHNldENvdXJzZURvY3VtZW50TmFtZSgnJyk7XG4gICAgICAgICAgICBzZXRDb3Vyc2VBdWRpb1VybCgnJyk7XG4gICAgICAgICAgICBzZXRDb3Vyc2VBdWRpb05hbWUoJycpO1xuICAgICAgICAgICAgc2V0VmlkZW9EdXJhdGlvbigwKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKHJlcy5tc2cgfHwgJ+WIm+W7uuivvueoi+Wksei0pScpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgIH1cbiAgICAgICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgICAgIGxhc3RFcnJvciA9IGVycm9yO1xuICAgICAgICAgIHJldHJ5Q291bnQrKztcblxuICAgICAgICAgIGlmIChyZXRyeUNvdW50IDw9IG1heFJldHJpZXMpIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5SEIOesrCR7cmV0cnlDb3VudH3mrKHph43or5UuLi5gKTtcbiAgICAgICAgICAgIG5vdGlmaWNhdGlvbi53YXJuaW5nKGDnvZHnu5zlvILluLjvvIzmraPlnKjph43or5UgKCR7cmV0cnlDb3VudH0vJHttYXhSZXRyaWVzfSlgKTtcbiAgICAgICAgICAgIC8vIOetieW+hTHnp5LlkI7ph43or5VcbiAgICAgICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDAwKSk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIOWmguaenOaJgOaciemHjeivlemDveWksei0peS6hu+8jOaKm+WHuuacgOWQjueahOmUmeivr1xuICAgICAgdGhyb3cgbGFzdEVycm9yO1xuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDliJvlu7ror77nqIvlpLHotKU6JywgZXJyb3IpO1xuXG4gICAgICAvLyDmm7Tor6bnu4bnmoTplJnor6/lpITnkIZcbiAgICAgIGlmIChlcnJvci5jb2RlID09PSAnRUNPTk5SRVNFVCcgfHwgZXJyb3IubWVzc2FnZT8uaW5jbHVkZXMoJ0VDT05OUkVTRVQnKSB8fFxuICAgICAgICAgIChlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSAmJiBlcnJvci5yZXNwb25zZS5kYXRhLm1lc3NhZ2UuaW5jbHVkZXMoJ0VDT05OUkVTRVQnKSkpIHtcbiAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfnvZHnu5zov57mjqXkuK3mlq3vvIzlj6/og73mmK/nvZHnu5zkuI3nqLPlrprmiJbmnI3liqHlmajnuYHlv5njgILor7fnqI3lkI7ph43or5XmiJbogZTns7vnrqHnkIblkZjjgIInKTtcbiAgICAgIH0gZWxzZSBpZiAoZXJyb3IuY29kZSA9PT0gJ05FVFdPUktfRVJST1InKSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign572R57uc6ZSZ6K+v77yM6K+35qOA5p+l572R57uc6L+e5o6lJyk7XG4gICAgICB9IGVsc2UgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQxMykge1xuICAgICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+S4iuS8oOaWh+S7tui/h+Wkp++8jOivt+WOi+e8qeWQjumHjeivlScpO1xuICAgICAgfSBlbHNlIGlmIChlcnJvci5yZXNwb25zZT8uc3RhdHVzID09PSA0MDApIHtcbiAgICAgICAgY29uc3QgZXJyb3JNc2cgPSBlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCBlcnJvci5tZXNzYWdlO1xuICAgICAgICBub3RpZmljYXRpb24uZXJyb3IoYOivt+axguWPguaVsOmUmeivrzogJHtlcnJvck1zZ31gKTtcbiAgICAgIH0gZWxzZSBpZiAoZXJyb3IucmVzcG9uc2U/LnN0YXR1cyA9PT0gNTAwKSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign5pyN5Yqh5Zmo5YaF6YOo6ZSZ6K+v77yM6K+36IGU57O7566h55CG5ZGYJyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBub3RpZmljYXRpb24uZXJyb3IoYOWIm+W7uuivvueoi+Wksei0pTogJHtlcnJvci5tZXNzYWdlIHx8ICfor7fnqI3lkI7ph43or5UnfWApO1xuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZygn8J+UjSDlrozmlbTplJnor6/kv6Hmga86Jywge1xuICAgICAgICBtZXNzYWdlOiBlcnJvci5tZXNzYWdlLFxuICAgICAgICBjb2RlOiBlcnJvci5jb2RlLFxuICAgICAgICBzdGF0dXM6IGVycm9yLnJlc3BvbnNlPy5zdGF0dXMsXG4gICAgICAgIGRhdGE6IGVycm9yLnJlc3BvbnNlPy5kYXRhXG4gICAgICB9KTtcbiAgICB9XG4gIH07XG5cbiAgLy8g57yW6L6R6K++56iLXG4gIGNvbnN0IGhhbmRsZUVkaXRDb3Vyc2UgPSBhc3luYyAodmFsdWVzOiBVcGRhdGVDb3Vyc2VSZXF1ZXN0KSA9PiB7XG4gICAgaWYgKCFlZGl0aW5nQ291cnNlKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBkYXRhOiByZXMgfSA9IGF3YWl0IGNvdXJzZUFwaS51cGRhdGVDb3Vyc2UoZWRpdGluZ0NvdXJzZS5pZCwgdmFsdWVzKTtcbiAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5zdWNjZXNzKCfmm7TmlrDor77nqIvmiJDlip8nKTtcbiAgICAgICAgZmV0Y2hDb3Vyc2VMaXN0KCk7XG4gICAgICAgIHNldElzRWRpdENvdXJzZU1vZGFsVmlzaWJsZShmYWxzZSk7XG4gICAgICAgIHNldEVkaXRpbmdDb3Vyc2UobnVsbCk7XG4gICAgICAgIGVkaXRDb3Vyc2VGb3JtLnJlc2V0RmllbGRzKCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBub3RpZmljYXRpb24uZXJyb3IocmVzLm1zZyB8fCAn5pu05paw6K++56iL5aSx6LSlJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDmm7TmlrDor77nqIvlpLHotKU6JywgZXJyb3IpO1xuICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfmm7TmlrDor77nqIvlpLHotKXvvIzor7fph43or5UnKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5Yig6Zmk6K++56iLXG4gIGNvbnN0IGhhbmRsZURlbGV0ZUNvdXJzZSA9IGFzeW5jIChjb3Vyc2VJZDogbnVtYmVyKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHsgZGF0YTogcmVzIH0gPSBhd2FpdCBjb3Vyc2VBcGkuZGVsZXRlQ291cnNlKGNvdXJzZUlkKTtcbiAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5zdWNjZXNzKCfliKDpmaTor77nqIvmiJDlip8nKTtcbiAgICAgICAgZmV0Y2hDb3Vyc2VMaXN0KCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBub3RpZmljYXRpb24uZXJyb3IocmVzLm1zZyB8fCAn5Yig6Zmk6K++56iL5aSx6LSlJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDliKDpmaTor77nqIvlpLHotKU6JywgZXJyb3IpO1xuICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfliKDpmaTor77nqIvlpLHotKXvvIzor7fph43or5UnKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5Yig6Zmk5a2Q6K++56iLXG4gIGNvbnN0IGhhbmRsZURlbGV0ZVN1YkNvdXJzZSA9IGFzeW5jIChjb3Vyc2VJZDogbnVtYmVyLCBzZXJpZXNJZDogbnVtYmVyKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5eR77iPIOWIoOmZpOWtkOivvueoi++8jOivvueoi0lEOicsIGNvdXJzZUlkLCAn57O75YiXSUQ6Jywgc2VyaWVzSWQpO1xuXG4gICAgICBjb25zdCB7IGRhdGE6IHJlcyB9ID0gYXdhaXQgY291cnNlQXBpLmRlbGV0ZUNvdXJzZShjb3Vyc2VJZCk7XG5cbiAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7XG4gICAgICAgIG1lc3NhZ2Uuc3VjY2Vzcygn5Yig6Zmk5a2Q6K++56iL5oiQ5YqfJyk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg5a2Q6K++56iL5Yig6Zmk5oiQ5Yqf77yM6YeN5paw6I635Y+W57O75YiX5a2Q6K++56iL5YiX6KGoJyk7XG5cbiAgICAgICAgLy8g6YeN5paw6I635Y+W6K+l57O75YiX55qE5a2Q6K++56iL5YiX6KGoXG4gICAgICAgIGF3YWl0IGZldGNoU2VyaWVzQ291cnNlcyhzZXJpZXNJZCk7XG5cbiAgICAgICAgY29uc29sZS5sb2coJ/CflIQg5a2Q6K++56iL5YiX6KGo5bey5Yi35pawJyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg5Yig6Zmk5a2Q6K++56iL5aSx6LSlOicsIHJlcy5tc2cpO1xuICAgICAgICBtZXNzYWdlLmVycm9yKHJlcy5tc2cgfHwgJ+WIoOmZpOWtkOivvueoi+Wksei0pScpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg5Yig6Zmk5a2Q6K++56iL5byC5bi4OicsIGVycm9yKTtcbiAgICAgIG1lc3NhZ2UuZXJyb3IoJ+WIoOmZpOWtkOivvueoi+Wksei0pe+8jOivt+mHjeivlScpO1xuICAgIH1cbiAgfTtcblxuICAvLyDliIfmjaLns7vliJflsZXlvIAv5pS26LW354q25oCBXG4gIGNvbnN0IHRvZ2dsZVNlcmllc0V4cGFuc2lvbiA9IGFzeW5jIChzZXJpZXNJZDogbnVtYmVyKSA9PiB7XG4gICAgY29uc29sZS5sb2coJ/CflIQg5YiH5o2i57O75YiX5bGV5byA54q25oCB77yM57O75YiXSUQ6Jywgc2VyaWVzSWQpO1xuICAgIGNvbnNvbGUubG9nKCfwn5OKIOW9k+WJjeWxleW8gOeKtuaAgTonLCBleHBhbmRlZFNlcmllcy5oYXMoc2VyaWVzSWQpKTtcblxuICAgIGlmIChleHBhbmRlZFNlcmllcy5oYXMoc2VyaWVzSWQpKSB7XG4gICAgICAvLyDmlLbotbdcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OBIOaUtui1t+ezu+WIlzonLCBzZXJpZXNJZCk7XG4gICAgICBzZXRFeHBhbmRlZFNlcmllcyhwcmV2ID0+IHtcbiAgICAgICAgY29uc3QgbmV3U2V0ID0gbmV3IFNldChwcmV2KTtcbiAgICAgICAgbmV3U2V0LmRlbGV0ZShzZXJpZXNJZCk7XG4gICAgICAgIHJldHVybiBuZXdTZXQ7XG4gICAgICB9KTtcbiAgICB9IGVsc2Uge1xuICAgICAgLy8g5bGV5byA77yM6ZyA6KaB6I635Y+W5a2Q6K++56iL5pWw5o2uXG4gICAgICBjb25zb2xlLmxvZygn8J+TgiDlsZXlvIDns7vliJfvvIzojrflj5blrZDor77nqIs6Jywgc2VyaWVzSWQpO1xuICAgICAgYXdhaXQgZmV0Y2hTZXJpZXNDb3Vyc2VzKHNlcmllc0lkKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5bGV5byA5omA5pyJ57O75YiXXG4gIGNvbnN0IGV4cGFuZEFsbFNlcmllcyA9IGFzeW5jICgpID0+IHtcbiAgICBjb25zb2xlLmxvZygn8J+TgiDlsZXlvIDmiYDmnInns7vliJfor77nqIsnKTtcbiAgICBmb3IgKGNvbnN0IHNlcmllcyBvZiBzZXJpZXNMaXN0KSB7XG4gICAgICBpZiAoIWV4cGFuZGVkU2VyaWVzLmhhcyhzZXJpZXMuaWQpKSB7XG4gICAgICAgIGF3YWl0IGZldGNoU2VyaWVzQ291cnNlcyhzZXJpZXMuaWQpO1xuICAgICAgfVxuICAgIH1cbiAgfTtcblxuICAvLyDmlLbotbfmiYDmnInns7vliJdcbiAgY29uc3QgY29sbGFwc2VBbGxTZXJpZXMgPSAoKSA9PiB7XG4gICAgY29uc29sZS5sb2coJ/Cfk4Eg5pS26LW35omA5pyJ57O75YiX6K++56iLJyk7XG4gICAgc2V0RXhwYW5kZWRTZXJpZXMobmV3IFNldCgpKTtcbiAgfTtcblxuICAvLyDmt7vliqDns7vliJfor77nqItcbiAgY29uc3QgaGFuZGxlQWRkU2VyaWVzID0gYXN5bmMgKHZhbHVlczogQ3JlYXRlQ291cnNlU2VyaWVzUmVxdWVzdCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBzZXJpZXNEYXRhID0ge1xuICAgICAgICAuLi52YWx1ZXMsXG4gICAgICAgIGNvdmVySW1hZ2U6IGNvdmVySW1hZ2VVcmxcbiAgICAgIH07XG5cbiAgICAgIGNvbnNvbGUubG9nKCfliJvlu7rns7vliJfor77nqIvmlbDmja46Jywgc2VyaWVzRGF0YSk7XG5cbiAgICAgIGNvbnN0IHsgZGF0YTogcmVzIH0gPSBhd2FpdCBjb3Vyc2VBcGkuY3JlYXRlQ291cnNlU2VyaWVzKHNlcmllc0RhdGEpO1xuXG4gICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkge1xuICAgICAgICBub3RpZmljYXRpb24uc3VjY2Vzcygn5Yib5bu657O75YiX6K++56iL5oiQ5YqfJyk7XG4gICAgICAgIGZldGNoQ291cnNlTGlzdCgpO1xuICAgICAgICBzZXRJc0FkZFNlcmllc01vZGFsVmlzaWJsZShmYWxzZSk7XG4gICAgICAgIGFkZFNlcmllc0Zvcm0ucmVzZXRGaWVsZHMoKTtcbiAgICAgICAgc2V0Q292ZXJJbWFnZVVybCgnJyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBub3RpZmljYXRpb24uZXJyb3IocmVzLm1zZyB8fCAn5Yib5bu657O75YiX6K++56iL5aSx6LSlJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDliJvlu7rns7vliJfor77nqIvlpLHotKU6JywgZXJyb3IpO1xuICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfliJvlu7rns7vliJfor77nqIvlpLHotKXvvIzor7fph43or5UnKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5Yib5bu66K++56iL5qCH562+XG4gIGNvbnN0IGhhbmRsZUFkZFRhZyA9IGFzeW5jICh2YWx1ZXM6IGFueSkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+Pt++4jyDliJvlu7ror77nqIvmoIfnrb7mlbDmja46JywgdmFsdWVzKTtcblxuICAgICAgY29uc3QgeyBkYXRhOiByZXMgfSA9IGF3YWl0IGNvdXJzZUFwaS5jcmVhdGVDb3Vyc2VUYWcodmFsdWVzKTtcblxuICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHtcbiAgICAgICAgbm90aWZpY2F0aW9uLnN1Y2Nlc3MoJ+WIm+W7uuagh+etvuaIkOWKnycpO1xuICAgICAgICBzZXRJc0FkZFRhZ01vZGFsVmlzaWJsZShmYWxzZSk7XG4gICAgICAgIGFkZFRhZ0Zvcm0ucmVzZXRGaWVsZHMoKTtcbiAgICAgICAgLy8g6YeN5paw6I635Y+W5qCH562+5YiX6KGoXG4gICAgICAgIGZldGNoQ291cnNlVGFncygpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKHJlcy5tc2cgfHwgJ+WIm+W7uuagh+etvuWksei0pScpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg5Yib5bu65qCH562+5aSx6LSlOicsIGVycm9yKTtcbiAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign5Yib5bu65qCH562+5aSx6LSl77yM6K+36YeN6K+VJyk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWPkeW4g+ezu+WIl+ivvueoi1xuICBjb25zdCBoYW5kbGVQdWJsaXNoU2VyaWVzID0gYXN5bmMgKHZhbHVlczogYW55KSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OiIOWPkeW4g+ezu+WIl+ivvueoi+aVsOaNrjonLCB2YWx1ZXMpO1xuXG4gICAgICBjb25zdCB7IGRhdGE6IHJlcyB9ID0gYXdhaXQgY291cnNlQXBpLnB1Ymxpc2hDb3Vyc2VTZXJpZXModmFsdWVzLnNlcmllc0lkKTtcblxuICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHtcbiAgICAgICAgbm90aWZpY2F0aW9uLnN1Y2Nlc3MoJ+WPkeW4g+ezu+WIl+ivvueoi+aIkOWKnycpO1xuICAgICAgICBzZXRJc1B1Ymxpc2hTZXJpZXNNb2RhbFZpc2libGUoZmFsc2UpO1xuICAgICAgICBwdWJsaXNoU2VyaWVzRm9ybS5yZXNldEZpZWxkcygpO1xuXG4gICAgICAgIC8vIOaYvuekuuWPkeW4g+e7k+aenOS/oeaBr1xuICAgICAgICBjb25zdCBwdWJsaXNoRGF0YSA9IHJlcy5kYXRhO1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOWPkeW4g+aIkOWKn++8jOezu+WIl+S/oeaBrzonLCBwdWJsaXNoRGF0YSk7XG5cbiAgICAgICAgLy8g5Y+v5Lul6YCJ5oup5pi+56S65Y+R5biD57uf6K6h5L+h5oGvXG4gICAgICAgIGlmIChwdWJsaXNoRGF0YS5wdWJsaXNoU3RhdHMpIHtcbiAgICAgICAgICBjb25zdCBzdGF0cyA9IHB1Ymxpc2hEYXRhLnB1Ymxpc2hTdGF0cztcbiAgICAgICAgICBjb25zdCBzdGF0c01lc3NhZ2UgPSBg5bey5Y+R5biDICR7cHVibGlzaERhdGEucHVibGlzaGVkQ291cnNlc30vJHtwdWJsaXNoRGF0YS50b3RhbENvdXJzZXN9IOS4quivvueoi++8jOWMheWQqyAke3N0YXRzLnZpZGVvQ291cnNlQ291bnR9IOS4quinhumikeivvueoi++8jOaAu+aXtumVvyAke01hdGgucm91bmQoc3RhdHMudG90YWxWaWRlb0R1cmF0aW9uIC8gNjApfSDliIbpkp9gO1xuICAgICAgICAgIG5vdGlmaWNhdGlvbi5pbmZvKHN0YXRzTWVzc2FnZSk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcihyZXMubXNnIHx8ICflj5HluIPns7vliJfor77nqIvlpLHotKUnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOWPkeW4g+ezu+WIl+ivvueoi+Wksei0pTonLCBlcnJvcik7XG4gICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+WPkeW4g+ezu+WIl+ivvueoi+Wksei0pe+8jOivt+mHjeivlScpO1xuICAgIH1cbiAgfTtcblxuICAvLyDojrflj5blj5HluIPnlKjnmoTns7vliJfor77nqIvliJfooahcbiAgY29uc3QgZmV0Y2hTZXJpZXNGb3JQdWJsaXNoID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRQdWJsaXNoTG9hZGluZyh0cnVlKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OdIOiOt+WPluWPkeW4g+eUqOezu+WIl+ivvueoi+WIl+ihqC4uLicpO1xuXG4gICAgICBjb25zdCB7IGRhdGE6IHJlcyB9ID0gYXdhaXQgY291cnNlQXBpLmdldE1hcmtldHBsYWNlU2VyaWVzKHtcbiAgICAgICAgcGFnZTogMSxcbiAgICAgICAgcGFnZVNpemU6IDUwXG4gICAgICB9KTtcblxuICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDAgJiYgcmVzLmRhdGE/Lmxpc3QpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDojrflj5blj5HluIPnlKjns7vliJfor77nqIvliJfooajmiJDlip86JywgcmVzLmRhdGEubGlzdCk7XG4gICAgICAgIHJldHVybiByZXMuZGF0YS5saXN0O1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOiOt+WPluWPkeW4g+eUqOezu+WIl+ivvueoi+WIl+ihqOWksei0pTonLCByZXMubXNnKTtcbiAgICAgICAgbWVzc2FnZS5lcnJvcihyZXMubXNnIHx8ICfojrflj5bns7vliJfor77nqIvliJfooajlpLHotKUnKTtcbiAgICAgICAgcmV0dXJuIFtdO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg6I635Y+W5Y+R5biD55So57O75YiX6K++56iL5YiX6KGo5byC5bi4OicsIGVycm9yKTtcbiAgICAgIG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluezu+WIl+ivvueoi+WIl+ihqOWksei0pe+8jOivt+mHjeivlScpO1xuICAgICAgcmV0dXJuIFtdO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRQdWJsaXNoTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOiOt+WPluaMh+Wumuezu+WIl+eahOivvueoi+ivpuaDhVxuICBjb25zdCBmZXRjaFNlcmllc0RldGFpbEZvclB1Ymxpc2ggPSBhc3luYyAoc2VyaWVzSWQ6IG51bWJlcikgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRQdWJsaXNoTG9hZGluZyh0cnVlKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OdIOiOt+WPluezu+WIl+ivvueoi+ivpuaDhe+8jOezu+WIl0lEOicsIHNlcmllc0lkKTtcblxuICAgICAgY29uc3QgeyBkYXRhOiByZXMgfSA9IGF3YWl0IGNvdXJzZUFwaS5nZXRNYXJrZXRwbGFjZVNlcmllc0RldGFpbChzZXJpZXNJZCk7XG5cbiAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwICYmIHJlcy5kYXRhKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg6I635Y+W57O75YiX6K++56iL6K+m5oOF5oiQ5YqfOicsIHJlcy5kYXRhKTtcbiAgICAgICAgcmV0dXJuIHJlcy5kYXRhO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOiOt+WPluezu+WIl+ivvueoi+ivpuaDheWksei0pTonLCByZXMubXNnKTtcbiAgICAgICAgbWVzc2FnZS5lcnJvcihyZXMubXNnIHx8ICfojrflj5bns7vliJfor77nqIvor6bmg4XlpLHotKUnKTtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDojrflj5bns7vliJfor77nqIvor6bmg4XlvILluLg6JywgZXJyb3IpO1xuICAgICAgbWVzc2FnZS5lcnJvcign6I635Y+W57O75YiX6K++56iL6K+m5oOF5aSx6LSl77yM6K+36YeN6K+VJyk7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0UHVibGlzaExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyDojrflj5blj5HluIPlvLnnqpfnmoTns7vliJfor77nqIvliJfooahcbiAgY29uc3QgZmV0Y2hQdWJsaXNoU2VyaWVzTGlzdCA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0UHVibGlzaEZvcm1Mb2FkaW5nKHRydWUpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk50g6I635Y+W5Y+R5biD5by556qX55qE57O75YiX6K++56iL5YiX6KGoLi4uJyk7XG5cbiAgICAgIGNvbnN0IHsgZGF0YTogcmVzIH0gPSBhd2FpdCBjb3Vyc2VBcGkuZ2V0TWFya2V0cGxhY2VTZXJpZXMoe1xuICAgICAgICBwYWdlOiAxLFxuICAgICAgICBwYWdlU2l6ZTogNTBcbiAgICAgIH0pO1xuXG4gICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCAmJiByZXMuZGF0YT8ubGlzdCkge1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOiOt+WPluWPkeW4g+W8ueeql+ezu+WIl+ivvueoi+WIl+ihqOaIkOWKnzonLCByZXMuZGF0YS5saXN0KTtcbiAgICAgICAgc2V0UHVibGlzaFNlcmllc0xpc3RGb3JNb2RhbChyZXMuZGF0YS5saXN0KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDojrflj5blj5HluIPlvLnnqpfns7vliJfor77nqIvliJfooajlpLHotKU6JywgcmVzLm1zZyk7XG4gICAgICAgIG1lc3NhZ2UuZXJyb3IocmVzLm1zZyB8fCAn6I635Y+W57O75YiX6K++56iL5YiX6KGo5aSx6LSlJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDojrflj5blj5HluIPlvLnnqpfns7vliJfor77nqIvliJfooajlvILluLg6JywgZXJyb3IpO1xuICAgICAgbWVzc2FnZS5lcnJvcign6I635Y+W57O75YiX6K++56iL5YiX6KGo5aSx6LSl77yM6K+36YeN6K+VJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldFB1Ymxpc2hGb3JtTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOiOt+WPluaMh+Wumuezu+WIl+S4i+eahOWtkOivvueoi+WIl+ihqO+8iOeUqOS6juWPkeW4g+W8ueeql++8iVxuICBjb25zdCBmZXRjaFB1Ymxpc2hDb3Vyc2VMaXN0ID0gYXN5bmMgKHNlcmllc0lkOiBudW1iZXIpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ/Cfk50g6I635Y+W5Y+R5biD5by556qX55qE5a2Q6K++56iL5YiX6KGo77yM57O75YiXSUQ6Jywgc2VyaWVzSWQpO1xuICAgICAgY29uc29sZS5sb2coJ/CflIQg5L2/55So6K++56iL566h55CGQVBJ6I635Y+W6I2J56i/54q25oCB55qE5a2Q6K++56iLLi4uJyk7XG5cbiAgICAgIC8vIOS9v+eUqOivvueoi+euoeeQhkFQSeiOt+WPluiNieeov+eKtuaAgeeahOivvueoi1xuICAgICAgY29uc3QgeyBkYXRhOiByZXMgfSA9IGF3YWl0IGNvdXJzZUFwaS5nZXRNYW5hZ2VtZW50U2VyaWVzQ291cnNlcyhzZXJpZXNJZCwge1xuICAgICAgICBwYWdlOiAxLFxuICAgICAgICBwYWdlU2l6ZTogNTAsXG4gICAgICAgIHN0YXR1czogMCAgLy8g5Y+q6I635Y+W6I2J56i/54q25oCB55qE6K++56iLXG4gICAgICB9KTtcblxuICAgICAgY29uc29sZS5sb2coJ/CflI0g6K++56iL566h55CGQVBJ5ZON5bqUOicsIHJlcyk7XG5cbiAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwICYmIHJlcy5kYXRhPy5saXN0KSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg6I635Y+W6I2J56i/54q25oCB5a2Q6K++56iL5YiX6KGo5oiQ5Yqf77yM5pWw6YePOicsIHJlcy5kYXRhLmxpc3QubGVuZ3RoKTtcbiAgICAgICAgc2V0UHVibGlzaENvdXJzZUxpc3RGb3JNb2RhbChyZXMuZGF0YS5saXN0KTtcblxuICAgICAgICBpZiAocmVzLmRhdGEubGlzdC5sZW5ndGggPT09IDApIHtcbiAgICAgICAgICBtZXNzYWdlLmluZm8oJ+ivpeezu+WIl+aaguaXoOiNieeov+eKtuaAgeeahOivvueoi+WPr+WPkeW4gycpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmxvZygn4pqg77iPIOivpeezu+WIl+aaguaXoOWtkOivvueoi+aIlkFQSeiwg+eUqOWksei0pScpO1xuICAgICAgICBzZXRQdWJsaXNoQ291cnNlTGlzdEZvck1vZGFsKFtdKTtcbiAgICAgICAgbWVzc2FnZS5pbmZvKCfor6Xns7vliJfmmoLml6DojYnnqL/nirbmgIHnmoTor77nqIvlj6/lj5HluIMnKTtcbiAgICAgIH1cblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg6I635Y+W5Y+R5biD5by556qX5a2Q6K++56iL5YiX6KGo5byC5bi4OicsIGVycm9yKTtcbiAgICAgIG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluWtkOivvueoi+WIl+ihqOWksei0pe+8jOivt+mHjeivlScpO1xuICAgICAgc2V0UHVibGlzaENvdXJzZUxpc3RGb3JNb2RhbChbXSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWkhOeQhuezu+WIl+mAieaLqe+8iOWPkeW4g+W8ueeql++8iVxuICBjb25zdCBoYW5kbGVQdWJsaXNoU2VyaWVzQ2hhbmdlID0gYXN5bmMgKHNlcmllc0lkOiBudW1iZXIpID0+IHtcbiAgICBjb25zb2xlLmxvZygn8J+TmiDlj5HluIPlvLnnqpfpgInmi6nns7vliJdJRDonLCBzZXJpZXNJZCk7XG4gICAgY29uc29sZS5sb2coJ/Cfk5og5b2T5YmN57O75YiX5YiX6KGoOicsIHB1Ymxpc2hTZXJpZXNMaXN0Rm9yTW9kYWwpO1xuXG4gICAgc2V0U2VsZWN0ZWRTZXJpZXNGb3JQdWJsaXNoKHNlcmllc0lkKTtcbiAgICBzZXRTZWxlY3RlZENvdXJzZUZvclB1Ymxpc2godW5kZWZpbmVkKTtcbiAgICBzZXRQdWJsaXNoQ291cnNlTGlzdEZvck1vZGFsKFtdKTtcblxuICAgIC8vIOmHjee9ruihqOWNleS4reeahOivvueoi+mAieaLqVxuICAgIHB1Ymxpc2hDb3Vyc2VGb3JtLnNldEZpZWxkc1ZhbHVlKHsgY291cnNlSWQ6IHVuZGVmaW5lZCB9KTtcblxuICAgIC8vIOiOt+WPluivpeezu+WIl+S4i+eahOWtkOivvueoi1xuICAgIGlmIChzZXJpZXNJZCkge1xuICAgICAgY29uc29sZS5sb2coJ/CflIQg5byA5aeL6I635Y+W57O75YiX5a2Q6K++56iLLi4uJyk7XG4gICAgICBzZXRQdWJsaXNoRm9ybUxvYWRpbmcodHJ1ZSk7XG5cbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IGZldGNoUHVibGlzaENvdXJzZUxpc3Qoc2VyaWVzSWQpO1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOWtkOivvueoi+iOt+WPluWujOaIkCcpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOWtkOivvueoi+iOt+WPluWksei0pTonLCBlcnJvcik7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRQdWJsaXNoRm9ybUxvYWRpbmcoZmFsc2UpO1xuICAgICAgfVxuICAgIH1cbiAgfTtcblxuICAvLyDlpITnkIbor77nqIvpgInmi6nvvIjlj5HluIPlvLnnqpfvvIlcbiAgY29uc3QgaGFuZGxlUHVibGlzaENvdXJzZUNoYW5nZSA9IChjb3Vyc2VJZDogbnVtYmVyKSA9PiB7XG4gICAgY29uc29sZS5sb2coJ/Cfk5Yg5Y+R5biD5by556qX6YCJ5oup6K++56iLSUQ6JywgY291cnNlSWQpO1xuICAgIHNldFNlbGVjdGVkQ291cnNlRm9yUHVibGlzaChjb3Vyc2VJZCk7XG4gIH07XG5cbiAgLy8g6YeN572u5Y+R5biD6K++56iL5by556qX54q25oCBXG4gIGNvbnN0IHJlc2V0UHVibGlzaENvdXJzZU1vZGFsID0gKCkgPT4ge1xuICAgIHNldElzUHVibGlzaENvdXJzZU1vZGFsVmlzaWJsZShmYWxzZSk7XG4gICAgc2V0U2VsZWN0ZWRTZXJpZXNGb3JQdWJsaXNoKHVuZGVmaW5lZCk7XG4gICAgc2V0U2VsZWN0ZWRDb3Vyc2VGb3JQdWJsaXNoKHVuZGVmaW5lZCk7XG4gICAgc2V0UHVibGlzaFNlcmllc0xpc3RGb3JNb2RhbChbXSk7XG4gICAgc2V0UHVibGlzaENvdXJzZUxpc3RGb3JNb2RhbChbXSk7XG4gICAgcHVibGlzaENvdXJzZUZvcm0ucmVzZXRGaWVsZHMoKTtcbiAgfTtcblxuICAvLyDmiZPlvIDlj5HluIPor77nqIvlvLnnqpdcbiAgY29uc3Qgb3BlblB1Ymxpc2hDb3Vyc2VNb2RhbCA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRJc1B1Ymxpc2hDb3Vyc2VNb2RhbFZpc2libGUodHJ1ZSk7XG4gICAgYXdhaXQgZmV0Y2hQdWJsaXNoU2VyaWVzTGlzdCgpO1xuICB9O1xuXG4gIC8vIOWPkeW4g+ivvueoi1xuICBjb25zdCBoYW5kbGVQdWJsaXNoQ291cnNlID0gYXN5bmMgKHZhbHVlczogYW55KSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGlmICghc2VsZWN0ZWRDb3Vyc2VGb3JQdWJsaXNoIHx8ICFzZWxlY3RlZFNlcmllc0ZvclB1Ymxpc2gpIHtcbiAgICAgICAgbWVzc2FnZS5lcnJvcign6K+36YCJ5oup57O75YiX6K++56iL5ZKM5a2Q6K++56iLJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgc2V0UHVibGlzaEZvcm1Mb2FkaW5nKHRydWUpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk6Ig5Y+R5biD6K++56iL77yM6K++56iLSUQ6Jywgc2VsZWN0ZWRDb3Vyc2VGb3JQdWJsaXNoKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OiIOezu+WIl0lEOicsIHNlbGVjdGVkU2VyaWVzRm9yUHVibGlzaCk7XG4gICAgICBjb25zb2xlLmxvZygn8J+TpCDooajljZXmlbDmja46JywgdmFsdWVzKTtcblxuICAgICAgLy8g6I635Y+W5b2T5YmN6YCJ5Lit55qE6K++56iL5L+h5oGvXG4gICAgICBjb25zdCBzZWxlY3RlZENvdXJzZSA9IHB1Ymxpc2hDb3Vyc2VMaXN0Rm9yTW9kYWwuZmluZChjID0+IGMuaWQgPT09IHNlbGVjdGVkQ291cnNlRm9yUHVibGlzaCk7XG4gICAgICBpZiAoIXNlbGVjdGVkQ291cnNlKSB7XG4gICAgICAgIG1lc3NhZ2UuZXJyb3IoJ+acquaJvuWIsOmAieS4reeahOivvueoi+S/oeaBrycpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5OWIOW9k+WJjeivvueoi+S/oeaBrzonLCBzZWxlY3RlZENvdXJzZSk7XG5cbiAgICAgIC8vIOS9v+eUqOS4k+mXqOeahOWPkeW4g+ivvueoi0FQSVxuICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg6LCD55So5Y+R5biD6K++56iLQVBJ77yM6K++56iLSUQ6Jywgc2VsZWN0ZWRDb3Vyc2VGb3JQdWJsaXNoKTtcbiAgICAgIGNvbnN0IHsgZGF0YTogcmVzIH0gPSBhd2FpdCBjb3Vyc2VBcGkucHVibGlzaENvdXJzZShzZWxlY3RlZENvdXJzZUZvclB1Ymxpc2gpO1xuXG4gICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkge1xuICAgICAgICBtZXNzYWdlLnN1Y2Nlc3MoJ+WPkeW4g+ivvueoi+aIkOWKnycpO1xuICAgICAgICByZXNldFB1Ymxpc2hDb3Vyc2VNb2RhbCgpO1xuXG4gICAgICAgIC8vIOaYvuekuuWPkeW4g+e7k+aenOS/oeaBr1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOWPkeW4g+aIkOWKn++8jOivvueoi+S/oeaBrzonLCByZXMuZGF0YSk7XG5cbiAgICAgICAgLy8g5Yi35paw6K++56iL5YiX6KGoXG4gICAgICAgIGF3YWl0IGZldGNoQ291cnNlTGlzdCgpO1xuXG4gICAgICAgIC8vIOWmguaenOW9k+WJjeezu+WIl+W3suWxleW8gO+8jOWIt+aWsOWtkOivvueoi+WIl+ihqFxuICAgICAgICBpZiAoc2VsZWN0ZWRTZXJpZXNGb3JQdWJsaXNoICYmIGV4cGFuZGVkU2VyaWVzLmhhcyhzZWxlY3RlZFNlcmllc0ZvclB1Ymxpc2gpKSB7XG4gICAgICAgICAgYXdhaXQgZmV0Y2hTZXJpZXNDb3Vyc2VzKHNlbGVjdGVkU2VyaWVzRm9yUHVibGlzaCk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDlj5HluIPor77nqIvlpLHotKU6JywgcmVzLm1zZyk7XG4gICAgICAgIG1lc3NhZ2UuZXJyb3IocmVzLm1zZyB8fCAn5Y+R5biD6K++56iL5aSx6LSlJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOWPkeW4g+ivvueoi+W8guW4uDonLCBlcnJvcik7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg6ZSZ6K+v6K+m5oOFOicsIGVycm9yLnJlc3BvbnNlPy5kYXRhKTtcbiAgICAgIG1lc3NhZ2UuZXJyb3IoJ+WPkeW4g+ivvueoi+Wksei0pe+8jOivt+mHjeivlScpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRQdWJsaXNoRm9ybUxvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyDph43nva7lj5HluIPlvLnnqpfnirbmgIFcbiAgY29uc3QgcmVzZXRQdWJsaXNoTW9kYWwgPSAoKSA9PiB7XG4gICAgc2V0SXNQdWJsaXNoQ291cnNlTW9kYWxWaXNpYmxlKGZhbHNlKTtcbiAgICBzZXRTZWxlY3RlZFNlcmllc0ZvclB1Ymxpc2godW5kZWZpbmVkKTtcbiAgICBzZXRTZWxlY3RlZENvdXJzZUZvclB1Ymxpc2godW5kZWZpbmVkKTtcbiAgICBzZXRQdWJsaXNoU2VyaWVzQ291cnNlcyhbXSk7XG4gICAgc2V0UHVibGlzaFNlcmllc09wdGlvbnMoW10pO1xuICAgIHB1Ymxpc2hDb3Vyc2VGb3JtLnJlc2V0RmllbGRzKCk7XG4gIH07XG5cblxuXG5cblxuICAvLyDlpITnkIblm77niYfkuIrkvKBcbiAgY29uc3QgaGFuZGxlSW1hZ2VVcGxvYWQ6IFVwbG9hZFByb3BzWydjdXN0b21SZXF1ZXN0J10gPSBhc3luYyAob3B0aW9ucykgPT4ge1xuICAgIGNvbnN0IHsgZmlsZSwgb25TdWNjZXNzLCBvbkVycm9yIH0gPSBvcHRpb25zO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIOiwg+eUqOWunumZheeahE9TU+S4iuS8oEFQSVxuICAgICAgY29uc3QgdXJsID0gYXdhaXQgdXBsb2FkQXBpLnVwbG9hZFRvT3NzKGZpbGUgYXMgRmlsZSk7XG4gICAgICBjb25zb2xlLmxvZygn57O75YiX5bCB6Z2i5Zu+54mH5LiK5Lyg5oiQ5Yqf77yMVVJMOicsIHVybCk7XG5cbiAgICAgIHNldENvdmVySW1hZ2VVcmwodXJsKTtcbiAgICAgIG9uU3VjY2Vzcz8uKHsgdXJsOiB1cmwgfSk7XG4gICAgICBtZXNzYWdlLnN1Y2Nlc3MoJ+WbvueJh+S4iuS8oOaIkOWKnycpO1xuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+ezu+WIl+WwgemdouWbvueJh+S4iuS8oOWksei0pTonLCBlcnJvcik7XG4gICAgICBtZXNzYWdlLmVycm9yKGDkuIrkvKDlpLHotKU6ICR7ZXJyb3IubWVzc2FnZSB8fCAn6K+356iN5ZCO6YeN6K+VJ31gKTtcbiAgICAgIG9uRXJyb3I/LihlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWkhOeQhuWbvueJh+WIoOmZpFxuICBjb25zdCBoYW5kbGVJbWFnZVJlbW92ZSA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRDb3ZlckltYWdlVXJsKCcnKTtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfTtcblxuICAvLyDlpITnkIbor77nqIvlsIHpnaLlm77niYfkuIrkvKBcbiAgY29uc3QgaGFuZGxlQ291cnNlQ292ZXJVcGxvYWQ6IFVwbG9hZFByb3BzWydjdXN0b21SZXF1ZXN0J10gPSBhc3luYyAob3B0aW9ucykgPT4ge1xuICAgIGNvbnN0IHsgZmlsZSwgb25TdWNjZXNzLCBvbkVycm9yIH0gPSBvcHRpb25zO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIOiwg+eUqOWunumZheeahE9TU+S4iuS8oEFQSVxuICAgICAgY29uc3QgdXJsID0gYXdhaXQgdXBsb2FkQXBpLnVwbG9hZFRvT3NzKGZpbGUgYXMgRmlsZSk7XG4gICAgICBjb25zb2xlLmxvZygn6K++56iL5bCB6Z2i5Zu+54mH5LiK5Lyg5oiQ5Yqf77yMVVJMOicsIHVybCk7XG5cbiAgICAgIHNldENvdXJzZUNvdmVySW1hZ2VVcmwodXJsKTtcbiAgICAgIG9uU3VjY2Vzcz8uKHsgdXJsOiB1cmwgfSk7XG4gICAgICBtZXNzYWdlLnN1Y2Nlc3MoJ+ivvueoi+WwgemdouS4iuS8oOaIkOWKnycpO1xuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+ivvueoi+WwgemdouWbvueJh+S4iuS8oOWksei0pTonLCBlcnJvcik7XG4gICAgICBtZXNzYWdlLmVycm9yKGDkuIrkvKDlpLHotKU6ICR7ZXJyb3IubWVzc2FnZSB8fCAn6K+356iN5ZCO6YeN6K+VJ31gKTtcbiAgICAgIG9uRXJyb3I/LihlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWkhOeQhuivvueoi+WwgemdouWIoOmZpFxuICBjb25zdCBoYW5kbGVDb3Vyc2VDb3ZlclJlbW92ZSA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRDb3Vyc2VDb3ZlckltYWdlVXJsKCcnKTtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfTtcblxuICAvLyDlpITnkIbpmYTku7botYTmupDkuIrkvKBcbiAgY29uc3QgaGFuZGxlQWRkaXRpb25hbFJlc291cmNlVXBsb2FkOiBVcGxvYWRQcm9wc1snY3VzdG9tUmVxdWVzdCddID0gYXN5bmMgKG9wdGlvbnMpID0+IHtcbiAgICBjb25zdCB7IGZpbGUsIG9uU3VjY2Vzcywgb25FcnJvciB9ID0gb3B0aW9ucztcblxuICAgIHRyeSB7XG4gICAgICAvLyDosIPnlKjlrp7pmYXnmoRPU1PkuIrkvKBBUElcbiAgICAgIGNvbnN0IHVybCA9IGF3YWl0IHVwbG9hZEFwaS51cGxvYWRUb09zcyhmaWxlIGFzIEZpbGUpO1xuICAgICAgY29uc29sZS5sb2coJ+mZhOS7tui1hOa6kOS4iuS8oOaIkOWKn++8jFVSTDonLCB1cmwpO1xuXG4gICAgICBzZXRBZGRpdGlvbmFsRmlsZXMocHJldiA9PiBbLi4ucHJldiwgdXJsXSk7XG4gICAgICBvblN1Y2Nlc3M/Lih7IHVybDogdXJsLCBuYW1lOiAoZmlsZSBhcyBGaWxlKS5uYW1lIH0pO1xuICAgICAgbWVzc2FnZS5zdWNjZXNzKGDpmYTku7YgJHsoZmlsZSBhcyBGaWxlKS5uYW1lfSDkuIrkvKDmiJDlip9gKTtcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfpmYTku7botYTmupDkuIrkvKDlpLHotKU6JywgZXJyb3IpO1xuICAgICAgbWVzc2FnZS5lcnJvcihg6ZmE5Lu2ICR7KGZpbGUgYXMgRmlsZSkubmFtZX0g5LiK5Lyg5aSx6LSlOiAke2Vycm9yLm1lc3NhZ2UgfHwgJ+ivt+eojeWQjumHjeivlSd9YCk7XG4gICAgICBvbkVycm9yPy4oZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICAvLyDlpITnkIbpmYTku7bliKDpmaRcbiAgY29uc3QgaGFuZGxlQWRkaXRpb25hbFJlc291cmNlUmVtb3ZlID0gYXN5bmMgKGZpbGU6IGFueSkgPT4ge1xuICAgIGNvbnN0IHVybCA9IGZpbGUudXJsIHx8IGZpbGUucmVzcG9uc2U/LnVybDtcbiAgICBzZXRBZGRpdGlvbmFsRmlsZXMocHJldiA9PiBwcmV2LmZpbHRlcihmID0+IGYgIT09IHVybCkpO1xuICAgIHJldHVybiB0cnVlO1xuICB9O1xuICAvLyDlpITnkIbop4bpopHkuIrkvKBcbiAgY29uc3QgaGFuZGxlVmlkZW9VcGxvYWQ6IFVwbG9hZFByb3BzWydjdXN0b21SZXF1ZXN0J10gPSBhc3luYyAob3B0aW9ucykgPT4ge1xuICAgIGNvbnN0IHsgZmlsZSwgb25TdWNjZXNzLCBvbkVycm9yIH0gPSBvcHRpb25zO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVybCA9IGF3YWl0IHVwbG9hZEFwaS51cGxvYWRUb09zcyhmaWxlIGFzIEZpbGUpO1xuICAgICAgY29uc29sZS5sb2coJ+ivvueoi+inhumikeS4iuS8oOaIkOWKn++8jFVSTDonLCB1cmwpO1xuXG4gICAgICBzZXRDb3Vyc2VWaWRlb1VybCh1cmwpO1xuICAgICAgc2V0Q291cnNlVmlkZW9OYW1lKChmaWxlIGFzIEZpbGUpLm5hbWUpO1xuXG4gICAgICAvLyDlpoLmnpzmmK/op4bpopHmlofku7bvvIzlsJ3or5Xojrflj5bml7bplb9cbiAgICAgIGNvbnN0IHZpZGVvRWxlbWVudCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ3ZpZGVvJyk7XG4gICAgICB2aWRlb0VsZW1lbnQuc3JjID0gdXJsO1xuICAgICAgdmlkZW9FbGVtZW50Lm9ubG9hZGVkbWV0YWRhdGEgPSAoKSA9PiB7XG4gICAgICAgIHNldFZpZGVvRHVyYXRpb24oTWF0aC5mbG9vcih2aWRlb0VsZW1lbnQuZHVyYXRpb24pKTtcbiAgICAgIH07XG5cbiAgICAgIG9uU3VjY2Vzcz8uKHsgdXJsOiB1cmwgfSk7XG4gICAgICBtZXNzYWdlLnN1Y2Nlc3MoJ+ivvueoi+inhumikeS4iuS8oOaIkOWKnycpO1xuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+ivvueoi+inhumikeS4iuS8oOWksei0pTonLCBlcnJvcik7XG4gICAgICBtZXNzYWdlLmVycm9yKGDop4bpopHkuIrkvKDlpLHotKU6ICR7ZXJyb3IubWVzc2FnZSB8fCAn6K+356iN5ZCO6YeN6K+VJ31gKTtcbiAgICAgIG9uRXJyb3I/LihlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWkhOeQhuinhumikeWIoOmZpFxuICBjb25zdCBoYW5kbGVWaWRlb1JlbW92ZSA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRDb3Vyc2VWaWRlb1VybCgnJyk7XG4gICAgc2V0Q291cnNlVmlkZW9OYW1lKCcnKTtcbiAgICBzZXRWaWRlb0R1cmF0aW9uKDApO1xuICAgIHJldHVybiB0cnVlO1xuICB9O1xuXG4gIC8vIOWkhOeQhuaWh+aho+S4iuS8oFxuICBjb25zdCBoYW5kbGVEb2N1bWVudFVwbG9hZDogVXBsb2FkUHJvcHNbJ2N1c3RvbVJlcXVlc3QnXSA9IGFzeW5jIChvcHRpb25zKSA9PiB7XG4gICAgY29uc3QgeyBmaWxlLCBvblN1Y2Nlc3MsIG9uRXJyb3IgfSA9IG9wdGlvbnM7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgdXJsID0gYXdhaXQgdXBsb2FkQXBpLnVwbG9hZFRvT3NzKGZpbGUgYXMgRmlsZSk7XG4gICAgICBjb25zb2xlLmxvZygn6K++56iL5paH5qGj5LiK5Lyg5oiQ5Yqf77yMVVJMOicsIHVybCk7XG5cbiAgICAgIHNldENvdXJzZURvY3VtZW50VXJsKHVybCk7XG4gICAgICBzZXRDb3Vyc2VEb2N1bWVudE5hbWUoKGZpbGUgYXMgRmlsZSkubmFtZSk7XG4gICAgICBvblN1Y2Nlc3M/Lih7IHVybDogdXJsIH0pO1xuICAgICAgbWVzc2FnZS5zdWNjZXNzKCfor77nqIvmlofmoaPkuIrkvKDmiJDlip8nKTtcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfor77nqIvmlofmoaPkuIrkvKDlpLHotKU6JywgZXJyb3IpO1xuICAgICAgbWVzc2FnZS5lcnJvcihg5paH5qGj5LiK5Lyg5aSx6LSlOiAke2Vycm9yLm1lc3NhZ2UgfHwgJ+ivt+eojeWQjumHjeivlSd9YCk7XG4gICAgICBvbkVycm9yPy4oZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICAvLyDlpITnkIbmlofmoaPliKDpmaRcbiAgY29uc3QgaGFuZGxlRG9jdW1lbnRSZW1vdmUgPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0Q291cnNlRG9jdW1lbnRVcmwoJycpO1xuICAgIHNldENvdXJzZURvY3VtZW50TmFtZSgnJyk7XG4gICAgcmV0dXJuIHRydWU7XG4gIH07XG5cbiAgLy8g5aSE55CG6Z+z6aKR5LiK5LygXG4gIGNvbnN0IGhhbmRsZUF1ZGlvVXBsb2FkOiBVcGxvYWRQcm9wc1snY3VzdG9tUmVxdWVzdCddID0gYXN5bmMgKG9wdGlvbnMpID0+IHtcbiAgICBjb25zdCB7IGZpbGUsIG9uU3VjY2Vzcywgb25FcnJvciB9ID0gb3B0aW9ucztcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCB1cmwgPSBhd2FpdCB1cGxvYWRBcGkudXBsb2FkVG9Pc3MoZmlsZSBhcyBGaWxlKTtcbiAgICAgIGNvbnNvbGUubG9nKCfor77nqIvpn7PpopHkuIrkvKDmiJDlip/vvIxVUkw6JywgdXJsKTtcblxuICAgICAgc2V0Q291cnNlQXVkaW9VcmwodXJsKTtcbiAgICAgIHNldENvdXJzZUF1ZGlvTmFtZSgoZmlsZSBhcyBGaWxlKS5uYW1lKTtcbiAgICAgIG9uU3VjY2Vzcz8uKHsgdXJsOiB1cmwgfSk7XG4gICAgICBtZXNzYWdlLnN1Y2Nlc3MoJ+ivvueoi+mfs+mikeS4iuS8oOaIkOWKnycpO1xuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+ivvueoi+mfs+mikeS4iuS8oOWksei0pTonLCBlcnJvcik7XG4gICAgICBtZXNzYWdlLmVycm9yKGDpn7PpopHkuIrkvKDlpLHotKU6ICR7ZXJyb3IubWVzc2FnZSB8fCAn6K+356iN5ZCO6YeN6K+VJ31gKTtcbiAgICAgIG9uRXJyb3I/LihlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWkhOeQhumfs+mikeWIoOmZpFxuICBjb25zdCBoYW5kbGVBdWRpb1JlbW92ZSA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRDb3Vyc2VBdWRpb1VybCgnJyk7XG4gICAgc2V0Q291cnNlQXVkaW9OYW1lKCcnKTtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfTtcblxuXG5cblxuXG4gIC8vIOaJk+W8gOe8lui+keaooeaAgeahhlxuICBjb25zdCBvcGVuRWRpdE1vZGFsID0gYXN5bmMgKGNvdXJzZTogQ291cnNlKSA9PiB7XG4gICAgc2V0RWRpdGluZ0NvdXJzZShjb3Vyc2UpO1xuICAgIGVkaXRDb3Vyc2VGb3JtLnNldEZpZWxkc1ZhbHVlKGNvdXJzZSk7XG4gICAgc2V0SXNFZGl0Q291cnNlTW9kYWxWaXNpYmxlKHRydWUpO1xuICB9O1xuXG4gIC8vIOi/h+a7pOivvueoi+WIl+ihqFxuICBjb25zdCBmaWx0ZXJlZENvdXJzZXMgPSAoY291cnNlTGlzdCB8fCBbXSkuZmlsdGVyKGNvdXJzZSA9PlxuICAgIGNvdXJzZS5uYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoS2V5d29yZC50b0xvd2VyQ2FzZSgpKSB8fFxuICAgIGNvdXJzZS5kZXNjcmlwdGlvbi50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaEtleXdvcmQudG9Mb3dlckNhc2UoKSkgfHxcbiAgICBjb3Vyc2UuY2F0ZWdvcnkudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hLZXl3b3JkLnRvTG93ZXJDYXNlKCkpXG4gICk7XG5cbiAgLy8g5YeG5aSH6KGo5qC85pWw5o2u77ya5bCG57O75YiX6K++56iL5ZKM5a2Q6K++56iL5ZCI5bm25Li65LiA5Liq5omB5bmz5YiX6KGoXG4gIGNvbnN0IHByZXBhcmVUYWJsZURhdGEgPSAoKSA9PiB7XG4gICAgY29uc3QgdGFibGVEYXRhOiBhbnlbXSA9IFtdO1xuXG4gICAgY29uc29sZS5sb2coJ/CflIQg5YeG5aSH6KGo5qC85pWw5o2uLi4uJyk7XG4gICAgY29uc29sZS5sb2coJ/Cfk4og57O75YiX6K++56iL5YiX6KGoOicsIHNlcmllc0xpc3QpO1xuICAgIGNvbnNvbGUubG9nKCfwn5OKIOWxleW8gOeahOezu+WIlzonLCBBcnJheS5mcm9tKGV4cGFuZGVkU2VyaWVzKSk7XG4gICAgY29uc29sZS5sb2coJ/Cfk4og5a2Q6K++56iL5pig5bCEOicsIHNlcmllc0NvdXJzZXNNYXApO1xuXG4gICAgc2VyaWVzTGlzdC5mb3JFYWNoKHNlcmllcyA9PiB7XG4gICAgICAvLyDmt7vliqDns7vliJfor77nqIvooYxcbiAgICAgIHRhYmxlRGF0YS5wdXNoKHtcbiAgICAgICAga2V5OiBgc2VyaWVzLSR7c2VyaWVzLmlkfWAsXG4gICAgICAgIGlkOiBzZXJpZXMuaWQsXG4gICAgICAgIHRpdGxlOiBzZXJpZXMudGl0bGUsXG4gICAgICAgIHN0YXR1czogc2VyaWVzLnN0YXR1cyxcbiAgICAgICAgdHlwZTogJ3NlcmllcycsXG4gICAgICAgIGlzRXhwYW5kZWQ6IGV4cGFuZGVkU2VyaWVzLmhhcyhzZXJpZXMuaWQpLFxuICAgICAgICBzZXJpZXNJZDogc2VyaWVzLmlkXG4gICAgICB9KTtcblxuICAgICAgLy8g5aaC5p6c57O75YiX5bey5bGV5byA77yM5re75Yqg5a2Q6K++56iL6KGMXG4gICAgICBpZiAoZXhwYW5kZWRTZXJpZXMuaGFzKHNlcmllcy5pZCkpIHtcbiAgICAgICAgY29uc3Qgc3ViQ291cnNlcyA9IHNlcmllc0NvdXJzZXNNYXAuZ2V0KHNlcmllcy5pZCkgfHwgW107XG4gICAgICAgIGNvbnNvbGUubG9nKGDwn5OaIOezu+WIlyAke3Nlcmllcy5pZH0g55qE5a2Q6K++56iLOmAsIHN1YkNvdXJzZXMpO1xuXG4gICAgICAgIHN1YkNvdXJzZXMuZm9yRWFjaChjb3Vyc2UgPT4ge1xuICAgICAgICAgIHRhYmxlRGF0YS5wdXNoKHtcbiAgICAgICAgICAgIGtleTogYGNvdXJzZS0ke2NvdXJzZS5pZH1gLFxuICAgICAgICAgICAgaWQ6IGNvdXJzZS5pZCxcbiAgICAgICAgICAgIHRpdGxlOiBjb3Vyc2UudGl0bGUsXG4gICAgICAgICAgICBzdGF0dXM6IGNvdXJzZS5zdGF0dXMsXG4gICAgICAgICAgICB0eXBlOiAnY291cnNlJyxcbiAgICAgICAgICAgIHNlcmllc0lkOiBzZXJpZXMuaWQsXG4gICAgICAgICAgICBwYXJlbnRTZXJpZXNUaXRsZTogc2VyaWVzLnRpdGxlXG4gICAgICAgICAgfSk7XG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH0pO1xuXG4gICAgY29uc29sZS5sb2coJ/Cfk4sg5pyA57uI6KGo5qC85pWw5o2uOicsIHRhYmxlRGF0YSk7XG4gICAgcmV0dXJuIHRhYmxlRGF0YTtcbiAgfTtcblxuICAvLyDooajmoLzliJflrprkuYlcbiAgY29uc3QgY29sdW1ucyA9IFtcbiAgICB7XG4gICAgICB0aXRsZTogJ+ezu+WIl+ivvueoi0lEJyxcbiAgICAgIGRhdGFJbmRleDogJ2lkJyxcbiAgICAgIGtleTogJ2lkJyxcbiAgICAgIHdpZHRoOiAxMjAsXG4gICAgfSxcbiAgICB7XG4gICAgICB0aXRsZTogJ+ezu+WIl+ivvueoiy/lrZDor77nqIvlkI3np7AnLFxuICAgICAgZGF0YUluZGV4OiAndGl0bGUnLFxuICAgICAga2V5OiAndGl0bGUnLFxuICAgICAgcmVuZGVyOiAodGV4dDogc3RyaW5nLCByZWNvcmQ6IGFueSkgPT4ge1xuICAgICAgICBpZiAocmVjb3JkLnR5cGUgPT09ICdzZXJpZXMnKSB7XG4gICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICBzaXplPVwic21hbGxcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHRvZ2dsZVNlcmllc0V4cGFuc2lvbihyZWNvcmQuaWQpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMCBtaW4tdy0wIGhvdmVyOmJnLWJsdWUtNTBcIlxuICAgICAgICAgICAgICAgIHN0eWxlPXt7IG1pbldpZHRoOiAnMjBweCcsIGhlaWdodDogJzIwcHgnIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7cmVjb3JkLmlzRXhwYW5kZWQgPyAn4pa8JyA6ICfilrYnfVxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ibHVlLTYwMCB0ZXh0LWJhc2VcIj57dGV4dH08L3NwYW4+XG4gICAgICAgICAgICAgIDxUYWcgY29sb3I9XCJibHVlXCIgY2xhc3NOYW1lPVwidGV4dC14c1wiPuezu+WIlzwvVGFnPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC04IGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj7ilJTilIA8L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDBcIj57dGV4dH08L3NwYW4+XG4gICAgICAgICAgICAgIDxUYWcgY29sb3I9XCJncmVlblwiIGNsYXNzTmFtZT1cInRleHQteHNcIj7lrZDor77nqIs8L1RhZz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICk7XG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgfSxcbiAgICB7XG4gICAgICB0aXRsZTogJ+WPkeW4g+eKtuaAgScsXG4gICAgICBkYXRhSW5kZXg6ICdzdGF0dXMnLFxuICAgICAga2V5OiAnc3RhdHVzJyxcbiAgICAgIHdpZHRoOiAxMDAsXG4gICAgICByZW5kZXI6IChzdGF0dXM6IG51bWJlciwgcmVjb3JkOiBhbnkpID0+IHtcbiAgICAgICAgY29uc3QgZ2V0U3RhdHVzQ29uZmlnID0gKHN0YXR1czogbnVtYmVyKSA9PiB7XG4gICAgICAgICAgc3dpdGNoIChzdGF0dXMpIHtcbiAgICAgICAgICAgIGNhc2UgMTogcmV0dXJuIHsgY29sb3I6ICdncmVlbicsIHRleHQ6ICflt7Llj5HluIMnIH07XG4gICAgICAgICAgICBjYXNlIDA6IHJldHVybiB7IGNvbG9yOiAnb3JhbmdlJywgdGV4dDogJ+iNieeovycgfTtcbiAgICAgICAgICAgIGNhc2UgMjogcmV0dXJuIHsgY29sb3I6ICdyZWQnLCB0ZXh0OiAn5bey5b2S5qGjJyB9O1xuICAgICAgICAgICAgZGVmYXVsdDogcmV0dXJuIHsgY29sb3I6ICdncmF5JywgdGV4dDogJ+acquefpScgfTtcbiAgICAgICAgICB9XG4gICAgICAgIH07XG5cbiAgICAgICAgY29uc3QgY29uZmlnID0gZ2V0U3RhdHVzQ29uZmlnKHN0YXR1cyk7XG4gICAgICAgIHJldHVybiA8VGFnIGNvbG9yPXtjb25maWcuY29sb3J9Pntjb25maWcudGV4dH08L1RhZz47XG4gICAgICB9LFxuICAgIH0sXG4gICAge1xuICAgICAgdGl0bGU6ICfmk43kvZwnLFxuICAgICAga2V5OiAnYWN0aW9uJyxcbiAgICAgIHdpZHRoOiAxNTAsXG4gICAgICByZW5kZXI6IChyZWNvcmQ6IGFueSkgPT4ge1xuICAgICAgICBpZiAocmVjb3JkLnR5cGUgPT09ICdzZXJpZXMnKSB7XG4gICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgIDxTcGFjZSBzaXplPVwic21hbGxcIj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHR5cGU9XCJsaW5rXCJcbiAgICAgICAgICAgICAgICBzaXplPVwic21hbGxcIlxuICAgICAgICAgICAgICAgIGljb249ezxFZGl0T3V0bGluZWQgLz59XG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgbWVzc2FnZS5pbmZvKCfns7vliJfor77nqIvnvJbovpHlip/og73lvoXlrp7njrAnKTtcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg57yW6L6RXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9TcGFjZT5cbiAgICAgICAgICApO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICA8U3BhY2Ugc2l6ZT1cInNtYWxsXCI+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICB0eXBlPVwibGlua1wiXG4gICAgICAgICAgICAgICAgc2l6ZT1cInNtYWxsXCJcbiAgICAgICAgICAgICAgICBpY29uPXs8RWRpdE91dGxpbmVkIC8+fVxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgIG1lc3NhZ2UuaW5mbygn5a2Q6K++56iL57yW6L6R5Yqf6IO95b6F5a6e546wJyk7XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwIGhvdmVyOnRleHQtYmx1ZS04MDBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg57yW6L6RXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8UG9wY29uZmlybVxuICAgICAgICAgICAgICAgIHRpdGxlPXtcbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxwPuehruWumuimgeWIoOmZpOi/meS4quWtkOivvueoi+WQl++8nzwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCB0ZXh0LXNtXCI+6K++56iL5ZCN56ew77yae3JlY29yZC50aXRsZX08L3A+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgdGV4dC1zbVwiPuaJgOWxnuezu+WIl++8mntyZWNvcmQucGFyZW50U2VyaWVzVGl0bGV9PC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIG9uQ29uZmlybT17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfl5HvuI8g55So5oi356Gu6K6k5Yig6Zmk5a2Q6K++56iLOicsIHJlY29yZCk7XG4gICAgICAgICAgICAgICAgICBoYW5kbGVEZWxldGVTdWJDb3Vyc2UocmVjb3JkLmlkLCByZWNvcmQuc2VyaWVzSWQpO1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgb2tUZXh0PVwi56Gu5a6a5Yig6ZmkXCJcbiAgICAgICAgICAgICAgICBjYW5jZWxUZXh0PVwi5Y+W5raIXCJcbiAgICAgICAgICAgICAgICBva1R5cGU9XCJkYW5nZXJcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgdHlwZT1cImxpbmtcIlxuICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtYWxsXCJcbiAgICAgICAgICAgICAgICAgIGRhbmdlclxuICAgICAgICAgICAgICAgICAgaWNvbj17PERlbGV0ZU91dGxpbmVkIC8+fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwIGhvdmVyOnRleHQtcmVkLTgwMFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAg5Yig6ZmkXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDwvUG9wY29uZmlybT5cbiAgICAgICAgICAgIDwvU3BhY2U+XG4gICAgICAgICAgKTtcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICB9LFxuICBdO1xuXG4gIC8vIOiOt+WPluaVmeW4iOWIl+ihqFxuICAvLyBjb25zdCBmZXRjaFRlYWNoZXJzID0gYXN5bmMgKCkgPT4ge1xuICAvLyAgIHRyeSB7XG4gIC8vICAgICBjb25zdCB7IGRhdGE6IHJlcyB9ID0gYXdhaXQgY291cnNlQXBpLmdldFRlYWNoZXJzKCk7XG4gIC8vICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkge1xuICAvLyAgICAgICBzZXRUZWFjaGVycyhyZXMuZGF0YSk7XG4gIC8vICAgICAgIGNvbnNvbGUubG9nKCfmiJDlip/ojrflj5bmlZnluIjliJfooag6JywgcmVzLmRhdGEpO1xuICAvLyAgICAgfSBlbHNlIHtcbiAgLy8gICAgICAgY29uc29sZS5sb2coJ0FQSei/lOWbnuaXoOaVsOaNru+8jOS9v+eUqOaooeaLn+aVmeW4iOaVsOaNricpO1xuICAvLyAgICAgICAvLyDkvb/nlKjmqKHmi5/mlbDmja5cbiAgLy8gICAgICAgY29uc3QgbW9ja1RlYWNoZXJzID0gW1xuICAvLyAgICAgICAgIHsgaWQ6IDEsIG5hbWU6ICflvKDogIHluIgnLCBlbWFpbDogJ3poYW5nQHNjaG9vbC5jb20nLCBzdWJqZWN0OiAn5pWw5a2mJywgc2Nob29sOiAn5a6e6aqM5bCP5a2mJywgYXZhdGFyOiAnJywgcGhvbmU6ICcxMzgwMDEzODAwMScgfSxcbiAgLy8gICAgICAgICB7IGlkOiAyLCBuYW1lOiAn5p2O6ICB5biIJywgZW1haWw6ICdsaUBzY2hvb2wuY29tJywgc3ViamVjdDogJ+ivreaWhycsIHNjaG9vbDogJ+WunumqjOWwj+WtpicsIGF2YXRhcjogJycsIHBob25lOiAnMTM4MDAxMzgwMDInIH0sXG4gIC8vICAgICAgICAgeyBpZDogMywgbmFtZTogJ+eOi+iAgeW4iCcsIGVtYWlsOiAnd2FuZ0BzY2hvb2wuY29tJywgc3ViamVjdDogJ+iLseivrScsIHNjaG9vbDogJ+esrOS6jOWwj+WtpicsIGF2YXRhcjogJycsIHBob25lOiAnMTM4MDAxMzgwMDMnIH0sXG4gIC8vICAgICAgICAgeyBpZDogNCwgbmFtZTogJ+i1teiAgeW4iCcsIGVtYWlsOiAnemhhb0BzY2hvb2wuY29tJywgc3ViamVjdDogJ+enkeWtpicsIHNjaG9vbDogJ+esrOS6jOWwj+WtpicsIGF2YXRhcjogJycsIHBob25lOiAnMTM4MDAxMzgwMDQnIH0sXG4gIC8vICAgICAgICAgeyBpZDogNSwgbmFtZTogJ+WImOiAgeW4iCcsIGVtYWlsOiAnbGl1QHNjaG9vbC5jb20nLCBzdWJqZWN0OiAn57yW56iLJywgc2Nob29sOiAn5a6e6aqM5Lit5a2mJywgYXZhdGFyOiAnJywgcGhvbmU6ICcxMzgwMDEzODAwNScgfSxcbiAgLy8gICAgICAgICB7IGlkOiA2LCBuYW1lOiAn6ZmI6ICB5biIJywgZW1haWw6ICdjaGVuQHNjaG9vbC5jb20nLCBzdWJqZWN0OiAn5L+h5oGv5oqA5pyvJywgc2Nob29sOiAn5a6e6aqM5Lit5a2mJywgYXZhdGFyOiAnJywgcGhvbmU6ICcxMzgwMDEzODAwNicgfVxuICAvLyAgICAgICBdO1xuICAvLyAgICAgICBzZXRUZWFjaGVycyhtb2NrVGVhY2hlcnMpO1xuICAvLyAgICAgfVxuICAvLyAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gIC8vICAgICBjb25zb2xlLmVycm9yKCfojrflj5bmlZnluIjliJfooajlpLHotKU6JywgZXJyb3IpO1xuICAvLyAgICAgLy8g5L2/55So5qih5ouf5pWw5o2uXG4gIC8vICAgICBjb25zdCBtb2NrVGVhY2hlcnMgPSBbXG4gIC8vICAgICAgIHsgaWQ6IDEsIG5hbWU6ICflvKDogIHluIgnLCBlbWFpbDogJ3poYW5nQHNjaG9vbC5jb20nLCBzdWJqZWN0OiAn5pWw5a2mJywgc2Nob29sOiAn5a6e6aqM5bCP5a2mJywgYXZhdGFyOiAnJywgcGhvbmU6ICcxMzgwMDEzODAwMScgfSxcbiAgLy8gICAgICAgeyBpZDogMiwgbmFtZTogJ+adjuiAgeW4iCcsIGVtYWlsOiAnbGlAc2Nob29sLmNvbScsIHN1YmplY3Q6ICfor63mlocnLCBzY2hvb2w6ICflrp7pqozlsI/lraYnLCBhdmF0YXI6ICcnLCBwaG9uZTogJzEzODAwMTM4MDAyJyB9LFxuICAvLyAgICAgICB7IGlkOiAzLCBuYW1lOiAn546L6ICB5biIJywgZW1haWw6ICd3YW5nQHNjaG9vbC5jb20nLCBzdWJqZWN0OiAn6Iux6K+tJywgc2Nob29sOiAn56ys5LqM5bCP5a2mJywgYXZhdGFyOiAnJywgcGhvbmU6ICcxMzgwMDEzODAwMycgfSxcbiAgLy8gICAgICAgeyBpZDogNCwgbmFtZTogJ+i1teiAgeW4iCcsIGVtYWlsOiAnemhhb0BzY2hvb2wuY29tJywgc3ViamVjdDogJ+enkeWtpicsIHNjaG9vbDogJ+esrOS6jOWwj+WtpicsIGF2YXRhcjogJycsIHBob25lOiAnMTM4MDAxMzgwMDQnIH0sXG4gIC8vICAgICAgIHsgaWQ6IDUsIG5hbWU6ICfliJjogIHluIgnLCBlbWFpbDogJ2xpdUBzY2hvb2wuY29tJywgc3ViamVjdDogJ+e8lueoiycsIHNjaG9vbDogJ+WunumqjOS4reWtpicsIGF2YXRhcjogJycsIHBob25lOiAnMTM4MDAxMzgwMDUnIH0sXG4gIC8vICAgICAgIHsgaWQ6IDYsIG5hbWU6ICfpmYjogIHluIgnLCBlbWFpbDogJ2NoZW5Ac2Nob29sLmNvbScsIHN1YmplY3Q6ICfkv6Hmga/mioDmnK8nLCBzY2hvb2w6ICflrp7pqozkuK3lraYnLCBhdmF0YXI6ICcnLCBwaG9uZTogJzEzODAwMTM4MDA2JyB9XG4gIC8vICAgICBdO1xuICAvLyAgICAgc2V0VGVhY2hlcnMobW9ja1RlYWNoZXJzKTtcbiAgLy8gICAgIGNvbnNvbGUubG9nKCfkvb/nlKjmqKHmi5/mlZnluIjmlbDmja46JywgbW9ja1RlYWNoZXJzKTtcbiAgLy8gICB9XG4gIC8vIH07XG5cbiAgLy8g6I635Y+W6K++56iL5qCH562+5YiX6KGoIC0g5L2/55So6K++56iL5biC5Zy6QVBJXG4gIGNvbnN0IGZldGNoQ291cnNlVGFncyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ/Cfj7fvuI8g5byA5aeL6I635Y+W6K++56iL5qCH562+5YiX6KGoLi4uJyk7XG4gICAgICBjb25zdCB7IGRhdGE6IHJlcyB9ID0gYXdhaXQgY291cnNlQXBpLmdldENvdXJzZVRhZ3Moe1xuICAgICAgICBwYWdlOiAxLFxuICAgICAgICBwYWdlU2l6ZTogMTAwLCAvLyDojrflj5bmm7TlpJrmoIfnrb7nlKjkuo7pgInmi6lcbiAgICAgICAgc3RhdHVzOiAxICAgICAgLy8g5Y+q6I635Y+W5ZCv55So55qE5qCH562+XG4gICAgICB9KTtcblxuICAgICAgY29uc29sZS5sb2coJ/Cfk6ggZ2V0Q291cnNlVGFncyBBUEnlk43lupQ6JywgcmVzKTtcblxuICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDAgJiYgcmVzLmRhdGEgJiYgcmVzLmRhdGEubGlzdCkge1xuICAgICAgICBjb25zdCB0YWdzID0gcmVzLmRhdGEubGlzdC5tYXAoKHRhZzogYW55KSA9PiAoe1xuICAgICAgICAgIGlkOiB0YWcuaWQsXG4gICAgICAgICAgbmFtZTogdGFnLm5hbWUsXG4gICAgICAgICAgY29sb3I6IHRhZy5jb2xvcixcbiAgICAgICAgICBjYXRlZ29yeTogdGFnLmNhdGVnb3J5LFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiB0YWcuZGVzY3JpcHRpb24gfHwgJydcbiAgICAgICAgfSkpO1xuXG4gICAgICAgIHNldENvdXJzZVRhZ3ModGFncyk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg5oiQ5Yqf6I635Y+W6K++56iL5qCH562+5YiX6KGoOicsIHRhZ3MpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS53YXJuKCfimqDvuI8gQVBJ6L+U5Zue5pWw5o2u5qC85byP5byC5bi4OicsIHJlcyk7XG4gICAgICAgIHNldENvdXJzZVRhZ3MoW10pO1xuICAgICAgICBub3RpZmljYXRpb24ud2FybmluZygn6I635Y+W5qCH562+5YiX6KGo5aSx6LSl77yM6K+35qOA5p+l572R57uc6L+e5o6lJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDojrflj5bor77nqIvmoIfnrb7lpLHotKU6JywgZXJyb3IpO1xuICAgICAgc2V0Q291cnNlVGFncyhbXSk7XG4gICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+iOt+WPluagh+etvuWIl+ihqOWksei0pe+8jOivt+mHjeivlScpO1xuICAgIH1cbiAgfTtcblxuICAvLyDojrflj5bor77nqIvns7vliJfliJfooaggLSDkvb/nlKjor77nqIvluILlnLpBUElcbiAgY29uc3QgZmV0Y2hDb3Vyc2VTZXJpZXMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SEIOW8gOWni+iOt+WPluivvueoi+W4guWcuuezu+WIl+ivvueoi+WIl+ihqC4uLicpO1xuICAgICAgY29uc3QgeyBkYXRhOiByZXMgfSA9IGF3YWl0IGNvdXJzZUFwaS5nZXRNYXJrZXRwbGFjZVNlcmllcyh7XG4gICAgICAgIHBhZ2U6IDEsXG4gICAgICAgIHBhZ2VTaXplOiA1MCAvLyDor77nqIvluILlnLpBUEnpmZDliLbmnIDlpKc1MFxuICAgICAgfSk7XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5OoIGdldE1hcmtldHBsYWNlU2VyaWVzIEFQSeWTjeW6lDonLCByZXMpO1xuXG4gICAgICAvLyDmo4Dmn6XmmK/lkKbmnInmm7TlpJrmlbDmja5cbiAgICAgIGlmIChyZXMuZGF0YT8ucGFnaW5hdGlvbj8udG90YWwgPiA1MCkge1xuICAgICAgICBjb25zb2xlLmxvZyhg4pqg77iPIOazqOaEj++8muaAu+WFseaciSAke3Jlcy5kYXRhLnBhZ2luYXRpb24udG90YWx9IOS4quezu+WIl+ivvueoi++8jOW9k+WJjeWPquaYvuekuuWJjTUw5LiqYCk7XG4gICAgICB9XG5cbiAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwICYmIHJlcy5kYXRhKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5OKIEFQSei/lOWbnueahOWujOaVtOaVsOaNrue7k+aehDonLCByZXMuZGF0YSk7XG5cbiAgICAgICAgaWYgKHJlcy5kYXRhLmxpc3QgJiYgQXJyYXkuaXNBcnJheShyZXMuZGF0YS5saXN0KSkge1xuICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5OLIOiOt+WPluWIsCAke3Jlcy5kYXRhLmxpc3QubGVuZ3RofSDkuKrns7vliJfor77nqItgKTtcblxuICAgICAgICAgIC8vIOWwhuivvueoi+W4guWcukFQSei/lOWbnueahOaVsOaNrui9rOaNouS4uue7hOS7tumcgOimgeeahOagvOW8j1xuICAgICAgICAgIGNvbnN0IGZvcm1hdHRlZFNlcmllcyA9IHJlcy5kYXRhLmxpc3QubWFwKChpdGVtOiBhbnksIGluZGV4OiBudW1iZXIpID0+IHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5SNIOWkhOeQhuesrCAke2luZGV4ICsgMX0g5Liq57O75YiXOmAsIHtcbiAgICAgICAgICAgICAgaWQ6IGl0ZW0uaWQsXG4gICAgICAgICAgICAgIHRpdGxlOiBpdGVtLnRpdGxlLFxuICAgICAgICAgICAgICBjYXRlZ29yeTogaXRlbS5jYXRlZ29yeSxcbiAgICAgICAgICAgICAgY2F0ZWdvcnlMYWJlbDogaXRlbS5jYXRlZ29yeUxhYmVsLFxuICAgICAgICAgICAgICB0YWdzOiBpdGVtLnRhZ3NcbiAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICBpZDogaXRlbS5pZCxcbiAgICAgICAgICAgICAgdGl0bGU6IGl0ZW0udGl0bGUsXG4gICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBpdGVtLmRlc2NyaXB0aW9uLFxuICAgICAgICAgICAgICBjb3ZlckltYWdlOiBpdGVtLmNvdmVySW1hZ2UgfHwgJycsXG4gICAgICAgICAgICAgIGNhdGVnb3J5OiBpdGVtLmNhdGVnb3J5TGFiZWwgfHwgKGl0ZW0uY2F0ZWdvcnkgPT09IDAgPyAn5a6Y5pa5JyA6ICfnpL7ljLonKSwgLy8g5L2/55SoY2F0ZWdvcnlMYWJlbOaIlui9rOaNomNhdGVnb3J5XG4gICAgICAgICAgICAgIHRlYWNoZXJJZHM6IFtdLCAvLyDor77nqIvluILlnLpBUEnkuI3ov5Tlm550ZWFjaGVySWRzXG4gICAgICAgICAgICAgIHRhZ0lkczogaXRlbS50YWdzPy5tYXAoKHRhZzogYW55KSA9PiB0YWcuaWQpIHx8IFtdLCAvLyDku450YWdz5pWw57uE5Lit5o+Q5Y+WSURcbiAgICAgICAgICAgICAgY3JlYXRlZEF0OiBpdGVtLmNyZWF0ZWRBdCB8fCBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICAgIHVwZGF0ZWRBdDogaXRlbS51cGRhdGVkQXQgfHwgbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgICB9O1xuICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgc2V0Q291cnNlU2VyaWVzKGZvcm1hdHRlZFNlcmllcyk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ+KchSDmiJDlip/ojrflj5bns7vliJfor77nqIvliJfooag6JywgZm9ybWF0dGVkU2VyaWVzKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBBUEnov5Tlm57mlbDmja7kuK3msqHmnIlsaXN05a2X5q615oiWbGlzdOS4jeaYr+aVsOe7hDonLCByZXMuZGF0YSk7XG4gICAgICAgICAgc2V0Q291cnNlU2VyaWVzKFtdKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS53YXJuKCfimqDvuI8gQVBJ6L+U5Zue5pWw5o2u5qC85byP5byC5bi4OicsIHtcbiAgICAgICAgICBjb2RlOiByZXMuY29kZSxcbiAgICAgICAgICBtZXNzYWdlOiByZXMubWVzc2FnZSxcbiAgICAgICAgICBkYXRhOiByZXMuZGF0YVxuICAgICAgICB9KTtcbiAgICAgICAgc2V0Q291cnNlU2VyaWVzKFtdKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOiOt+WPluivvueoi+ezu+WIl+Wksei0pTonLCBlcnJvcik7XG4gICAgICBzZXRDb3Vyc2VTZXJpZXMoW10pO1xuICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfojrflj5bns7vliJfor77nqIvliJfooajlpLHotKXvvIzor7fph43or5UnKTtcbiAgICB9XG4gIH07XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmZXRjaENvdXJzZUxpc3QoKTtcblxuICAgIGZldGNoQ291cnNlVGFncygpO1xuICAgIGZldGNoQ291cnNlU2VyaWVzKCk7XG4gIH0sIFtdKTtcblxuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICA8Q2FyZFxuICAgICAgICB0aXRsZT1cIuivvueoi+euoeeQhlwiXG4gICAgICAgIGV4dHJhPXs8QnV0dG9uIHR5cGU9XCJwcmltYXJ5XCIgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgIGZldGNoQ291cnNlTGlzdCgpO1xuICAgICAgICAgIHNldElzQ291cnNlTW9kYWxWaXNpYmxlKHRydWUpO1xuICAgICAgICB9fT7mn6XnnIvlhajpg6g8L0J1dHRvbj59XG4gICAgICAgIGNsYXNzTmFtZT1cInNoYWRvdy1zbVwiXG4gICAgICA+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgPEJ1dHRvbiBibG9jayBvbkNsaWNrPXsoKSA9PiBzZXRJc0FkZENvdXJzZU1vZGFsVmlzaWJsZSh0cnVlKX0+XG4gICAgICAgICAgICDmt7vliqDor77nqItcbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8QnV0dG9uIGJsb2NrIG9uQ2xpY2s9eygpID0+IHNldElzQWRkU2VyaWVzTW9kYWxWaXNpYmxlKHRydWUpfT5cbiAgICAgICAgICAgIOa3u+WKoOezu+WIl+ivvueoi1xuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDxCdXR0b24gYmxvY2sgb25DbGljaz17KCkgPT4gc2V0SXNBZGRUYWdNb2RhbFZpc2libGUodHJ1ZSl9IHR5cGU9XCJkYXNoZWRcIj5cbiAgICAgICAgICAgIOa3u+WKoOivvueoi+agh+etvlxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDxCdXR0b24gYmxvY2sgb25DbGljaz17b3BlblB1Ymxpc2hDb3Vyc2VNb2RhbH0gc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiAnd2hpdGUnLCBib3JkZXJDb2xvcjogJyNkOWQ5ZDknLCBjb2xvcjogJyMwMDAwMDBkOScgfX0+XG4gICAgICAgICAgICDlj5HluIPor77nqItcbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8QnV0dG9uIGJsb2NrIG9uQ2xpY2s9eygpID0+IHNldElzUHVibGlzaFNlcmllc01vZGFsVmlzaWJsZSh0cnVlKX0gc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiAnd2hpdGUnLCBib3JkZXJDb2xvcjogJyNkOWQ5ZDknLCBjb2xvcjogJyMwMDAwMDBkOScgfX0+XG4gICAgICAgICAgICDlj5HluIPns7vliJfor77nqItcbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L0NhcmQ+XG5cbiAgICAgIHsvKiDor77nqIvnrqHnkIbkuLvmqKHmgIHmoYYgKi99XG4gICAgICA8TW9kYWxcbiAgICAgICAgdGl0bGU9XCLor77nqIvnrqHnkIZcIlxuICAgICAgICBvcGVuPXtpc0NvdXJzZU1vZGFsVmlzaWJsZX1cbiAgICAgICAgb25DYW5jZWw9eygpID0+IHNldElzQ291cnNlTW9kYWxWaXNpYmxlKGZhbHNlKX1cbiAgICAgICAgZm9vdGVyPXtudWxsfVxuICAgICAgICB3aWR0aD17MTAwMH1cbiAgICAgID5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgbWItM1wiPlxuICAgICAgICAgICAgPFNlYXJjaFxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuaQnOe0ouezu+WIl+ivvueoi+WQjeensFwiXG4gICAgICAgICAgICAgIGFsbG93Q2xlYXJcbiAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IDMwMCB9fVxuICAgICAgICAgICAgICBvblNlYXJjaD17c2V0U2VhcmNoS2V5d29yZH1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWFyY2hLZXl3b3JkKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTJcIj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHR5cGU9XCJwcmltYXJ5XCJcbiAgICAgICAgICAgICAgICBpY29uPXs8UGx1c091dGxpbmVkIC8+fVxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzQWRkQ291cnNlTW9kYWxWaXNpYmxlKHRydWUpfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg5re75Yqg6K++56iLXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdHlwZT1cImRlZmF1bHRcIlxuICAgICAgICAgICAgICAgIGljb249ezxQbHVzT3V0bGluZWQgLz59XG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNBZGRTZXJpZXNNb2RhbFZpc2libGUodHJ1ZSl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDmt7vliqDns7vliJfor77nqItcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiDnu5/orqHkv6Hmga/lkozlv6vmjbfmk43kvZwgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgYmctZ3JheS01MCBwLTMgcm91bmRlZFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC00IHRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICA8c3Bhbj7ns7vliJfor77nqIvmgLvmlbA6IDxzdHJvbmcgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMFwiPntzZXJpZXNMaXN0Lmxlbmd0aH08L3N0cm9uZz48L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuPuW3suWxleW8gOezu+WIlzogPHN0cm9uZyBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTYwMFwiPntleHBhbmRlZFNlcmllcy5zaXplfTwvc3Ryb25nPjwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4+5bey5Yqg6L295a2Q6K++56iLOiA8c3Ryb25nIGNsYXNzTmFtZT1cInRleHQtb3JhbmdlLTYwMFwiPlxuICAgICAgICAgICAgICAgIHtBcnJheS5mcm9tKHNlcmllc0NvdXJzZXNNYXAudmFsdWVzKCkpLnJlZHVjZSgodG90YWwsIGNvdXJzZXMpID0+IHRvdGFsICsgY291cnNlcy5sZW5ndGgsIDApfVxuICAgICAgICAgICAgICA8L3N0cm9uZz48L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yXCI+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICBzaXplPVwic21hbGxcIlxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtleHBhbmRBbGxTZXJpZXN9XG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3Nlcmllc0xvYWRpbmd9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMCBob3Zlcjp0ZXh0LWJsdWUtODAwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIOWxleW8gOaJgOaciVxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHNpemU9XCJzbWFsbFwiXG4gICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2NvbGxhcHNlQWxsU2VyaWVzfVxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtzZXJpZXNMb2FkaW5nfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC1ncmF5LTgwMFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDmlLbotbfmiYDmnIlcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPFRhYmxlXG4gICAgICAgICAgY29sdW1ucz17Y29sdW1uc31cbiAgICAgICAgICBkYXRhU291cmNlPXtwcmVwYXJlVGFibGVEYXRhKCl9XG4gICAgICAgICAgcm93S2V5PVwia2V5XCJcbiAgICAgICAgICBsb2FkaW5nPXtzZXJpZXNMb2FkaW5nfVxuICAgICAgICAgIHBhZ2luYXRpb249e3tcbiAgICAgICAgICAgIHBhZ2VTaXplOiAyMCxcbiAgICAgICAgICAgIHNob3dTaXplQ2hhbmdlcjogZmFsc2UsXG4gICAgICAgICAgICBzaG93VG90YWw6ICh0b3RhbCkgPT4gYOWFsSAke3RvdGFsfSDmnaHorrDlvZVgLFxuICAgICAgICAgIH19XG4gICAgICAgICAgc2l6ZT1cInNtYWxsXCJcbiAgICAgICAgLz5cbiAgICAgIDwvTW9kYWw+XG5cbiAgICAgIHsvKiDmt7vliqDor77nqIvmqKHmgIHmoYYgKi99XG4gICAgICA8TW9kYWxcbiAgICAgICAgdGl0bGU9XCLmt7vliqDor77nqItcIlxuICAgICAgICBvcGVuPXtpc0FkZENvdXJzZU1vZGFsVmlzaWJsZX1cbiAgICAgICAgb25DYW5jZWw9eygpID0+IHtcbiAgICAgICAgICBzZXRJc0FkZENvdXJzZU1vZGFsVmlzaWJsZShmYWxzZSk7XG4gICAgICAgICAgYWRkQ291cnNlRm9ybS5yZXNldEZpZWxkcygpO1xuICAgICAgICAgIHNldENvdXJzZUNvdmVySW1hZ2VVcmwoJycpO1xuICAgICAgICAgIHNldEFkZGl0aW9uYWxGaWxlcyhbXSk7XG4gICAgICAgICAgc2V0Q291cnNlVmlkZW9VcmwoJycpO1xuICAgICAgICAgIHNldENvdXJzZVZpZGVvTmFtZSgnJyk7XG4gICAgICAgICAgc2V0Q291cnNlRG9jdW1lbnRVcmwoJycpO1xuICAgICAgICAgIHNldENvdXJzZURvY3VtZW50TmFtZSgnJyk7XG4gICAgICAgICAgc2V0Q291cnNlQXVkaW9VcmwoJycpO1xuICAgICAgICAgIHNldENvdXJzZUF1ZGlvTmFtZSgnJyk7XG4gICAgICAgICAgc2V0VmlkZW9EdXJhdGlvbigwKTtcbiAgICAgICAgfX1cbiAgICAgICAgb25Paz17KCkgPT4gYWRkQ291cnNlRm9ybS5zdWJtaXQoKX1cbiAgICAgICAgb2tUZXh0PVwi56Gu5a6aXCJcbiAgICAgICAgY2FuY2VsVGV4dD1cIuWPlua2iFwiXG4gICAgICA+XG4gICAgICAgIDxGb3JtXG4gICAgICAgICAgZm9ybT17YWRkQ291cnNlRm9ybX1cbiAgICAgICAgICBsYXlvdXQ9XCJ2ZXJ0aWNhbFwiXG4gICAgICAgICAgb25GaW5pc2g9e2hhbmRsZUFkZENvdXJzZX1cbiAgICAgICAgPlxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJzZXJpZXNJZFwiXG4gICAgICAgICAgICBsYWJlbD1cIuaJgOWxnuezu+WIl+ivvueoi1wiXG4gICAgICAgICAgICBydWxlcz17W3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nmiYDlsZ7ns7vliJfor77nqIsnIH1dfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTZWxlY3RcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fpgInmi6nns7vliJfor77nqItcIlxuICAgICAgICAgICAgICBzaG93U2VhcmNoXG4gICAgICAgICAgICAgIG9wdGlvbkZpbHRlclByb3A9XCJjaGlsZHJlblwiXG4gICAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiAnMTAwJScgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2NvdXJzZVNlcmllcy5tYXAoc2VyaWVzID0+IChcbiAgICAgICAgICAgICAgICA8T3B0aW9uIGtleT17c2VyaWVzLmlkfSB2YWx1ZT17c2VyaWVzLmlkfSB0aXRsZT17YCR7c2VyaWVzLnRpdGxlfSAtICR7c2VyaWVzLmRlc2NyaXB0aW9ufWB9PlxuICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICBvdmVyZmxvdzogJ2hpZGRlbicsXG4gICAgICAgICAgICAgICAgICAgIHRleHRPdmVyZmxvdzogJ2VsbGlwc2lzJyxcbiAgICAgICAgICAgICAgICAgICAgd2hpdGVTcGFjZTogJ25vd3JhcCcsXG4gICAgICAgICAgICAgICAgICAgIG1heFdpZHRoOiAnMTAwJSdcbiAgICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT17eyBmb250V2VpZ2h0OiA1MDAgfX0+e3Nlcmllcy50aXRsZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPXt7IGZvbnRTaXplOiAnMTJweCcsIGNvbG9yOiAnIzY2NicsIG1hcmdpbkxlZnQ6ICc4cHgnIH19PlxuICAgICAgICAgICAgICAgICAgICAgICh7c2VyaWVzLmNhdGVnb3J5fSlcbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9PcHRpb24+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9TZWxlY3Q+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwidGl0bGVcIlxuICAgICAgICAgICAgbGFiZWw9XCLor77nqIvlkI3np7BcIlxuICAgICAgICAgICAgcnVsZXM9e1t7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl6K++56iL5ZCN56ewJyB9XX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8SW5wdXQgcGxhY2Vob2xkZXI9XCLor7fovpPlhaXor77nqIvlkI3np7BcIiAvPlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbmFtZT1cImRlc2NyaXB0aW9uXCJcbiAgICAgICAgICAgIGxhYmVsPVwi6K++56iL5o+P6L+wXCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeivvueoi+aPj+i/sCcgfV19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPElucHV0LlRleHRBcmVhXG4gICAgICAgICAgICAgIHJvd3M9ezR9XG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36K+m57uG5o+P6L+w6K++56iL5YaF5a6544CB55uu5qCH5ZKM54m56ImyLi4uXCJcbiAgICAgICAgICAgICAgc2hvd0NvdW50XG4gICAgICAgICAgICAgIG1heExlbmd0aD17NTAwfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIGxhYmVsPVwi6K++56iL5bCB6Z2iXCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+S4iuS8oOivvueoi+WwgemdoicgfV19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFVwbG9hZC5EcmFnZ2VyXG4gICAgICAgICAgICAgIG5hbWU9XCJjb3Vyc2VDb3ZlclwiXG4gICAgICAgICAgICAgIGN1c3RvbVJlcXVlc3Q9e2hhbmRsZUNvdXJzZUNvdmVyVXBsb2FkfVxuICAgICAgICAgICAgICBvblJlbW92ZT17aGFuZGxlQ291cnNlQ292ZXJSZW1vdmV9XG4gICAgICAgICAgICAgIGFjY2VwdD1cImltYWdlLypcIlxuICAgICAgICAgICAgICBtYXhDb3VudD17MX1cbiAgICAgICAgICAgICAgbGlzdFR5cGU9XCJwaWN0dXJlXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2NvdXJzZUNvdmVySW1hZ2VVcmwgPyAoXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxpbWcgc3JjPXtjb3Vyc2VDb3ZlckltYWdlVXJsfSBhbHQ9XCLor77nqIvlsIHpnaLpooTop4hcIiBzdHlsZT17eyB3aWR0aDogJzEwMCUnLCBtYXhIZWlnaHQ6ICcyMDBweCcsIG9iamVjdEZpdDogJ2NvdmVyJyB9fSAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJhbnQtdXBsb2FkLWRyYWctaWNvblwiPlxuICAgICAgICAgICAgICAgICAgICA8SW5ib3hPdXRsaW5lZCAvPlxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiYW50LXVwbG9hZC10ZXh0XCI+54K55Ye75oiW5ouW5ou95paH5Lu25Yiw5q2k5Yy65Z+f5LiK5LygPC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiYW50LXVwbG9hZC1oaW50XCI+XG4gICAgICAgICAgICAgICAgICAgIOaUr+aMgeWNleS4quaWh+S7tuS4iuS8oO+8jOW7uuiuruS4iuS8oGpwZ+OAgXBuZ+agvOW8j+WbvueJh++8jOWkp+Wwj+S4jei2hei/hzJNQlxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9VcGxvYWQuRHJhZ2dlcj5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJvcmRlckluZGV4XCJcbiAgICAgICAgICAgIGxhYmVsPVwi6K++56iL5bqP5Y+3XCJcbiAgICAgICAgICAgIHJ1bGVzPXtbXG4gICAgICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXor77nqIvluo/lj7cnIH0sXG4gICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICB0eXBlOiAnbnVtYmVyJyxcbiAgICAgICAgICAgICAgICBtaW46IDAsXG4gICAgICAgICAgICAgICAgbWVzc2FnZTogJ+ivvueoi+W6j+WPt+W/hemhu+Wkp+S6juetieS6jjAnLFxuICAgICAgICAgICAgICAgIHRyYW5zZm9ybTogKHZhbHVlKSA9PiBOdW1iZXIodmFsdWUpXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIF19XG4gICAgICAgICAgICB0b29sdGlwPVwi5Zyo57O75YiX6K++56iL5Lit55qE5o6S5bqP5L2N572u77yM5pWw5a2X6LaK5bCP5o6S5bqP6LaK6Z2g5YmN77yM5LuOMOW8gOWni1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPElucHV0IHR5cGU9XCJudW1iZXJcIiBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpeivvueoi+WcqOezu+WIl+S4reeahOW6j+WPt++8iOS7jjDlvIDlp4vvvIlcIiBtaW49ezB9IC8+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cblxuXG4gICAgICAgICAgey8qIOinhumikeS4iuS8oCAqL31cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBsYWJlbD1cIuivvueoi+inhumikVwiXG4gICAgICAgICAgICB0b29sdGlwPVwi5LiK5Lyg6K++56iL6KeG6aKR5paH5Lu277yM57O757uf5bCG6Ieq5Yqo6K+G5Yir5pe26ZW/562J5L+h5oGvXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8VXBsb2FkLkRyYWdnZXJcbiAgICAgICAgICAgICAgbmFtZT1cImNvdXJzZVZpZGVvXCJcbiAgICAgICAgICAgICAgY3VzdG9tUmVxdWVzdD17aGFuZGxlVmlkZW9VcGxvYWR9XG4gICAgICAgICAgICAgIG9uUmVtb3ZlPXtoYW5kbGVWaWRlb1JlbW92ZX1cbiAgICAgICAgICAgICAgYWNjZXB0PVwidmlkZW8vKlwiXG4gICAgICAgICAgICAgIG1heENvdW50PXsxfVxuICAgICAgICAgICAgICBsaXN0VHlwZT1cInBpY3R1cmVcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7Y291cnNlVmlkZW9VcmwgPyAoXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDx2aWRlb1xuICAgICAgICAgICAgICAgICAgICBzcmM9e2NvdXJzZVZpZGVvVXJsfVxuICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogJzEwMCUnLCBtYXhIZWlnaHQ6ICcyMDBweCcgfX1cbiAgICAgICAgICAgICAgICAgICAgY29udHJvbHNcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8cCBzdHlsZT17eyBtYXJnaW5Ub3A6IDgsIGNvbG9yOiAnIzY2NicgfX0+XG4gICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VWaWRlb05hbWUgfHwgJ+ivvueoi+inhumikSd9XG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImFudC11cGxvYWQtZHJhZy1pY29uXCI+XG4gICAgICAgICAgICAgICAgICAgIDxJbmJveE91dGxpbmVkIC8+XG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJhbnQtdXBsb2FkLXRleHRcIj7ngrnlh7vmiJbmi5bmi73op4bpopHmlofku7bliLDmraTljLrln5/kuIrkvKA8L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJhbnQtdXBsb2FkLWhpbnRcIj5cbiAgICAgICAgICAgICAgICAgICAg5pSv5oyBTVA044CBQVZJ44CBTU9W562J5qC85byP77yM5aSn5bCP5LiN6LaF6L+HMTAwTUJcbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvVXBsb2FkLkRyYWdnZXI+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICB7Lyog5paH5qGj5LiK5LygICovfVxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIGxhYmVsPVwi6K++56iL5paH5qGjXCJcbiAgICAgICAgICAgIHRvb2x0aXA9XCLkuIrkvKDor77nqIvnm7jlhbPmlofmoaPvvIzlpoJQUFTjgIFQREbnrYlcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxVcGxvYWQuRHJhZ2dlclxuICAgICAgICAgICAgICBuYW1lPVwiY291cnNlRG9jdW1lbnRcIlxuICAgICAgICAgICAgICBjdXN0b21SZXF1ZXN0PXtoYW5kbGVEb2N1bWVudFVwbG9hZH1cbiAgICAgICAgICAgICAgb25SZW1vdmU9e2hhbmRsZURvY3VtZW50UmVtb3ZlfVxuICAgICAgICAgICAgICBhY2NlcHQ9XCIucGRmLC5kb2MsLmRvY3gsLnBwdCwucHB0eFwiXG4gICAgICAgICAgICAgIG1heENvdW50PXsxfVxuICAgICAgICAgICAgICBsaXN0VHlwZT1cInBpY3R1cmVcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7Y291cnNlRG9jdW1lbnRVcmwgPyAoXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgcGFkZGluZzogJzIwcHgnLCB0ZXh0QWxpZ246ICdjZW50ZXInIH19PlxuICAgICAgICAgICAgICAgICAgICA8SW5ib3hPdXRsaW5lZCBzdHlsZT17eyBmb250U2l6ZTogJzQ4cHgnLCBjb2xvcjogJyMxODkwZmYnIH19IC8+XG4gICAgICAgICAgICAgICAgICAgIDxwIHN0eWxlPXt7IG1hcmdpblRvcDogOCwgY29sb3I6ICcjNjY2JyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICB7Y291cnNlRG9jdW1lbnROYW1lIHx8ICfor77nqIvmlofmoaMnfVxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiYW50LXVwbG9hZC1kcmFnLWljb25cIj5cbiAgICAgICAgICAgICAgICAgICAgPEluYm94T3V0bGluZWQgLz5cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImFudC11cGxvYWQtdGV4dFwiPueCueWHu+aIluaLluaLveaWh+aho+aWh+S7tuWIsOatpOWMuuWfn+S4iuS8oDwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImFudC11cGxvYWQtaGludFwiPlxuICAgICAgICAgICAgICAgICAgICDmlK/mjIFQREbjgIFXb3Jk44CBUFBU5qC85byP77yM5aSn5bCP5LiN6LaF6L+HNTBNQlxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9VcGxvYWQuRHJhZ2dlcj5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIHsvKiDpn7PpopHkuIrkvKAgKi99XG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbGFiZWw9XCLor77nqIvpn7PpopFcIlxuICAgICAgICAgICAgdG9vbHRpcD1cIuS4iuS8oOivvueoi+mfs+mikeaWh+S7tlwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFVwbG9hZC5EcmFnZ2VyXG4gICAgICAgICAgICAgIG5hbWU9XCJjb3Vyc2VBdWRpb1wiXG4gICAgICAgICAgICAgIGN1c3RvbVJlcXVlc3Q9e2hhbmRsZUF1ZGlvVXBsb2FkfVxuICAgICAgICAgICAgICBvblJlbW92ZT17aGFuZGxlQXVkaW9SZW1vdmV9XG4gICAgICAgICAgICAgIGFjY2VwdD1cImF1ZGlvLypcIlxuICAgICAgICAgICAgICBtYXhDb3VudD17MX1cbiAgICAgICAgICAgICAgbGlzdFR5cGU9XCJwaWN0dXJlXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2NvdXJzZUF1ZGlvVXJsID8gKFxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8YXVkaW9cbiAgICAgICAgICAgICAgICAgICAgc3JjPXtjb3Vyc2VBdWRpb1VybH1cbiAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6ICcxMDAlJyB9fVxuICAgICAgICAgICAgICAgICAgICBjb250cm9sc1xuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDxwIHN0eWxlPXt7IG1hcmdpblRvcDogOCwgY29sb3I6ICcjNjY2JyB9fT5cbiAgICAgICAgICAgICAgICAgICAge2NvdXJzZUF1ZGlvTmFtZSB8fCAn6K++56iL6Z+z6aKRJ31cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiYW50LXVwbG9hZC1kcmFnLWljb25cIj5cbiAgICAgICAgICAgICAgICAgICAgPEluYm94T3V0bGluZWQgLz5cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImFudC11cGxvYWQtdGV4dFwiPueCueWHu+aIluaLluaLvemfs+mikeaWh+S7tuWIsOatpOWMuuWfn+S4iuS8oDwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImFudC11cGxvYWQtaGludFwiPlxuICAgICAgICAgICAgICAgICAgICDmlK/mjIFNUDPjgIFXQVbjgIFBQUPnrYnmoLzlvI/vvIzlpKflsI/kuI3otoXov4c1ME1CXG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L1VwbG9hZC5EcmFnZ2VyPlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbmFtZT1cInRlYWNoaW5nT2JqZWN0aXZlc1wiXG4gICAgICAgICAgICBsYWJlbD1cIuaVmeWtpuebruagh1wiXG4gICAgICAgICAgICB0b29sdGlwPVwi5a2m5ZGY5a6M5oiQ5pys6K++56iL5ZCO5bqU6K+l6L6+5Yiw55qE5a2m5Lmg55uu5qCHXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8U2VsZWN0XG4gICAgICAgICAgICAgIG1vZGU9XCJ0YWdzXCJcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLnkIbop6NOb2RlLmpz55qE5Z+65pys5qaC5b+15ZKM54m554K577yM5o6M5o+hTm9kZS5qc+eahOWuieijheWSjOeOr+Wig+mFjee9rlwiXG4gICAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiAnMTAwJScgfX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBsYWJlbD1cIumZhOS7tui1hOa6kFwiXG4gICAgICAgICAgICB0b29sdGlwPVwi5LiK5Lyg6K++56iL55u45YWz55qE6ZmE5Lu26LWE5rqQ77yM5aaCUFBU44CB5paH5qGj44CB5Luj56CB562JXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8VXBsb2FkXG4gICAgICAgICAgICAgIG5hbWU9XCJhZGRpdGlvbmFsUmVzb3VyY2VzXCJcbiAgICAgICAgICAgICAgY3VzdG9tUmVxdWVzdD17aGFuZGxlQWRkaXRpb25hbFJlc291cmNlVXBsb2FkfVxuICAgICAgICAgICAgICBvblJlbW92ZT17aGFuZGxlQWRkaXRpb25hbFJlc291cmNlUmVtb3ZlfVxuICAgICAgICAgICAgICBtdWx0aXBsZVxuICAgICAgICAgICAgICBhY2NlcHQ9XCIucGRmLC5kb2MsLmRvY3gsLnBwdCwucHB0eCwueGxzLC54bHN4LC56aXAsLnJhciwudHh0XCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPEJ1dHRvbiBpY29uPXs8VXBsb2FkT3V0bGluZWQgLz59PuS4iuS8oOmZhOS7tui1hOa6kDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9VcGxvYWQ+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGZvbnRTaXplOiAnMTJweCcsIGNvbG9yOiAnIzY2NicsIG1hcmdpblRvcDogNCB9fT5cbiAgICAgICAgICAgICAg5pSv5oyB5LiK5LygUERG44CBT2ZmaWNl5paH5qGj44CB5Y6L57yp5YyF562J5qC85byP5paH5Lu277yM5Y2V5Liq5paH5Lu25LiN6LaF6L+HMTBNQlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG4gICAgICAgIDwvRm9ybT5cbiAgICAgIDwvTW9kYWw+XG5cbiAgICAgIHsvKiDnvJbovpHor77nqIvmqKHmgIHmoYYgKi99XG4gICAgICA8TW9kYWxcbiAgICAgICAgdGl0bGU9XCLnvJbovpHor77nqItcIlxuICAgICAgICBvcGVuPXtpc0VkaXRDb3Vyc2VNb2RhbFZpc2libGV9XG4gICAgICAgIG9uQ2FuY2VsPXsoKSA9PiB7XG4gICAgICAgICAgc2V0SXNFZGl0Q291cnNlTW9kYWxWaXNpYmxlKGZhbHNlKTtcbiAgICAgICAgICBzZXRFZGl0aW5nQ291cnNlKG51bGwpO1xuICAgICAgICAgIGVkaXRDb3Vyc2VGb3JtLnJlc2V0RmllbGRzKCk7XG4gICAgICAgIH19XG4gICAgICAgIG9uT2s9eygpID0+IGVkaXRDb3Vyc2VGb3JtLnN1Ym1pdCgpfVxuICAgICAgICBva1RleHQ9XCLnoa7lrppcIlxuICAgICAgICBjYW5jZWxUZXh0PVwi5Y+W5raIXCJcbiAgICAgID5cbiAgICAgICAgPEZvcm1cbiAgICAgICAgICBmb3JtPXtlZGl0Q291cnNlRm9ybX1cbiAgICAgICAgICBsYXlvdXQ9XCJ2ZXJ0aWNhbFwiXG4gICAgICAgICAgb25GaW5pc2g9e2hhbmRsZUVkaXRDb3Vyc2V9XG4gICAgICAgID5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwibmFtZVwiXG4gICAgICAgICAgICBsYWJlbD1cIuivvueoi+WQjeensFwiXG4gICAgICAgICAgICBydWxlcz17W3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXor77nqIvlkI3np7AnIH1dfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxJbnB1dCBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpeivvueoi+WQjeensFwiIC8+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwiZGVzY3JpcHRpb25cIlxuICAgICAgICAgICAgbGFiZWw9XCLor77nqIvmj4/ov7BcIlxuICAgICAgICAgICAgcnVsZXM9e1t7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl6K++56iL5o+P6L+wJyB9XX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8SW5wdXQuVGV4dEFyZWEgcm93cz17M30gcGxhY2Vob2xkZXI9XCLor7fovpPlhaXor77nqIvmj4/ov7BcIiAvPlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbmFtZT1cImNhdGVnb3J5XCJcbiAgICAgICAgICAgIGxhYmVsPVwi6K++56iL5YiG57G7XCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqeivvueoi+WIhuexuycgfV19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFNlbGVjdCBwbGFjZWhvbGRlcj1cIuivt+mAieaLqeivvueoi+WIhuexu1wiPlxuICAgICAgICAgICAgICA8T3B0aW9uIHZhbHVlPVwi57yW56iL5Z+656GAXCI+57yW56iL5Z+656GAPC9PcHRpb24+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9XCLnvJbnqIvov5vpmLZcIj7nvJbnqIvov5vpmLY8L09wdGlvbj5cbiAgICAgICAgICAgICAgPE9wdGlvbiB2YWx1ZT1cIueul+azleaAnee7tFwiPueul+azleaAnee7tDwvT3B0aW9uPlxuICAgICAgICAgICAgICA8T3B0aW9uIHZhbHVlPVwi6aG555uu5a6e5oiYXCI+6aG555uu5a6e5oiYPC9PcHRpb24+XG4gICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJzdGF0dXNcIlxuICAgICAgICAgICAgbGFiZWw9XCLor77nqIvnirbmgIFcIlxuICAgICAgICAgICAgcnVsZXM9e1t7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup6K++56iL54q25oCBJyB9XX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8U2VsZWN0PlxuICAgICAgICAgICAgICA8T3B0aW9uIHZhbHVlPVwiYWN0aXZlXCI+5ZCv55SoPC9PcHRpb24+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9XCJpbmFjdGl2ZVwiPuemgeeUqDwvT3B0aW9uPlxuICAgICAgICAgICAgPC9TZWxlY3Q+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG4gICAgICAgIDwvRm9ybT5cbiAgICAgIDwvTW9kYWw+XG5cbiAgICAgIHsvKiDmt7vliqDns7vliJfor77nqIvmqKHmgIHmoYYgKi99XG4gICAgICA8TW9kYWxcbiAgICAgICAgdGl0bGU9XCLliJvlu7rns7vliJfor77nqItcIlxuICAgICAgICBvcGVuPXtpc0FkZFNlcmllc01vZGFsVmlzaWJsZX1cbiAgICAgICAgb25DYW5jZWw9eygpID0+IHtcbiAgICAgICAgICBzZXRJc0FkZFNlcmllc01vZGFsVmlzaWJsZShmYWxzZSk7XG4gICAgICAgICAgYWRkU2VyaWVzRm9ybS5yZXNldEZpZWxkcygpO1xuICAgICAgICAgIHNldENvdmVySW1hZ2VVcmwoJycpO1xuICAgICAgICB9fVxuICAgICAgICBvbk9rPXsoKSA9PiBhZGRTZXJpZXNGb3JtLnN1Ym1pdCgpfVxuICAgICAgICBva1RleHQ9XCLliJvlu7rns7vliJfor77nqItcIlxuICAgICAgICBjYW5jZWxUZXh0PVwi5Y+W5raIXCJcbiAgICAgICAgd2lkdGg9ezgwMH1cbiAgICAgID5cbiAgICAgICAgPEZvcm1cbiAgICAgICAgICBmb3JtPXthZGRTZXJpZXNGb3JtfVxuICAgICAgICAgIGxheW91dD1cInZlcnRpY2FsXCJcbiAgICAgICAgICBvbkZpbmlzaD17aGFuZGxlQWRkU2VyaWVzfVxuICAgICAgICA+XG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbmFtZT1cInRpdGxlXCJcbiAgICAgICAgICAgIGxhYmVsPVwi57O75YiX6K++56iL5ZCN56ewXCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeezu+WIl+ivvueoi+WQjeensCcgfV19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPElucHV0IHBsYWNlaG9sZGVyPVwi5L6L5aaC77yaUmVhY3TlhajmoIjlvIDlj5Hlrp7miJhcIiAvPlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbmFtZT1cImRlc2NyaXB0aW9uXCJcbiAgICAgICAgICAgIGxhYmVsPVwi6K++56iL5LuL57uNXCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeivvueoi+S7i+e7jScgfV19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPElucHV0LlRleHRBcmVhXG4gICAgICAgICAgICAgIHJvd3M9ezR9XG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36K+m57uG5o+P6L+w57O75YiX6K++56iL55qE5YaF5a6544CB55uu5qCH5ZKM54m56ImyLi4uXCJcbiAgICAgICAgICAgICAgc2hvd0NvdW50XG4gICAgICAgICAgICAgIG1heExlbmd0aD17NTAwfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIGxhYmVsPVwi5bCB6Z2i5Zu+54mHXCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+S4iuS8oOWwgemdouWbvueJhycgfV19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFVwbG9hZC5EcmFnZ2VyXG4gICAgICAgICAgICAgIG5hbWU9XCJjb3ZlckltYWdlXCJcbiAgICAgICAgICAgICAgY3VzdG9tUmVxdWVzdD17aGFuZGxlSW1hZ2VVcGxvYWR9XG4gICAgICAgICAgICAgIG9uUmVtb3ZlPXtoYW5kbGVJbWFnZVJlbW92ZX1cbiAgICAgICAgICAgICAgYWNjZXB0PVwiaW1hZ2UvKlwiXG4gICAgICAgICAgICAgIG1heENvdW50PXsxfVxuICAgICAgICAgICAgICBsaXN0VHlwZT1cInBpY3R1cmVcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7Y292ZXJJbWFnZVVybCA/IChcbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGltZyBzcmM9e2NvdmVySW1hZ2VVcmx9IGFsdD1cIuWwgemdoumihOiniFwiIHN0eWxlPXt7IHdpZHRoOiAnMTAwJScsIG1heEhlaWdodDogJzIwMHB4Jywgb2JqZWN0Rml0OiAnY292ZXInIH19IC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImFudC11cGxvYWQtZHJhZy1pY29uXCI+XG4gICAgICAgICAgICAgICAgICAgIDxJbmJveE91dGxpbmVkIC8+XG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJhbnQtdXBsb2FkLXRleHRcIj7ngrnlh7vmiJbmi5bmi73mlofku7bliLDmraTljLrln5/kuIrkvKA8L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJhbnQtdXBsb2FkLWhpbnRcIj5cbiAgICAgICAgICAgICAgICAgICAg5pSv5oyB5Y2V5Liq5paH5Lu25LiK5Lyg77yM5bu66K6u5LiK5LyganBn44CBcG5n5qC85byP5Zu+54mH77yM5aSn5bCP5LiN6LaF6L+HMk1CXG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L1VwbG9hZC5EcmFnZ2VyPlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG5cblxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJjYXRlZ29yeVwiXG4gICAgICAgICAgICBsYWJlbD1cIuaYr+WQpuS4uuWumOaWueezu+WIl+ivvueoi1wiXG4gICAgICAgICAgICBydWxlcz17W3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nmmK/lkKbkuLrlrpjmlrnns7vliJfor77nqIsnIH1dfVxuICAgICAgICAgICAgaW5pdGlhbFZhbHVlPXswfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTZWxlY3QgcGxhY2Vob2xkZXI9XCLor7fpgInmi6lcIj5cbiAgICAgICAgICAgICAgPE9wdGlvbiB2YWx1ZT17MX0+5piv77yI5a6Y5pa577yJPC9PcHRpb24+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9ezB9PuWQpu+8iOekvuWMuu+8iTwvT3B0aW9uPlxuICAgICAgICAgICAgPC9TZWxlY3Q+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwicHJvamVjdE1lbWJlcnNcIlxuICAgICAgICAgICAgbGFiZWw9XCLor77nqIvmiJDlkZhcIlxuICAgICAgICAgICAgcnVsZXM9e1t7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl6K++56iL5oiQ5ZGYJyB9XX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fovpPlhaXor77nqIvmiJDlkZjvvIzlpoLvvJrnjovogIHluIjjgIHmnY7liqnmlZnjgIHlvKDlkIzlraZcIlxuICAgICAgICAgICAgICBzaG93Q291bnRcbiAgICAgICAgICAgICAgbWF4TGVuZ3RoPXsyMDB9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbmFtZT1cInRhZ0lkc1wiXG4gICAgICAgICAgICBsYWJlbD1cIuagh+etvumAieaLqVwiXG4gICAgICAgICAgICBydWxlcz17W3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nmoIfnrb4nIH1dfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTZWxlY3RcbiAgICAgICAgICAgICAgbW9kZT1cIm11bHRpcGxlXCJcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fpgInmi6nnm7jlhbPmoIfnrb5cIlxuICAgICAgICAgICAgICBvcHRpb25MYWJlbFByb3A9XCJsYWJlbFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtjb3Vyc2VUYWdzLm1hcCh0YWcgPT4gKFxuICAgICAgICAgICAgICAgIDxPcHRpb24ga2V5PXt0YWcuaWR9IHZhbHVlPXt0YWcuaWR9IGxhYmVsPXt0YWcubmFtZX0+XG4gICAgICAgICAgICAgICAgICA8VGFnIGNvbG9yPXt0YWcuY29sb3J9Pnt0YWcubmFtZX08L1RhZz5cbiAgICAgICAgICAgICAgICA8L09wdGlvbj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cbiAgICAgICAgPC9Gb3JtPlxuICAgICAgPC9Nb2RhbD5cblxuICAgICAgey8qIOa3u+WKoOivvueoi+agh+etvuaooeaAgeahhiAqL31cbiAgICAgIDxNb2RhbFxuICAgICAgICB0aXRsZT1cIuWIm+W7uuivvueoi+agh+etvlwiXG4gICAgICAgIG9wZW49e2lzQWRkVGFnTW9kYWxWaXNpYmxlfVxuICAgICAgICBvbkNhbmNlbD17KCkgPT4ge1xuICAgICAgICAgIHNldElzQWRkVGFnTW9kYWxWaXNpYmxlKGZhbHNlKTtcbiAgICAgICAgICBhZGRUYWdGb3JtLnJlc2V0RmllbGRzKCk7XG4gICAgICAgIH19XG4gICAgICAgIG9uT2s9eygpID0+IGFkZFRhZ0Zvcm0uc3VibWl0KCl9XG4gICAgICAgIG9rVGV4dD1cIuWIm+W7uuagh+etvlwiXG4gICAgICAgIGNhbmNlbFRleHQ9XCLlj5bmtohcIlxuICAgICAgICB3aWR0aD17NjAwfVxuICAgICAgPlxuICAgICAgICA8Rm9ybVxuICAgICAgICAgIGZvcm09e2FkZFRhZ0Zvcm19XG4gICAgICAgICAgbGF5b3V0PVwidmVydGljYWxcIlxuICAgICAgICAgIG9uRmluaXNoPXtoYW5kbGVBZGRUYWd9XG4gICAgICAgID5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwibmFtZVwiXG4gICAgICAgICAgICBsYWJlbD1cIuagh+etvuWQjeensFwiXG4gICAgICAgICAgICBydWxlcz17W1xuICAgICAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5qCH562+5ZCN56ewJyB9LFxuICAgICAgICAgICAgICB7IG1heDogMjAsIG1lc3NhZ2U6ICfmoIfnrb7lkI3np7DkuI3og73otoXov4cyMOS4quWtl+espicgfVxuICAgICAgICAgICAgXX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8SW5wdXQgcGxhY2Vob2xkZXI9XCLkvovlpoLvvJrpq5jnuqfjgIHnvJbnqIvjgIHlrp7miJhcIiAvPlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbmFtZT1cImNvbG9yXCJcbiAgICAgICAgICAgIGxhYmVsPVwi5qCH562+6aKc6ImyXCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqeagh+etvuminOiJsicgfV19XG4gICAgICAgICAgICBpbml0aWFsVmFsdWU9XCIjMDA3YmZmXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8U2VsZWN0IHBsYWNlaG9sZGVyPVwi6K+36YCJ5oup5qCH562+6aKc6ImyXCI+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9XCIjMDA3YmZmXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTQgaC00IHJvdW5kZWRcIiBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICcjMDA3YmZmJyB9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIOiTneiJsiAoIzAwN2JmZilcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9PcHRpb24+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9XCIjMjhhNzQ1XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTQgaC00IHJvdW5kZWRcIiBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICcjMjhhNzQ1JyB9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIOe7v+iJsiAoIzI4YTc0NSlcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9PcHRpb24+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9XCIjZGMzNTQ1XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTQgaC00IHJvdW5kZWRcIiBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICcjZGMzNTQ1JyB9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIOe6ouiJsiAoI2RjMzU0NSlcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9PcHRpb24+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9XCIjZmZjMTA3XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTQgaC00IHJvdW5kZWRcIiBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICcjZmZjMTA3JyB9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIOm7hOiJsiAoI2ZmYzEwNylcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9PcHRpb24+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9XCIjNmY0MmMxXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTQgaC00IHJvdW5kZWRcIiBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICcjNmY0MmMxJyB9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIOe0q+iJsiAoIzZmNDJjMSlcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9PcHRpb24+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9XCIjZmQ3ZTE0XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTQgaC00IHJvdW5kZWRcIiBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICcjZmQ3ZTE0JyB9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIOapmeiJsiAoI2ZkN2UxNClcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9PcHRpb24+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9XCIjMjBjOTk3XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTQgaC00IHJvdW5kZWRcIiBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICcjMjBjOTk3JyB9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIOmdkuiJsiAoIzIwYzk5NylcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9PcHRpb24+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9XCIjNmM3NTdkXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTQgaC00IHJvdW5kZWRcIiBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICcjNmM3NTdkJyB9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIOeBsOiJsiAoIzZjNzU3ZClcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9PcHRpb24+XG4gICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJjYXRlZ29yeVwiXG4gICAgICAgICAgICBsYWJlbD1cIuagh+etvuWIhuexu1wiXG4gICAgICAgICAgICBydWxlcz17W3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nmoIfnrb7liIbnsbsnIH1dfVxuICAgICAgICAgICAgaW5pdGlhbFZhbHVlPXsxfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTZWxlY3QgcGxhY2Vob2xkZXI9XCLor7fpgInmi6nmoIfnrb7liIbnsbtcIj5cbiAgICAgICAgICAgICAgPE9wdGlvbiB2YWx1ZT17MH0+6Zq+5bqm5qCH562+PC9PcHRpb24+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9ezF9Puexu+Wei+agh+etvjwvT3B0aW9uPlxuICAgICAgICAgICAgICA8T3B0aW9uIHZhbHVlPXsyfT7nibnoibLmoIfnrb48L09wdGlvbj5cbiAgICAgICAgICAgICAgPE9wdGlvbiB2YWx1ZT17M30+5YW25LuW5qCH562+PC9PcHRpb24+XG4gICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJkZXNjcmlwdGlvblwiXG4gICAgICAgICAgICBsYWJlbD1cIuagh+etvuaPj+i/sFwiXG4gICAgICAgICAgICBydWxlcz17W3sgbWF4OiAxMDAsIG1lc3NhZ2U6ICfmoIfnrb7mj4/ov7DkuI3og73otoXov4cxMDDkuKrlrZfnrKYnIH1dfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxJbnB1dC5UZXh0QXJlYVxuICAgICAgICAgICAgICByb3dzPXszfVxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpeagh+etvueahOivpue7huaPj+i/sC4uLlwiXG4gICAgICAgICAgICAgIHNob3dDb3VudFxuICAgICAgICAgICAgICBtYXhMZW5ndGg9ezEwMH1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwic3RhdHVzXCJcbiAgICAgICAgICAgIGxhYmVsPVwi5qCH562+54q25oCBXCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqeagh+etvueKtuaAgScgfV19XG4gICAgICAgICAgICBpbml0aWFsVmFsdWU9ezF9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFNlbGVjdCBwbGFjZWhvbGRlcj1cIuivt+mAieaLqeagh+etvueKtuaAgVwiPlxuICAgICAgICAgICAgICA8T3B0aW9uIHZhbHVlPXsxfT7lkK/nlKg8L09wdGlvbj5cbiAgICAgICAgICAgICAgPE9wdGlvbiB2YWx1ZT17MH0+56aB55SoPC9PcHRpb24+XG4gICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cbiAgICAgICAgPC9Gb3JtPlxuICAgICAgPC9Nb2RhbD5cblxuICAgICAgey8qIOWPkeW4g+ezu+WIl+ivvueoi+aooeaAgeahhiAqL31cbiAgICAgIDxNb2RhbFxuICAgICAgICB0aXRsZT1cIuWPkeW4g+ezu+WIl+ivvueoi1wiXG4gICAgICAgIG9wZW49e2lzUHVibGlzaFNlcmllc01vZGFsVmlzaWJsZX1cbiAgICAgICAgb25DYW5jZWw9eygpID0+IHtcbiAgICAgICAgICBzZXRJc1B1Ymxpc2hTZXJpZXNNb2RhbFZpc2libGUoZmFsc2UpO1xuICAgICAgICAgIHB1Ymxpc2hTZXJpZXNGb3JtLnJlc2V0RmllbGRzKCk7XG4gICAgICAgIH19XG4gICAgICAgIG9uT2s9eygpID0+IHB1Ymxpc2hTZXJpZXNGb3JtLnN1Ym1pdCgpfVxuICAgICAgICBva1RleHQ9XCLlj5HluIPns7vliJdcIlxuICAgICAgICBjYW5jZWxUZXh0PVwi5Y+W5raIXCJcbiAgICAgICAgd2lkdGg9ezYwMH1cbiAgICAgID5cbiAgICAgICAgPEZvcm1cbiAgICAgICAgICBmb3JtPXtwdWJsaXNoU2VyaWVzRm9ybX1cbiAgICAgICAgICBsYXlvdXQ9XCJ2ZXJ0aWNhbFwiXG4gICAgICAgICAgb25GaW5pc2g9e2hhbmRsZVB1Ymxpc2hTZXJpZXN9XG4gICAgICAgID5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwic2VyaWVzSWRcIlxuICAgICAgICAgICAgbGFiZWw9XCLpgInmi6nopoHlj5HluIPnmoTns7vliJfor77nqItcIlxuICAgICAgICAgICAgcnVsZXM9e1t7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup6KaB5Y+R5biD55qE57O75YiX6K++56iLJyB9XX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8U2VsZWN0XG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36YCJ5oup57O75YiX6K++56iLXCJcbiAgICAgICAgICAgICAgc2hvd1NlYXJjaFxuICAgICAgICAgICAgICBmaWx0ZXJPcHRpb249eyhpbnB1dCwgb3B0aW9uKSA9PlxuICAgICAgICAgICAgICAgIChvcHRpb24/LmNoaWxkcmVuIGFzIHVua25vd24gYXMgc3RyaW5nKT8udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhpbnB1dC50b0xvd2VyQ2FzZSgpKVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtjb3Vyc2VTZXJpZXMubWFwKChzZXJpZXMpID0+IChcbiAgICAgICAgICAgICAgICA8T3B0aW9uIGtleT17c2VyaWVzLmlkfSB2YWx1ZT17c2VyaWVzLmlkfT5cbiAgICAgICAgICAgICAgICAgIHtzZXJpZXMudGl0bGV9ICh7c2VyaWVzLmNhdGVnb3J5fSlcbiAgICAgICAgICAgICAgICA8L09wdGlvbj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJwdWJsaXNoTm90ZVwiXG4gICAgICAgICAgICBsYWJlbD1cIuWPkeW4g+ivtOaYjlwiXG4gICAgICAgICAgICBydWxlcz17W3sgcmVxdWlyZWQ6IGZhbHNlIH1dfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxJbnB1dC5UZXh0QXJlYVxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpeWPkeW4g+ivtOaYju+8iOWPr+mAie+8iVwiXG4gICAgICAgICAgICAgIHJvd3M9ezN9XG4gICAgICAgICAgICAgIG1heExlbmd0aD17MjAwfVxuICAgICAgICAgICAgICBzaG93Q291bnRcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNTAgcC00IHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPuWPkeW4g+ivtOaYju+8mjwvaDQ+XG4gICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIHNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICA8bGk+4oCiIOWPkeW4g+WQjuezu+WIl+ivvueoi+WwhuWcqOivvueoi+W4guWcuuS4reWFrOW8gOaYvuekujwvbGk+XG4gICAgICAgICAgICAgIDxsaT7igKIg5Y+q5pyJ5bey5a6M5oiQ55qE6K++56iL5omN5Lya6KKr5Y+R5biDPC9saT5cbiAgICAgICAgICAgICAgPGxpPuKAoiDlj5HluIPlkI7lj6/ku6Xmn6XnnIvor6bnu4bnmoTlj5HluIPnu5/orqHkv6Hmga88L2xpPlxuICAgICAgICAgICAgICA8bGk+4oCiIOWPkeW4g+eKtuaAgeWPr+S7pemaj+aXtuS/ruaUuTwvbGk+XG4gICAgICAgICAgICA8L3VsPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0Zvcm0+XG4gICAgICA8L01vZGFsPlxuXG4gICAgICB7Lyog5Y+R5biD6K++56iL5qih5oCB5qGGICovfVxuICAgICAgPE1vZGFsXG4gICAgICAgIHRpdGxlPVwi5Y+R5biD6K++56iLXCJcbiAgICAgICAgb3Blbj17aXNQdWJsaXNoQ291cnNlTW9kYWxWaXNpYmxlfVxuICAgICAgICBvbkNhbmNlbD17cmVzZXRQdWJsaXNoQ291cnNlTW9kYWx9XG4gICAgICAgIGZvb3Rlcj17bnVsbH1cbiAgICAgICAgd2lkdGg9ezcwMH1cbiAgICAgICAgZGVzdHJveU9uQ2xvc2VcbiAgICAgID5cbiAgICAgICAgPEZvcm1cbiAgICAgICAgICBmb3JtPXtwdWJsaXNoQ291cnNlRm9ybX1cbiAgICAgICAgICBsYXlvdXQ9XCJ2ZXJ0aWNhbFwiXG4gICAgICAgICAgb25GaW5pc2g9e2hhbmRsZVB1Ymxpc2hDb3Vyc2V9XG4gICAgICAgICAgY2xhc3NOYW1lPVwibXQtNFwiXG4gICAgICAgID5cbiAgICAgICAgICB7Lyog56ys5LiA5Liq5LiL5ouJ5qGG77ya6YCJ5oup57O75YiX6K++56iLICovfVxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJzZXJpZXNJZFwiXG4gICAgICAgICAgICBsYWJlbD1cIumAieaLqeezu+WIl+ivvueoi1wiXG4gICAgICAgICAgICBydWxlcz17W3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nns7vliJfor77nqIsnIH1dfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTZWxlY3RcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fpgInmi6nns7vliJfor77nqItcIlxuICAgICAgICAgICAgICBsb2FkaW5nPXtwdWJsaXNoRm9ybUxvYWRpbmd9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVQdWJsaXNoU2VyaWVzQ2hhbmdlfVxuICAgICAgICAgICAgICBzaG93U2VhcmNoXG4gICAgICAgICAgICAgIGZpbHRlck9wdGlvbj17KGlucHV0LCBvcHRpb24pID0+XG4gICAgICAgICAgICAgICAgKG9wdGlvbj8uY2hpbGRyZW4gYXMgdW5rbm93biBhcyBzdHJpbmcpPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGlucHV0LnRvTG93ZXJDYXNlKCkpXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge3B1Ymxpc2hTZXJpZXNMaXN0Rm9yTW9kYWwubWFwKHNlcmllcyA9PiAoXG4gICAgICAgICAgICAgICAgPE9wdGlvbiBrZXk9e3Nlcmllcy5pZH0gdmFsdWU9e3Nlcmllcy5pZH0+XG4gICAgICAgICAgICAgICAgICB7c2VyaWVzLnRpdGxlfSAoSUQ6IHtzZXJpZXMuaWR9KVxuICAgICAgICAgICAgICAgIDwvT3B0aW9uPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvU2VsZWN0PlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG4gICAgICAgICAgey8qIOesrOS6jOS4quS4i+aLieahhu+8mumAieaLqeWtkOivvueoiyAqL31cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwiY291cnNlSWRcIlxuICAgICAgICAgICAgbGFiZWw9XCLpgInmi6nlrZDor77nqItcIlxuICAgICAgICAgICAgcnVsZXM9e1t7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup6KaB5Y+R5biD55qE5a2Q6K++56iLJyB9XX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8U2VsZWN0XG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPXtzZWxlY3RlZFNlcmllc0ZvclB1Ymxpc2ggPyBcIuivt+mAieaLqeimgeWPkeW4g+eahOWtkOivvueoi1wiIDogXCLor7flhYjpgInmi6nns7vliJfor77nqItcIn1cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFzZWxlY3RlZFNlcmllc0ZvclB1Ymxpc2h9XG4gICAgICAgICAgICAgIGxvYWRpbmc9e3B1Ymxpc2hGb3JtTG9hZGluZyAmJiAhIXNlbGVjdGVkU2VyaWVzRm9yUHVibGlzaH1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZVB1Ymxpc2hDb3Vyc2VDaGFuZ2V9XG4gICAgICAgICAgICAgIHNob3dTZWFyY2hcbiAgICAgICAgICAgICAgZmlsdGVyT3B0aW9uPXsoaW5wdXQsIG9wdGlvbikgPT5cbiAgICAgICAgICAgICAgICAob3B0aW9uPy5jaGlsZHJlbiBhcyB1bmtub3duIGFzIHN0cmluZyk/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoaW5wdXQudG9Mb3dlckNhc2UoKSlcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICBub3RGb3VuZENvbnRlbnQ9e1xuICAgICAgICAgICAgICAgIHB1Ymxpc2hGb3JtTG9hZGluZyAmJiBzZWxlY3RlZFNlcmllc0ZvclB1Ymxpc2hcbiAgICAgICAgICAgICAgICAgID8gXCLmraPlnKjliqDovb3lrZDor77nqIsuLi5cIlxuICAgICAgICAgICAgICAgICAgOiBzZWxlY3RlZFNlcmllc0ZvclB1Ymxpc2hcbiAgICAgICAgICAgICAgICAgICAgPyBcIuivpeezu+WIl+aaguaXoOWtkOivvueoi1wiXG4gICAgICAgICAgICAgICAgICAgIDogXCLor7flhYjpgInmi6nns7vliJfor77nqItcIlxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtwdWJsaXNoQ291cnNlTGlzdEZvck1vZGFsLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICAgICAgcHVibGlzaENvdXJzZUxpc3RGb3JNb2RhbC5tYXAoY291cnNlID0+IChcbiAgICAgICAgICAgICAgICAgIDxPcHRpb24ga2V5PXtjb3Vyc2UuaWR9IHZhbHVlPXtjb3Vyc2UuaWR9PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntjb3Vyc2UudGl0bGV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRhZyBjb2xvcj17Y291cnNlLnN0YXR1cyA9PT0gMSA/ICdncmVlbicgOiBjb3Vyc2Uuc3RhdHVzID09PSAwID8gJ29yYW5nZScgOiAncmVkJ30gY2xhc3NOYW1lPVwidGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Y291cnNlLnN0YXR1cyA9PT0gMSA/ICflt7Llj5HluIMnIDogY291cnNlLnN0YXR1cyA9PT0gMCA/ICfojYnnqL8nIDogJ+W3suW9kuahoyd9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L1RhZz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC14c1wiPklEOiB7Y291cnNlLmlkfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L09wdGlvbj5cbiAgICAgICAgICAgICAgICApKVxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIHNlbGVjdGVkU2VyaWVzRm9yUHVibGlzaCAmJiAhcHVibGlzaEZvcm1Mb2FkaW5nID8gKFxuICAgICAgICAgICAgICAgICAgPE9wdGlvbiBkaXNhYmxlZCB2YWx1ZT1cIm5vLWNvdXJzZXNcIj5cbiAgICAgICAgICAgICAgICAgICAg6K+l57O75YiX5pqC5peg5a2Q6K++56iLXG4gICAgICAgICAgICAgICAgICA8L09wdGlvbj5cbiAgICAgICAgICAgICAgICApIDogbnVsbFxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9TZWxlY3Q+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICB7Lyog6LCD6K+V5L+h5oGv5pi+56S6ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS01MCBwLTMgcm91bmRlZC1sZyBtYi00IHRleHQteHNcIj5cbiAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj7osIPor5Xkv6Hmga88L2g0PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTEgdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICA8cD7lt7LpgInmi6nns7vliJdJRDoge3NlbGVjdGVkU2VyaWVzRm9yUHVibGlzaCB8fCAn5pyq6YCJ5oupJ308L3A+XG4gICAgICAgICAgICAgIDxwPuW3sumAieaLqeivvueoi0lEOiB7c2VsZWN0ZWRDb3Vyc2VGb3JQdWJsaXNoIHx8ICfmnKrpgInmi6knfTwvcD5cbiAgICAgICAgICAgICAgPHA+57O75YiX5YiX6KGo5pWw6YePOiB7cHVibGlzaFNlcmllc0xpc3RGb3JNb2RhbC5sZW5ndGh9PC9wPlxuICAgICAgICAgICAgICA8cD7lrZDor77nqIvliJfooajmlbDph486IHtwdWJsaXNoQ291cnNlTGlzdEZvck1vZGFsLmxlbmd0aH08L3A+XG4gICAgICAgICAgICAgIDxwPuWKoOi9veeKtuaAgToge3B1Ymxpc2hGb3JtTG9hZGluZyA/ICfliqDovb3kuK0nIDogJ+epuumXsid9PC9wPlxuICAgICAgICAgICAgICB7cHVibGlzaENvdXJzZUxpc3RGb3JNb2RhbC5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPHA+5a2Q6K++56iL5YiX6KGoOjwvcD5cbiAgICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJtbC00IGxpc3QtZGlzY1wiPlxuICAgICAgICAgICAgICAgICAgICB7cHVibGlzaENvdXJzZUxpc3RGb3JNb2RhbC5tYXAoY291cnNlID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8bGkga2V5PXtjb3Vyc2UuaWR9PklEOiB7Y291cnNlLmlkfSwg5ZCN56ewOiB7Y291cnNlLnRpdGxlfTwvbGk+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIOmAieS4reivvueoi+S/oeaBr+aYvuekuiAqL31cbiAgICAgICAgICB7c2VsZWN0ZWRDb3Vyc2VGb3JQdWJsaXNoICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS01MCBwLTQgcm91bmRlZC1sZyBtYi00XCI+XG4gICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS05MDAgbWItMlwiPuWNs+WwhuWPkeW4g+eahOivvueoizwvaDQ+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtNzAwXCI+XG4gICAgICAgICAgICAgICAgPHA+6K++56iLSUQ6IHtzZWxlY3RlZENvdXJzZUZvclB1Ymxpc2h9PC9wPlxuICAgICAgICAgICAgICAgIDxwPuaJgOWxnuezu+WIlzoge3B1Ymxpc2hTZXJpZXNMaXN0Rm9yTW9kYWwuZmluZChzID0+IHMuaWQgPT09IHNlbGVjdGVkU2VyaWVzRm9yUHVibGlzaCk/LnRpdGxlfTwvcD5cbiAgICAgICAgICAgICAgICA8cD7or77nqIvlkI3np7A6IHtwdWJsaXNoQ291cnNlTGlzdEZvck1vZGFsLmZpbmQoYyA9PiBjLmlkID09PSBzZWxlY3RlZENvdXJzZUZvclB1Ymxpc2gpPy50aXRsZX08L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMiB0ZXh0LWJsdWUtNjAwIGZvbnQtbWVkaXVtXCI+54K55Ye7XCLlj5HluIPmraTor77nqItcIuWwhuiwg+eUqOWPkeW4g0FQSe+8mlBPU1QgL2FwaS92MS9jb3Vyc2UtbWFuYWdlbWVudC9jb3Vyc2VzL3tzZWxlY3RlZENvdXJzZUZvclB1Ymxpc2h9L3B1Ymxpc2g8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIHB0LTQgYm9yZGVyLXRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgIHtzZWxlY3RlZENvdXJzZUZvclB1Ymxpc2ggPyAoXG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmVlbi02MDBcIj7inJMg5bey6YCJ5oup6K++56iL77yM5Y+v5Lul5Y+R5biDPC9zcGFuPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxzcGFuPuivt+WFiOmAieaLqeezu+WIl+ivvueoi+WSjOWtkOivvueoizwvc3Bhbj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yXCI+XG4gICAgICAgICAgICAgIDxCdXR0b24gb25DbGljaz17cmVzZXRQdWJsaXNoQ291cnNlTW9kYWx9PlxuICAgICAgICAgICAgICAgIOWPlua2iFxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHR5cGU9XCJwcmltYXJ5XCJcbiAgICAgICAgICAgICAgICBodG1sVHlwZT1cInN1Ym1pdFwiXG4gICAgICAgICAgICAgICAgbG9hZGluZz17cHVibGlzaEZvcm1Mb2FkaW5nfVxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXshc2VsZWN0ZWRDb3Vyc2VGb3JQdWJsaXNofVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17c2VsZWN0ZWRDb3Vyc2VGb3JQdWJsaXNoID8gJ2JnLWdyZWVuLTYwMCBob3ZlcjpiZy1ncmVlbi03MDAgYm9yZGVyLWdyZWVuLTYwMCcgOiAnJ31cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtwdWJsaXNoRm9ybUxvYWRpbmcgPyAn5Y+R5biD5LitLi4uJyA6ICflj5HluIPmraTor77nqIsnfVxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0Zvcm0+XG4gICAgICA8L01vZGFsPlxuICAgIDwvPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgQ291cnNlTWFuYWdlbWVudDtcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkNhcmQiLCJCdXR0b24iLCJUYWJsZSIsIk1vZGFsIiwiRm9ybSIsIklucHV0IiwiU2VsZWN0IiwiU3BhY2UiLCJUYWciLCJQb3Bjb25maXJtIiwiVXBsb2FkIiwibWVzc2FnZSIsIlBsdXNPdXRsaW5lZCIsIkVkaXRPdXRsaW5lZCIsIkRlbGV0ZU91dGxpbmVkIiwiVXBsb2FkT3V0bGluZWQiLCJJbmJveE91dGxpbmVkIiwiR2V0Tm90aWZpY2F0aW9uIiwiY291cnNlQXBpIiwidXBsb2FkQXBpIiwiU2VhcmNoIiwiT3B0aW9uIiwiQ291cnNlTWFuYWdlbWVudCIsInB1Ymxpc2hTZXJpZXNMaXN0Rm9yTW9kYWwiLCJwdWJsaXNoQ291cnNlTGlzdEZvck1vZGFsIiwiY291cnNlTGlzdCIsInNldENvdXJzZUxpc3QiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImlzQ291cnNlTW9kYWxWaXNpYmxlIiwic2V0SXNDb3Vyc2VNb2RhbFZpc2libGUiLCJpc0FkZENvdXJzZU1vZGFsVmlzaWJsZSIsInNldElzQWRkQ291cnNlTW9kYWxWaXNpYmxlIiwiaXNFZGl0Q291cnNlTW9kYWxWaXNpYmxlIiwic2V0SXNFZGl0Q291cnNlTW9kYWxWaXNpYmxlIiwiaXNBZGRTZXJpZXNNb2RhbFZpc2libGUiLCJzZXRJc0FkZFNlcmllc01vZGFsVmlzaWJsZSIsImlzQWRkVGFnTW9kYWxWaXNpYmxlIiwic2V0SXNBZGRUYWdNb2RhbFZpc2libGUiLCJpc1B1Ymxpc2hTZXJpZXNNb2RhbFZpc2libGUiLCJzZXRJc1B1Ymxpc2hTZXJpZXNNb2RhbFZpc2libGUiLCJpc1B1Ymxpc2hDb3Vyc2VNb2RhbFZpc2libGUiLCJzZXRJc1B1Ymxpc2hDb3Vyc2VNb2RhbFZpc2libGUiLCJlZGl0aW5nQ291cnNlIiwic2V0RWRpdGluZ0NvdXJzZSIsInNlYXJjaEtleXdvcmQiLCJzZXRTZWFyY2hLZXl3b3JkIiwidGVhY2hlcnMiLCJzZXRUZWFjaGVycyIsImNvdXJzZVRhZ3MiLCJzZXRDb3Vyc2VUYWdzIiwiY292ZXJJbWFnZVVybCIsInNldENvdmVySW1hZ2VVcmwiLCJzZXJpZXNMaXN0Iiwic2V0U2VyaWVzTGlzdCIsInNlcmllc0NvdXJzZXNNYXAiLCJzZXRTZXJpZXNDb3Vyc2VzTWFwIiwiTWFwIiwiZXhwYW5kZWRTZXJpZXMiLCJzZXRFeHBhbmRlZFNlcmllcyIsIlNldCIsInNlcmllc0xvYWRpbmciLCJzZXRTZXJpZXNMb2FkaW5nIiwic2VsZWN0ZWRTZXJpZXNGb3JQdWJsaXNoIiwic2V0U2VsZWN0ZWRTZXJpZXNGb3JQdWJsaXNoIiwidW5kZWZpbmVkIiwic2VsZWN0ZWRDb3Vyc2VGb3JQdWJsaXNoIiwic2V0U2VsZWN0ZWRDb3Vyc2VGb3JQdWJsaXNoIiwicHVibGlzaFNlcmllc0NvdXJzZXMiLCJzZXRQdWJsaXNoU2VyaWVzQ291cnNlcyIsInB1Ymxpc2hMb2FkaW5nIiwic2V0UHVibGlzaExvYWRpbmciLCJwdWJsaXNoU2VyaWVzT3B0aW9ucyIsInNldFB1Ymxpc2hTZXJpZXNPcHRpb25zIiwic2V0UHVibGlzaFNlcmllc0xpc3RGb3JNb2RhbCIsInNldFB1Ymxpc2hDb3Vyc2VMaXN0Rm9yTW9kYWwiLCJwdWJsaXNoRm9ybUxvYWRpbmciLCJzZXRQdWJsaXNoRm9ybUxvYWRpbmciLCJjb3Vyc2VTZXJpZXMiLCJzZXRDb3Vyc2VTZXJpZXMiLCJjb3Vyc2VDb3ZlckltYWdlVXJsIiwic2V0Q291cnNlQ292ZXJJbWFnZVVybCIsImFkZGl0aW9uYWxGaWxlcyIsInNldEFkZGl0aW9uYWxGaWxlcyIsImNvdXJzZVZpZGVvVXJsIiwic2V0Q291cnNlVmlkZW9VcmwiLCJjb3Vyc2VWaWRlb05hbWUiLCJzZXRDb3Vyc2VWaWRlb05hbWUiLCJjb3Vyc2VEb2N1bWVudFVybCIsInNldENvdXJzZURvY3VtZW50VXJsIiwiY291cnNlRG9jdW1lbnROYW1lIiwic2V0Q291cnNlRG9jdW1lbnROYW1lIiwidmlkZW9EdXJhdGlvbiIsInNldFZpZGVvRHVyYXRpb24iLCJhZGRDb3Vyc2VGb3JtIiwidXNlRm9ybSIsImVkaXRDb3Vyc2VGb3JtIiwiYWRkU2VyaWVzRm9ybSIsImFkZFRhZ0Zvcm0iLCJwdWJsaXNoU2VyaWVzRm9ybSIsInB1Ymxpc2hDb3Vyc2VGb3JtIiwibm90aWZpY2F0aW9uIiwiZmV0Y2hTZXJpZXNMaXN0IiwicmVzIiwiY29uc29sZSIsImxvZyIsImRhdGEiLCJnZXRNYXJrZXRwbGFjZVNlcmllcyIsInBhZ2UiLCJwYWdlU2l6ZSIsImNvZGUiLCJsaXN0IiwiZXJyb3IiLCJtc2ciLCJmZXRjaFNlcmllc0NvdXJzZXMiLCJzZXJpZXNJZCIsImdldE1hbmFnZW1lbnRTZXJpZXNDb3Vyc2VzIiwicHJldiIsInNldCIsImFkZCIsImZldGNoQ291cnNlTGlzdCIsImhhbmRsZUFkZENvdXJzZSIsInZhbHVlcyIsImNvbnRlbnRDb25maWciLCJoYXNWaWRlbyIsImhhc0RvY3VtZW50IiwidmlkZW8iLCJ1cmwiLCJuYW1lIiwiZG9jdW1lbnQiLCJjb3Vyc2VEYXRhIiwicGFyc2VJbnQiLCJ0aXRsZSIsInRyaW0iLCJkZXNjcmlwdGlvbiIsImNvdmVySW1hZ2UiLCJoYXNBdWRpbyIsImNvdXJzZUF1ZGlvVXJsIiwidGVhY2hpbmdJbmZvIiwidGVhY2hpbmdPYmplY3RpdmVzIiwibGVuZ3RoIiwiY29udGVudCIsIkFycmF5IiwiaXNBcnJheSIsImFkZGl0aW9uYWxSZXNvdXJjZXMiLCJtYXAiLCJmaWxlIiwic3BsaXQiLCJwb3AiLCJvcmRlckluZGV4IiwiSlNPTiIsInN0cmluZ2lmeSIsInJldHJ5Q291bnQiLCJtYXhSZXRyaWVzIiwibGFzdEVycm9yIiwiY3JlYXRlQ291cnNlIiwic3VjY2VzcyIsInJlc2V0RmllbGRzIiwic2V0Q291cnNlQXVkaW9VcmwiLCJzZXRDb3Vyc2VBdWRpb05hbWUiLCJ3YXJuaW5nIiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwiaW5jbHVkZXMiLCJyZXNwb25zZSIsInN0YXR1cyIsImVycm9yTXNnIiwiaGFuZGxlRWRpdENvdXJzZSIsInVwZGF0ZUNvdXJzZSIsImlkIiwiaGFuZGxlRGVsZXRlQ291cnNlIiwiY291cnNlSWQiLCJkZWxldGVDb3Vyc2UiLCJoYW5kbGVEZWxldGVTdWJDb3Vyc2UiLCJ0b2dnbGVTZXJpZXNFeHBhbnNpb24iLCJoYXMiLCJuZXdTZXQiLCJkZWxldGUiLCJleHBhbmRBbGxTZXJpZXMiLCJzZXJpZXMiLCJjb2xsYXBzZUFsbFNlcmllcyIsImhhbmRsZUFkZFNlcmllcyIsInNlcmllc0RhdGEiLCJjcmVhdGVDb3Vyc2VTZXJpZXMiLCJoYW5kbGVBZGRUYWciLCJjcmVhdGVDb3Vyc2VUYWciLCJmZXRjaENvdXJzZVRhZ3MiLCJoYW5kbGVQdWJsaXNoU2VyaWVzIiwicHVibGlzaENvdXJzZVNlcmllcyIsInB1Ymxpc2hEYXRhIiwicHVibGlzaFN0YXRzIiwic3RhdHMiLCJzdGF0c01lc3NhZ2UiLCJwdWJsaXNoZWRDb3Vyc2VzIiwidG90YWxDb3Vyc2VzIiwiTWF0aCIsInZpZGVvQ291cnNlQ291bnQiLCJyb3VuZCIsInRvdGFsVmlkZW9EdXJhdGlvbiIsImluZm8iLCJmZXRjaFNlcmllc0ZvclB1Ymxpc2giLCJmZXRjaFNlcmllc0RldGFpbEZvclB1Ymxpc2giLCJnZXRNYXJrZXRwbGFjZVNlcmllc0RldGFpbCIsImZldGNoUHVibGlzaFNlcmllc0xpc3QiLCJmZXRjaFB1Ymxpc2hDb3Vyc2VMaXN0IiwiaGFuZGxlUHVibGlzaFNlcmllc0NoYW5nZSIsInNldEZpZWxkc1ZhbHVlIiwiaGFuZGxlUHVibGlzaENvdXJzZUNoYW5nZSIsInJlc2V0UHVibGlzaENvdXJzZU1vZGFsIiwib3BlblB1Ymxpc2hDb3Vyc2VNb2RhbCIsImhhbmRsZVB1Ymxpc2hDb3Vyc2UiLCJzZWxlY3RlZENvdXJzZSIsImZpbmQiLCJjIiwicHVibGlzaENvdXJzZSIsInJlc2V0UHVibGlzaE1vZGFsIiwiaGFuZGxlSW1hZ2VVcGxvYWQiLCJvcHRpb25zIiwib25TdWNjZXNzIiwib25FcnJvciIsInVwbG9hZFRvT3NzIiwiaGFuZGxlSW1hZ2VSZW1vdmUiLCJoYW5kbGVDb3Vyc2VDb3ZlclVwbG9hZCIsImhhbmRsZUNvdXJzZUNvdmVyUmVtb3ZlIiwiaGFuZGxlQWRkaXRpb25hbFJlc291cmNlVXBsb2FkIiwiaGFuZGxlQWRkaXRpb25hbFJlc291cmNlUmVtb3ZlIiwiZmlsdGVyIiwiZiIsImhhbmRsZVZpZGVvVXBsb2FkIiwidmlkZW9FbGVtZW50IiwiY3JlYXRlRWxlbWVudCIsInNyYyIsIm9ubG9hZGVkbWV0YWRhdGEiLCJmbG9vciIsImR1cmF0aW9uIiwiaGFuZGxlVmlkZW9SZW1vdmUiLCJoYW5kbGVEb2N1bWVudFVwbG9hZCIsImhhbmRsZURvY3VtZW50UmVtb3ZlIiwiaGFuZGxlQXVkaW9VcGxvYWQiLCJoYW5kbGVBdWRpb1JlbW92ZSIsIm9wZW5FZGl0TW9kYWwiLCJjb3Vyc2UiLCJmaWx0ZXJlZENvdXJzZXMiLCJ0b0xvd2VyQ2FzZSIsImNhdGVnb3J5IiwicHJlcGFyZVRhYmxlRGF0YSIsInRhYmxlRGF0YSIsImZyb20iLCJmb3JFYWNoIiwicHVzaCIsImtleSIsInR5cGUiLCJpc0V4cGFuZGVkIiwic3ViQ291cnNlcyIsImdldCIsInBhcmVudFNlcmllc1RpdGxlIiwiY29sdW1ucyIsImRhdGFJbmRleCIsIndpZHRoIiwicmVuZGVyIiwidGV4dCIsInJlY29yZCIsImRpdiIsImNsYXNzTmFtZSIsInNpemUiLCJvbkNsaWNrIiwic3R5bGUiLCJtaW5XaWR0aCIsImhlaWdodCIsInNwYW4iLCJjb2xvciIsImdldFN0YXR1c0NvbmZpZyIsImNvbmZpZyIsImljb24iLCJwIiwib25Db25maXJtIiwib2tUZXh0IiwiY2FuY2VsVGV4dCIsIm9rVHlwZSIsImRhbmdlciIsImdldENvdXJzZVRhZ3MiLCJ0YWdzIiwidGFnIiwid2FybiIsImZldGNoQ291cnNlU2VyaWVzIiwicGFnaW5hdGlvbiIsInRvdGFsIiwiZm9ybWF0dGVkU2VyaWVzIiwiaXRlbSIsImluZGV4IiwiY2F0ZWdvcnlMYWJlbCIsInRlYWNoZXJJZHMiLCJ0YWdJZHMiLCJjcmVhdGVkQXQiLCJEYXRlIiwidG9JU09TdHJpbmciLCJ1cGRhdGVkQXQiLCJleHRyYSIsImJsb2NrIiwiYmFja2dyb3VuZENvbG9yIiwiYm9yZGVyQ29sb3IiLCJvcGVuIiwib25DYW5jZWwiLCJmb290ZXIiLCJwbGFjZWhvbGRlciIsImFsbG93Q2xlYXIiLCJvblNlYXJjaCIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsInZhbHVlIiwic3Ryb25nIiwicmVkdWNlIiwiY291cnNlcyIsImRpc2FibGVkIiwiZGF0YVNvdXJjZSIsInJvd0tleSIsInNob3dTaXplQ2hhbmdlciIsInNob3dUb3RhbCIsIm9uT2siLCJzdWJtaXQiLCJmb3JtIiwibGF5b3V0Iiwib25GaW5pc2giLCJJdGVtIiwibGFiZWwiLCJydWxlcyIsInJlcXVpcmVkIiwic2hvd1NlYXJjaCIsIm9wdGlvbkZpbHRlclByb3AiLCJvdmVyZmxvdyIsInRleHRPdmVyZmxvdyIsIndoaXRlU3BhY2UiLCJtYXhXaWR0aCIsImZvbnRXZWlnaHQiLCJmb250U2l6ZSIsIm1hcmdpbkxlZnQiLCJUZXh0QXJlYSIsInJvd3MiLCJzaG93Q291bnQiLCJtYXhMZW5ndGgiLCJEcmFnZ2VyIiwiY3VzdG9tUmVxdWVzdCIsIm9uUmVtb3ZlIiwiYWNjZXB0IiwibWF4Q291bnQiLCJsaXN0VHlwZSIsImltZyIsImFsdCIsIm1heEhlaWdodCIsIm9iamVjdEZpdCIsIm1pbiIsInRyYW5zZm9ybSIsIk51bWJlciIsInRvb2x0aXAiLCJjb250cm9scyIsIm1hcmdpblRvcCIsInBhZGRpbmciLCJ0ZXh0QWxpZ24iLCJhdWRpbyIsImNvdXJzZUF1ZGlvTmFtZSIsIm1vZGUiLCJtdWx0aXBsZSIsImluaXRpYWxWYWx1ZSIsIm9wdGlvbkxhYmVsUHJvcCIsIm1heCIsImZpbHRlck9wdGlvbiIsImlucHV0Iiwib3B0aW9uIiwiY2hpbGRyZW4iLCJoNCIsInVsIiwibGkiLCJkZXN0cm95T25DbG9zZSIsIm5vdEZvdW5kQ29udGVudCIsInMiLCJodG1sVHlwZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin-space/components/course-management.tsx\n"));

/***/ })

});