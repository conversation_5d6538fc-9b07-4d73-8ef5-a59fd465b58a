"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-space/page",{

/***/ "(app-pages-browser)/./app/admin-space/components/course-management.tsx":
/*!**********************************************************!*\
  !*** ./app/admin-space/components/course-management.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/popconfirm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/upload/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EditOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/InboxOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/UploadOutlined.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst { Search } = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nconst { Option } = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\nconst CourseManagement = ()=>{\n    var _publishSeriesListForModal_find, _publishCourseListForModal_find;\n    _s();\n    const [courseList, setCourseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCourseModalVisible, setIsCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddCourseModalVisible, setIsAddCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditCourseModalVisible, setIsEditCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddSeriesModalVisible, setIsAddSeriesModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddTagModalVisible, setIsAddTagModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishSeriesModalVisible, setIsPublishSeriesModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishCourseModalVisible, setIsPublishCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingCourse, setEditingCourse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchKeyword, setSearchKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [teachers, setTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseTags, setCourseTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coverImageUrl, setCoverImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 新增：系列课程和子课程管理相关状态\n    const [seriesList, setSeriesList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [seriesCoursesMap, setSeriesCoursesMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [expandedSeries, setExpandedSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [seriesLoading, setSeriesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 发布课程相关状态\n    const [selectedSeriesForPublish, setSelectedSeriesForPublish] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [selectedCourseForPublish, setSelectedCourseForPublish] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [publishSeriesCourses, setPublishSeriesCourses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [publishLoading, setPublishLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishSeriesOptions, setPublishSeriesOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 新的发布课程状态\n    const [publishSeriesListForModal, setPublishSeriesListForModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [publishCourseListForModal, setPublishCourseListForModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [publishFormLoading, setPublishFormLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [courseSeries, setCourseSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseCoverImageUrl, setCourseCoverImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [additionalFiles, setAdditionalFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseVideoUrl, setCourseVideoUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseVideoName, setCourseVideoName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseDocumentUrl, setCourseDocumentUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseDocumentName, setCourseDocumentName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseAudioUrl, setCourseAudioUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseAudioName, setCourseAudioName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoDuration, setVideoDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [addCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [editCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [addSeriesForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [addTagForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [publishSeriesForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [publishCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__.GetNotification)();\n    // 获取系列课程列表\n    const fetchSeriesList = async ()=>{\n        try {\n            var _res_data;\n            setSeriesLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取系列课程列表成功:\", res.data.list);\n                setSeriesList(res.data.list);\n            } else {\n                console.error(\"❌ 获取系列课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程列表失败，请重试\");\n        } finally{\n            setSeriesLoading(false);\n        }\n    };\n    // 获取指定系列下的子课程列表\n    const fetchSeriesCourses = async (seriesId)=>{\n        try {\n            var _res_data;\n            console.log(\"\\uD83D\\uDCDD 获取系列子课程列表，系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getSeriesCourseList(seriesId, {\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取系列子课程列表成功:\", res.data.list);\n                setSeriesCoursesMap((prev)=>new Map(prev.set(seriesId, res.data.list)));\n                setExpandedSeries((prev)=>new Set(prev.add(seriesId)));\n            } else {\n                console.error(\"❌ 获取系列子课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取子课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列子课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取子课程列表失败，请重试\");\n        }\n    };\n    // 获取课程列表（保留原有功能）\n    const fetchCourseList = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDCDD 获取课程列表...\");\n            // 获取系列课程列表\n            await fetchSeriesList();\n        } catch (error) {\n            console.error(\"❌ 获取课程列表失败:\", error);\n            notification.error(\"获取课程列表失败，请重试\");\n        }\n    };\n    // 添加课程\n    const handleAddCourse = async (values)=>{\n        try {\n            // 构建内容配置，只包含有效的媒体文件\n            const contentConfig = {\n                hasVideo: courseVideoUrl ? 1 : 0,\n                hasDocument: courseDocumentUrl ? 1 : 0,\n                hasAudio: courseAudioUrl ? 1 : 0\n            };\n            if (courseVideoUrl) {\n                contentConfig.video = {\n                    url: courseVideoUrl,\n                    name: courseVideoName || \"课程视频.mp4\"\n                };\n            }\n            if (courseDocumentUrl) {\n                contentConfig.document = {\n                    url: courseDocumentUrl,\n                    name: courseDocumentName || \"课程文档.pdf\"\n                };\n            }\n            if (courseAudioUrl) {\n                contentConfig.audio = {\n                    url: courseAudioUrl,\n                    name: courseAudioName || \"课程音频.mp3\"\n                };\n            }\n            const courseData = {\n                seriesId: parseInt(values.seriesId),\n                title: values.title.trim(),\n                description: values.description.trim(),\n                coverImage: courseCoverImageUrl,\n                hasVideo: courseVideoUrl ? 1 : 0,\n                hasDocument: courseDocumentUrl ? 1 : 0,\n                hasAudio: courseAudioUrl ? 1 : 0,\n                videoDuration: videoDuration || 0,\n                contentConfig,\n                teachingInfo: values.teachingObjectives && values.teachingObjectives.length > 0 ? [\n                    {\n                        title: \"教学目标\",\n                        content: Array.isArray(values.teachingObjectives) ? values.teachingObjectives : [\n                            values.teachingObjectives\n                        ]\n                    }\n                ] : [],\n                additionalResources: additionalFiles.map((file)=>({\n                        title: file.split(\"/\").pop() || \"file\",\n                        url: file,\n                        description: \"课程附件资源\"\n                    })),\n                orderIndex: parseInt(values.orderIndex) || 0\n            };\n            // 验证必要字段\n            if (!courseData.seriesId) {\n                notification.error(\"请选择所属系列课程\");\n                return;\n            }\n            if (!courseData.title) {\n                notification.error(\"请输入课程名称\");\n                return;\n            }\n            if (!courseData.coverImage) {\n                notification.error(\"请上传课程封面\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDCE4 提交课程数据:\", courseData);\n            console.log(\"\\uD83D\\uDCCA 数据大小估算:\", JSON.stringify(courseData).length, \"字符\");\n            // 添加重试机制\n            let retryCount = 0;\n            const maxRetries = 2;\n            let lastError;\n            while(retryCount <= maxRetries){\n                try {\n                    const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourse(courseData);\n                    // 如果成功，跳出重试循环\n                    if (res.code === 200) {\n                        notification.success(\"创建课程成功\");\n                        fetchCourseList();\n                        setIsAddCourseModalVisible(false);\n                        addCourseForm.resetFields();\n                        setCourseCoverImageUrl(\"\");\n                        setAdditionalFiles([]);\n                        setCourseVideoUrl(\"\");\n                        setCourseVideoName(\"\");\n                        setCourseDocumentUrl(\"\");\n                        setCourseDocumentName(\"\");\n                        setCourseAudioUrl(\"\");\n                        setCourseAudioName(\"\");\n                        setVideoDuration(0);\n                        return;\n                    } else {\n                        notification.error(res.msg || \"创建课程失败\");\n                        return;\n                    }\n                } catch (error) {\n                    lastError = error;\n                    retryCount++;\n                    if (retryCount <= maxRetries) {\n                        console.log(\"\\uD83D\\uDD04 第\".concat(retryCount, \"次重试...\"));\n                        notification.warning(\"网络异常，正在重试 (\".concat(retryCount, \"/\").concat(maxRetries, \")\"));\n                        // 等待1秒后重试\n                        await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    }\n                }\n            }\n            // 如果所有重试都失败了，抛出最后的错误\n            throw lastError;\n        } catch (error) {\n            var _error_message, _error_response_data, _error_response, _error_response1, _error_response2, _error_response3, _error_response4, _error_response5;\n            console.error(\"❌ 创建课程失败:\", error);\n            // 更详细的错误处理\n            if (error.code === \"ECONNRESET\" || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"ECONNRESET\")) || ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) && error.response.data.message.includes(\"ECONNRESET\")) {\n                notification.error(\"网络连接中断，可能是网络不稳定或服务器繁忙。请稍后重试或联系管理员。\");\n            } else if (error.code === \"NETWORK_ERROR\") {\n                notification.error(\"网络错误，请检查网络连接\");\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 413) {\n                notification.error(\"上传文件过大，请压缩后重试\");\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 400) {\n                var _error_response_data1, _error_response6;\n                const errorMsg = ((_error_response6 = error.response) === null || _error_response6 === void 0 ? void 0 : (_error_response_data1 = _error_response6.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || error.message;\n                notification.error(\"请求参数错误: \".concat(errorMsg));\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 500) {\n                notification.error(\"服务器内部错误，请联系管理员\");\n            } else {\n                notification.error(\"创建课程失败: \".concat(error.message || \"请稍后重试\"));\n            }\n            console.log(\"\\uD83D\\uDD0D 完整错误信息:\", {\n                message: error.message,\n                code: error.code,\n                status: (_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status,\n                data: (_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.data\n            });\n        }\n    };\n    // 编辑课程\n    const handleEditCourse = async (values)=>{\n        if (!editingCourse) return;\n        try {\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.updateCourse(editingCourse.id, values);\n            if (res.code === 200) {\n                notification.success(\"更新课程成功\");\n                fetchCourseList();\n                setIsEditCourseModalVisible(false);\n                setEditingCourse(null);\n                editCourseForm.resetFields();\n            } else {\n                notification.error(res.msg || \"更新课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 更新课程失败:\", error);\n            notification.error(\"更新课程失败，请重试\");\n        }\n    };\n    // 删除课程\n    const handleDeleteCourse = async (courseId)=>{\n        try {\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.deleteCourse(courseId);\n            if (res.code === 200) {\n                notification.success(\"删除课程成功\");\n                fetchCourseList();\n            } else {\n                notification.error(res.msg || \"删除课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 删除课程失败:\", error);\n            notification.error(\"删除课程失败，请重试\");\n        }\n    };\n    // 删除子课程\n    const handleDeleteSubCourse = async (courseId, seriesId)=>{\n        try {\n            console.log(\"\\uD83D\\uDDD1️ 删除子课程，课程ID:\", courseId, \"系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.deleteCourse(courseId);\n            if (res.code === 200) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"删除子课程成功\");\n                console.log(\"✅ 子课程删除成功，重新获取系列子课程列表\");\n                // 重新获取该系列的子课程列表\n                await fetchSeriesCourses(seriesId);\n                console.log(\"\\uD83D\\uDD04 子课程列表已刷新\");\n            } else {\n                console.error(\"❌ 删除子课程失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"删除子课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 删除子课程异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"删除子课程失败，请重试\");\n        }\n    };\n    // 切换系列展开/收起状态\n    const toggleSeriesExpansion = async (seriesId)=>{\n        console.log(\"\\uD83D\\uDD04 切换系列展开状态，系列ID:\", seriesId);\n        console.log(\"\\uD83D\\uDCCA 当前展开状态:\", expandedSeries.has(seriesId));\n        if (expandedSeries.has(seriesId)) {\n            // 收起\n            console.log(\"\\uD83D\\uDCC1 收起系列:\", seriesId);\n            setExpandedSeries((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(seriesId);\n                return newSet;\n            });\n        } else {\n            // 展开，需要获取子课程数据\n            console.log(\"\\uD83D\\uDCC2 展开系列，获取子课程:\", seriesId);\n            await fetchSeriesCourses(seriesId);\n        }\n    };\n    // 展开所有系列\n    const expandAllSeries = async ()=>{\n        console.log(\"\\uD83D\\uDCC2 展开所有系列课程\");\n        for (const series of seriesList){\n            if (!expandedSeries.has(series.id)) {\n                await fetchSeriesCourses(series.id);\n            }\n        }\n    };\n    // 收起所有系列\n    const collapseAllSeries = ()=>{\n        console.log(\"\\uD83D\\uDCC1 收起所有系列课程\");\n        setExpandedSeries(new Set());\n    };\n    // 添加系列课程\n    const handleAddSeries = async (values)=>{\n        try {\n            const seriesData = {\n                ...values,\n                coverImage: coverImageUrl\n            };\n            console.log(\"创建系列课程数据:\", seriesData);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourseSeries(seriesData);\n            if (res.code === 200) {\n                notification.success(\"创建系列课程成功\");\n                fetchCourseList();\n                setIsAddSeriesModalVisible(false);\n                addSeriesForm.resetFields();\n                setCoverImageUrl(\"\");\n            } else {\n                notification.error(res.msg || \"创建系列课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 创建系列课程失败:\", error);\n            notification.error(\"创建系列课程失败，请重试\");\n        }\n    };\n    // 创建课程标签\n    const handleAddTag = async (values)=>{\n        try {\n            console.log(\"\\uD83C\\uDFF7️ 创建课程标签数据:\", values);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourseTag(values);\n            if (res.code === 200) {\n                notification.success(\"创建标签成功\");\n                setIsAddTagModalVisible(false);\n                addTagForm.resetFields();\n                // 重新获取标签列表\n                fetchCourseTags();\n            } else {\n                notification.error(res.msg || \"创建标签失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 创建标签失败:\", error);\n            notification.error(\"创建标签失败，请重试\");\n        }\n    };\n    // 发布系列课程\n    const handlePublishSeries = async (values)=>{\n        try {\n            console.log(\"\\uD83D\\uDCE2 发布系列课程数据:\", values);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourseSeries(values.seriesId);\n            if (res.code === 200) {\n                notification.success(\"发布系列课程成功\");\n                setIsPublishSeriesModalVisible(false);\n                publishSeriesForm.resetFields();\n                // 显示发布结果信息\n                const publishData = res.data;\n                console.log(\"✅ 发布成功，系列信息:\", publishData);\n                // 可以选择显示发布统计信息\n                if (publishData.publishStats) {\n                    const stats = publishData.publishStats;\n                    const statsMessage = \"已发布 \".concat(publishData.publishedCourses, \"/\").concat(publishData.totalCourses, \" 个课程，包含 \").concat(stats.videoCourseCount, \" 个视频课程，总时长 \").concat(Math.round(stats.totalVideoDuration / 60), \" 分钟\");\n                    notification.info(statsMessage);\n                }\n            } else {\n                notification.error(res.msg || \"发布系列课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 发布系列课程失败:\", error);\n            notification.error(\"发布系列课程失败，请重试\");\n        }\n    };\n    // 获取发布用的系列课程列表\n    const fetchSeriesForPublish = async ()=>{\n        try {\n            var _res_data;\n            setPublishLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取发布用系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取发布用系列课程列表成功:\", res.data.list);\n                return res.data.list;\n            } else {\n                console.error(\"❌ 获取发布用系列课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程列表失败\");\n                return [];\n            }\n        } catch (error) {\n            console.error(\"❌ 获取发布用系列课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程列表失败，请重试\");\n            return [];\n        } finally{\n            setPublishLoading(false);\n        }\n    };\n    // 获取指定系列的课程详情\n    const fetchSeriesDetailForPublish = async (seriesId)=>{\n        try {\n            setPublishLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取系列课程详情，系列ID:\", seriesId);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeriesDetail(seriesId);\n            if (res.code === 200 && res.data) {\n                console.log(\"✅ 获取系列课程详情成功:\", res.data);\n                return res.data;\n            } else {\n                console.error(\"❌ 获取系列课程详情失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程详情失败\");\n                return null;\n            }\n        } catch (error) {\n            console.error(\"❌ 获取系列课程详情异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程详情失败，请重试\");\n            return null;\n        } finally{\n            setPublishLoading(false);\n        }\n    };\n    // 获取发布弹窗的系列课程列表\n    const fetchPublishSeriesList = async ()=>{\n        try {\n            var _res_data;\n            setPublishFormLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取发布弹窗的系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50\n            });\n            if (res.code === 200 && ((_res_data = res.data) === null || _res_data === void 0 ? void 0 : _res_data.list)) {\n                console.log(\"✅ 获取发布弹窗系列课程列表成功:\", res.data.list);\n                setPublishSeriesListForModal(res.data.list);\n            } else {\n                console.error(\"❌ 获取发布弹窗系列课程列表失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"获取系列课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取发布弹窗系列课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取系列课程列表失败，请重试\");\n        } finally{\n            setPublishFormLoading(false);\n        }\n    };\n    // 获取指定系列下的子课程列表（用于发布弹窗）\n    const fetchPublishCourseList = async (seriesId)=>{\n        try {\n            console.log(\"\\uD83D\\uDCDD 获取发布弹窗的子课程列表，系列ID:\", seriesId);\n            // 首先尝试使用课程市场API\n            try {\n                console.log(\"\\uD83D\\uDD04 尝试使用课程市场API获取系列详情...\");\n                const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeriesDetail(seriesId);\n                console.log(\"\\uD83D\\uDD0D 课程市场API响应:\", res);\n                if (res.code === 200 && res.data) {\n                    // 检查不同可能的数据结构\n                    let courseList = [];\n                    if (res.data.courseList) {\n                        courseList = res.data.courseList;\n                        console.log(\"✅ 从courseList字段获取子课程:\", courseList);\n                    } else if (res.data.courses) {\n                        courseList = res.data.courses;\n                        console.log(\"✅ 从courses字段获取子课程:\", courseList);\n                    } else if (Array.isArray(res.data)) {\n                        courseList = res.data;\n                        console.log(\"✅ 直接从data数组获取子课程:\", courseList);\n                    } else {\n                        console.log(\"⚠️ 课程市场API未找到子课程数据，尝试备用API...\");\n                        throw new Error(\"No course list found in marketplace API\");\n                    }\n                    if (courseList && courseList.length > 0) {\n                        console.log(\"✅ 设置子课程列表，数量:\", courseList.length);\n                        setPublishCourseListForModal(courseList);\n                        return;\n                    }\n                }\n            } catch (marketplaceError) {\n                console.log(\"⚠️ 课程市场API失败，尝试使用课程管理API...\");\n                // 备用方案：使用课程管理API\n                try {\n                    var _res2_data;\n                    const { data: res2 } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getSeriesCourseList(seriesId, {\n                        page: 1,\n                        pageSize: 50\n                    });\n                    console.log(\"\\uD83D\\uDD0D 课程管理API响应:\", res2);\n                    if (res2.code === 200 && ((_res2_data = res2.data) === null || _res2_data === void 0 ? void 0 : _res2_data.list)) {\n                        console.log(\"✅ 从课程管理API获取子课程列表成功:\", res2.data.list);\n                        setPublishCourseListForModal(res2.data.list);\n                        return;\n                    }\n                } catch (managementError) {\n                    console.error(\"❌ 课程管理API也失败:\", managementError);\n                }\n            }\n            // 如果所有API都失败\n            console.log(\"⚠️ 该系列暂无子课程或API调用失败\");\n            setPublishCourseListForModal([]);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"该系列暂无子课程\");\n        } catch (error) {\n            console.error(\"❌ 获取发布弹窗子课程列表异常:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"获取子课程列表失败，请重试\");\n            setPublishCourseListForModal([]);\n        }\n    };\n    // 处理系列选择（发布弹窗）\n    const handlePublishSeriesChange = async (seriesId)=>{\n        console.log(\"\\uD83D\\uDCDA 发布弹窗选择系列ID:\", seriesId);\n        console.log(\"\\uD83D\\uDCDA 当前系列列表:\", publishSeriesListForModal);\n        setSelectedSeriesForPublish(seriesId);\n        setSelectedCourseForPublish(undefined);\n        setPublishCourseListForModal([]);\n        // 重置表单中的课程选择\n        publishCourseForm.setFieldsValue({\n            courseId: undefined\n        });\n        // 获取该系列下的子课程\n        if (seriesId) {\n            console.log(\"\\uD83D\\uDD04 开始获取系列子课程...\");\n            setPublishFormLoading(true);\n            try {\n                await fetchPublishCourseList(seriesId);\n                console.log(\"✅ 子课程获取完成\");\n            } catch (error) {\n                console.error(\"❌ 子课程获取失败:\", error);\n            } finally{\n                setPublishFormLoading(false);\n            }\n        }\n    };\n    // 处理课程选择（发布弹窗）\n    const handlePublishCourseChange = (courseId)=>{\n        console.log(\"\\uD83D\\uDCD6 发布弹窗选择课程ID:\", courseId);\n        setSelectedCourseForPublish(courseId);\n    };\n    // 重置发布课程弹窗状态\n    const resetPublishCourseModal = ()=>{\n        setIsPublishCourseModalVisible(false);\n        setSelectedSeriesForPublish(undefined);\n        setSelectedCourseForPublish(undefined);\n        setPublishSeriesListForModal([]);\n        setPublishCourseListForModal([]);\n        publishCourseForm.resetFields();\n    };\n    // 打开发布课程弹窗\n    const openPublishCourseModal = async ()=>{\n        setIsPublishCourseModalVisible(true);\n        await fetchPublishSeriesList();\n    };\n    // 发布课程\n    const handlePublishCourse = async (values)=>{\n        try {\n            if (!selectedCourseForPublish || !selectedSeriesForPublish) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"请选择系列课程和子课程\");\n                return;\n            }\n            setPublishFormLoading(true);\n            console.log(\"\\uD83D\\uDCE2 发布课程，课程ID:\", selectedCourseForPublish);\n            console.log(\"\\uD83D\\uDCE2 系列ID:\", selectedSeriesForPublish);\n            console.log(\"\\uD83D\\uDCE4 表单数据:\", values);\n            // 获取当前选中的课程信息\n            const selectedCourse = publishCourseListForModal.find((c)=>c.id === selectedCourseForPublish);\n            if (!selectedCourse) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"未找到选中的课程信息\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDCD6 当前课程信息:\", selectedCourse);\n            // 使用专门的发布课程API\n            console.log(\"\\uD83D\\uDCE4 调用发布课程API，课程ID:\", selectedCourseForPublish);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourse(selectedCourseForPublish);\n            if (res.code === 200) {\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"发布课程成功\");\n                resetPublishCourseModal();\n                // 显示发布结果信息\n                console.log(\"✅ 发布成功，课程信息:\", res.data);\n                // 刷新课程列表\n                await fetchCourseList();\n                // 如果当前系列已展开，刷新子课程列表\n                if (selectedSeriesForPublish && expandedSeries.has(selectedSeriesForPublish)) {\n                    await fetchSeriesCourses(selectedSeriesForPublish);\n                }\n            } else {\n                console.error(\"❌ 发布课程失败:\", res.msg);\n                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(res.msg || \"发布课程失败\");\n            }\n        } catch (error) {\n            var _error_response;\n            console.error(\"❌ 发布课程异常:\", error);\n            console.error(\"❌ 错误详情:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"发布课程失败，请重试\");\n        } finally{\n            setPublishFormLoading(false);\n        }\n    };\n    // 重置发布弹窗状态\n    const resetPublishModal = ()=>{\n        setIsPublishCourseModalVisible(false);\n        setSelectedSeriesForPublish(undefined);\n        setSelectedCourseForPublish(undefined);\n        setPublishSeriesCourses([]);\n        setPublishSeriesOptions([]);\n        publishCourseForm.resetFields();\n    };\n    // 处理图片上传\n    const handleImageUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"系列封面图片上传成功，URL:\", url);\n            setCoverImageUrl(url);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"图片上传成功\");\n        } catch (error) {\n            console.error(\"系列封面图片上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理图片删除\n    const handleImageRemove = async ()=>{\n        setCoverImageUrl(\"\");\n        return true;\n    };\n    // 处理课程封面图片上传\n    const handleCourseCoverUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程封面图片上传成功，URL:\", url);\n            setCourseCoverImageUrl(url);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程封面上传成功\");\n        } catch (error) {\n            console.error(\"课程封面图片上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理课程封面删除\n    const handleCourseCoverRemove = async ()=>{\n        setCourseCoverImageUrl(\"\");\n        return true;\n    };\n    // 处理附件资源上传\n    const handleAdditionalResourceUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"附件资源上传成功，URL:\", url);\n            setAdditionalFiles((prev)=>[\n                    ...prev,\n                    url\n                ]);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url,\n                name: file.name\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"附件 \".concat(file.name, \" 上传成功\"));\n        } catch (error) {\n            console.error(\"附件资源上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"附件 \".concat(file.name, \" 上传失败: \").concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理附件删除\n    const handleAdditionalResourceRemove = async (file)=>{\n        var _file_response;\n        const url = file.url || ((_file_response = file.response) === null || _file_response === void 0 ? void 0 : _file_response.url);\n        setAdditionalFiles((prev)=>prev.filter((f)=>f !== url));\n        return true;\n    };\n    // 处理视频上传\n    const handleVideoUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程视频上传成功，URL:\", url);\n            setCourseVideoUrl(url);\n            setCourseVideoName(file.name);\n            // 如果是视频文件，尝试获取时长\n            const videoElement = document.createElement(\"video\");\n            videoElement.src = url;\n            videoElement.onloadedmetadata = ()=>{\n                setVideoDuration(Math.floor(videoElement.duration));\n            };\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程视频上传成功\");\n        } catch (error) {\n            console.error(\"课程视频上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"视频上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理视频删除\n    const handleVideoRemove = async ()=>{\n        setCourseVideoUrl(\"\");\n        setCourseVideoName(\"\");\n        setVideoDuration(0);\n        return true;\n    };\n    // 处理文档上传\n    const handleDocumentUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程文档上传成功，URL:\", url);\n            setCourseDocumentUrl(url);\n            setCourseDocumentName(file.name);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程文档上传成功\");\n        } catch (error) {\n            console.error(\"课程文档上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"文档上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理文档删除\n    const handleDocumentRemove = async ()=>{\n        setCourseDocumentUrl(\"\");\n        setCourseDocumentName(\"\");\n        return true;\n    };\n    // 处理音频上传\n    const handleAudioUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程音频上传成功，URL:\", url);\n            setCourseAudioUrl(url);\n            setCourseAudioName(file.name);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程音频上传成功\");\n        } catch (error) {\n            console.error(\"课程音频上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"音频上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理音频删除\n    const handleAudioRemove = async ()=>{\n        setCourseAudioUrl(\"\");\n        setCourseAudioName(\"\");\n        return true;\n    };\n    // 打开编辑模态框\n    const openEditModal = async (course)=>{\n        setEditingCourse(course);\n        editCourseForm.setFieldsValue(course);\n        setIsEditCourseModalVisible(true);\n    };\n    // 过滤课程列表\n    const filteredCourses = (courseList || []).filter((course)=>course.name.toLowerCase().includes(searchKeyword.toLowerCase()) || course.description.toLowerCase().includes(searchKeyword.toLowerCase()) || course.category.toLowerCase().includes(searchKeyword.toLowerCase()));\n    // 准备表格数据：将系列课程和子课程合并为一个扁平列表\n    const prepareTableData = ()=>{\n        const tableData = [];\n        console.log(\"\\uD83D\\uDD04 准备表格数据...\");\n        console.log(\"\\uD83D\\uDCCA 系列课程列表:\", seriesList);\n        console.log(\"\\uD83D\\uDCCA 展开的系列:\", Array.from(expandedSeries));\n        console.log(\"\\uD83D\\uDCCA 子课程映射:\", seriesCoursesMap);\n        seriesList.forEach((series)=>{\n            // 添加系列课程行\n            tableData.push({\n                key: \"series-\".concat(series.id),\n                id: series.id,\n                title: series.title,\n                status: series.status,\n                type: \"series\",\n                isExpanded: expandedSeries.has(series.id),\n                seriesId: series.id\n            });\n            // 如果系列已展开，添加子课程行\n            if (expandedSeries.has(series.id)) {\n                const subCourses = seriesCoursesMap.get(series.id) || [];\n                console.log(\"\\uD83D\\uDCDA 系列 \".concat(series.id, \" 的子课程:\"), subCourses);\n                subCourses.forEach((course)=>{\n                    tableData.push({\n                        key: \"course-\".concat(course.id),\n                        id: course.id,\n                        title: course.title,\n                        status: course.status,\n                        type: \"course\",\n                        seriesId: series.id,\n                        parentSeriesTitle: series.title\n                    });\n                });\n            }\n        });\n        console.log(\"\\uD83D\\uDCCB 最终表格数据:\", tableData);\n        return tableData;\n    };\n    // 表格列定义\n    const columns = [\n        {\n            title: \"系列课程ID\",\n            dataIndex: \"id\",\n            key: \"id\",\n            width: 120\n        },\n        {\n            title: \"系列课程/子课程名称\",\n            dataIndex: \"title\",\n            key: \"title\",\n            render: (text, record)=>{\n                if (record.type === \"series\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                type: \"text\",\n                                size: \"small\",\n                                onClick: ()=>toggleSeriesExpansion(record.id),\n                                className: \"p-0 min-w-0 hover:bg-blue-50\",\n                                style: {\n                                    minWidth: \"20px\",\n                                    height: \"20px\"\n                                },\n                                children: record.isExpanded ? \"▼\" : \"▶\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 981,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-blue-600 text-base\",\n                                children: text\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 990,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                color: \"blue\",\n                                className: \"text-xs\",\n                                children: \"系列\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 991,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 980,\n                        columnNumber: 13\n                    }, undefined);\n                } else {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-8 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400\",\n                                children: \"└─\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 997,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-700\",\n                                children: text\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 998,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                color: \"green\",\n                                className: \"text-xs\",\n                                children: \"子课程\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 999,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 996,\n                        columnNumber: 13\n                    }, undefined);\n                }\n            }\n        },\n        {\n            title: \"发布状态\",\n            dataIndex: \"status\",\n            key: \"status\",\n            width: 100,\n            render: (status, record)=>{\n                const getStatusConfig = (status)=>{\n                    switch(status){\n                        case 1:\n                            return {\n                                color: \"green\",\n                                text: \"已发布\"\n                            };\n                        case 0:\n                            return {\n                                color: \"orange\",\n                                text: \"草稿\"\n                            };\n                        case 2:\n                            return {\n                                color: \"red\",\n                                text: \"已归档\"\n                            };\n                        default:\n                            return {\n                                color: \"gray\",\n                                text: \"未知\"\n                            };\n                    }\n                };\n                const config = getStatusConfig(status);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    color: config.color,\n                    children: config.text\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1021,\n                    columnNumber: 16\n                }, undefined);\n            }\n        },\n        {\n            title: \"操作\",\n            key: \"action\",\n            width: 150,\n            render: (record)=>{\n                if (record.type === \"series\") {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        size: \"small\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            type: \"link\",\n                            size: \"small\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1035,\n                                columnNumber: 23\n                            }, void 0),\n                            onClick: ()=>{\n                                _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"系列课程编辑功能待实现\");\n                            },\n                            children: \"编辑\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1032,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1031,\n                        columnNumber: 13\n                    }, undefined);\n                } else {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        size: \"small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                type: \"link\",\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1050,\n                                    columnNumber: 23\n                                }, void 0),\n                                onClick: ()=>{\n                                    _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].info(\"子课程编辑功能待实现\");\n                                },\n                                className: \"text-blue-600 hover:text-blue-800\",\n                                children: \"编辑\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1047,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"确定要删除这个子课程吗？\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1061,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: [\n                                                \"课程名称：\",\n                                                record.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1062,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: [\n                                                \"所属系列：\",\n                                                record.parentSeriesTitle\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1063,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1060,\n                                    columnNumber: 19\n                                }, void 0),\n                                onConfirm: ()=>{\n                                    console.log(\"\\uD83D\\uDDD1️ 用户确认删除子课程:\", record);\n                                    handleDeleteSubCourse(record.id, record.seriesId);\n                                },\n                                okText: \"确定删除\",\n                                cancelText: \"取消\",\n                                okType: \"danger\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    type: \"link\",\n                                    size: \"small\",\n                                    danger: true,\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1078,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    className: \"text-red-600 hover:text-red-800\",\n                                    children: \"删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1074,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1058,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1046,\n                        columnNumber: 13\n                    }, undefined);\n                }\n            }\n        }\n    ];\n    // 获取教师列表\n    // const fetchTeachers = async () => {\n    //   try {\n    //     const { data: res } = await courseApi.getTeachers();\n    //     if (res.code === 200) {\n    //       setTeachers(res.data);\n    //       console.log('成功获取教师列表:', res.data);\n    //     } else {\n    //       console.log('API返回无数据，使用模拟教师数据');\n    //       // 使用模拟数据\n    //       const mockTeachers = [\n    //         { id: 1, name: '张老师', email: '<EMAIL>', subject: '数学', school: '实验小学', avatar: '', phone: '13800138001' },\n    //         { id: 2, name: '李老师', email: '<EMAIL>', subject: '语文', school: '实验小学', avatar: '', phone: '13800138002' },\n    //         { id: 3, name: '王老师', email: '<EMAIL>', subject: '英语', school: '第二小学', avatar: '', phone: '13800138003' },\n    //         { id: 4, name: '赵老师', email: '<EMAIL>', subject: '科学', school: '第二小学', avatar: '', phone: '13800138004' },\n    //         { id: 5, name: '刘老师', email: '<EMAIL>', subject: '编程', school: '实验中学', avatar: '', phone: '13800138005' },\n    //         { id: 6, name: '陈老师', email: '<EMAIL>', subject: '信息技术', school: '实验中学', avatar: '', phone: '13800138006' }\n    //       ];\n    //       setTeachers(mockTeachers);\n    //     }\n    //   } catch (error) {\n    //     console.error('获取教师列表失败:', error);\n    //     // 使用模拟数据\n    //     const mockTeachers = [\n    //       { id: 1, name: '张老师', email: '<EMAIL>', subject: '数学', school: '实验小学', avatar: '', phone: '13800138001' },\n    //       { id: 2, name: '李老师', email: '<EMAIL>', subject: '语文', school: '实验小学', avatar: '', phone: '13800138002' },\n    //       { id: 3, name: '王老师', email: '<EMAIL>', subject: '英语', school: '第二小学', avatar: '', phone: '13800138003' },\n    //       { id: 4, name: '赵老师', email: '<EMAIL>', subject: '科学', school: '第二小学', avatar: '', phone: '13800138004' },\n    //       { id: 5, name: '刘老师', email: '<EMAIL>', subject: '编程', school: '实验中学', avatar: '', phone: '13800138005' },\n    //       { id: 6, name: '陈老师', email: '<EMAIL>', subject: '信息技术', school: '实验中学', avatar: '', phone: '13800138006' }\n    //     ];\n    //     setTeachers(mockTeachers);\n    //     console.log('使用模拟教师数据:', mockTeachers);\n    //   }\n    // };\n    // 获取课程标签列表 - 使用课程市场API\n    const fetchCourseTags = async ()=>{\n        try {\n            console.log(\"\\uD83C\\uDFF7️ 开始获取课程标签列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseTags({\n                page: 1,\n                pageSize: 100,\n                status: 1 // 只获取启用的标签\n            });\n            console.log(\"\\uD83D\\uDCE8 getCourseTags API响应:\", res);\n            if (res.code === 200 && res.data && res.data.list) {\n                const tags = res.data.list.map((tag)=>({\n                        id: tag.id,\n                        name: tag.name,\n                        color: tag.color,\n                        category: tag.category,\n                        description: tag.description || \"\"\n                    }));\n                setCourseTags(tags);\n                console.log(\"✅ 成功获取课程标签列表:\", tags);\n            } else {\n                console.warn(\"⚠️ API返回数据格式异常:\", res);\n                setCourseTags([]);\n                notification.warning(\"获取标签列表失败，请检查网络连接\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取课程标签失败:\", error);\n            setCourseTags([]);\n            notification.error(\"获取标签列表失败，请重试\");\n        }\n    };\n    // 获取课程系列列表 - 使用课程市场API\n    const fetchCourseSeries = async ()=>{\n        try {\n            var _res_data_pagination, _res_data;\n            console.log(\"\\uD83D\\uDD04 开始获取课程市场系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50 // 课程市场API限制最大50\n            });\n            console.log(\"\\uD83D\\uDCE8 getMarketplaceSeries API响应:\", res);\n            // 检查是否有更多数据\n            if (((_res_data = res.data) === null || _res_data === void 0 ? void 0 : (_res_data_pagination = _res_data.pagination) === null || _res_data_pagination === void 0 ? void 0 : _res_data_pagination.total) > 50) {\n                console.log(\"⚠️ 注意：总共有 \".concat(res.data.pagination.total, \" 个系列课程，当前只显示前50个\"));\n            }\n            if (res.code === 200 && res.data) {\n                console.log(\"\\uD83D\\uDCCA API返回的完整数据结构:\", res.data);\n                if (res.data.list && Array.isArray(res.data.list)) {\n                    console.log(\"\\uD83D\\uDCCB 获取到 \".concat(res.data.list.length, \" 个系列课程\"));\n                    // 将课程市场API返回的数据转换为组件需要的格式\n                    const formattedSeries = res.data.list.map((item, index)=>{\n                        var _item_tags;\n                        console.log(\"\\uD83D\\uDD0D 处理第 \".concat(index + 1, \" 个系列:\"), {\n                            id: item.id,\n                            title: item.title,\n                            category: item.category,\n                            categoryLabel: item.categoryLabel,\n                            tags: item.tags\n                        });\n                        return {\n                            id: item.id,\n                            title: item.title,\n                            description: item.description,\n                            coverImage: item.coverImage || \"\",\n                            category: item.categoryLabel || (item.category === 0 ? \"官方\" : \"社区\"),\n                            teacherIds: [],\n                            tagIds: ((_item_tags = item.tags) === null || _item_tags === void 0 ? void 0 : _item_tags.map((tag)=>tag.id)) || [],\n                            createdAt: item.createdAt || new Date().toISOString(),\n                            updatedAt: item.updatedAt || new Date().toISOString()\n                        };\n                    });\n                    setCourseSeries(formattedSeries);\n                    console.log(\"✅ 成功获取系列课程列表:\", formattedSeries);\n                } else {\n                    console.warn(\"⚠️ API返回数据中没有list字段或list不是数组:\", res.data);\n                    setCourseSeries([]);\n                }\n            } else {\n                console.warn(\"⚠️ API返回数据格式异常:\", {\n                    code: res.code,\n                    message: res.message,\n                    data: res.data\n                });\n                setCourseSeries([]);\n            }\n        } catch (error) {\n            console.error(\"❌ 获取课程系列失败:\", error);\n            setCourseSeries([]);\n            notification.error(\"获取系列课程列表失败，请重试\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchCourseList();\n        fetchCourseTags();\n        fetchCourseSeries();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                title: \"课程管理\",\n                extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    type: \"primary\",\n                    onClick: ()=>{\n                        fetchCourseList();\n                        setIsCourseModalVisible(true);\n                    },\n                    children: \"查看全部\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1239,\n                    columnNumber: 16\n                }, void 0),\n                className: \"shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddCourseModalVisible(true),\n                            children: \"添加课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1246,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddSeriesModalVisible(true),\n                            children: \"添加系列课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1249,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddTagModalVisible(true),\n                            type: \"dashed\",\n                            children: \"添加课程标签\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1252,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: openPublishCourseModal,\n                            style: {\n                                backgroundColor: \"white\",\n                                borderColor: \"#d9d9d9\",\n                                color: \"#000000d9\"\n                            },\n                            children: \"发布课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1255,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsPublishSeriesModalVisible(true),\n                            style: {\n                                backgroundColor: \"white\",\n                                borderColor: \"#d9d9d9\",\n                                color: \"#000000d9\"\n                            },\n                            children: \"发布系列课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1258,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1245,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1237,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"课程管理\",\n                open: isCourseModalVisible,\n                onCancel: ()=>setIsCourseModalVisible(false),\n                footer: null,\n                width: 1000,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Search, {\n                                        placeholder: \"搜索系列课程名称\",\n                                        allowClear: true,\n                                        style: {\n                                            width: 300\n                                        },\n                                        onSearch: setSearchKeyword,\n                                        onChange: (e)=>setSearchKeyword(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1274,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                type: \"primary\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1284,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                onClick: ()=>setIsAddCourseModalVisible(true),\n                                                children: \"添加课程\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1282,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                type: \"default\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1291,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                onClick: ()=>setIsAddSeriesModalVisible(true),\n                                                children: \"添加系列课程\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1289,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1281,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1273,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center bg-gray-50 p-3 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-4 text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"系列课程总数: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"text-blue-600\",\n                                                        children: seriesList.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                        lineNumber: 1302,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1302,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"已展开系列: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"text-green-600\",\n                                                        children: expandedSeries.size\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                        lineNumber: 1303,\n                                                        columnNumber: 28\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1303,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"已加载子课程: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        className: \"text-orange-600\",\n                                                        children: Array.from(seriesCoursesMap.values()).reduce((total, courses)=>total + courses.length, 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                        lineNumber: 1304,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1304,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1301,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: \"small\",\n                                                type: \"text\",\n                                                onClick: expandAllSeries,\n                                                disabled: seriesLoading,\n                                                className: \"text-blue-600 hover:text-blue-800\",\n                                                children: \"展开所有\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1310,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: \"small\",\n                                                type: \"text\",\n                                                onClick: collapseAllSeries,\n                                                disabled: seriesLoading,\n                                                className: \"text-gray-600 hover:text-gray-800\",\n                                                children: \"收起所有\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1319,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1309,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1300,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1272,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        columns: columns,\n                        dataSource: prepareTableData(),\n                        rowKey: \"key\",\n                        loading: seriesLoading,\n                        pagination: {\n                            pageSize: 20,\n                            showSizeChanger: false,\n                            showTotal: (total)=>\"共 \".concat(total, \" 条记录\")\n                        },\n                        size: \"small\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 1332,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1265,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"添加课程\",\n                open: isAddCourseModalVisible,\n                onCancel: ()=>{\n                    setIsAddCourseModalVisible(false);\n                    addCourseForm.resetFields();\n                    setCourseCoverImageUrl(\"\");\n                    setAdditionalFiles([]);\n                    setCourseVideoUrl(\"\");\n                    setCourseVideoName(\"\");\n                    setCourseDocumentUrl(\"\");\n                    setCourseDocumentName(\"\");\n                    setCourseAudioUrl(\"\");\n                    setCourseAudioName(\"\");\n                    setVideoDuration(0);\n                },\n                onOk: ()=>addCourseForm.submit(),\n                okText: \"确定\",\n                cancelText: \"取消\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddCourse,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"所属系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择所属系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                showSearch: true,\n                                optionFilterProp: \"children\",\n                                style: {\n                                    width: \"100%\"\n                                },\n                                children: courseSeries.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        title: \"\".concat(series.title, \" - \").concat(series.description),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                overflow: \"hidden\",\n                                                textOverflow: \"ellipsis\",\n                                                whiteSpace: \"nowrap\",\n                                                maxWidth: \"100%\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontWeight: 500\n                                                    },\n                                                    children: series.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1391,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"12px\",\n                                                        color: \"#666\",\n                                                        marginLeft: \"8px\"\n                                                    },\n                                                    children: [\n                                                        \"(\",\n                                                        series.category,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1392,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1385,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, series.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1384,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1377,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1372,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"title\",\n                            label: \"课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程名称\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1406,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1401,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程描述\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程描述\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 4,\n                                placeholder: \"请详细描述课程内容、目标和特色...\",\n                                showCount: true,\n                                maxLength: 500\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1414,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1409,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程封面\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请上传课程封面\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseCover\",\n                                customRequest: handleCourseCoverUpload,\n                                onRemove: handleCourseCoverRemove,\n                                accept: \"image/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseCoverImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: courseCoverImageUrl,\n                                        alt: \"课程封面预览\",\n                                        style: {\n                                            width: \"100%\",\n                                            maxHeight: \"200px\",\n                                            objectFit: \"cover\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1436,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1435,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1441,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1440,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1443,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持单个文件上传，建议上传jpg、png格式图片，大小不超过2MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1444,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1439,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1426,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1422,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"orderIndex\",\n                            label: \"课程序号\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程序号\"\n                                },\n                                {\n                                    type: \"number\",\n                                    min: 0,\n                                    message: \"课程序号必须大于等于0\",\n                                    transform: (value)=>Number(value)\n                                }\n                            ],\n                            tooltip: \"在系列课程中的排序位置，数字越小排序越靠前，从0开始\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                type: \"number\",\n                                placeholder: \"请输入课程在系列中的序号（从0开始）\",\n                                min: 0\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1466,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1452,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程视频\",\n                            tooltip: \"上传课程视频文件，系统将自动识别时长等信息\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseVideo\",\n                                customRequest: handleVideoUpload,\n                                onRemove: handleVideoRemove,\n                                accept: \"video/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseVideoUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            src: courseVideoUrl,\n                                            style: {\n                                                width: \"100%\",\n                                                maxHeight: \"200px\"\n                                            },\n                                            controls: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1486,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                marginTop: 8,\n                                                color: \"#666\"\n                                            },\n                                            children: courseVideoName || \"课程视频\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1491,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1485,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1498,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1497,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽视频文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1500,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持MP4、AVI、MOV等格式，大小不超过100MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1501,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1496,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1476,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1472,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程文档\",\n                            tooltip: \"上传课程相关文档，如PPT、PDF等\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseDocument\",\n                                customRequest: handleDocumentUpload,\n                                onRemove: handleDocumentRemove,\n                                accept: \".pdf,.doc,.docx,.ppt,.pptx\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseDocumentUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"20px\",\n                                            textAlign: \"center\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                style: {\n                                                    fontSize: \"48px\",\n                                                    color: \"#1890ff\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1525,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    marginTop: 8,\n                                                    color: \"#666\"\n                                                },\n                                                children: courseDocumentName || \"课程文档\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1526,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1524,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1523,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1534,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1533,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文档文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1536,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持PDF、Word、PPT格式，大小不超过50MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1537,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1532,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1514,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1510,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程音频\",\n                            tooltip: \"上传课程音频文件\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseAudio\",\n                                customRequest: handleAudioUpload,\n                                onRemove: handleAudioRemove,\n                                accept: \"audio/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseAudioUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                                            src: courseAudioUrl,\n                                            style: {\n                                                width: \"100%\"\n                                            },\n                                            controls: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1560,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                marginTop: 8,\n                                                color: \"#666\"\n                                            },\n                                            children: courseAudioName || \"课程音频\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1565,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1559,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1572,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1571,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽音频文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1574,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持MP3、WAV、AAC等格式，大小不超过50MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1575,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1570,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1550,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1546,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"teachingObjectives\",\n                            label: \"教学目标\",\n                            tooltip: \"学员完成本课程后应该达到的学习目标\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                mode: \"tags\",\n                                placeholder: \"理解Node.js的基本概念和特点，掌握Node.js的安装和环境配置\",\n                                style: {\n                                    width: \"100%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1588,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1583,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"附件资源\",\n                            tooltip: \"上传课程相关的附件资源，如PPT、文档、代码等\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    name: \"additionalResources\",\n                                    customRequest: handleAdditionalResourceUpload,\n                                    onRemove: handleAdditionalResourceRemove,\n                                    multiple: true,\n                                    accept: \".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.zip,.rar,.txt\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1606,\n                                            columnNumber: 29\n                                        }, void 0),\n                                        children: \"上传附件资源\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1606,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1599,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: \"12px\",\n                                        color: \"#666\",\n                                        marginTop: 4\n                                    },\n                                    children: \"支持上传PDF、Office文档、压缩包等格式文件，单个文件不超过10MB\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1608,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1595,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1367,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1347,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"编辑课程\",\n                open: isEditCourseModalVisible,\n                onCancel: ()=>{\n                    setIsEditCourseModalVisible(false);\n                    setEditingCourse(null);\n                    editCourseForm.resetFields();\n                },\n                onOk: ()=>editCourseForm.submit(),\n                okText: \"确定\",\n                cancelText: \"取消\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: editCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handleEditCourse,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"name\",\n                            label: \"课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程名称\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1638,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1633,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程描述\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程描述\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 3,\n                                placeholder: \"请输入课程描述\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1646,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1641,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"课程分类\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择课程分类\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择课程分类\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"编程基础\",\n                                        children: \"编程基础\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1655,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"编程进阶\",\n                                        children: \"编程进阶\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1656,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"算法思维\",\n                                        children: \"算法思维\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1657,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"项目实战\",\n                                        children: \"项目实战\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1658,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1654,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1649,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"status\",\n                            label: \"课程状态\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择课程状态\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"active\",\n                                        children: \"启用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1668,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"inactive\",\n                                        children: \"禁用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1669,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1667,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1662,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1628,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1616,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"创建系列课程\",\n                open: isAddSeriesModalVisible,\n                onCancel: ()=>{\n                    setIsAddSeriesModalVisible(false);\n                    addSeriesForm.resetFields();\n                    setCoverImageUrl(\"\");\n                },\n                onOk: ()=>addSeriesForm.submit(),\n                okText: \"创建系列课程\",\n                cancelText: \"取消\",\n                width: 800,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addSeriesForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddSeries,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"title\",\n                            label: \"系列课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入系列课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"例如：React全栈开发实战\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1699,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1694,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程介绍\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程介绍\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 4,\n                                placeholder: \"请详细描述系列课程的内容、目标和特色...\",\n                                showCount: true,\n                                maxLength: 500\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1707,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1702,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"封面图片\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请上传封面图片\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"coverImage\",\n                                customRequest: handleImageUpload,\n                                onRemove: handleImageRemove,\n                                accept: \"image/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: coverImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: coverImageUrl,\n                                        alt: \"封面预览\",\n                                        style: {\n                                            width: \"100%\",\n                                            maxHeight: \"200px\",\n                                            objectFit: \"cover\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1729,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1728,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1734,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1733,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1736,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持单个文件上传，建议上传jpg、png格式图片，大小不超过2MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1737,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1732,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1719,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1715,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"是否为官方系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择是否为官方系列课程\"\n                                }\n                            ],\n                            initialValue: 0,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"是（官方）\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1754,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"否（社区）\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1755,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1753,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1747,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"projectMembers\",\n                            label: \"课程成员\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程成员\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程成员，如：王老师、李助教、张同学\",\n                                showCount: true,\n                                maxLength: 200\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1764,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1759,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"tagIds\",\n                            label: \"标签选择\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                mode: \"multiple\",\n                                placeholder: \"请选择相关标签\",\n                                optionLabelProp: \"label\",\n                                children: courseTags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: tag.id,\n                                        label: tag.name,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            color: tag.color,\n                                            children: tag.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1783,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, tag.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1782,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1776,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1771,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1689,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1676,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"创建课程标签\",\n                open: isAddTagModalVisible,\n                onCancel: ()=>{\n                    setIsAddTagModalVisible(false);\n                    addTagForm.resetFields();\n                },\n                onOk: ()=>addTagForm.submit(),\n                okText: \"创建标签\",\n                cancelText: \"取消\",\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addTagForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddTag,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"name\",\n                            label: \"标签名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入标签名称\"\n                                },\n                                {\n                                    max: 20,\n                                    message: \"标签名称不能超过20个字符\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"例如：高级、编程、实战\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1817,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1809,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"color\",\n                            label: \"标签颜色\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签颜色\"\n                                }\n                            ],\n                            initialValue: \"#007bff\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签颜色\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#007bff\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#007bff\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1829,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"蓝色 (#007bff)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1828,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1827,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#28a745\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#28a745\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1835,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"绿色 (#28a745)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1834,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1833,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#dc3545\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#dc3545\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1841,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"红色 (#dc3545)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1840,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1839,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#ffc107\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#ffc107\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1847,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"黄色 (#ffc107)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1846,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1845,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#6f42c1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#6f42c1\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1853,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"紫色 (#6f42c1)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1852,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1851,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#fd7e14\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#fd7e14\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1859,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"橙色 (#fd7e14)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1858,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1857,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#20c997\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#20c997\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1865,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"青色 (#20c997)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1864,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1863,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#6c757d\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#6c757d\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1871,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"灰色 (#6c757d)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1870,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1869,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1826,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1820,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"标签分类\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签分类\"\n                                }\n                            ],\n                            initialValue: 1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签分类\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"难度标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1885,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"类型标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1886,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 2,\n                                        children: \"特色标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1887,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 3,\n                                        children: \"其他标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1888,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1884,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1878,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"标签描述\",\n                            rules: [\n                                {\n                                    max: 100,\n                                    message: \"标签描述不能超过100个字符\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 3,\n                                placeholder: \"请输入标签的详细描述...\",\n                                showCount: true,\n                                maxLength: 100\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1897,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1892,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"status\",\n                            label: \"标签状态\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签状态\"\n                                }\n                            ],\n                            initialValue: 1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签状态\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"启用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1912,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"禁用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1913,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1911,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1905,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1804,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1792,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"发布系列课程\",\n                open: isPublishSeriesModalVisible,\n                onCancel: ()=>{\n                    setIsPublishSeriesModalVisible(false);\n                    publishSeriesForm.resetFields();\n                },\n                onOk: ()=>publishSeriesForm.submit(),\n                okText: \"发布系列\",\n                cancelText: \"取消\",\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: publishSeriesForm,\n                    layout: \"vertical\",\n                    onFinish: handlePublishSeries,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"选择要发布的系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择要发布的系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                showSearch: true,\n                                filterOption: (input, option)=>{\n                                    var _option_children;\n                                    return option === null || option === void 0 ? void 0 : (_option_children = option.children) === null || _option_children === void 0 ? void 0 : _option_children.toLowerCase().includes(input.toLowerCase());\n                                },\n                                children: courseSeries.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        children: [\n                                            series.title,\n                                            \" (\",\n                                            series.category,\n                                            \")\"\n                                        ]\n                                    }, series.id, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1950,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1942,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1937,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"publishNote\",\n                            label: \"发布说明\",\n                            rules: [\n                                {\n                                    required: false\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                placeholder: \"请输入发布说明（可选）\",\n                                rows: 3,\n                                maxLength: 200,\n                                showCount: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1962,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1957,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"发布说明：\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1971,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-gray-600 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布后系列课程将在课程市场中公开显示\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1973,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 只有已完成的课程才会被发布\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1974,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布后可以查看详细的发布统计信息\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1975,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布状态可以随时修改\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1976,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1972,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1970,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1932,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1920,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"发布课程\",\n                open: isPublishCourseModalVisible,\n                onCancel: resetPublishCourseModal,\n                footer: null,\n                width: 700,\n                destroyOnClose: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: publishCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handlePublishCourse,\n                    className: \"mt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"选择系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                loading: publishFormLoading,\n                                onChange: handlePublishSeriesChange,\n                                showSearch: true,\n                                filterOption: (input, option)=>{\n                                    var _option_children;\n                                    return option === null || option === void 0 ? void 0 : (_option_children = option.children) === null || _option_children === void 0 ? void 0 : _option_children.toLowerCase().includes(input.toLowerCase());\n                                },\n                                children: publishSeriesListForModal.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        children: [\n                                            series.title,\n                                            \" (ID: \",\n                                            series.id,\n                                            \")\"\n                                        ]\n                                    }, series.id, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2013,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2003,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1998,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"courseId\",\n                            label: \"选择子课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择要发布的子课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: selectedSeriesForPublish ? \"请选择要发布的子课程\" : \"请先选择系列课程\",\n                                disabled: !selectedSeriesForPublish,\n                                loading: publishFormLoading && !!selectedSeriesForPublish,\n                                onChange: handlePublishCourseChange,\n                                showSearch: true,\n                                filterOption: (input, option)=>{\n                                    var _option_children;\n                                    return option === null || option === void 0 ? void 0 : (_option_children = option.children) === null || _option_children === void 0 ? void 0 : _option_children.toLowerCase().includes(input.toLowerCase());\n                                },\n                                notFoundContent: publishFormLoading && selectedSeriesForPublish ? \"正在加载子课程...\" : selectedSeriesForPublish ? \"该系列暂无子课程\" : \"请先选择系列课程\",\n                                children: publishCourseListForModal.length > 0 ? publishCourseListForModal.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: course.id,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: course.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 2047,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            color: course.status === 1 ? \"green\" : course.status === 0 ? \"orange\" : \"red\",\n                                                            className: \"text-xs\",\n                                                            children: course.status === 1 ? \"已发布\" : course.status === 0 ? \"草稿\" : \"已归档\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 2049,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400 text-xs\",\n                                                            children: [\n                                                                \"ID: \",\n                                                                course.id\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 2052,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 2048,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2046,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, course.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2045,\n                                        columnNumber: 19\n                                    }, undefined)) : selectedSeriesForPublish && !publishFormLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                    disabled: true,\n                                    value: \"no-courses\",\n                                    children: \"该系列暂无子课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2059,\n                                    columnNumber: 19\n                                }, undefined) : null\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 2026,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 2021,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-3 rounded-lg mb-4 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-gray-700 mb-2\",\n                                    children: \"调试信息\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2069,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1 text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"已选择系列ID: \",\n                                                selectedSeriesForPublish || \"未选择\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2071,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"已选择课程ID: \",\n                                                selectedCourseForPublish || \"未选择\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2072,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"系列列表数量: \",\n                                                publishSeriesListForModal.length\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2073,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"子课程列表数量: \",\n                                                publishCourseListForModal.length\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2074,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"加载状态: \",\n                                                publishFormLoading ? \"加载中\" : \"空闲\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2075,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        publishCourseListForModal.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"子课程列表:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 2078,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"ml-4 list-disc\",\n                                                    children: publishCourseListForModal.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                \"ID: \",\n                                                                course.id,\n                                                                \", 名称: \",\n                                                                course.title\n                                                            ]\n                                                        }, course.id, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                            lineNumber: 2081,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 2079,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2077,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2070,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 2068,\n                            columnNumber: 11\n                        }, undefined),\n                        selectedCourseForPublish && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 p-4 rounded-lg mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-blue-900 mb-2\",\n                                    children: \"即将发布的课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2092,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"课程ID: \",\n                                                selectedCourseForPublish\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2094,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"所属系列: \",\n                                                (_publishSeriesListForModal_find = publishSeriesListForModal.find((s)=>s.id === selectedSeriesForPublish)) === null || _publishSeriesListForModal_find === void 0 ? void 0 : _publishSeriesListForModal_find.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2095,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"课程名称: \",\n                                                (_publishCourseListForModal_find = publishCourseListForModal.find((c)=>c.id === selectedCourseForPublish)) === null || _publishCourseListForModal_find === void 0 ? void 0 : _publishCourseListForModal_find.title\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2096,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-blue-600 font-medium\",\n                                            children: '点击\"发布此课程\"将把该课程状态设置为已发布'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2097,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2093,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 2091,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center pt-4 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: selectedCourseForPublish ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-600\",\n                                        children: \"✓ 已选择课程，可以发布\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2105,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"请先选择系列课程和子课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 2107,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2103,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            onClick: resetPublishCourseModal,\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2111,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            type: \"primary\",\n                                            htmlType: \"submit\",\n                                            loading: publishFormLoading,\n                                            disabled: !selectedCourseForPublish,\n                                            className: selectedCourseForPublish ? \"bg-green-600 hover:bg-green-700 border-green-600\" : \"\",\n                                            children: publishFormLoading ? \"发布中...\" : \"发布此课程\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 2114,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 2110,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 2102,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1991,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1983,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(CourseManagement, \"BJPzNCW7DZRrfGWDDhF5pcIAstQ=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm\n    ];\n});\n_c = CourseManagement;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseManagement);\nvar _c;\n$RefreshReg$(_c, \"CourseManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9hZG1pbi1zcGFjZS9jb21wb25lbnRzL2NvdXJzZS1tYW5hZ2VtZW50LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUU0QztBQUNvRTtBQUNjO0FBQ2xEO0FBQ2dGO0FBQy9HO0FBRzdDLE1BQU0sRUFBRXNCLE1BQU0sRUFBRSxHQUFHZixpSkFBS0E7QUFDeEIsTUFBTSxFQUFFZ0IsTUFBTSxFQUFFLEdBQUdmLGlKQUFNQTtBQXdCekIsTUFBTWdCLG1CQUFvRDtRQTJnRWhDQyxpQ0FDQUM7O0lBM2dFeEIsTUFBTSxDQUFDQyxZQUFZQyxjQUFjLEdBQUc1QiwrQ0FBUUEsQ0FBVyxFQUFFO0lBQ3pELE1BQU0sQ0FBQzZCLFNBQVNDLFdBQVcsR0FBRzlCLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQytCLHNCQUFzQkMsd0JBQXdCLEdBQUdoQywrQ0FBUUEsQ0FBQztJQUNqRSxNQUFNLENBQUNpQyx5QkFBeUJDLDJCQUEyQixHQUFHbEMsK0NBQVFBLENBQUM7SUFDdkUsTUFBTSxDQUFDbUMsMEJBQTBCQyw0QkFBNEIsR0FBR3BDLCtDQUFRQSxDQUFDO0lBQ3pFLE1BQU0sQ0FBQ3FDLHlCQUF5QkMsMkJBQTJCLEdBQUd0QywrQ0FBUUEsQ0FBQztJQUN2RSxNQUFNLENBQUN1QyxzQkFBc0JDLHdCQUF3QixHQUFHeEMsK0NBQVFBLENBQUM7SUFDakUsTUFBTSxDQUFDeUMsNkJBQTZCQywrQkFBK0IsR0FBRzFDLCtDQUFRQSxDQUFDO0lBQy9FLE1BQU0sQ0FBQzJDLDZCQUE2QkMsK0JBQStCLEdBQUc1QywrQ0FBUUEsQ0FBQztJQUMvRSxNQUFNLENBQUM2QyxlQUFlQyxpQkFBaUIsR0FBRzlDLCtDQUFRQSxDQUFnQjtJQUNsRSxNQUFNLENBQUMrQyxlQUFlQyxpQkFBaUIsR0FBR2hELCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ2lELFVBQVVDLFlBQVksR0FBR2xELCtDQUFRQSxDQUFZLEVBQUU7SUFDdEQsTUFBTSxDQUFDbUQsWUFBWUMsY0FBYyxHQUFHcEQsK0NBQVFBLENBQWMsRUFBRTtJQUM1RCxNQUFNLENBQUNxRCxlQUFlQyxpQkFBaUIsR0FBR3RELCtDQUFRQSxDQUFTO0lBRTNELG9CQUFvQjtJQUNwQixNQUFNLENBQUN1RCxZQUFZQyxjQUFjLEdBQUd4RCwrQ0FBUUEsQ0FBUSxFQUFFO0lBQ3RELE1BQU0sQ0FBQ3lELGtCQUFrQkMsb0JBQW9CLEdBQUcxRCwrQ0FBUUEsQ0FBcUIsSUFBSTJEO0lBQ2pGLE1BQU0sQ0FBQ0MsZ0JBQWdCQyxrQkFBa0IsR0FBRzdELCtDQUFRQSxDQUFjLElBQUk4RDtJQUN0RSxNQUFNLENBQUNDLGVBQWVDLGlCQUFpQixHQUFHaEUsK0NBQVFBLENBQUM7SUFFbkQsV0FBVztJQUNYLE1BQU0sQ0FBQ2lFLDBCQUEwQkMsNEJBQTRCLEdBQUdsRSwrQ0FBUUEsQ0FBcUJtRTtJQUM3RixNQUFNLENBQUNDLDBCQUEwQkMsNEJBQTRCLEdBQUdyRSwrQ0FBUUEsQ0FBcUJtRTtJQUM3RixNQUFNLENBQUNHLHNCQUFzQkMsd0JBQXdCLEdBQUd2RSwrQ0FBUUEsQ0FBUSxFQUFFO0lBQzFFLE1BQU0sQ0FBQ3dFLGdCQUFnQkMsa0JBQWtCLEdBQUd6RSwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUMwRSxzQkFBc0JDLHdCQUF3QixHQUFHM0UsK0NBQVFBLENBQVEsRUFBRTtJQUUxRSxXQUFXO0lBQ1gsTUFBTSxDQUFDeUIsMkJBQTJCbUQsNkJBQTZCLEdBQUc1RSwrQ0FBUUEsQ0FBUSxFQUFFO0lBQ3BGLE1BQU0sQ0FBQzBCLDJCQUEyQm1ELDZCQUE2QixHQUFHN0UsK0NBQVFBLENBQVEsRUFBRTtJQUNwRixNQUFNLENBQUM4RSxvQkFBb0JDLHNCQUFzQixHQUFHL0UsK0NBQVFBLENBQUM7SUFFN0QsTUFBTSxDQUFDZ0YsY0FBY0MsZ0JBQWdCLEdBQUdqRiwrQ0FBUUEsQ0FBaUIsRUFBRTtJQUNuRSxNQUFNLENBQUNrRixxQkFBcUJDLHVCQUF1QixHQUFHbkYsK0NBQVFBLENBQVM7SUFDdkUsTUFBTSxDQUFDb0YsaUJBQWlCQyxtQkFBbUIsR0FBR3JGLCtDQUFRQSxDQUFXLEVBQUU7SUFDbkUsTUFBTSxDQUFDc0YsZ0JBQWdCQyxrQkFBa0IsR0FBR3ZGLCtDQUFRQSxDQUFTO0lBQzdELE1BQU0sQ0FBQ3dGLGlCQUFpQkMsbUJBQW1CLEdBQUd6RiwrQ0FBUUEsQ0FBUztJQUMvRCxNQUFNLENBQUMwRixtQkFBbUJDLHFCQUFxQixHQUFHM0YsK0NBQVFBLENBQVM7SUFDbkUsTUFBTSxDQUFDNEYsb0JBQW9CQyxzQkFBc0IsR0FBRzdGLCtDQUFRQSxDQUFTO0lBQ3JFLE1BQU0sQ0FBQzhGLGdCQUFnQkMsa0JBQWtCLEdBQUcvRiwrQ0FBUUEsQ0FBUztJQUM3RCxNQUFNLENBQUNnRyxpQkFBaUJDLG1CQUFtQixHQUFHakcsK0NBQVFBLENBQVM7SUFDL0QsTUFBTSxDQUFDa0csZUFBZUMsaUJBQWlCLEdBQUduRywrQ0FBUUEsQ0FBUztJQUUzRCxNQUFNLENBQUNvRyxjQUFjLEdBQUc5RixpSkFBSUEsQ0FBQytGLE9BQU87SUFDcEMsTUFBTSxDQUFDQyxlQUFlLEdBQUdoRyxpSkFBSUEsQ0FBQytGLE9BQU87SUFDckMsTUFBTSxDQUFDRSxjQUFjLEdBQUdqRyxpSkFBSUEsQ0FBQytGLE9BQU87SUFDcEMsTUFBTSxDQUFDRyxXQUFXLEdBQUdsRyxpSkFBSUEsQ0FBQytGLE9BQU87SUFDakMsTUFBTSxDQUFDSSxrQkFBa0IsR0FBR25HLGlKQUFJQSxDQUFDK0YsT0FBTztJQUN4QyxNQUFNLENBQUNLLGtCQUFrQixHQUFHcEcsaUpBQUlBLENBQUMrRixPQUFPO0lBQ3hDLE1BQU1NLGVBQWV4RiwwRkFBZUE7SUFJcEMsV0FBVztJQUNYLE1BQU15RixrQkFBa0I7UUFDdEIsSUFBSTtnQkFTc0JDO1lBUnhCN0MsaUJBQWlCO1lBQ2pCOEMsUUFBUUMsR0FBRyxDQUFDO1lBRVosTUFBTSxFQUFFQyxNQUFNSCxHQUFHLEVBQUUsR0FBRyxNQUFNekYsc0RBQVNBLENBQUM2RixvQkFBb0IsQ0FBQztnQkFDekRDLE1BQU07Z0JBQ05DLFVBQVU7WUFDWjtZQUVBLElBQUlOLElBQUlPLElBQUksS0FBSyxTQUFPUCxZQUFBQSxJQUFJRyxJQUFJLGNBQVJILGdDQUFBQSxVQUFVUSxJQUFJLEdBQUU7Z0JBQ3RDUCxRQUFRQyxHQUFHLENBQUMsaUJBQWlCRixJQUFJRyxJQUFJLENBQUNLLElBQUk7Z0JBQzFDN0QsY0FBY3FELElBQUlHLElBQUksQ0FBQ0ssSUFBSTtZQUM3QixPQUFPO2dCQUNMUCxRQUFRUSxLQUFLLENBQUMsaUJBQWlCVCxJQUFJVSxHQUFHO2dCQUN0QzFHLGlKQUFPQSxDQUFDeUcsS0FBSyxDQUFDVCxJQUFJVSxHQUFHLElBQUk7WUFDM0I7UUFDRixFQUFFLE9BQU9ELE9BQU87WUFDZFIsUUFBUVEsS0FBSyxDQUFDLGlCQUFpQkE7WUFDL0J6RyxpSkFBT0EsQ0FBQ3lHLEtBQUssQ0FBQztRQUNoQixTQUFVO1lBQ1J0RCxpQkFBaUI7UUFDbkI7SUFDRjtJQUVBLGdCQUFnQjtJQUNoQixNQUFNd0QscUJBQXFCLE9BQU9DO1FBQ2hDLElBQUk7Z0JBUXNCWjtZQVB4QkMsUUFBUUMsR0FBRyxDQUFDLGdDQUFzQlU7WUFFbEMsTUFBTSxFQUFFVCxNQUFNSCxHQUFHLEVBQUUsR0FBRyxNQUFNekYsc0RBQVNBLENBQUNzRyxtQkFBbUIsQ0FBQ0QsVUFBVTtnQkFDbEVQLE1BQU07Z0JBQ05DLFVBQVU7WUFDWjtZQUVBLElBQUlOLElBQUlPLElBQUksS0FBSyxTQUFPUCxZQUFBQSxJQUFJRyxJQUFJLGNBQVJILGdDQUFBQSxVQUFVUSxJQUFJLEdBQUU7Z0JBQ3RDUCxRQUFRQyxHQUFHLENBQUMsa0JBQWtCRixJQUFJRyxJQUFJLENBQUNLLElBQUk7Z0JBQzNDM0Qsb0JBQW9CaUUsQ0FBQUEsT0FBUSxJQUFJaEUsSUFBSWdFLEtBQUtDLEdBQUcsQ0FBQ0gsVUFBVVosSUFBSUcsSUFBSSxDQUFDSyxJQUFJO2dCQUNwRXhELGtCQUFrQjhELENBQUFBLE9BQVEsSUFBSTdELElBQUk2RCxLQUFLRSxHQUFHLENBQUNKO1lBQzdDLE9BQU87Z0JBQ0xYLFFBQVFRLEtBQUssQ0FBQyxrQkFBa0JULElBQUlVLEdBQUc7Z0JBQ3ZDMUcsaUpBQU9BLENBQUN5RyxLQUFLLENBQUNULElBQUlVLEdBQUcsSUFBSTtZQUMzQjtRQUNGLEVBQUUsT0FBT0QsT0FBTztZQUNkUixRQUFRUSxLQUFLLENBQUMsa0JBQWtCQTtZQUNoQ3pHLGlKQUFPQSxDQUFDeUcsS0FBSyxDQUFDO1FBQ2hCO0lBQ0Y7SUFFQSxpQkFBaUI7SUFDakIsTUFBTVEsa0JBQWtCO1FBQ3RCLElBQUk7WUFDRmhCLFFBQVFDLEdBQUcsQ0FBQztZQUVaLFdBQVc7WUFDWCxNQUFNSDtRQUNSLEVBQUUsT0FBT1UsT0FBTztZQUNkUixRQUFRUSxLQUFLLENBQUMsZUFBZUE7WUFDN0JYLGFBQWFXLEtBQUssQ0FBQztRQUNyQjtJQUNGO0lBRUEsT0FBTztJQUNQLE1BQU1TLGtCQUFrQixPQUFPQztRQUM3QixJQUFJO1lBQ0Ysb0JBQW9CO1lBQ3BCLE1BQU1DLGdCQUFxQjtnQkFDekJDLFVBQVU1QyxpQkFBaUIsSUFBSTtnQkFDL0I2QyxhQUFhekMsb0JBQW9CLElBQUk7Z0JBQ3JDMEMsVUFBVXRDLGlCQUFpQixJQUFJO1lBQ2pDO1lBRUEsSUFBSVIsZ0JBQWdCO2dCQUNsQjJDLGNBQWNJLEtBQUssR0FBRztvQkFDcEJDLEtBQUtoRDtvQkFDTGlELE1BQU0vQyxtQkFBbUI7Z0JBQzNCO1lBQ0Y7WUFFQSxJQUFJRSxtQkFBbUI7Z0JBQ3JCdUMsY0FBY08sUUFBUSxHQUFHO29CQUN2QkYsS0FBSzVDO29CQUNMNkMsTUFBTTNDLHNCQUFzQjtnQkFDOUI7WUFDRjtZQUVBLElBQUlFLGdCQUFnQjtnQkFDbEJtQyxjQUFjUSxLQUFLLEdBQUc7b0JBQ3BCSCxLQUFLeEM7b0JBQ0x5QyxNQUFNdkMsbUJBQW1CO2dCQUMzQjtZQUNGO1lBRUEsTUFBTTBDLGFBQWE7Z0JBQ2pCakIsVUFBVWtCLFNBQVNYLE9BQU9QLFFBQVE7Z0JBQ2xDbUIsT0FBT1osT0FBT1ksS0FBSyxDQUFDQyxJQUFJO2dCQUN4QkMsYUFBYWQsT0FBT2MsV0FBVyxDQUFDRCxJQUFJO2dCQUNwQ0UsWUFBWTdEO2dCQUNaZ0QsVUFBVTVDLGlCQUFpQixJQUFJO2dCQUMvQjZDLGFBQWF6QyxvQkFBb0IsSUFBSTtnQkFDckMwQyxVQUFVdEMsaUJBQWlCLElBQUk7Z0JBQy9CSSxlQUFlQSxpQkFBaUI7Z0JBQ2hDK0I7Z0JBQ0FlLGNBQWNoQixPQUFPaUIsa0JBQWtCLElBQUlqQixPQUFPaUIsa0JBQWtCLENBQUNDLE1BQU0sR0FBRyxJQUFJO29CQUFDO3dCQUNqRk4sT0FBTzt3QkFDUE8sU0FBU0MsTUFBTUMsT0FBTyxDQUFDckIsT0FBT2lCLGtCQUFrQixJQUFJakIsT0FBT2lCLGtCQUFrQixHQUFHOzRCQUFDakIsT0FBT2lCLGtCQUFrQjt5QkFBQztvQkFDN0c7aUJBQUUsR0FBRyxFQUFFO2dCQUNQSyxxQkFBcUJsRSxnQkFBZ0JtRSxHQUFHLENBQUNDLENBQUFBLE9BQVM7d0JBQ2hEWixPQUFPWSxLQUFLQyxLQUFLLENBQUMsS0FBS0MsR0FBRyxNQUFNO3dCQUNoQ3BCLEtBQUtrQjt3QkFDTFYsYUFBYTtvQkFDZjtnQkFDQWEsWUFBWWhCLFNBQVNYLE9BQU8yQixVQUFVLEtBQUs7WUFDN0M7WUFFQSxTQUFTO1lBQ1QsSUFBSSxDQUFDakIsV0FBV2pCLFFBQVEsRUFBRTtnQkFDeEJkLGFBQWFXLEtBQUssQ0FBQztnQkFDbkI7WUFDRjtZQUNBLElBQUksQ0FBQ29CLFdBQVdFLEtBQUssRUFBRTtnQkFDckJqQyxhQUFhVyxLQUFLLENBQUM7Z0JBQ25CO1lBQ0Y7WUFDQSxJQUFJLENBQUNvQixXQUFXSyxVQUFVLEVBQUU7Z0JBQzFCcEMsYUFBYVcsS0FBSyxDQUFDO2dCQUNuQjtZQUNGO1lBRUFSLFFBQVFDLEdBQUcsQ0FBQyx3QkFBYzJCO1lBQzFCNUIsUUFBUUMsR0FBRyxDQUFDLHdCQUFjNkMsS0FBS0MsU0FBUyxDQUFDbkIsWUFBWVEsTUFBTSxFQUFFO1lBRTdELFNBQVM7WUFDVCxJQUFJWSxhQUFhO1lBQ2pCLE1BQU1DLGFBQWE7WUFDbkIsSUFBSUM7WUFFSixNQUFPRixjQUFjQyxXQUFZO2dCQUMvQixJQUFJO29CQUNGLE1BQU0sRUFBRS9DLE1BQU1ILEdBQUcsRUFBRSxHQUFHLE1BQU16RixzREFBU0EsQ0FBQzZJLFlBQVksQ0FBQ3ZCO29CQUVuRCxjQUFjO29CQUNkLElBQUk3QixJQUFJTyxJQUFJLEtBQUssS0FBSzt3QkFDcEJULGFBQWF1RCxPQUFPLENBQUM7d0JBQ3JCcEM7d0JBQ0E1RiwyQkFBMkI7d0JBQzNCa0UsY0FBYytELFdBQVc7d0JBQ3pCaEYsdUJBQXVCO3dCQUN2QkUsbUJBQW1CLEVBQUU7d0JBQ3JCRSxrQkFBa0I7d0JBQ2xCRSxtQkFBbUI7d0JBQ25CRSxxQkFBcUI7d0JBQ3JCRSxzQkFBc0I7d0JBQ3RCRSxrQkFBa0I7d0JBQ2xCRSxtQkFBbUI7d0JBQ25CRSxpQkFBaUI7d0JBQ2pCO29CQUNGLE9BQU87d0JBQ0xRLGFBQWFXLEtBQUssQ0FBQ1QsSUFBSVUsR0FBRyxJQUFJO3dCQUM5QjtvQkFDRjtnQkFDRixFQUFFLE9BQU9ELE9BQVk7b0JBQ25CMEMsWUFBWTFDO29CQUNad0M7b0JBRUEsSUFBSUEsY0FBY0MsWUFBWTt3QkFDNUJqRCxRQUFRQyxHQUFHLENBQUMsaUJBQWtCLE9BQVgrQyxZQUFXO3dCQUM5Qm5ELGFBQWF5RCxPQUFPLENBQUMsY0FBNEJMLE9BQWRELFlBQVcsS0FBYyxPQUFYQyxZQUFXO3dCQUM1RCxVQUFVO3dCQUNWLE1BQU0sSUFBSU0sUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUztvQkFDbkQ7Z0JBQ0Y7WUFDRjtZQUVBLHFCQUFxQjtZQUNyQixNQUFNTjtRQUNSLEVBQUUsT0FBTzFDLE9BQVk7Z0JBSWdCQSxnQkFDOUJBLHNCQUFBQSxpQkFJTUEsa0JBRUFBLGtCQUdBQSxrQkFTREEsa0JBQ0ZBO1lBdkJSUixRQUFRUSxLQUFLLENBQUMsYUFBYUE7WUFFM0IsV0FBVztZQUNYLElBQUlBLE1BQU1GLElBQUksS0FBSyxrQkFBZ0JFLGlCQUFBQSxNQUFNekcsT0FBTyxjQUFieUcscUNBQUFBLGVBQWVrRCxRQUFRLENBQUMsa0JBQ3REbEQsRUFBQUEsa0JBQUFBLE1BQU1tRCxRQUFRLGNBQWRuRCx1Q0FBQUEsdUJBQUFBLGdCQUFnQk4sSUFBSSxjQUFwQk0sMkNBQUFBLHFCQUFzQnpHLE9BQU8sS0FBSXlHLE1BQU1tRCxRQUFRLENBQUN6RCxJQUFJLENBQUNuRyxPQUFPLENBQUMySixRQUFRLENBQUMsZUFBZ0I7Z0JBQ3pGN0QsYUFBYVcsS0FBSyxDQUFDO1lBQ3JCLE9BQU8sSUFBSUEsTUFBTUYsSUFBSSxLQUFLLGlCQUFpQjtnQkFDekNULGFBQWFXLEtBQUssQ0FBQztZQUNyQixPQUFPLElBQUlBLEVBQUFBLG1CQUFBQSxNQUFNbUQsUUFBUSxjQUFkbkQsdUNBQUFBLGlCQUFnQm9ELE1BQU0sTUFBSyxLQUFLO2dCQUN6Qy9ELGFBQWFXLEtBQUssQ0FBQztZQUNyQixPQUFPLElBQUlBLEVBQUFBLG1CQUFBQSxNQUFNbUQsUUFBUSxjQUFkbkQsdUNBQUFBLGlCQUFnQm9ELE1BQU0sTUFBSyxLQUFLO29CQUN4QnBELHVCQUFBQTtnQkFBakIsTUFBTXFELFdBQVdyRCxFQUFBQSxtQkFBQUEsTUFBTW1ELFFBQVEsY0FBZG5ELHdDQUFBQSx3QkFBQUEsaUJBQWdCTixJQUFJLGNBQXBCTSw0Q0FBQUEsc0JBQXNCekcsT0FBTyxLQUFJeUcsTUFBTXpHLE9BQU87Z0JBQy9EOEYsYUFBYVcsS0FBSyxDQUFDLFdBQW9CLE9BQVRxRDtZQUNoQyxPQUFPLElBQUlyRCxFQUFBQSxtQkFBQUEsTUFBTW1ELFFBQVEsY0FBZG5ELHVDQUFBQSxpQkFBZ0JvRCxNQUFNLE1BQUssS0FBSztnQkFDekMvRCxhQUFhVyxLQUFLLENBQUM7WUFDckIsT0FBTztnQkFDTFgsYUFBYVcsS0FBSyxDQUFDLFdBQW9DLE9BQXpCQSxNQUFNekcsT0FBTyxJQUFJO1lBQ2pEO1lBRUFpRyxRQUFRQyxHQUFHLENBQUMsd0JBQWM7Z0JBQ3hCbEcsU0FBU3lHLE1BQU16RyxPQUFPO2dCQUN0QnVHLE1BQU1FLE1BQU1GLElBQUk7Z0JBQ2hCc0QsTUFBTSxHQUFFcEQsbUJBQUFBLE1BQU1tRCxRQUFRLGNBQWRuRCx1Q0FBQUEsaUJBQWdCb0QsTUFBTTtnQkFDOUIxRCxJQUFJLEdBQUVNLG1CQUFBQSxNQUFNbUQsUUFBUSxjQUFkbkQsdUNBQUFBLGlCQUFnQk4sSUFBSTtZQUM1QjtRQUNGO0lBQ0Y7SUFFQSxPQUFPO0lBQ1AsTUFBTTRELG1CQUFtQixPQUFPNUM7UUFDOUIsSUFBSSxDQUFDbkYsZUFBZTtRQUVwQixJQUFJO1lBQ0YsTUFBTSxFQUFFbUUsTUFBTUgsR0FBRyxFQUFFLEdBQUcsTUFBTXpGLHNEQUFTQSxDQUFDeUosWUFBWSxDQUFDaEksY0FBY2lJLEVBQUUsRUFBRTlDO1lBQ3JFLElBQUluQixJQUFJTyxJQUFJLEtBQUssS0FBSztnQkFDcEJULGFBQWF1RCxPQUFPLENBQUM7Z0JBQ3JCcEM7Z0JBQ0ExRiw0QkFBNEI7Z0JBQzVCVSxpQkFBaUI7Z0JBQ2pCd0QsZUFBZTZELFdBQVc7WUFDNUIsT0FBTztnQkFDTHhELGFBQWFXLEtBQUssQ0FBQ1QsSUFBSVUsR0FBRyxJQUFJO1lBQ2hDO1FBQ0YsRUFBRSxPQUFPRCxPQUFPO1lBQ2RSLFFBQVFRLEtBQUssQ0FBQyxhQUFhQTtZQUMzQlgsYUFBYVcsS0FBSyxDQUFDO1FBQ3JCO0lBQ0Y7SUFFQSxPQUFPO0lBQ1AsTUFBTXlELHFCQUFxQixPQUFPQztRQUNoQyxJQUFJO1lBQ0YsTUFBTSxFQUFFaEUsTUFBTUgsR0FBRyxFQUFFLEdBQUcsTUFBTXpGLHNEQUFTQSxDQUFDNkosWUFBWSxDQUFDRDtZQUNuRCxJQUFJbkUsSUFBSU8sSUFBSSxLQUFLLEtBQUs7Z0JBQ3BCVCxhQUFhdUQsT0FBTyxDQUFDO2dCQUNyQnBDO1lBQ0YsT0FBTztnQkFDTG5CLGFBQWFXLEtBQUssQ0FBQ1QsSUFBSVUsR0FBRyxJQUFJO1lBQ2hDO1FBQ0YsRUFBRSxPQUFPRCxPQUFPO1lBQ2RSLFFBQVFRLEtBQUssQ0FBQyxhQUFhQTtZQUMzQlgsYUFBYVcsS0FBSyxDQUFDO1FBQ3JCO0lBQ0Y7SUFFQSxRQUFRO0lBQ1IsTUFBTTRELHdCQUF3QixPQUFPRixVQUFrQnZEO1FBQ3JELElBQUk7WUFDRlgsUUFBUUMsR0FBRyxDQUFDLDZCQUFtQmlFLFVBQVUsU0FBU3ZEO1lBRWxELE1BQU0sRUFBRVQsTUFBTUgsR0FBRyxFQUFFLEdBQUcsTUFBTXpGLHNEQUFTQSxDQUFDNkosWUFBWSxDQUFDRDtZQUVuRCxJQUFJbkUsSUFBSU8sSUFBSSxLQUFLLEtBQUs7Z0JBQ3BCdkcsaUpBQU9BLENBQUNxSixPQUFPLENBQUM7Z0JBQ2hCcEQsUUFBUUMsR0FBRyxDQUFDO2dCQUVaLGdCQUFnQjtnQkFDaEIsTUFBTVMsbUJBQW1CQztnQkFFekJYLFFBQVFDLEdBQUcsQ0FBQztZQUNkLE9BQU87Z0JBQ0xELFFBQVFRLEtBQUssQ0FBQyxjQUFjVCxJQUFJVSxHQUFHO2dCQUNuQzFHLGlKQUFPQSxDQUFDeUcsS0FBSyxDQUFDVCxJQUFJVSxHQUFHLElBQUk7WUFDM0I7UUFDRixFQUFFLE9BQU9ELE9BQU87WUFDZFIsUUFBUVEsS0FBSyxDQUFDLGNBQWNBO1lBQzVCekcsaUpBQU9BLENBQUN5RyxLQUFLLENBQUM7UUFDaEI7SUFDRjtJQUVBLGNBQWM7SUFDZCxNQUFNNkQsd0JBQXdCLE9BQU8xRDtRQUNuQ1gsUUFBUUMsR0FBRyxDQUFDLCtCQUFxQlU7UUFDakNYLFFBQVFDLEdBQUcsQ0FBQyx3QkFBY25ELGVBQWV3SCxHQUFHLENBQUMzRDtRQUU3QyxJQUFJN0QsZUFBZXdILEdBQUcsQ0FBQzNELFdBQVc7WUFDaEMsS0FBSztZQUNMWCxRQUFRQyxHQUFHLENBQUMsc0JBQVlVO1lBQ3hCNUQsa0JBQWtCOEQsQ0FBQUE7Z0JBQ2hCLE1BQU0wRCxTQUFTLElBQUl2SCxJQUFJNkQ7Z0JBQ3ZCMEQsT0FBT0MsTUFBTSxDQUFDN0Q7Z0JBQ2QsT0FBTzREO1lBQ1Q7UUFDRixPQUFPO1lBQ0wsZUFBZTtZQUNmdkUsUUFBUUMsR0FBRyxDQUFDLDRCQUFrQlU7WUFDOUIsTUFBTUQsbUJBQW1CQztRQUMzQjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQU04RCxrQkFBa0I7UUFDdEJ6RSxRQUFRQyxHQUFHLENBQUM7UUFDWixLQUFLLE1BQU15RSxVQUFVakksV0FBWTtZQUMvQixJQUFJLENBQUNLLGVBQWV3SCxHQUFHLENBQUNJLE9BQU9WLEVBQUUsR0FBRztnQkFDbEMsTUFBTXRELG1CQUFtQmdFLE9BQU9WLEVBQUU7WUFDcEM7UUFDRjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQU1XLG9CQUFvQjtRQUN4QjNFLFFBQVFDLEdBQUcsQ0FBQztRQUNabEQsa0JBQWtCLElBQUlDO0lBQ3hCO0lBRUEsU0FBUztJQUNULE1BQU00SCxrQkFBa0IsT0FBTzFEO1FBQzdCLElBQUk7WUFDRixNQUFNMkQsYUFBYTtnQkFDakIsR0FBRzNELE1BQU07Z0JBQ1RlLFlBQVkxRjtZQUNkO1lBRUF5RCxRQUFRQyxHQUFHLENBQUMsYUFBYTRFO1lBRXpCLE1BQU0sRUFBRTNFLE1BQU1ILEdBQUcsRUFBRSxHQUFHLE1BQU16RixzREFBU0EsQ0FBQ3dLLGtCQUFrQixDQUFDRDtZQUV6RCxJQUFJOUUsSUFBSU8sSUFBSSxLQUFLLEtBQUs7Z0JBQ3BCVCxhQUFhdUQsT0FBTyxDQUFDO2dCQUNyQnBDO2dCQUNBeEYsMkJBQTJCO2dCQUMzQmlFLGNBQWM0RCxXQUFXO2dCQUN6QjdHLGlCQUFpQjtZQUNuQixPQUFPO2dCQUNMcUQsYUFBYVcsS0FBSyxDQUFDVCxJQUFJVSxHQUFHLElBQUk7WUFDaEM7UUFDRixFQUFFLE9BQU9ELE9BQU87WUFDZFIsUUFBUVEsS0FBSyxDQUFDLGVBQWVBO1lBQzdCWCxhQUFhVyxLQUFLLENBQUM7UUFDckI7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNdUUsZUFBZSxPQUFPN0Q7UUFDMUIsSUFBSTtZQUNGbEIsUUFBUUMsR0FBRyxDQUFDLDJCQUFpQmlCO1lBRTdCLE1BQU0sRUFBRWhCLE1BQU1ILEdBQUcsRUFBRSxHQUFHLE1BQU16RixzREFBU0EsQ0FBQzBLLGVBQWUsQ0FBQzlEO1lBRXRELElBQUluQixJQUFJTyxJQUFJLEtBQUssS0FBSztnQkFDcEJULGFBQWF1RCxPQUFPLENBQUM7Z0JBQ3JCMUgsd0JBQXdCO2dCQUN4QmdFLFdBQVcyRCxXQUFXO2dCQUN0QixXQUFXO2dCQUNYNEI7WUFDRixPQUFPO2dCQUNMcEYsYUFBYVcsS0FBSyxDQUFDVCxJQUFJVSxHQUFHLElBQUk7WUFDaEM7UUFDRixFQUFFLE9BQU9ELE9BQU87WUFDZFIsUUFBUVEsS0FBSyxDQUFDLGFBQWFBO1lBQzNCWCxhQUFhVyxLQUFLLENBQUM7UUFDckI7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNMEUsc0JBQXNCLE9BQU9oRTtRQUNqQyxJQUFJO1lBQ0ZsQixRQUFRQyxHQUFHLENBQUMsMEJBQWdCaUI7WUFFNUIsTUFBTSxFQUFFaEIsTUFBTUgsR0FBRyxFQUFFLEdBQUcsTUFBTXpGLHNEQUFTQSxDQUFDNkssbUJBQW1CLENBQUNqRSxPQUFPUCxRQUFRO1lBRXpFLElBQUlaLElBQUlPLElBQUksS0FBSyxLQUFLO2dCQUNwQlQsYUFBYXVELE9BQU8sQ0FBQztnQkFDckJ4SCwrQkFBK0I7Z0JBQy9CK0Qsa0JBQWtCMEQsV0FBVztnQkFFN0IsV0FBVztnQkFDWCxNQUFNK0IsY0FBY3JGLElBQUlHLElBQUk7Z0JBQzVCRixRQUFRQyxHQUFHLENBQUMsZ0JBQWdCbUY7Z0JBRTVCLGVBQWU7Z0JBQ2YsSUFBSUEsWUFBWUMsWUFBWSxFQUFFO29CQUM1QixNQUFNQyxRQUFRRixZQUFZQyxZQUFZO29CQUN0QyxNQUFNRSxlQUFlLE9BQXVDSCxPQUFoQ0EsWUFBWUksZ0JBQWdCLEVBQUMsS0FBc0NGLE9BQW5DRixZQUFZSyxZQUFZLEVBQUMsWUFBOENDLE9BQXBDSixNQUFNSyxnQkFBZ0IsRUFBQyxlQUF1RCxPQUExQ0QsS0FBS0UsS0FBSyxDQUFDTixNQUFNTyxrQkFBa0IsR0FBRyxLQUFJO29CQUM3S2hHLGFBQWFpRyxJQUFJLENBQUNQO2dCQUNwQjtZQUNGLE9BQU87Z0JBQ0wxRixhQUFhVyxLQUFLLENBQUNULElBQUlVLEdBQUcsSUFBSTtZQUNoQztRQUNGLEVBQUUsT0FBT0QsT0FBTztZQUNkUixRQUFRUSxLQUFLLENBQUMsZUFBZUE7WUFDN0JYLGFBQWFXLEtBQUssQ0FBQztRQUNyQjtJQUNGO0lBRUEsZUFBZTtJQUNmLE1BQU11Rix3QkFBd0I7UUFDNUIsSUFBSTtnQkFTc0JoRztZQVJ4QnBDLGtCQUFrQjtZQUNsQnFDLFFBQVFDLEdBQUcsQ0FBQztZQUVaLE1BQU0sRUFBRUMsTUFBTUgsR0FBRyxFQUFFLEdBQUcsTUFBTXpGLHNEQUFTQSxDQUFDNkYsb0JBQW9CLENBQUM7Z0JBQ3pEQyxNQUFNO2dCQUNOQyxVQUFVO1lBQ1o7WUFFQSxJQUFJTixJQUFJTyxJQUFJLEtBQUssU0FBT1AsWUFBQUEsSUFBSUcsSUFBSSxjQUFSSCxnQ0FBQUEsVUFBVVEsSUFBSSxHQUFFO2dCQUN0Q1AsUUFBUUMsR0FBRyxDQUFDLG9CQUFvQkYsSUFBSUcsSUFBSSxDQUFDSyxJQUFJO2dCQUM3QyxPQUFPUixJQUFJRyxJQUFJLENBQUNLLElBQUk7WUFDdEIsT0FBTztnQkFDTFAsUUFBUVEsS0FBSyxDQUFDLG9CQUFvQlQsSUFBSVUsR0FBRztnQkFDekMxRyxpSkFBT0EsQ0FBQ3lHLEtBQUssQ0FBQ1QsSUFBSVUsR0FBRyxJQUFJO2dCQUN6QixPQUFPLEVBQUU7WUFDWDtRQUNGLEVBQUUsT0FBT0QsT0FBTztZQUNkUixRQUFRUSxLQUFLLENBQUMsb0JBQW9CQTtZQUNsQ3pHLGlKQUFPQSxDQUFDeUcsS0FBSyxDQUFDO1lBQ2QsT0FBTyxFQUFFO1FBQ1gsU0FBVTtZQUNSN0Msa0JBQWtCO1FBQ3BCO0lBQ0Y7SUFFQSxjQUFjO0lBQ2QsTUFBTXFJLDhCQUE4QixPQUFPckY7UUFDekMsSUFBSTtZQUNGaEQsa0JBQWtCO1lBQ2xCcUMsUUFBUUMsR0FBRyxDQUFDLCtCQUFxQlU7WUFFakMsTUFBTSxFQUFFVCxNQUFNSCxHQUFHLEVBQUUsR0FBRyxNQUFNekYsc0RBQVNBLENBQUMyTCwwQkFBMEIsQ0FBQ3RGO1lBRWpFLElBQUlaLElBQUlPLElBQUksS0FBSyxPQUFPUCxJQUFJRyxJQUFJLEVBQUU7Z0JBQ2hDRixRQUFRQyxHQUFHLENBQUMsaUJBQWlCRixJQUFJRyxJQUFJO2dCQUNyQyxPQUFPSCxJQUFJRyxJQUFJO1lBQ2pCLE9BQU87Z0JBQ0xGLFFBQVFRLEtBQUssQ0FBQyxpQkFBaUJULElBQUlVLEdBQUc7Z0JBQ3RDMUcsaUpBQU9BLENBQUN5RyxLQUFLLENBQUNULElBQUlVLEdBQUcsSUFBSTtnQkFDekIsT0FBTztZQUNUO1FBQ0YsRUFBRSxPQUFPRCxPQUFPO1lBQ2RSLFFBQVFRLEtBQUssQ0FBQyxpQkFBaUJBO1lBQy9CekcsaUpBQU9BLENBQUN5RyxLQUFLLENBQUM7WUFDZCxPQUFPO1FBQ1QsU0FBVTtZQUNSN0Msa0JBQWtCO1FBQ3BCO0lBQ0Y7SUFFQSxnQkFBZ0I7SUFDaEIsTUFBTXVJLHlCQUF5QjtRQUM3QixJQUFJO2dCQVNzQm5HO1lBUnhCOUIsc0JBQXNCO1lBQ3RCK0IsUUFBUUMsR0FBRyxDQUFDO1lBRVosTUFBTSxFQUFFQyxNQUFNSCxHQUFHLEVBQUUsR0FBRyxNQUFNekYsc0RBQVNBLENBQUM2RixvQkFBb0IsQ0FBQztnQkFDekRDLE1BQU07Z0JBQ05DLFVBQVU7WUFDWjtZQUVBLElBQUlOLElBQUlPLElBQUksS0FBSyxTQUFPUCxZQUFBQSxJQUFJRyxJQUFJLGNBQVJILGdDQUFBQSxVQUFVUSxJQUFJLEdBQUU7Z0JBQ3RDUCxRQUFRQyxHQUFHLENBQUMscUJBQXFCRixJQUFJRyxJQUFJLENBQUNLLElBQUk7Z0JBQzlDekMsNkJBQTZCaUMsSUFBSUcsSUFBSSxDQUFDSyxJQUFJO1lBQzVDLE9BQU87Z0JBQ0xQLFFBQVFRLEtBQUssQ0FBQyxxQkFBcUJULElBQUlVLEdBQUc7Z0JBQzFDMUcsaUpBQU9BLENBQUN5RyxLQUFLLENBQUNULElBQUlVLEdBQUcsSUFBSTtZQUMzQjtRQUNGLEVBQUUsT0FBT0QsT0FBTztZQUNkUixRQUFRUSxLQUFLLENBQUMscUJBQXFCQTtZQUNuQ3pHLGlKQUFPQSxDQUFDeUcsS0FBSyxDQUFDO1FBQ2hCLFNBQVU7WUFDUnZDLHNCQUFzQjtRQUN4QjtJQUNGO0lBRUEsd0JBQXdCO0lBQ3hCLE1BQU1rSSx5QkFBeUIsT0FBT3hGO1FBQ3BDLElBQUk7WUFDRlgsUUFBUUMsR0FBRyxDQUFDLG1DQUF5QlU7WUFFckMsZ0JBQWdCO1lBQ2hCLElBQUk7Z0JBQ0ZYLFFBQVFDLEdBQUcsQ0FBQztnQkFDWixNQUFNLEVBQUVDLE1BQU1ILEdBQUcsRUFBRSxHQUFHLE1BQU16RixzREFBU0EsQ0FBQzJMLDBCQUEwQixDQUFDdEY7Z0JBRWpFWCxRQUFRQyxHQUFHLENBQUMsMkJBQWlCRjtnQkFFN0IsSUFBSUEsSUFBSU8sSUFBSSxLQUFLLE9BQU9QLElBQUlHLElBQUksRUFBRTtvQkFDaEMsY0FBYztvQkFDZCxJQUFJckYsYUFBYSxFQUFFO29CQUVuQixJQUFJa0YsSUFBSUcsSUFBSSxDQUFDckYsVUFBVSxFQUFFO3dCQUN2QkEsYUFBYWtGLElBQUlHLElBQUksQ0FBQ3JGLFVBQVU7d0JBQ2hDbUYsUUFBUUMsR0FBRyxDQUFDLHlCQUF5QnBGO29CQUN2QyxPQUFPLElBQUlrRixJQUFJRyxJQUFJLENBQUNrRyxPQUFPLEVBQUU7d0JBQzNCdkwsYUFBYWtGLElBQUlHLElBQUksQ0FBQ2tHLE9BQU87d0JBQzdCcEcsUUFBUUMsR0FBRyxDQUFDLHNCQUFzQnBGO29CQUNwQyxPQUFPLElBQUl5SCxNQUFNQyxPQUFPLENBQUN4QyxJQUFJRyxJQUFJLEdBQUc7d0JBQ2xDckYsYUFBYWtGLElBQUlHLElBQUk7d0JBQ3JCRixRQUFRQyxHQUFHLENBQUMscUJBQXFCcEY7b0JBQ25DLE9BQU87d0JBQ0xtRixRQUFRQyxHQUFHLENBQUM7d0JBQ1osTUFBTSxJQUFJb0csTUFBTTtvQkFDbEI7b0JBRUEsSUFBSXhMLGNBQWNBLFdBQVd1SCxNQUFNLEdBQUcsR0FBRzt3QkFDdkNwQyxRQUFRQyxHQUFHLENBQUMsaUJBQWlCcEYsV0FBV3VILE1BQU07d0JBQzlDckUsNkJBQTZCbEQ7d0JBQzdCO29CQUNGO2dCQUNGO1lBQ0YsRUFBRSxPQUFPeUwsa0JBQWtCO2dCQUN6QnRHLFFBQVFDLEdBQUcsQ0FBQztnQkFFWixpQkFBaUI7Z0JBQ2pCLElBQUk7d0JBUXVCc0c7b0JBUHpCLE1BQU0sRUFBRXJHLE1BQU1xRyxJQUFJLEVBQUUsR0FBRyxNQUFNak0sc0RBQVNBLENBQUNzRyxtQkFBbUIsQ0FBQ0QsVUFBVTt3QkFDbkVQLE1BQU07d0JBQ05DLFVBQVU7b0JBQ1o7b0JBRUFMLFFBQVFDLEdBQUcsQ0FBQywyQkFBaUJzRztvQkFFN0IsSUFBSUEsS0FBS2pHLElBQUksS0FBSyxTQUFPaUcsYUFBQUEsS0FBS3JHLElBQUksY0FBVHFHLGlDQUFBQSxXQUFXaEcsSUFBSSxHQUFFO3dCQUN4Q1AsUUFBUUMsR0FBRyxDQUFDLHdCQUF3QnNHLEtBQUtyRyxJQUFJLENBQUNLLElBQUk7d0JBQ2xEeEMsNkJBQTZCd0ksS0FBS3JHLElBQUksQ0FBQ0ssSUFBSTt3QkFDM0M7b0JBQ0Y7Z0JBQ0YsRUFBRSxPQUFPaUcsaUJBQWlCO29CQUN4QnhHLFFBQVFRLEtBQUssQ0FBQyxpQkFBaUJnRztnQkFDakM7WUFDRjtZQUVBLGFBQWE7WUFDYnhHLFFBQVFDLEdBQUcsQ0FBQztZQUNabEMsNkJBQTZCLEVBQUU7WUFDL0JoRSxpSkFBT0EsQ0FBQytMLElBQUksQ0FBQztRQUVmLEVBQUUsT0FBT3RGLE9BQU87WUFDZFIsUUFBUVEsS0FBSyxDQUFDLG9CQUFvQkE7WUFDbEN6RyxpSkFBT0EsQ0FBQ3lHLEtBQUssQ0FBQztZQUNkekMsNkJBQTZCLEVBQUU7UUFDakM7SUFDRjtJQUVBLGVBQWU7SUFDZixNQUFNMEksNEJBQTRCLE9BQU85RjtRQUN2Q1gsUUFBUUMsR0FBRyxDQUFDLDRCQUFrQlU7UUFDOUJYLFFBQVFDLEdBQUcsQ0FBQyx3QkFBY3RGO1FBRTFCeUMsNEJBQTRCdUQ7UUFDNUJwRCw0QkFBNEJGO1FBQzVCVSw2QkFBNkIsRUFBRTtRQUUvQixhQUFhO1FBQ2I2QixrQkFBa0I4RyxjQUFjLENBQUM7WUFBRXhDLFVBQVU3RztRQUFVO1FBRXZELGFBQWE7UUFDYixJQUFJc0QsVUFBVTtZQUNaWCxRQUFRQyxHQUFHLENBQUM7WUFDWmhDLHNCQUFzQjtZQUV0QixJQUFJO2dCQUNGLE1BQU1rSSx1QkFBdUJ4RjtnQkFDN0JYLFFBQVFDLEdBQUcsQ0FBQztZQUNkLEVBQUUsT0FBT08sT0FBTztnQkFDZFIsUUFBUVEsS0FBSyxDQUFDLGNBQWNBO1lBQzlCLFNBQVU7Z0JBQ1J2QyxzQkFBc0I7WUFDeEI7UUFDRjtJQUNGO0lBRUEsZUFBZTtJQUNmLE1BQU0wSSw0QkFBNEIsQ0FBQ3pDO1FBQ2pDbEUsUUFBUUMsR0FBRyxDQUFDLDRCQUFrQmlFO1FBQzlCM0csNEJBQTRCMkc7SUFDOUI7SUFFQSxhQUFhO0lBQ2IsTUFBTTBDLDBCQUEwQjtRQUM5QjlLLCtCQUErQjtRQUMvQnNCLDRCQUE0QkM7UUFDNUJFLDRCQUE0QkY7UUFDNUJTLDZCQUE2QixFQUFFO1FBQy9CQyw2QkFBNkIsRUFBRTtRQUMvQjZCLGtCQUFrQnlELFdBQVc7SUFDL0I7SUFFQSxXQUFXO0lBQ1gsTUFBTXdELHlCQUF5QjtRQUM3Qi9LLCtCQUErQjtRQUMvQixNQUFNb0s7SUFDUjtJQUVBLE9BQU87SUFDUCxNQUFNWSxzQkFBc0IsT0FBTzVGO1FBQ2pDLElBQUk7WUFDRixJQUFJLENBQUM1RCw0QkFBNEIsQ0FBQ0gsMEJBQTBCO2dCQUMxRHBELGlKQUFPQSxDQUFDeUcsS0FBSyxDQUFDO2dCQUNkO1lBQ0Y7WUFFQXZDLHNCQUFzQjtZQUN0QitCLFFBQVFDLEdBQUcsQ0FBQywyQkFBaUIzQztZQUM3QjBDLFFBQVFDLEdBQUcsQ0FBQyxzQkFBWTlDO1lBQ3hCNkMsUUFBUUMsR0FBRyxDQUFDLHNCQUFZaUI7WUFFeEIsY0FBYztZQUNkLE1BQU02RixpQkFBaUJuTSwwQkFBMEJvTSxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVqRCxFQUFFLEtBQUsxRztZQUNwRSxJQUFJLENBQUN5SixnQkFBZ0I7Z0JBQ25CaE4saUpBQU9BLENBQUN5RyxLQUFLLENBQUM7Z0JBQ2Q7WUFDRjtZQUVBUixRQUFRQyxHQUFHLENBQUMsd0JBQWM4RztZQUUxQixlQUFlO1lBQ2YvRyxRQUFRQyxHQUFHLENBQUMsZ0NBQXNCM0M7WUFDbEMsTUFBTSxFQUFFNEMsTUFBTUgsR0FBRyxFQUFFLEdBQUcsTUFBTXpGLHNEQUFTQSxDQUFDNE0sYUFBYSxDQUFDNUo7WUFFcEQsSUFBSXlDLElBQUlPLElBQUksS0FBSyxLQUFLO2dCQUNwQnZHLGlKQUFPQSxDQUFDcUosT0FBTyxDQUFDO2dCQUNoQndEO2dCQUVBLFdBQVc7Z0JBQ1g1RyxRQUFRQyxHQUFHLENBQUMsZ0JBQWdCRixJQUFJRyxJQUFJO2dCQUVwQyxTQUFTO2dCQUNULE1BQU1jO2dCQUVOLG9CQUFvQjtnQkFDcEIsSUFBSTdELDRCQUE0QkwsZUFBZXdILEdBQUcsQ0FBQ25ILDJCQUEyQjtvQkFDNUUsTUFBTXVELG1CQUFtQnZEO2dCQUMzQjtZQUNGLE9BQU87Z0JBQ0w2QyxRQUFRUSxLQUFLLENBQUMsYUFBYVQsSUFBSVUsR0FBRztnQkFDbEMxRyxpSkFBT0EsQ0FBQ3lHLEtBQUssQ0FBQ1QsSUFBSVUsR0FBRyxJQUFJO1lBQzNCO1FBQ0YsRUFBRSxPQUFPRCxPQUFZO2dCQUVNQTtZQUR6QlIsUUFBUVEsS0FBSyxDQUFDLGFBQWFBO1lBQzNCUixRQUFRUSxLQUFLLENBQUMsWUFBV0Esa0JBQUFBLE1BQU1tRCxRQUFRLGNBQWRuRCxzQ0FBQUEsZ0JBQWdCTixJQUFJO1lBQzdDbkcsaUpBQU9BLENBQUN5RyxLQUFLLENBQUM7UUFDaEIsU0FBVTtZQUNSdkMsc0JBQXNCO1FBQ3hCO0lBQ0Y7SUFFQSxXQUFXO0lBQ1gsTUFBTWtKLG9CQUFvQjtRQUN4QnJMLCtCQUErQjtRQUMvQnNCLDRCQUE0QkM7UUFDNUJFLDRCQUE0QkY7UUFDNUJJLHdCQUF3QixFQUFFO1FBQzFCSSx3QkFBd0IsRUFBRTtRQUMxQitCLGtCQUFrQnlELFdBQVc7SUFDL0I7SUFNQSxTQUFTO0lBQ1QsTUFBTStELG9CQUFrRCxPQUFPQztRQUM3RCxNQUFNLEVBQUUzRSxJQUFJLEVBQUU0RSxTQUFTLEVBQUVDLE9BQU8sRUFBRSxHQUFHRjtRQUVyQyxJQUFJO1lBQ0YsZ0JBQWdCO1lBQ2hCLE1BQU03RixNQUFNLE1BQU1qSCxzREFBU0EsQ0FBQ2lOLFdBQVcsQ0FBQzlFO1lBQ3hDMUMsUUFBUUMsR0FBRyxDQUFDLG1CQUFtQnVCO1lBRS9CaEYsaUJBQWlCZ0Y7WUFDakI4RixzQkFBQUEsZ0NBQUFBLFVBQVk7Z0JBQUU5RixLQUFLQTtZQUFJO1lBQ3ZCekgsaUpBQU9BLENBQUNxSixPQUFPLENBQUM7UUFDbEIsRUFBRSxPQUFPNUMsT0FBWTtZQUNuQlIsUUFBUVEsS0FBSyxDQUFDLGVBQWVBO1lBQzdCekcsaUpBQU9BLENBQUN5RyxLQUFLLENBQUMsU0FBa0MsT0FBekJBLE1BQU16RyxPQUFPLElBQUk7WUFDeEN3TixvQkFBQUEsOEJBQUFBLFFBQVUvRztRQUNaO0lBQ0Y7SUFFQSxTQUFTO0lBQ1QsTUFBTWlILG9CQUFvQjtRQUN4QmpMLGlCQUFpQjtRQUNqQixPQUFPO0lBQ1Q7SUFFQSxhQUFhO0lBQ2IsTUFBTWtMLDBCQUF3RCxPQUFPTDtRQUNuRSxNQUFNLEVBQUUzRSxJQUFJLEVBQUU0RSxTQUFTLEVBQUVDLE9BQU8sRUFBRSxHQUFHRjtRQUVyQyxJQUFJO1lBQ0YsZ0JBQWdCO1lBQ2hCLE1BQU03RixNQUFNLE1BQU1qSCxzREFBU0EsQ0FBQ2lOLFdBQVcsQ0FBQzlFO1lBQ3hDMUMsUUFBUUMsR0FBRyxDQUFDLG1CQUFtQnVCO1lBRS9CbkQsdUJBQXVCbUQ7WUFDdkI4RixzQkFBQUEsZ0NBQUFBLFVBQVk7Z0JBQUU5RixLQUFLQTtZQUFJO1lBQ3ZCekgsaUpBQU9BLENBQUNxSixPQUFPLENBQUM7UUFDbEIsRUFBRSxPQUFPNUMsT0FBWTtZQUNuQlIsUUFBUVEsS0FBSyxDQUFDLGVBQWVBO1lBQzdCekcsaUpBQU9BLENBQUN5RyxLQUFLLENBQUMsU0FBa0MsT0FBekJBLE1BQU16RyxPQUFPLElBQUk7WUFDeEN3TixvQkFBQUEsOEJBQUFBLFFBQVUvRztRQUNaO0lBQ0Y7SUFFQSxXQUFXO0lBQ1gsTUFBTW1ILDBCQUEwQjtRQUM5QnRKLHVCQUF1QjtRQUN2QixPQUFPO0lBQ1Q7SUFFQSxXQUFXO0lBQ1gsTUFBTXVKLGlDQUErRCxPQUFPUDtRQUMxRSxNQUFNLEVBQUUzRSxJQUFJLEVBQUU0RSxTQUFTLEVBQUVDLE9BQU8sRUFBRSxHQUFHRjtRQUVyQyxJQUFJO1lBQ0YsZ0JBQWdCO1lBQ2hCLE1BQU03RixNQUFNLE1BQU1qSCxzREFBU0EsQ0FBQ2lOLFdBQVcsQ0FBQzlFO1lBQ3hDMUMsUUFBUUMsR0FBRyxDQUFDLGlCQUFpQnVCO1lBRTdCakQsbUJBQW1Cc0MsQ0FBQUEsT0FBUTt1QkFBSUE7b0JBQU1XO2lCQUFJO1lBQ3pDOEYsc0JBQUFBLGdDQUFBQSxVQUFZO2dCQUFFOUYsS0FBS0E7Z0JBQUtDLE1BQU0sS0FBZUEsSUFBSTtZQUFDO1lBQ2xEMUgsaUpBQU9BLENBQUNxSixPQUFPLENBQUMsTUFBMEIsT0FBcEIsS0FBZTNCLElBQUksRUFBQztRQUM1QyxFQUFFLE9BQU9qQixPQUFZO1lBQ25CUixRQUFRUSxLQUFLLENBQUMsYUFBYUE7WUFDM0J6RyxpSkFBT0EsQ0FBQ3lHLEtBQUssQ0FBQyxNQUFtQ0EsT0FBN0IsS0FBZWlCLElBQUksRUFBQyxXQUFrQyxPQUF6QmpCLE1BQU16RyxPQUFPLElBQUk7WUFDbEV3TixvQkFBQUEsOEJBQUFBLFFBQVUvRztRQUNaO0lBQ0Y7SUFFQSxTQUFTO0lBQ1QsTUFBTXFILGlDQUFpQyxPQUFPbkY7WUFDcEJBO1FBQXhCLE1BQU1sQixNQUFNa0IsS0FBS2xCLEdBQUcsTUFBSWtCLGlCQUFBQSxLQUFLaUIsUUFBUSxjQUFiakIscUNBQUFBLGVBQWVsQixHQUFHO1FBQzFDakQsbUJBQW1Cc0MsQ0FBQUEsT0FBUUEsS0FBS2lILE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsTUFBTXZHO1FBQ2xELE9BQU87SUFDVDtJQUNBLFNBQVM7SUFDVCxNQUFNd0csb0JBQWtELE9BQU9YO1FBQzdELE1BQU0sRUFBRTNFLElBQUksRUFBRTRFLFNBQVMsRUFBRUMsT0FBTyxFQUFFLEdBQUdGO1FBRXJDLElBQUk7WUFDRixNQUFNN0YsTUFBTSxNQUFNakgsc0RBQVNBLENBQUNpTixXQUFXLENBQUM5RTtZQUN4QzFDLFFBQVFDLEdBQUcsQ0FBQyxpQkFBaUJ1QjtZQUU3Qi9DLGtCQUFrQitDO1lBQ2xCN0MsbUJBQW1CLEtBQWU4QyxJQUFJO1lBRXRDLGlCQUFpQjtZQUNqQixNQUFNd0csZUFBZXZHLFNBQVN3RyxhQUFhLENBQUM7WUFDNUNELGFBQWFFLEdBQUcsR0FBRzNHO1lBQ25CeUcsYUFBYUcsZ0JBQWdCLEdBQUc7Z0JBQzlCL0ksaUJBQWlCcUcsS0FBSzJDLEtBQUssQ0FBQ0osYUFBYUssUUFBUTtZQUNuRDtZQUVBaEIsc0JBQUFBLGdDQUFBQSxVQUFZO2dCQUFFOUYsS0FBS0E7WUFBSTtZQUN2QnpILGlKQUFPQSxDQUFDcUosT0FBTyxDQUFDO1FBQ2xCLEVBQUUsT0FBTzVDLE9BQVk7WUFDbkJSLFFBQVFRLEtBQUssQ0FBQyxhQUFhQTtZQUMzQnpHLGlKQUFPQSxDQUFDeUcsS0FBSyxDQUFDLFdBQW9DLE9BQXpCQSxNQUFNekcsT0FBTyxJQUFJO1lBQzFDd04sb0JBQUFBLDhCQUFBQSxRQUFVL0c7UUFDWjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQU0rSCxvQkFBb0I7UUFDeEI5SixrQkFBa0I7UUFDbEJFLG1CQUFtQjtRQUNuQlUsaUJBQWlCO1FBQ2pCLE9BQU87SUFDVDtJQUVBLFNBQVM7SUFDVCxNQUFNbUosdUJBQXFELE9BQU9uQjtRQUNoRSxNQUFNLEVBQUUzRSxJQUFJLEVBQUU0RSxTQUFTLEVBQUVDLE9BQU8sRUFBRSxHQUFHRjtRQUVyQyxJQUFJO1lBQ0YsTUFBTTdGLE1BQU0sTUFBTWpILHNEQUFTQSxDQUFDaU4sV0FBVyxDQUFDOUU7WUFDeEMxQyxRQUFRQyxHQUFHLENBQUMsaUJBQWlCdUI7WUFFN0IzQyxxQkFBcUIyQztZQUNyQnpDLHNCQUFzQixLQUFlMEMsSUFBSTtZQUN6QzZGLHNCQUFBQSxnQ0FBQUEsVUFBWTtnQkFBRTlGLEtBQUtBO1lBQUk7WUFDdkJ6SCxpSkFBT0EsQ0FBQ3FKLE9BQU8sQ0FBQztRQUNsQixFQUFFLE9BQU81QyxPQUFZO1lBQ25CUixRQUFRUSxLQUFLLENBQUMsYUFBYUE7WUFDM0J6RyxpSkFBT0EsQ0FBQ3lHLEtBQUssQ0FBQyxXQUFvQyxPQUF6QkEsTUFBTXpHLE9BQU8sSUFBSTtZQUMxQ3dOLG9CQUFBQSw4QkFBQUEsUUFBVS9HO1FBQ1o7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNaUksdUJBQXVCO1FBQzNCNUoscUJBQXFCO1FBQ3JCRSxzQkFBc0I7UUFDdEIsT0FBTztJQUNUO0lBRUEsU0FBUztJQUNULE1BQU0ySixvQkFBa0QsT0FBT3JCO1FBQzdELE1BQU0sRUFBRTNFLElBQUksRUFBRTRFLFNBQVMsRUFBRUMsT0FBTyxFQUFFLEdBQUdGO1FBRXJDLElBQUk7WUFDRixNQUFNN0YsTUFBTSxNQUFNakgsc0RBQVNBLENBQUNpTixXQUFXLENBQUM5RTtZQUN4QzFDLFFBQVFDLEdBQUcsQ0FBQyxpQkFBaUJ1QjtZQUU3QnZDLGtCQUFrQnVDO1lBQ2xCckMsbUJBQW1CLEtBQWVzQyxJQUFJO1lBQ3RDNkYsc0JBQUFBLGdDQUFBQSxVQUFZO2dCQUFFOUYsS0FBS0E7WUFBSTtZQUN2QnpILGlKQUFPQSxDQUFDcUosT0FBTyxDQUFDO1FBQ2xCLEVBQUUsT0FBTzVDLE9BQVk7WUFDbkJSLFFBQVFRLEtBQUssQ0FBQyxhQUFhQTtZQUMzQnpHLGlKQUFPQSxDQUFDeUcsS0FBSyxDQUFDLFdBQW9DLE9BQXpCQSxNQUFNekcsT0FBTyxJQUFJO1lBQzFDd04sb0JBQUFBLDhCQUFBQSxRQUFVL0c7UUFDWjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQU1tSSxvQkFBb0I7UUFDeEIxSixrQkFBa0I7UUFDbEJFLG1CQUFtQjtRQUNuQixPQUFPO0lBQ1Q7SUFNQSxVQUFVO0lBQ1YsTUFBTXlKLGdCQUFnQixPQUFPQztRQUMzQjdNLGlCQUFpQjZNO1FBQ2pCckosZUFBZWtILGNBQWMsQ0FBQ21DO1FBQzlCdk4sNEJBQTRCO0lBQzlCO0lBRUEsU0FBUztJQUNULE1BQU13TixrQkFBa0IsQ0FBQ2pPLGNBQWMsRUFBRSxFQUFFaU4sTUFBTSxDQUFDZSxDQUFBQSxTQUNoREEsT0FBT3BILElBQUksQ0FBQ3NILFdBQVcsR0FBR3JGLFFBQVEsQ0FBQ3pILGNBQWM4TSxXQUFXLE9BQzVERixPQUFPN0csV0FBVyxDQUFDK0csV0FBVyxHQUFHckYsUUFBUSxDQUFDekgsY0FBYzhNLFdBQVcsT0FDbkVGLE9BQU9HLFFBQVEsQ0FBQ0QsV0FBVyxHQUFHckYsUUFBUSxDQUFDekgsY0FBYzhNLFdBQVc7SUFHbEUsNEJBQTRCO0lBQzVCLE1BQU1FLG1CQUFtQjtRQUN2QixNQUFNQyxZQUFtQixFQUFFO1FBRTNCbEosUUFBUUMsR0FBRyxDQUFDO1FBQ1pELFFBQVFDLEdBQUcsQ0FBQyx3QkFBY3hEO1FBQzFCdUQsUUFBUUMsR0FBRyxDQUFDLHVCQUFhcUMsTUFBTTZHLElBQUksQ0FBQ3JNO1FBQ3BDa0QsUUFBUUMsR0FBRyxDQUFDLHVCQUFhdEQ7UUFFekJGLFdBQVcyTSxPQUFPLENBQUMxRSxDQUFBQTtZQUNqQixVQUFVO1lBQ1Z3RSxVQUFVRyxJQUFJLENBQUM7Z0JBQ2JDLEtBQUssVUFBb0IsT0FBVjVFLE9BQU9WLEVBQUU7Z0JBQ3hCQSxJQUFJVSxPQUFPVixFQUFFO2dCQUNibEMsT0FBTzRDLE9BQU81QyxLQUFLO2dCQUNuQjhCLFFBQVFjLE9BQU9kLE1BQU07Z0JBQ3JCMkYsTUFBTTtnQkFDTkMsWUFBWTFNLGVBQWV3SCxHQUFHLENBQUNJLE9BQU9WLEVBQUU7Z0JBQ3hDckQsVUFBVStELE9BQU9WLEVBQUU7WUFDckI7WUFFQSxpQkFBaUI7WUFDakIsSUFBSWxILGVBQWV3SCxHQUFHLENBQUNJLE9BQU9WLEVBQUUsR0FBRztnQkFDakMsTUFBTXlGLGFBQWE5TSxpQkFBaUIrTSxHQUFHLENBQUNoRixPQUFPVixFQUFFLEtBQUssRUFBRTtnQkFDeERoRSxRQUFRQyxHQUFHLENBQUMsbUJBQW1CLE9BQVZ5RSxPQUFPVixFQUFFLEVBQUMsV0FBU3lGO2dCQUV4Q0EsV0FBV0wsT0FBTyxDQUFDUCxDQUFBQTtvQkFDakJLLFVBQVVHLElBQUksQ0FBQzt3QkFDYkMsS0FBSyxVQUFvQixPQUFWVCxPQUFPN0UsRUFBRTt3QkFDeEJBLElBQUk2RSxPQUFPN0UsRUFBRTt3QkFDYmxDLE9BQU8rRyxPQUFPL0csS0FBSzt3QkFDbkI4QixRQUFRaUYsT0FBT2pGLE1BQU07d0JBQ3JCMkYsTUFBTTt3QkFDTjVJLFVBQVUrRCxPQUFPVixFQUFFO3dCQUNuQjJGLG1CQUFtQmpGLE9BQU81QyxLQUFLO29CQUNqQztnQkFDRjtZQUNGO1FBQ0Y7UUFFQTlCLFFBQVFDLEdBQUcsQ0FBQyx3QkFBY2lKO1FBQzFCLE9BQU9BO0lBQ1Q7SUFFQSxRQUFRO0lBQ1IsTUFBTVUsVUFBVTtRQUNkO1lBQ0U5SCxPQUFPO1lBQ1ArSCxXQUFXO1lBQ1hQLEtBQUs7WUFDTFEsT0FBTztRQUNUO1FBQ0E7WUFDRWhJLE9BQU87WUFDUCtILFdBQVc7WUFDWFAsS0FBSztZQUNMUyxRQUFRLENBQUNDLE1BQWNDO2dCQUNyQixJQUFJQSxPQUFPVixJQUFJLEtBQUssVUFBVTtvQkFDNUIscUJBQ0UsOERBQUNXO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQzlRLGlKQUFNQTtnQ0FDTGtRLE1BQUs7Z0NBQ0xhLE1BQUs7Z0NBQ0xDLFNBQVMsSUFBTWhHLHNCQUFzQjRGLE9BQU9qRyxFQUFFO2dDQUM5Q21HLFdBQVU7Z0NBQ1ZHLE9BQU87b0NBQUVDLFVBQVU7b0NBQVFDLFFBQVE7Z0NBQU87MENBRXpDUCxPQUFPVCxVQUFVLEdBQUcsTUFBTTs7Ozs7OzBDQUU3Qiw4REFBQ2lCO2dDQUFLTixXQUFVOzBDQUF1Q0g7Ozs7OzswQ0FDdkQsOERBQUNwUSxrSkFBR0E7Z0NBQUM4USxPQUFNO2dDQUFPUCxXQUFVOzBDQUFVOzs7Ozs7Ozs7Ozs7Z0JBRzVDLE9BQU87b0JBQ0wscUJBQ0UsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ007Z0NBQUtOLFdBQVU7MENBQWdCOzs7Ozs7MENBQ2hDLDhEQUFDTTtnQ0FBS04sV0FBVTswQ0FBaUJIOzs7Ozs7MENBQ2pDLDhEQUFDcFEsa0pBQUdBO2dDQUFDOFEsT0FBTTtnQ0FBUVAsV0FBVTswQ0FBVTs7Ozs7Ozs7Ozs7O2dCQUc3QztZQUNGO1FBQ0Y7UUFDQTtZQUNFckksT0FBTztZQUNQK0gsV0FBVztZQUNYUCxLQUFLO1lBQ0xRLE9BQU87WUFDUEMsUUFBUSxDQUFDbkcsUUFBZ0JxRztnQkFDdkIsTUFBTVUsa0JBQWtCLENBQUMvRztvQkFDdkIsT0FBUUE7d0JBQ04sS0FBSzs0QkFBRyxPQUFPO2dDQUFFOEcsT0FBTztnQ0FBU1YsTUFBTTs0QkFBTTt3QkFDN0MsS0FBSzs0QkFBRyxPQUFPO2dDQUFFVSxPQUFPO2dDQUFVVixNQUFNOzRCQUFLO3dCQUM3QyxLQUFLOzRCQUFHLE9BQU87Z0NBQUVVLE9BQU87Z0NBQU9WLE1BQU07NEJBQU07d0JBQzNDOzRCQUFTLE9BQU87Z0NBQUVVLE9BQU87Z0NBQVFWLE1BQU07NEJBQUs7b0JBQzlDO2dCQUNGO2dCQUVBLE1BQU1ZLFNBQVNELGdCQUFnQi9HO2dCQUMvQixxQkFBTyw4REFBQ2hLLGtKQUFHQTtvQkFBQzhRLE9BQU9FLE9BQU9GLEtBQUs7OEJBQUdFLE9BQU9aLElBQUk7Ozs7OztZQUMvQztRQUNGO1FBQ0E7WUFDRWxJLE9BQU87WUFDUHdILEtBQUs7WUFDTFEsT0FBTztZQUNQQyxRQUFRLENBQUNFO2dCQUNQLElBQUlBLE9BQU9WLElBQUksS0FBSyxVQUFVO29CQUM1QixxQkFDRSw4REFBQzVQLGtKQUFLQTt3QkFBQ3lRLE1BQUs7a0NBQ1YsNEVBQUMvUSxpSkFBTUE7NEJBQ0xrUSxNQUFLOzRCQUNMYSxNQUFLOzRCQUNMUyxvQkFBTSw4REFBQzVRLHNKQUFZQTs7Ozs7NEJBQ25Cb1EsU0FBUztnQ0FDUHRRLGlKQUFPQSxDQUFDK0wsSUFBSSxDQUFDOzRCQUNmO3NDQUNEOzs7Ozs7Ozs7OztnQkFLUCxPQUFPO29CQUNMLHFCQUNFLDhEQUFDbk0sa0pBQUtBO3dCQUFDeVEsTUFBSzs7MENBQ1YsOERBQUMvUSxpSkFBTUE7Z0NBQ0xrUSxNQUFLO2dDQUNMYSxNQUFLO2dDQUNMUyxvQkFBTSw4REFBQzVRLHNKQUFZQTs7Ozs7Z0NBQ25Cb1EsU0FBUztvQ0FDUHRRLGlKQUFPQSxDQUFDK0wsSUFBSSxDQUFDO2dDQUNmO2dDQUNBcUUsV0FBVTswQ0FDWDs7Ozs7OzBDQUdELDhEQUFDdFEsa0pBQVVBO2dDQUNUaUkscUJBQ0UsOERBQUNvSTs7c0RBQ0MsOERBQUNZO3NEQUFFOzs7Ozs7c0RBQ0gsOERBQUNBOzRDQUFFWCxXQUFVOztnREFBd0I7Z0RBQU1GLE9BQU9uSSxLQUFLOzs7Ozs7O3NEQUN2RCw4REFBQ2dKOzRDQUFFWCxXQUFVOztnREFBd0I7Z0RBQU1GLE9BQU9OLGlCQUFpQjs7Ozs7Ozs7Ozs7OztnQ0FHdkVvQixXQUFXO29DQUNUL0ssUUFBUUMsR0FBRyxDQUFDLDRCQUFrQmdLO29DQUM5QjdGLHNCQUFzQjZGLE9BQU9qRyxFQUFFLEVBQUVpRyxPQUFPdEosUUFBUTtnQ0FDbEQ7Z0NBQ0FxSyxRQUFPO2dDQUNQQyxZQUFXO2dDQUNYQyxRQUFPOzBDQUVQLDRFQUFDN1IsaUpBQU1BO29DQUNMa1EsTUFBSztvQ0FDTGEsTUFBSztvQ0FDTGUsTUFBTTtvQ0FDTk4sb0JBQU0sOERBQUMzUSxzSkFBY0E7Ozs7O29DQUNyQmlRLFdBQVU7OENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7O2dCQU1UO1lBQ0Y7UUFDRjtLQUNEO0lBRUQsU0FBUztJQUNULHNDQUFzQztJQUN0QyxVQUFVO0lBQ1YsMkRBQTJEO0lBQzNELDhCQUE4QjtJQUM5QiwrQkFBK0I7SUFDL0IsNENBQTRDO0lBQzVDLGVBQWU7SUFDZiwwQ0FBMEM7SUFDMUMsa0JBQWtCO0lBQ2xCLCtCQUErQjtJQUMvQiw4SEFBOEg7SUFDOUgsMkhBQTJIO0lBQzNILDZIQUE2SDtJQUM3SCw2SEFBNkg7SUFDN0gsNEhBQTRIO0lBQzVILDhIQUE4SDtJQUM5SCxXQUFXO0lBQ1gsbUNBQW1DO0lBQ25DLFFBQVE7SUFDUixzQkFBc0I7SUFDdEIseUNBQXlDO0lBQ3pDLGdCQUFnQjtJQUNoQiw2QkFBNkI7SUFDN0IsNEhBQTRIO0lBQzVILHlIQUF5SDtJQUN6SCwySEFBMkg7SUFDM0gsMkhBQTJIO0lBQzNILDBIQUEwSDtJQUMxSCw0SEFBNEg7SUFDNUgsU0FBUztJQUNULGlDQUFpQztJQUNqQyw4Q0FBOEM7SUFDOUMsTUFBTTtJQUNOLEtBQUs7SUFFTCx1QkFBdUI7SUFDdkIsTUFBTWxGLGtCQUFrQjtRQUN0QixJQUFJO1lBQ0ZqRixRQUFRQyxHQUFHLENBQUM7WUFDWixNQUFNLEVBQUVDLE1BQU1ILEdBQUcsRUFBRSxHQUFHLE1BQU16RixzREFBU0EsQ0FBQzhRLGFBQWEsQ0FBQztnQkFDbERoTCxNQUFNO2dCQUNOQyxVQUFVO2dCQUNWdUQsUUFBUSxFQUFPLFdBQVc7WUFDNUI7WUFFQTVELFFBQVFDLEdBQUcsQ0FBQyxxQ0FBMkJGO1lBRXZDLElBQUlBLElBQUlPLElBQUksS0FBSyxPQUFPUCxJQUFJRyxJQUFJLElBQUlILElBQUlHLElBQUksQ0FBQ0ssSUFBSSxFQUFFO2dCQUNqRCxNQUFNOEssT0FBT3RMLElBQUlHLElBQUksQ0FBQ0ssSUFBSSxDQUFDa0MsR0FBRyxDQUFDLENBQUM2SSxNQUFjO3dCQUM1Q3RILElBQUlzSCxJQUFJdEgsRUFBRTt3QkFDVnZDLE1BQU02SixJQUFJN0osSUFBSTt3QkFDZGlKLE9BQU9ZLElBQUlaLEtBQUs7d0JBQ2hCMUIsVUFBVXNDLElBQUl0QyxRQUFRO3dCQUN0QmhILGFBQWFzSixJQUFJdEosV0FBVyxJQUFJO29CQUNsQztnQkFFQTFGLGNBQWMrTztnQkFDZHJMLFFBQVFDLEdBQUcsQ0FBQyxpQkFBaUJvTDtZQUMvQixPQUFPO2dCQUNMckwsUUFBUXVMLElBQUksQ0FBQyxtQkFBbUJ4TDtnQkFDaEN6RCxjQUFjLEVBQUU7Z0JBQ2hCdUQsYUFBYXlELE9BQU8sQ0FBQztZQUN2QjtRQUNGLEVBQUUsT0FBTzlDLE9BQU87WUFDZFIsUUFBUVEsS0FBSyxDQUFDLGVBQWVBO1lBQzdCbEUsY0FBYyxFQUFFO1lBQ2hCdUQsYUFBYVcsS0FBSyxDQUFDO1FBQ3JCO0lBQ0Y7SUFFQSx1QkFBdUI7SUFDdkIsTUFBTWdMLG9CQUFvQjtRQUN4QixJQUFJO2dCQVVFekwsc0JBQUFBO1lBVEpDLFFBQVFDLEdBQUcsQ0FBQztZQUNaLE1BQU0sRUFBRUMsTUFBTUgsR0FBRyxFQUFFLEdBQUcsTUFBTXpGLHNEQUFTQSxDQUFDNkYsb0JBQW9CLENBQUM7Z0JBQ3pEQyxNQUFNO2dCQUNOQyxVQUFVLEdBQUcsZ0JBQWdCO1lBQy9CO1lBRUFMLFFBQVFDLEdBQUcsQ0FBQyw0Q0FBa0NGO1lBRTlDLFlBQVk7WUFDWixJQUFJQSxFQUFBQSxZQUFBQSxJQUFJRyxJQUFJLGNBQVJILGlDQUFBQSx1QkFBQUEsVUFBVTBMLFVBQVUsY0FBcEIxTCwyQ0FBQUEscUJBQXNCMkwsS0FBSyxJQUFHLElBQUk7Z0JBQ3BDMUwsUUFBUUMsR0FBRyxDQUFDLGFBQXVDLE9BQTFCRixJQUFJRyxJQUFJLENBQUN1TCxVQUFVLENBQUNDLEtBQUssRUFBQztZQUNyRDtZQUVBLElBQUkzTCxJQUFJTyxJQUFJLEtBQUssT0FBT1AsSUFBSUcsSUFBSSxFQUFFO2dCQUNoQ0YsUUFBUUMsR0FBRyxDQUFDLDhCQUFvQkYsSUFBSUcsSUFBSTtnQkFFeEMsSUFBSUgsSUFBSUcsSUFBSSxDQUFDSyxJQUFJLElBQUkrQixNQUFNQyxPQUFPLENBQUN4QyxJQUFJRyxJQUFJLENBQUNLLElBQUksR0FBRztvQkFDakRQLFFBQVFDLEdBQUcsQ0FBQyxvQkFBK0IsT0FBckJGLElBQUlHLElBQUksQ0FBQ0ssSUFBSSxDQUFDNkIsTUFBTSxFQUFDO29CQUUzQywwQkFBMEI7b0JBQzFCLE1BQU11SixrQkFBa0I1TCxJQUFJRyxJQUFJLENBQUNLLElBQUksQ0FBQ2tDLEdBQUcsQ0FBQyxDQUFDbUosTUFBV0M7NEJBZ0IxQ0Q7d0JBZlY1TCxRQUFRQyxHQUFHLENBQUMsb0JBQW9CLE9BQVY0TCxRQUFRLEdBQUUsVUFBUTs0QkFDdEM3SCxJQUFJNEgsS0FBSzVILEVBQUU7NEJBQ1hsQyxPQUFPOEosS0FBSzlKLEtBQUs7NEJBQ2pCa0gsVUFBVTRDLEtBQUs1QyxRQUFROzRCQUN2QjhDLGVBQWVGLEtBQUtFLGFBQWE7NEJBQ2pDVCxNQUFNTyxLQUFLUCxJQUFJO3dCQUNqQjt3QkFFQSxPQUFPOzRCQUNMckgsSUFBSTRILEtBQUs1SCxFQUFFOzRCQUNYbEMsT0FBTzhKLEtBQUs5SixLQUFLOzRCQUNqQkUsYUFBYTRKLEtBQUs1SixXQUFXOzRCQUM3QkMsWUFBWTJKLEtBQUszSixVQUFVLElBQUk7NEJBQy9CK0csVUFBVTRDLEtBQUtFLGFBQWEsSUFBS0YsQ0FBQUEsS0FBSzVDLFFBQVEsS0FBSyxJQUFJLE9BQU8sSUFBRzs0QkFDakUrQyxZQUFZLEVBQUU7NEJBQ2RDLFFBQVFKLEVBQUFBLGFBQUFBLEtBQUtQLElBQUksY0FBVE8saUNBQUFBLFdBQVduSixHQUFHLENBQUMsQ0FBQzZJLE1BQWFBLElBQUl0SCxFQUFFLE1BQUssRUFBRTs0QkFDbERpSSxXQUFXTCxLQUFLSyxTQUFTLElBQUksSUFBSUMsT0FBT0MsV0FBVzs0QkFDbkRDLFdBQVdSLEtBQUtRLFNBQVMsSUFBSSxJQUFJRixPQUFPQyxXQUFXO3dCQUNyRDtvQkFDRjtvQkFFQWhPLGdCQUFnQndOO29CQUNoQjNMLFFBQVFDLEdBQUcsQ0FBQyxpQkFBaUIwTDtnQkFDL0IsT0FBTztvQkFDTDNMLFFBQVF1TCxJQUFJLENBQUMsaUNBQWlDeEwsSUFBSUcsSUFBSTtvQkFDdEQvQixnQkFBZ0IsRUFBRTtnQkFDcEI7WUFDRixPQUFPO2dCQUNMNkIsUUFBUXVMLElBQUksQ0FBQyxtQkFBbUI7b0JBQzlCakwsTUFBTVAsSUFBSU8sSUFBSTtvQkFDZHZHLFNBQVNnRyxJQUFJaEcsT0FBTztvQkFDcEJtRyxNQUFNSCxJQUFJRyxJQUFJO2dCQUNoQjtnQkFDQS9CLGdCQUFnQixFQUFFO1lBQ3BCO1FBQ0YsRUFBRSxPQUFPcUMsT0FBTztZQUNkUixRQUFRUSxLQUFLLENBQUMsZUFBZUE7WUFDN0JyQyxnQkFBZ0IsRUFBRTtZQUNsQjBCLGFBQWFXLEtBQUssQ0FBQztRQUNyQjtJQUNGO0lBRUFySCxnREFBU0EsQ0FBQztRQUNSNkg7UUFFQWlFO1FBQ0F1RztJQUNGLEdBQUcsRUFBRTtJQUVMLHFCQUNFOzswQkFDRSw4REFBQ3BTLGtKQUFJQTtnQkFDSDBJLE9BQU07Z0JBQ051SyxxQkFBTyw4REFBQ2hULGlKQUFNQTtvQkFBQ2tRLE1BQUs7b0JBQVVjLFNBQVM7d0JBQ3JDcko7d0JBQ0E5Rix3QkFBd0I7b0JBQzFCOzhCQUFHOzs7Ozs7Z0JBQ0hpUCxXQUFVOzBCQUVWLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUM5USxpSkFBTUE7NEJBQUNpVCxLQUFLOzRCQUFDakMsU0FBUyxJQUFNalAsMkJBQTJCO3NDQUFPOzs7Ozs7c0NBRy9ELDhEQUFDL0IsaUpBQU1BOzRCQUFDaVQsS0FBSzs0QkFBQ2pDLFNBQVMsSUFBTTdPLDJCQUEyQjtzQ0FBTzs7Ozs7O3NDQUcvRCw4REFBQ25DLGlKQUFNQTs0QkFBQ2lULEtBQUs7NEJBQUNqQyxTQUFTLElBQU0zTyx3QkFBd0I7NEJBQU82TixNQUFLO3NDQUFTOzs7Ozs7c0NBRzFFLDhEQUFDbFEsaUpBQU1BOzRCQUFDaVQsS0FBSzs0QkFBQ2pDLFNBQVN4RDs0QkFBd0J5RCxPQUFPO2dDQUFFaUMsaUJBQWlCO2dDQUFTQyxhQUFhO2dDQUFXOUIsT0FBTzs0QkFBWTtzQ0FBRzs7Ozs7O3NDQUdoSSw4REFBQ3JSLGlKQUFNQTs0QkFBQ2lULEtBQUs7NEJBQUNqQyxTQUFTLElBQU16TywrQkFBK0I7NEJBQU8wTyxPQUFPO2dDQUFFaUMsaUJBQWlCO2dDQUFTQyxhQUFhO2dDQUFXOUIsT0FBTzs0QkFBWTtzQ0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT3hKLDhEQUFDblIsa0pBQUtBO2dCQUNKdUksT0FBTTtnQkFDTjJLLE1BQU14UjtnQkFDTnlSLFVBQVUsSUFBTXhSLHdCQUF3QjtnQkFDeEN5UixRQUFRO2dCQUNSN0MsT0FBTzs7a0NBRVAsOERBQUNJO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDM1A7d0NBQ0NvUyxhQUFZO3dDQUNaQyxVQUFVO3dDQUNWdkMsT0FBTzs0Q0FBRVIsT0FBTzt3Q0FBSTt3Q0FDcEJnRCxVQUFVNVE7d0NBQ1Y2USxVQUFVLENBQUNDLElBQU05USxpQkFBaUI4USxFQUFFQyxNQUFNLENBQUNDLEtBQUs7Ozs7OztrREFFbEQsOERBQUNoRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUM5USxpSkFBTUE7Z0RBQ0xrUSxNQUFLO2dEQUNMc0Isb0JBQU0sOERBQUM3USxzSkFBWUE7Ozs7O2dEQUNuQnFRLFNBQVMsSUFBTWpQLDJCQUEyQjswREFDM0M7Ozs7OzswREFHRCw4REFBQy9CLGlKQUFNQTtnREFDTGtRLE1BQUs7Z0RBQ0xzQixvQkFBTSw4REFBQzdRLHNKQUFZQTs7Ozs7Z0RBQ25CcVEsU0FBUyxJQUFNN08sMkJBQTJCOzBEQUMzQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU9MLDhEQUFDME87Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNNOztvREFBSztrRUFBUSw4REFBQzBDO3dEQUFPaEQsV0FBVTtrRUFBaUIxTixXQUFXMkYsTUFBTTs7Ozs7Ozs7Ozs7OzBEQUNsRSw4REFBQ3FJOztvREFBSztrRUFBTyw4REFBQzBDO3dEQUFPaEQsV0FBVTtrRUFBa0JyTixlQUFlc04sSUFBSTs7Ozs7Ozs7Ozs7OzBEQUNwRSw4REFBQ0s7O29EQUFLO2tFQUFRLDhEQUFDMEM7d0RBQU9oRCxXQUFVO2tFQUM3QjdILE1BQU02RyxJQUFJLENBQUN4TSxpQkFBaUJ1RSxNQUFNLElBQUlrTSxNQUFNLENBQUMsQ0FBQzFCLE9BQU90RixVQUFZc0YsUUFBUXRGLFFBQVFoRSxNQUFNLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFJOUYsOERBQUM4SDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUM5USxpSkFBTUE7Z0RBQ0wrUSxNQUFLO2dEQUNMYixNQUFLO2dEQUNMYyxTQUFTNUY7Z0RBQ1Q0SSxVQUFVcFE7Z0RBQ1ZrTixXQUFVOzBEQUNYOzs7Ozs7MERBR0QsOERBQUM5USxpSkFBTUE7Z0RBQ0wrUSxNQUFLO2dEQUNMYixNQUFLO2dEQUNMYyxTQUFTMUY7Z0RBQ1QwSSxVQUFVcFE7Z0RBQ1ZrTixXQUFVOzBEQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBT1AsOERBQUM3USxrSkFBS0E7d0JBQ0pzUSxTQUFTQTt3QkFDVDBELFlBQVlyRTt3QkFDWnNFLFFBQU87d0JBQ1B4UyxTQUFTa0M7d0JBQ1R3TyxZQUFZOzRCQUNWcEwsVUFBVTs0QkFDVm1OLGlCQUFpQjs0QkFDakJDLFdBQVcsQ0FBQy9CLFFBQVUsS0FBVyxPQUFOQSxPQUFNO3dCQUNuQzt3QkFDQXRCLE1BQUs7Ozs7Ozs7Ozs7OzswQkFLVCw4REFBQzdRLGtKQUFLQTtnQkFDSnVJLE9BQU07Z0JBQ04ySyxNQUFNdFI7Z0JBQ051UixVQUFVO29CQUNSdFIsMkJBQTJCO29CQUMzQmtFLGNBQWMrRCxXQUFXO29CQUN6QmhGLHVCQUF1QjtvQkFDdkJFLG1CQUFtQixFQUFFO29CQUNyQkUsa0JBQWtCO29CQUNsQkUsbUJBQW1CO29CQUNuQkUscUJBQXFCO29CQUNyQkUsc0JBQXNCO29CQUN0QkUsa0JBQWtCO29CQUNsQkUsbUJBQW1CO29CQUNuQkUsaUJBQWlCO2dCQUNuQjtnQkFDQXFPLE1BQU0sSUFBTXBPLGNBQWNxTyxNQUFNO2dCQUNoQzNDLFFBQU87Z0JBQ1BDLFlBQVc7MEJBRVgsNEVBQUN6UixpSkFBSUE7b0JBQ0hvVSxNQUFNdE87b0JBQ051TyxRQUFPO29CQUNQQyxVQUFVN007O3NDQUVWLDhEQUFDekgsaUpBQUlBLENBQUN1VSxJQUFJOzRCQUNSdE0sTUFBSzs0QkFDTHVNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7b0NBQU1uVSxTQUFTO2dDQUFZOzZCQUFFO3NDQUVqRCw0RUFBQ0wsaUpBQU1BO2dDQUNMa1QsYUFBWTtnQ0FDWnVCLFVBQVU7Z0NBQ1ZDLGtCQUFpQjtnQ0FDakI5RCxPQUFPO29DQUFFUixPQUFPO2dDQUFPOzBDQUV0QjVMLGFBQWF1RSxHQUFHLENBQUNpQyxDQUFBQSx1QkFDaEIsOERBQUNqSzt3Q0FBdUJ5UyxPQUFPeEksT0FBT1YsRUFBRTt3Q0FBRWxDLE9BQU8sR0FBcUI0QyxPQUFsQkEsT0FBTzVDLEtBQUssRUFBQyxPQUF3QixPQUFuQjRDLE9BQU8xQyxXQUFXO2tEQUN0Riw0RUFBQ2tJOzRDQUFJSSxPQUFPO2dEQUNWK0QsVUFBVTtnREFDVkMsY0FBYztnREFDZEMsWUFBWTtnREFDWkMsVUFBVTs0Q0FDWjs7OERBQ0UsOERBQUMvRDtvREFBS0gsT0FBTzt3REFBRW1FLFlBQVk7b0RBQUk7OERBQUkvSixPQUFPNUMsS0FBSzs7Ozs7OzhEQUMvQyw4REFBQzJJO29EQUFLSCxPQUFPO3dEQUFFb0UsVUFBVTt3REFBUWhFLE9BQU87d0RBQVFpRSxZQUFZO29EQUFNOzt3REFBRzt3REFDakVqSyxPQUFPc0UsUUFBUTt3REFBQzs7Ozs7Ozs7Ozs7Ozt1Q0FUWHRFLE9BQU9WLEVBQUU7Ozs7Ozs7Ozs7Ozs7OztzQ0FpQjVCLDhEQUFDeEssaUpBQUlBLENBQUN1VSxJQUFJOzRCQUNSdE0sTUFBSzs0QkFDTHVNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7b0NBQU1uVSxTQUFTO2dDQUFVOzZCQUFFO3NDQUUvQyw0RUFBQ04saUpBQUtBO2dDQUFDbVQsYUFBWTs7Ozs7Ozs7Ozs7c0NBR3JCLDhEQUFDcFQsaUpBQUlBLENBQUN1VSxJQUFJOzRCQUNSdE0sTUFBSzs0QkFDTHVNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7b0NBQU1uVSxTQUFTO2dDQUFVOzZCQUFFO3NDQUUvQyw0RUFBQ04saUpBQUtBLENBQUNtVixRQUFRO2dDQUNiQyxNQUFNO2dDQUNOakMsYUFBWTtnQ0FDWmtDLFNBQVM7Z0NBQ1RDLFdBQVc7Ozs7Ozs7Ozs7O3NDQUlmLDhEQUFDdlYsaUpBQUlBLENBQUN1VSxJQUFJOzRCQUNSQyxPQUFNOzRCQUNOQyxPQUFPO2dDQUFDO29DQUFFQyxVQUFVO29DQUFNblUsU0FBUztnQ0FBVTs2QkFBRTtzQ0FFL0MsNEVBQUNELGtKQUFNQSxDQUFDa1YsT0FBTztnQ0FDYnZOLE1BQUs7Z0NBQ0x3TixlQUFldkg7Z0NBQ2Z3SCxVQUFVdkg7Z0NBQ1Z3SCxRQUFPO2dDQUNQQyxVQUFVO2dDQUNWQyxVQUFTOzBDQUVSalIsb0NBQ0MsOERBQUM4TDs4Q0FDQyw0RUFBQ29GO3dDQUFJbkgsS0FBSy9KO3dDQUFxQm1SLEtBQUk7d0NBQVNqRixPQUFPOzRDQUFFUixPQUFPOzRDQUFRMEYsV0FBVzs0Q0FBU0MsV0FBVzt3Q0FBUTs7Ozs7Ozs7Ozs4REFHN0csOERBQUN2Rjs7c0RBQ0MsOERBQUNZOzRDQUFFWCxXQUFVO3NEQUNYLDRFQUFDL1Asc0pBQWFBOzs7Ozs7Ozs7O3NEQUVoQiw4REFBQzBROzRDQUFFWCxXQUFVO3NEQUFrQjs7Ozs7O3NEQUMvQiw4REFBQ1c7NENBQUVYLFdBQVU7c0RBQWtCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQVF2Qyw4REFBQzNRLGlKQUFJQSxDQUFDdVUsSUFBSTs0QkFDUnRNLE1BQUs7NEJBQ0x1TSxPQUFNOzRCQUNOQyxPQUFPO2dDQUNMO29DQUFFQyxVQUFVO29DQUFNblUsU0FBUztnQ0FBVTtnQ0FDckM7b0NBQ0V3UCxNQUFNO29DQUNObUcsS0FBSztvQ0FDTDNWLFNBQVM7b0NBQ1Q0VixXQUFXLENBQUN6QyxRQUFVMEMsT0FBTzFDO2dDQUMvQjs2QkFDRDs0QkFDRDJDLFNBQVE7c0NBRVIsNEVBQUNwVyxpSkFBS0E7Z0NBQUM4UCxNQUFLO2dDQUFTcUQsYUFBWTtnQ0FBcUI4QyxLQUFLOzs7Ozs7Ozs7OztzQ0FNN0QsOERBQUNsVyxpSkFBSUEsQ0FBQ3VVLElBQUk7NEJBQ1JDLE9BQU07NEJBQ042QixTQUFRO3NDQUVSLDRFQUFDL1Ysa0pBQU1BLENBQUNrVixPQUFPO2dDQUNidk4sTUFBSztnQ0FDTHdOLGVBQWVqSDtnQ0FDZmtILFVBQVUzRztnQ0FDVjRHLFFBQU87Z0NBQ1BDLFVBQVU7Z0NBQ1ZDLFVBQVM7MENBRVI3USwrQkFDQyw4REFBQzBMOztzREFDQyw4REFBQzNJOzRDQUNDNEcsS0FBSzNKOzRDQUNMOEwsT0FBTztnREFBRVIsT0FBTztnREFBUTBGLFdBQVc7NENBQVE7NENBQzNDTSxRQUFROzs7Ozs7c0RBRVYsOERBQUNoRjs0Q0FBRVIsT0FBTztnREFBRXlGLFdBQVc7Z0RBQUdyRixPQUFPOzRDQUFPO3NEQUNyQ2hNLG1CQUFtQjs7Ozs7Ozs7Ozs7OERBSXhCLDhEQUFDd0w7O3NEQUNDLDhEQUFDWTs0Q0FBRVgsV0FBVTtzREFDWCw0RUFBQy9QLHNKQUFhQTs7Ozs7Ozs7OztzREFFaEIsOERBQUMwUTs0Q0FBRVgsV0FBVTtzREFBa0I7Ozs7OztzREFDL0IsOERBQUNXOzRDQUFFWCxXQUFVO3NEQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FTdkMsOERBQUMzUSxpSkFBSUEsQ0FBQ3VVLElBQUk7NEJBQ1JDLE9BQU07NEJBQ042QixTQUFRO3NDQUVSLDRFQUFDL1Ysa0pBQU1BLENBQUNrVixPQUFPO2dDQUNidk4sTUFBSztnQ0FDTHdOLGVBQWV6RztnQ0FDZjBHLFVBQVV6RztnQ0FDVjBHLFFBQU87Z0NBQ1BDLFVBQVU7Z0NBQ1ZDLFVBQVM7MENBRVJ6USxrQ0FDQyw4REFBQ3NMOzhDQUNDLDRFQUFDQTt3Q0FBSUksT0FBTzs0Q0FBRTBGLFNBQVM7NENBQVFDLFdBQVc7d0NBQVM7OzBEQUNqRCw4REFBQzdWLHNKQUFhQTtnREFBQ2tRLE9BQU87b0RBQUVvRSxVQUFVO29EQUFRaEUsT0FBTztnREFBVTs7Ozs7OzBEQUMzRCw4REFBQ0k7Z0RBQUVSLE9BQU87b0RBQUV5RixXQUFXO29EQUFHckYsT0FBTztnREFBTzswREFDckM1TCxzQkFBc0I7Ozs7Ozs7Ozs7Ozs7Ozs7OERBSzdCLDhEQUFDb0w7O3NEQUNDLDhEQUFDWTs0Q0FBRVgsV0FBVTtzREFDWCw0RUFBQy9QLHNKQUFhQTs7Ozs7Ozs7OztzREFFaEIsOERBQUMwUTs0Q0FBRVgsV0FBVTtzREFBa0I7Ozs7OztzREFDL0IsOERBQUNXOzRDQUFFWCxXQUFVO3NEQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FTdkMsOERBQUMzUSxpSkFBSUEsQ0FBQ3VVLElBQUk7NEJBQ1JDLE9BQU07NEJBQ042QixTQUFRO3NDQUVSLDRFQUFDL1Ysa0pBQU1BLENBQUNrVixPQUFPO2dDQUNidk4sTUFBSztnQ0FDTHdOLGVBQWV2RztnQ0FDZndHLFVBQVV2RztnQ0FDVndHLFFBQU87Z0NBQ1BDLFVBQVU7Z0NBQ1ZDLFVBQVM7MENBRVJyUSwrQkFDQyw4REFBQ2tMOztzREFDQyw4REFBQ3ZJOzRDQUNDd0csS0FBS25KOzRDQUNMc0wsT0FBTztnREFBRVIsT0FBTzs0Q0FBTzs0Q0FDdkJnRyxRQUFROzs7Ozs7c0RBRVYsOERBQUNoRjs0Q0FBRVIsT0FBTztnREFBRXlGLFdBQVc7Z0RBQUdyRixPQUFPOzRDQUFPO3NEQUNyQ3hMLG1CQUFtQjs7Ozs7Ozs7Ozs7OERBSXhCLDhEQUFDZ0w7O3NEQUNDLDhEQUFDWTs0Q0FBRVgsV0FBVTtzREFDWCw0RUFBQy9QLHNKQUFhQTs7Ozs7Ozs7OztzREFFaEIsOERBQUMwUTs0Q0FBRVgsV0FBVTtzREFBa0I7Ozs7OztzREFDL0IsOERBQUNXOzRDQUFFWCxXQUFVO3NEQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FRdkMsOERBQUMzUSxpSkFBSUEsQ0FBQ3VVLElBQUk7NEJBQ1J0TSxNQUFLOzRCQUNMdU0sT0FBTTs0QkFDTjZCLFNBQVE7c0NBRVIsNEVBQUNuVyxpSkFBTUE7Z0NBQ0x3VyxNQUFLO2dDQUNMdEQsYUFBWTtnQ0FDWnRDLE9BQU87b0NBQUVSLE9BQU87Z0NBQU87Ozs7Ozs7Ozs7O3NDQUkzQiw4REFBQ3RRLGlKQUFJQSxDQUFDdVUsSUFBSTs0QkFDUkMsT0FBTTs0QkFDTjZCLFNBQVE7OzhDQUVSLDhEQUFDL1Ysa0pBQU1BO29DQUNMMkgsTUFBSztvQ0FDTHdOLGVBQWVySDtvQ0FDZnNILFVBQVVySDtvQ0FDVnNJLFFBQVE7b0NBQ1JoQixRQUFPOzhDQUVQLDRFQUFDOVYsaUpBQU1BO3dDQUFDd1Isb0JBQU0sOERBQUMxUSxzSkFBY0E7Ozs7O2tEQUFLOzs7Ozs7Ozs7Ozs4Q0FFcEMsOERBQUMrUDtvQ0FBSUksT0FBTzt3Q0FBRW9FLFVBQVU7d0NBQVFoRSxPQUFPO3dDQUFRcUYsV0FBVztvQ0FBRTs4Q0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUXJFLDhEQUFDeFcsa0pBQUtBO2dCQUNKdUksT0FBTTtnQkFDTjJLLE1BQU1wUjtnQkFDTnFSLFVBQVU7b0JBQ1JwUiw0QkFBNEI7b0JBQzVCVSxpQkFBaUI7b0JBQ2pCd0QsZUFBZTZELFdBQVc7Z0JBQzVCO2dCQUNBcUssTUFBTSxJQUFNbE8sZUFBZW1PLE1BQU07Z0JBQ2pDM0MsUUFBTztnQkFDUEMsWUFBVzswQkFFWCw0RUFBQ3pSLGlKQUFJQTtvQkFDSG9VLE1BQU1wTztvQkFDTnFPLFFBQU87b0JBQ1BDLFVBQVVoSzs7c0NBRVYsOERBQUN0SyxpSkFBSUEsQ0FBQ3VVLElBQUk7NEJBQ1J0TSxNQUFLOzRCQUNMdU0sT0FBTTs0QkFDTkMsT0FBTztnQ0FBQztvQ0FBRUMsVUFBVTtvQ0FBTW5VLFNBQVM7Z0NBQVU7NkJBQUU7c0NBRS9DLDRFQUFDTixpSkFBS0E7Z0NBQUNtVCxhQUFZOzs7Ozs7Ozs7OztzQ0FHckIsOERBQUNwVCxpSkFBSUEsQ0FBQ3VVLElBQUk7NEJBQ1J0TSxNQUFLOzRCQUNMdU0sT0FBTTs0QkFDTkMsT0FBTztnQ0FBQztvQ0FBRUMsVUFBVTtvQ0FBTW5VLFNBQVM7Z0NBQVU7NkJBQUU7c0NBRS9DLDRFQUFDTixpSkFBS0EsQ0FBQ21WLFFBQVE7Z0NBQUNDLE1BQU07Z0NBQUdqQyxhQUFZOzs7Ozs7Ozs7OztzQ0FHdkMsOERBQUNwVCxpSkFBSUEsQ0FBQ3VVLElBQUk7NEJBQ1J0TSxNQUFLOzRCQUNMdU0sT0FBTTs0QkFDTkMsT0FBTztnQ0FBQztvQ0FBRUMsVUFBVTtvQ0FBTW5VLFNBQVM7Z0NBQVU7NkJBQUU7c0NBRS9DLDRFQUFDTCxpSkFBTUE7Z0NBQUNrVCxhQUFZOztrREFDbEIsOERBQUNuUzt3Q0FBT3lTLE9BQU07a0RBQU87Ozs7OztrREFDckIsOERBQUN6Uzt3Q0FBT3lTLE9BQU07a0RBQU87Ozs7OztrREFDckIsOERBQUN6Uzt3Q0FBT3lTLE9BQU07a0RBQU87Ozs7OztrREFDckIsOERBQUN6Uzt3Q0FBT3lTLE9BQU07a0RBQU87Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUl6Qiw4REFBQzFULGlKQUFJQSxDQUFDdVUsSUFBSTs0QkFDUnRNLE1BQUs7NEJBQ0x1TSxPQUFNOzRCQUNOQyxPQUFPO2dDQUFDO29DQUFFQyxVQUFVO29DQUFNblUsU0FBUztnQ0FBVTs2QkFBRTtzQ0FFL0MsNEVBQUNMLGlKQUFNQTs7a0RBQ0wsOERBQUNlO3dDQUFPeVMsT0FBTTtrREFBUzs7Ozs7O2tEQUN2Qiw4REFBQ3pTO3dDQUFPeVMsT0FBTTtrREFBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPakMsOERBQUMzVCxrSkFBS0E7Z0JBQ0p1SSxPQUFNO2dCQUNOMkssTUFBTWxSO2dCQUNObVIsVUFBVTtvQkFDUmxSLDJCQUEyQjtvQkFDM0JpRSxjQUFjNEQsV0FBVztvQkFDekI3RyxpQkFBaUI7Z0JBQ25CO2dCQUNBa1IsTUFBTSxJQUFNak8sY0FBY2tPLE1BQU07Z0JBQ2hDM0MsUUFBTztnQkFDUEMsWUFBVztnQkFDWG5CLE9BQU87MEJBRVAsNEVBQUN0USxpSkFBSUE7b0JBQ0hvVSxNQUFNbk87b0JBQ05vTyxRQUFPO29CQUNQQyxVQUFVbEo7O3NDQUVWLDhEQUFDcEwsaUpBQUlBLENBQUN1VSxJQUFJOzRCQUNSdE0sTUFBSzs0QkFDTHVNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7b0NBQU1uVSxTQUFTO2dDQUFZOzZCQUFFO3NDQUVqRCw0RUFBQ04saUpBQUtBO2dDQUFDbVQsYUFBWTs7Ozs7Ozs7Ozs7c0NBR3JCLDhEQUFDcFQsaUpBQUlBLENBQUN1VSxJQUFJOzRCQUNSdE0sTUFBSzs0QkFDTHVNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7b0NBQU1uVSxTQUFTO2dDQUFVOzZCQUFFO3NDQUUvQyw0RUFBQ04saUpBQUtBLENBQUNtVixRQUFRO2dDQUNiQyxNQUFNO2dDQUNOakMsYUFBWTtnQ0FDWmtDLFNBQVM7Z0NBQ1RDLFdBQVc7Ozs7Ozs7Ozs7O3NDQUlmLDhEQUFDdlYsaUpBQUlBLENBQUN1VSxJQUFJOzRCQUNSQyxPQUFNOzRCQUNOQyxPQUFPO2dDQUFDO29DQUFFQyxVQUFVO29DQUFNblUsU0FBUztnQ0FBVTs2QkFBRTtzQ0FFL0MsNEVBQUNELGtKQUFNQSxDQUFDa1YsT0FBTztnQ0FDYnZOLE1BQUs7Z0NBQ0x3TixlQUFlN0g7Z0NBQ2Y4SCxVQUFVekg7Z0NBQ1YwSCxRQUFPO2dDQUNQQyxVQUFVO2dDQUNWQyxVQUFTOzBDQUVSOVMsOEJBQ0MsOERBQUMyTjs4Q0FDQyw0RUFBQ29GO3dDQUFJbkgsS0FBSzVMO3dDQUFlZ1QsS0FBSTt3Q0FBT2pGLE9BQU87NENBQUVSLE9BQU87NENBQVEwRixXQUFXOzRDQUFTQyxXQUFXO3dDQUFROzs7Ozs7Ozs7OzhEQUdyRyw4REFBQ3ZGOztzREFDQyw4REFBQ1k7NENBQUVYLFdBQVU7c0RBQ1gsNEVBQUMvUCxzSkFBYUE7Ozs7Ozs7Ozs7c0RBRWhCLDhEQUFDMFE7NENBQUVYLFdBQVU7c0RBQWtCOzs7Ozs7c0RBQy9CLDhEQUFDVzs0Q0FBRVgsV0FBVTtzREFBa0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBVXZDLDhEQUFDM1EsaUpBQUlBLENBQUN1VSxJQUFJOzRCQUNSdE0sTUFBSzs0QkFDTHVNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7b0NBQU1uVSxTQUFTO2dDQUFlOzZCQUFFOzRCQUNwRHFXLGNBQWM7c0NBRWQsNEVBQUMxVyxpSkFBTUE7Z0NBQUNrVCxhQUFZOztrREFDbEIsOERBQUNuUzt3Q0FBT3lTLE9BQU87a0RBQUc7Ozs7OztrREFDbEIsOERBQUN6Uzt3Q0FBT3lTLE9BQU87a0RBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUl0Qiw4REFBQzFULGlKQUFJQSxDQUFDdVUsSUFBSTs0QkFDUnRNLE1BQUs7NEJBQ0x1TSxPQUFNOzRCQUNOQyxPQUFPO2dDQUFDO29DQUFFQyxVQUFVO29DQUFNblUsU0FBUztnQ0FBVTs2QkFBRTtzQ0FFL0MsNEVBQUNOLGlKQUFLQTtnQ0FDSm1ULGFBQVk7Z0NBQ1prQyxTQUFTO2dDQUNUQyxXQUFXOzs7Ozs7Ozs7OztzQ0FJZiw4REFBQ3ZWLGlKQUFJQSxDQUFDdVUsSUFBSTs0QkFDUnRNLE1BQUs7NEJBQ0x1TSxPQUFNOzRCQUNOQyxPQUFPO2dDQUFDO29DQUFFQyxVQUFVO29DQUFNblUsU0FBUztnQ0FBUTs2QkFBRTtzQ0FFN0MsNEVBQUNMLGlKQUFNQTtnQ0FDTHdXLE1BQUs7Z0NBQ0x0RCxhQUFZO2dDQUNaeUQsaUJBQWdCOzBDQUVmaFUsV0FBV29HLEdBQUcsQ0FBQzZJLENBQUFBLG9CQUNkLDhEQUFDN1E7d0NBQW9CeVMsT0FBTzVCLElBQUl0SCxFQUFFO3dDQUFFZ0ssT0FBTzFDLElBQUk3SixJQUFJO2tEQUNqRCw0RUFBQzdILGtKQUFHQTs0Q0FBQzhRLE9BQU9ZLElBQUlaLEtBQUs7c0RBQUdZLElBQUk3SixJQUFJOzs7Ozs7dUNBRHJCNkosSUFBSXRILEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVU3Qiw4REFBQ3pLLGtKQUFLQTtnQkFDSnVJLE9BQU07Z0JBQ04ySyxNQUFNaFI7Z0JBQ05pUixVQUFVO29CQUNSaFIsd0JBQXdCO29CQUN4QmdFLFdBQVcyRCxXQUFXO2dCQUN4QjtnQkFDQXFLLE1BQU0sSUFBTWhPLFdBQVdpTyxNQUFNO2dCQUM3QjNDLFFBQU87Z0JBQ1BDLFlBQVc7Z0JBQ1huQixPQUFPOzBCQUVQLDRFQUFDdFEsaUpBQUlBO29CQUNIb1UsTUFBTWxPO29CQUNObU8sUUFBTztvQkFDUEMsVUFBVS9JOztzQ0FFViw4REFBQ3ZMLGlKQUFJQSxDQUFDdVUsSUFBSTs0QkFDUnRNLE1BQUs7NEJBQ0x1TSxPQUFNOzRCQUNOQyxPQUFPO2dDQUNMO29DQUFFQyxVQUFVO29DQUFNblUsU0FBUztnQ0FBVTtnQ0FDckM7b0NBQUV1VyxLQUFLO29DQUFJdlcsU0FBUztnQ0FBZ0I7NkJBQ3JDO3NDQUVELDRFQUFDTixpSkFBS0E7Z0NBQUNtVCxhQUFZOzs7Ozs7Ozs7OztzQ0FHckIsOERBQUNwVCxpSkFBSUEsQ0FBQ3VVLElBQUk7NEJBQ1J0TSxNQUFLOzRCQUNMdU0sT0FBTTs0QkFDTkMsT0FBTztnQ0FBQztvQ0FBRUMsVUFBVTtvQ0FBTW5VLFNBQVM7Z0NBQVU7NkJBQUU7NEJBQy9DcVcsY0FBYTtzQ0FFYiw0RUFBQzFXLGlKQUFNQTtnQ0FBQ2tULGFBQVk7O2tEQUNsQiw4REFBQ25TO3dDQUFPeVMsT0FBTTtrREFDWiw0RUFBQ2hEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7b0RBQWtCRyxPQUFPO3dEQUFFaUMsaUJBQWlCO29EQUFVOzs7Ozs7Z0RBQVM7Ozs7Ozs7Ozs7OztrREFJbEYsOERBQUM5Ujt3Q0FBT3lTLE9BQU07a0RBQ1osNEVBQUNoRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVO29EQUFrQkcsT0FBTzt3REFBRWlDLGlCQUFpQjtvREFBVTs7Ozs7O2dEQUFTOzs7Ozs7Ozs7Ozs7a0RBSWxGLDhEQUFDOVI7d0NBQU95UyxPQUFNO2tEQUNaLDRFQUFDaEQ7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTtvREFBa0JHLE9BQU87d0RBQUVpQyxpQkFBaUI7b0RBQVU7Ozs7OztnREFBUzs7Ozs7Ozs7Ozs7O2tEQUlsRiw4REFBQzlSO3dDQUFPeVMsT0FBTTtrREFDWiw0RUFBQ2hEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7b0RBQWtCRyxPQUFPO3dEQUFFaUMsaUJBQWlCO29EQUFVOzs7Ozs7Z0RBQVM7Ozs7Ozs7Ozs7OztrREFJbEYsOERBQUM5Ujt3Q0FBT3lTLE9BQU07a0RBQ1osNEVBQUNoRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVO29EQUFrQkcsT0FBTzt3REFBRWlDLGlCQUFpQjtvREFBVTs7Ozs7O2dEQUFTOzs7Ozs7Ozs7Ozs7a0RBSWxGLDhEQUFDOVI7d0NBQU95UyxPQUFNO2tEQUNaLDRFQUFDaEQ7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTtvREFBa0JHLE9BQU87d0RBQUVpQyxpQkFBaUI7b0RBQVU7Ozs7OztnREFBUzs7Ozs7Ozs7Ozs7O2tEQUlsRiw4REFBQzlSO3dDQUFPeVMsT0FBTTtrREFDWiw0RUFBQ2hEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7b0RBQWtCRyxPQUFPO3dEQUFFaUMsaUJBQWlCO29EQUFVOzs7Ozs7Z0RBQVM7Ozs7Ozs7Ozs7OztrREFJbEYsOERBQUM5Ujt3Q0FBT3lTLE9BQU07a0RBQ1osNEVBQUNoRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVO29EQUFrQkcsT0FBTzt3REFBRWlDLGlCQUFpQjtvREFBVTs7Ozs7O2dEQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FPdEYsOERBQUMvUyxpSkFBSUEsQ0FBQ3VVLElBQUk7NEJBQ1J0TSxNQUFLOzRCQUNMdU0sT0FBTTs0QkFDTkMsT0FBTztnQ0FBQztvQ0FBRUMsVUFBVTtvQ0FBTW5VLFNBQVM7Z0NBQVU7NkJBQUU7NEJBQy9DcVcsY0FBYztzQ0FFZCw0RUFBQzFXLGlKQUFNQTtnQ0FBQ2tULGFBQVk7O2tEQUNsQiw4REFBQ25TO3dDQUFPeVMsT0FBTztrREFBRzs7Ozs7O2tEQUNsQiw4REFBQ3pTO3dDQUFPeVMsT0FBTztrREFBRzs7Ozs7O2tEQUNsQiw4REFBQ3pTO3dDQUFPeVMsT0FBTztrREFBRzs7Ozs7O2tEQUNsQiw4REFBQ3pTO3dDQUFPeVMsT0FBTztrREFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSXRCLDhEQUFDMVQsaUpBQUlBLENBQUN1VSxJQUFJOzRCQUNSdE0sTUFBSzs0QkFDTHVNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVxQyxLQUFLO29DQUFLdlcsU0FBUztnQ0FBaUI7NkJBQUU7c0NBRWhELDRFQUFDTixpSkFBS0EsQ0FBQ21WLFFBQVE7Z0NBQ2JDLE1BQU07Z0NBQ05qQyxhQUFZO2dDQUNaa0MsU0FBUztnQ0FDVEMsV0FBVzs7Ozs7Ozs7Ozs7c0NBSWYsOERBQUN2VixpSkFBSUEsQ0FBQ3VVLElBQUk7NEJBQ1J0TSxNQUFLOzRCQUNMdU0sT0FBTTs0QkFDTkMsT0FBTztnQ0FBQztvQ0FBRUMsVUFBVTtvQ0FBTW5VLFNBQVM7Z0NBQVU7NkJBQUU7NEJBQy9DcVcsY0FBYztzQ0FFZCw0RUFBQzFXLGlKQUFNQTtnQ0FBQ2tULGFBQVk7O2tEQUNsQiw4REFBQ25TO3dDQUFPeVMsT0FBTztrREFBRzs7Ozs7O2tEQUNsQiw4REFBQ3pTO3dDQUFPeVMsT0FBTztrREFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPMUIsOERBQUMzVCxrSkFBS0E7Z0JBQ0p1SSxPQUFNO2dCQUNOMkssTUFBTTlRO2dCQUNOK1EsVUFBVTtvQkFDUjlRLCtCQUErQjtvQkFDL0IrRCxrQkFBa0IwRCxXQUFXO2dCQUMvQjtnQkFDQXFLLE1BQU0sSUFBTS9OLGtCQUFrQmdPLE1BQU07Z0JBQ3BDM0MsUUFBTztnQkFDUEMsWUFBVztnQkFDWG5CLE9BQU87MEJBRVAsNEVBQUN0USxpSkFBSUE7b0JBQ0hvVSxNQUFNak87b0JBQ05rTyxRQUFPO29CQUNQQyxVQUFVNUk7O3NDQUVWLDhEQUFDMUwsaUpBQUlBLENBQUN1VSxJQUFJOzRCQUNSdE0sTUFBSzs0QkFDTHVNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7b0NBQU1uVSxTQUFTO2dDQUFjOzZCQUFFO3NDQUVuRCw0RUFBQ0wsaUpBQU1BO2dDQUNMa1QsYUFBWTtnQ0FDWnVCLFVBQVU7Z0NBQ1ZvQyxjQUFjLENBQUNDLE9BQU9DO3dDQUNuQkE7MkNBQUFBLG1CQUFBQSw4QkFBQUEsbUJBQUFBLE9BQVFDLFFBQVEsY0FBaEJELHVDQUFELGlCQUF5QzFILFdBQVcsR0FBR3JGLFFBQVEsQ0FBQzhNLE1BQU16SCxXQUFXOzswQ0FHbEY3SyxhQUFhdUUsR0FBRyxDQUFDLENBQUNpQyx1QkFDakIsOERBQUNqSzt3Q0FBdUJ5UyxPQUFPeEksT0FBT1YsRUFBRTs7NENBQ3JDVSxPQUFPNUMsS0FBSzs0Q0FBQzs0Q0FBRzRDLE9BQU9zRSxRQUFROzRDQUFDOzt1Q0FEdEJ0RSxPQUFPVixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7c0NBTzVCLDhEQUFDeEssaUpBQUlBLENBQUN1VSxJQUFJOzRCQUNSdE0sTUFBSzs0QkFDTHVNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7Z0NBQU07NkJBQUU7c0NBRTVCLDRFQUFDelUsaUpBQUtBLENBQUNtVixRQUFRO2dDQUNiaEMsYUFBWTtnQ0FDWmlDLE1BQU07Z0NBQ05FLFdBQVc7Z0NBQ1hELFNBQVM7Ozs7Ozs7Ozs7O3NDQUliLDhEQUFDNUU7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDd0c7b0NBQUd4RyxXQUFVOzhDQUF5Qzs7Ozs7OzhDQUN2RCw4REFBQ3lHO29DQUFHekcsV0FBVTs7c0RBQ1osOERBQUMwRztzREFBRzs7Ozs7O3NEQUNKLDhEQUFDQTtzREFBRzs7Ozs7O3NEQUNKLDhEQUFDQTtzREFBRzs7Ozs7O3NEQUNKLDhEQUFDQTtzREFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT1osOERBQUN0WCxrSkFBS0E7Z0JBQ0p1SSxPQUFNO2dCQUNOMkssTUFBTTVRO2dCQUNONlEsVUFBVTlGO2dCQUNWK0YsUUFBUTtnQkFDUjdDLE9BQU87Z0JBQ1BnSCxjQUFjOzBCQUVkLDRFQUFDdFgsaUpBQUlBO29CQUNIb1UsTUFBTWhPO29CQUNOaU8sUUFBTztvQkFDUEMsVUFBVWhIO29CQUNWcUQsV0FBVTs7c0NBR1YsOERBQUMzUSxpSkFBSUEsQ0FBQ3VVLElBQUk7NEJBQ1J0TSxNQUFLOzRCQUNMdU0sT0FBTTs0QkFDTkMsT0FBTztnQ0FBQztvQ0FBRUMsVUFBVTtvQ0FBTW5VLFNBQVM7Z0NBQVU7NkJBQUU7c0NBRS9DLDRFQUFDTCxpSkFBTUE7Z0NBQ0xrVCxhQUFZO2dDQUNaN1IsU0FBU2lEO2dDQUNUK08sVUFBVXRHO2dDQUNWMEgsVUFBVTtnQ0FDVm9DLGNBQWMsQ0FBQ0MsT0FBT0M7d0NBQ25CQTsyQ0FBQUEsbUJBQUFBLDhCQUFBQSxtQkFBQUEsT0FBUUMsUUFBUSxjQUFoQkQsdUNBQUQsaUJBQXlDMUgsV0FBVyxHQUFHckYsUUFBUSxDQUFDOE0sTUFBTXpILFdBQVc7OzBDQUdsRnBPLDBCQUEwQjhILEdBQUcsQ0FBQ2lDLENBQUFBLHVCQUM3Qiw4REFBQ2pLO3dDQUF1QnlTLE9BQU94SSxPQUFPVixFQUFFOzs0Q0FDckNVLE9BQU81QyxLQUFLOzRDQUFDOzRDQUFPNEMsT0FBT1YsRUFBRTs0Q0FBQzs7dUNBRHBCVSxPQUFPVixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7c0NBUTVCLDhEQUFDeEssaUpBQUlBLENBQUN1VSxJQUFJOzRCQUNSdE0sTUFBSzs0QkFDTHVNLE9BQU07NEJBQ05DLE9BQU87Z0NBQUM7b0NBQUVDLFVBQVU7b0NBQU1uVSxTQUFTO2dDQUFhOzZCQUFFO3NDQUVsRCw0RUFBQ0wsaUpBQU1BO2dDQUNMa1QsYUFBYXpQLDJCQUEyQixlQUFlO2dDQUN2RGtRLFVBQVUsQ0FBQ2xRO2dDQUNYcEMsU0FBU2lELHNCQUFzQixDQUFDLENBQUNiO2dDQUNqQzRQLFVBQVVwRztnQ0FDVndILFVBQVU7Z0NBQ1ZvQyxjQUFjLENBQUNDLE9BQU9DO3dDQUNuQkE7MkNBQUFBLG1CQUFBQSw4QkFBQUEsbUJBQUFBLE9BQVFDLFFBQVEsY0FBaEJELHVDQUFELGlCQUF5QzFILFdBQVcsR0FBR3JGLFFBQVEsQ0FBQzhNLE1BQU16SCxXQUFXOztnQ0FFbkZnSSxpQkFDRS9TLHNCQUFzQmIsMkJBQ2xCLGVBQ0FBLDJCQUNFLGFBQ0E7MENBR1B2QywwQkFBMEJ3SCxNQUFNLEdBQUcsSUFDbEN4SCwwQkFBMEI2SCxHQUFHLENBQUNvRyxDQUFBQSx1QkFDNUIsOERBQUNwTzt3Q0FBdUJ5UyxPQUFPckUsT0FBTzdFLEVBQUU7a0RBQ3RDLDRFQUFDa0c7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDTTs4REFBTTVCLE9BQU8vRyxLQUFLOzs7Ozs7OERBQ25CLDhEQUFDb0k7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDdlEsa0pBQUdBOzREQUFDOFEsT0FBTzdCLE9BQU9qRixNQUFNLEtBQUssSUFBSSxVQUFVaUYsT0FBT2pGLE1BQU0sS0FBSyxJQUFJLFdBQVc7NERBQU91RyxXQUFVO3NFQUMzRnRCLE9BQU9qRixNQUFNLEtBQUssSUFBSSxRQUFRaUYsT0FBT2pGLE1BQU0sS0FBSyxJQUFJLE9BQU87Ozs7OztzRUFFOUQsOERBQUM2Rzs0REFBS04sV0FBVTs7Z0VBQXdCO2dFQUFLdEIsT0FBTzdFLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7dUNBUC9DNkUsT0FBTzdFLEVBQUU7Ozs7cURBYXhCN0csNEJBQTRCLENBQUNhLG1DQUMzQiw4REFBQ3ZEO29DQUFPNFMsUUFBUTtvQ0FBQ0gsT0FBTTs4Q0FBYTs7Ozs7Z0RBR2xDOzs7Ozs7Ozs7OztzQ0FNViw4REFBQ2hEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ3dHO29DQUFHeEcsV0FBVTs4Q0FBaUM7Ozs7Ozs4Q0FDL0MsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ1c7O2dEQUFFO2dEQUFVM04sNEJBQTRCOzs7Ozs7O3NEQUN6Qyw4REFBQzJOOztnREFBRTtnREFBVXhOLDRCQUE0Qjs7Ozs7OztzREFDekMsOERBQUN3Tjs7Z0RBQUU7Z0RBQVNuUSwwQkFBMEJ5SCxNQUFNOzs7Ozs7O3NEQUM1Qyw4REFBQzBJOztnREFBRTtnREFBVWxRLDBCQUEwQndILE1BQU07Ozs7Ozs7c0RBQzdDLDhEQUFDMEk7O2dEQUFFO2dEQUFPOU0scUJBQXFCLFFBQVE7Ozs7Ozs7d0NBQ3RDcEQsMEJBQTBCd0gsTUFBTSxHQUFHLG1CQUNsQyw4REFBQzhIOzs4REFDQyw4REFBQ1k7OERBQUU7Ozs7Ozs4REFDSCw4REFBQzhGO29EQUFHekcsV0FBVTs4REFDWHZQLDBCQUEwQjZILEdBQUcsQ0FBQ29HLENBQUFBLHVCQUM3Qiw4REFBQ2dJOztnRUFBbUI7Z0VBQUtoSSxPQUFPN0UsRUFBRTtnRUFBQztnRUFBTzZFLE9BQU8vRyxLQUFLOzsyREFBN0MrRyxPQUFPN0UsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt3QkFTN0IxRywwQ0FDQyw4REFBQzRNOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ3dHO29DQUFHeEcsV0FBVTs4Q0FBeUM7Ozs7Ozs4Q0FDdkQsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ1c7O2dEQUFFO2dEQUFPeE47Ozs7Ozs7c0RBQ1YsOERBQUN3Tjs7Z0RBQUU7aURBQU9uUSxrQ0FBQUEsMEJBQTBCcU0sSUFBSSxDQUFDZ0ssQ0FBQUEsSUFBS0EsRUFBRWhOLEVBQUUsS0FBSzdHLHVDQUE3Q3hDLHNEQUFBQSxnQ0FBd0VtSCxLQUFLOzs7Ozs7O3NEQUN2Riw4REFBQ2dKOztnREFBRTtpREFBT2xRLGtDQUFBQSwwQkFBMEJvTSxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVqRCxFQUFFLEtBQUsxRyx1Q0FBN0MxQyxzREFBQUEsZ0NBQXdFa0gsS0FBSzs7Ozs7OztzREFDdkYsOERBQUNnSjs0Q0FBRVgsV0FBVTtzREFBaUM7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLcEQsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ1o3TSx5Q0FDQyw4REFBQ21OO3dDQUFLTixXQUFVO2tEQUFpQjs7Ozs7a0VBRWpDLDhEQUFDTTtrREFBSzs7Ozs7Ozs7Ozs7OENBR1YsOERBQUNQO29DQUFJQyxXQUFVOztzREFDYiw4REFBQzlRLGlKQUFNQTs0Q0FBQ2dSLFNBQVN6RDtzREFBeUI7Ozs7OztzREFHMUMsOERBQUN2TixpSkFBTUE7NENBQ0xrUSxNQUFLOzRDQUNMMEgsVUFBUzs0Q0FDVGxXLFNBQVNpRDs0Q0FDVHFQLFVBQVUsQ0FBQy9QOzRDQUNYNk0sV0FBVzdNLDJCQUEyQixxREFBcUQ7c0RBRTFGVSxxQkFBcUIsV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVFqRDtHQTdpRU10RDs7UUE2Q29CbEIsaUpBQUlBLENBQUMrRjtRQUNKL0YsaUpBQUlBLENBQUMrRjtRQUNOL0YsaUpBQUlBLENBQUMrRjtRQUNSL0YsaUpBQUlBLENBQUMrRjtRQUNFL0YsaUpBQUlBLENBQUMrRjtRQUNML0YsaUpBQUlBLENBQUMrRjs7O0tBbEQ3QjdFO0FBK2lFTiwrREFBZUEsZ0JBQWdCQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC9hZG1pbi1zcGFjZS9jb21wb25lbnRzL2NvdXJzZS1tYW5hZ2VtZW50LnRzeD82ZDcyIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQ2FyZCwgQnV0dG9uLCBUYWJsZSwgTW9kYWwsIEZvcm0sIElucHV0LCBTZWxlY3QsIFNwYWNlLCBUYWcsIFBvcGNvbmZpcm0sIFVwbG9hZCwgbWVzc2FnZSB9IGZyb20gJ2FudGQnO1xuaW1wb3J0IHsgUGx1c091dGxpbmVkLCBFZGl0T3V0bGluZWQsIERlbGV0ZU91dGxpbmVkLCBTZWFyY2hPdXRsaW5lZCwgVXBsb2FkT3V0bGluZWQsIEluYm94T3V0bGluZWQgfSBmcm9tICdAYW50LWRlc2lnbi9pY29ucyc7XG5pbXBvcnQgeyBHZXROb3RpZmljYXRpb24gfSBmcm9tICdsb2dpYy1jb21tb24vZGlzdC9jb21wb25lbnRzL05vdGlmaWNhdGlvbic7XG5pbXBvcnQgeyBjb3Vyc2VBcGksIENvdXJzZSwgQ3JlYXRlQ291cnNlUmVxdWVzdCwgVXBkYXRlQ291cnNlUmVxdWVzdCwgQ3JlYXRlQ291cnNlU2VyaWVzUmVxdWVzdCwgVGVhY2hlciwgQ291cnNlVGFnLCBDb3Vyc2VTZXJpZXMgfSBmcm9tICdAL2xpYi9hcGkvY291cnNlJztcbmltcG9ydCB7IHVwbG9hZEFwaSB9IGZyb20gJ0AvbGliL2FwaS91cGxvYWQnO1xuaW1wb3J0IHR5cGUgeyBVcGxvYWRGaWxlLCBVcGxvYWRQcm9wcyB9IGZyb20gJ2FudGQvZXMvdXBsb2FkL2ludGVyZmFjZSc7XG5cbmNvbnN0IHsgU2VhcmNoIH0gPSBJbnB1dDtcbmNvbnN0IHsgT3B0aW9uIH0gPSBTZWxlY3Q7XG5cbmludGVyZmFjZSBDb3Vyc2VNYW5hZ2VtZW50UHJvcHMge1xuICAvLyDlj6/ku6Xmt7vliqDpnIDopoHnmoRwcm9wc1xufVxuXG4vLyDor77nqIvooajljZXmlbDmja7nsbvlnotcbmludGVyZmFjZSBDb3Vyc2VGb3JtRGF0YSB7XG4gIG5hbWU6IHN0cmluZztcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgY2F0ZWdvcnk6IHN0cmluZztcbiAgc3RhdHVzOiAnYWN0aXZlJyB8ICdpbmFjdGl2ZSc7XG4gIGNvbnRlbnRDb25maWc/OiB7XG4gICAgZHVyYXRpb24/OiBudW1iZXI7XG4gICAgZGlmZmljdWx0eT86ICdiZWdpbm5lcicgfCAnaW50ZXJtZWRpYXRlJyB8ICdhZHZhbmNlZCc7XG4gICAgcHJlcmVxdWlzaXRlcz86IHN0cmluZ1tdO1xuICB9O1xuICB0ZWFjaGluZ0luZm8/OiB7XG4gICAgb2JqZWN0aXZlcz86IHN0cmluZ1tdO1xuICAgIG1ldGhvZHM/OiBzdHJpbmdbXTtcbiAgICBtYXRlcmlhbHM/OiBzdHJpbmdbXTtcbiAgfTtcbn1cblxuY29uc3QgQ291cnNlTWFuYWdlbWVudDogUmVhY3QuRkM8Q291cnNlTWFuYWdlbWVudFByb3BzPiA9ICgpID0+IHtcbiAgY29uc3QgW2NvdXJzZUxpc3QsIHNldENvdXJzZUxpc3RdID0gdXNlU3RhdGU8Q291cnNlW10+KFtdKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNDb3Vyc2VNb2RhbFZpc2libGUsIHNldElzQ291cnNlTW9kYWxWaXNpYmxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzQWRkQ291cnNlTW9kYWxWaXNpYmxlLCBzZXRJc0FkZENvdXJzZU1vZGFsVmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpc0VkaXRDb3Vyc2VNb2RhbFZpc2libGUsIHNldElzRWRpdENvdXJzZU1vZGFsVmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpc0FkZFNlcmllc01vZGFsVmlzaWJsZSwgc2V0SXNBZGRTZXJpZXNNb2RhbFZpc2libGVdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNBZGRUYWdNb2RhbFZpc2libGUsIHNldElzQWRkVGFnTW9kYWxWaXNpYmxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzUHVibGlzaFNlcmllc01vZGFsVmlzaWJsZSwgc2V0SXNQdWJsaXNoU2VyaWVzTW9kYWxWaXNpYmxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzUHVibGlzaENvdXJzZU1vZGFsVmlzaWJsZSwgc2V0SXNQdWJsaXNoQ291cnNlTW9kYWxWaXNpYmxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2VkaXRpbmdDb3Vyc2UsIHNldEVkaXRpbmdDb3Vyc2VdID0gdXNlU3RhdGU8Q291cnNlIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzZWFyY2hLZXl3b3JkLCBzZXRTZWFyY2hLZXl3b3JkXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3RlYWNoZXJzLCBzZXRUZWFjaGVyc10gPSB1c2VTdGF0ZTxUZWFjaGVyW10+KFtdKTtcbiAgY29uc3QgW2NvdXJzZVRhZ3MsIHNldENvdXJzZVRhZ3NdID0gdXNlU3RhdGU8Q291cnNlVGFnW10+KFtdKTtcbiAgY29uc3QgW2NvdmVySW1hZ2VVcmwsIHNldENvdmVySW1hZ2VVcmxdID0gdXNlU3RhdGU8c3RyaW5nPignJyk7XG5cbiAgLy8g5paw5aKe77ya57O75YiX6K++56iL5ZKM5a2Q6K++56iL566h55CG55u45YWz54q25oCBXG4gIGNvbnN0IFtzZXJpZXNMaXN0LCBzZXRTZXJpZXNMaXN0XSA9IHVzZVN0YXRlPGFueVtdPihbXSk7XG4gIGNvbnN0IFtzZXJpZXNDb3Vyc2VzTWFwLCBzZXRTZXJpZXNDb3Vyc2VzTWFwXSA9IHVzZVN0YXRlPE1hcDxudW1iZXIsIGFueVtdPj4obmV3IE1hcCgpKTtcbiAgY29uc3QgW2V4cGFuZGVkU2VyaWVzLCBzZXRFeHBhbmRlZFNlcmllc10gPSB1c2VTdGF0ZTxTZXQ8bnVtYmVyPj4obmV3IFNldCgpKTtcbiAgY29uc3QgW3Nlcmllc0xvYWRpbmcsIHNldFNlcmllc0xvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIOWPkeW4g+ivvueoi+ebuOWFs+eKtuaAgVxuICBjb25zdCBbc2VsZWN0ZWRTZXJpZXNGb3JQdWJsaXNoLCBzZXRTZWxlY3RlZFNlcmllc0ZvclB1Ymxpc2hdID0gdXNlU3RhdGU8bnVtYmVyIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xuICBjb25zdCBbc2VsZWN0ZWRDb3Vyc2VGb3JQdWJsaXNoLCBzZXRTZWxlY3RlZENvdXJzZUZvclB1Ymxpc2hdID0gdXNlU3RhdGU8bnVtYmVyIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xuICBjb25zdCBbcHVibGlzaFNlcmllc0NvdXJzZXMsIHNldFB1Ymxpc2hTZXJpZXNDb3Vyc2VzXSA9IHVzZVN0YXRlPGFueVtdPihbXSk7XG4gIGNvbnN0IFtwdWJsaXNoTG9hZGluZywgc2V0UHVibGlzaExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbcHVibGlzaFNlcmllc09wdGlvbnMsIHNldFB1Ymxpc2hTZXJpZXNPcHRpb25zXSA9IHVzZVN0YXRlPGFueVtdPihbXSk7XG5cbiAgLy8g5paw55qE5Y+R5biD6K++56iL54q25oCBXG4gIGNvbnN0IFtwdWJsaXNoU2VyaWVzTGlzdEZvck1vZGFsLCBzZXRQdWJsaXNoU2VyaWVzTGlzdEZvck1vZGFsXSA9IHVzZVN0YXRlPGFueVtdPihbXSk7XG4gIGNvbnN0IFtwdWJsaXNoQ291cnNlTGlzdEZvck1vZGFsLCBzZXRQdWJsaXNoQ291cnNlTGlzdEZvck1vZGFsXSA9IHVzZVN0YXRlPGFueVtdPihbXSk7XG4gIGNvbnN0IFtwdWJsaXNoRm9ybUxvYWRpbmcsIHNldFB1Ymxpc2hGb3JtTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgY29uc3QgW2NvdXJzZVNlcmllcywgc2V0Q291cnNlU2VyaWVzXSA9IHVzZVN0YXRlPENvdXJzZVNlcmllc1tdPihbXSk7XG4gIGNvbnN0IFtjb3Vyc2VDb3ZlckltYWdlVXJsLCBzZXRDb3Vyc2VDb3ZlckltYWdlVXJsXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xuICBjb25zdCBbYWRkaXRpb25hbEZpbGVzLCBzZXRBZGRpdGlvbmFsRmlsZXNdID0gdXNlU3RhdGU8c3RyaW5nW10+KFtdKTtcbiAgY29uc3QgW2NvdXJzZVZpZGVvVXJsLCBzZXRDb3Vyc2VWaWRlb1VybF0gPSB1c2VTdGF0ZTxzdHJpbmc+KCcnKTtcbiAgY29uc3QgW2NvdXJzZVZpZGVvTmFtZSwgc2V0Q291cnNlVmlkZW9OYW1lXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xuICBjb25zdCBbY291cnNlRG9jdW1lbnRVcmwsIHNldENvdXJzZURvY3VtZW50VXJsXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xuICBjb25zdCBbY291cnNlRG9jdW1lbnROYW1lLCBzZXRDb3Vyc2VEb2N1bWVudE5hbWVdID0gdXNlU3RhdGU8c3RyaW5nPignJyk7XG4gIGNvbnN0IFtjb3Vyc2VBdWRpb1VybCwgc2V0Q291cnNlQXVkaW9VcmxdID0gdXNlU3RhdGU8c3RyaW5nPignJyk7XG4gIGNvbnN0IFtjb3Vyc2VBdWRpb05hbWUsIHNldENvdXJzZUF1ZGlvTmFtZV0gPSB1c2VTdGF0ZTxzdHJpbmc+KCcnKTtcbiAgY29uc3QgW3ZpZGVvRHVyYXRpb24sIHNldFZpZGVvRHVyYXRpb25dID0gdXNlU3RhdGU8bnVtYmVyPigwKTtcblxuICBjb25zdCBbYWRkQ291cnNlRm9ybV0gPSBGb3JtLnVzZUZvcm0oKTtcbiAgY29uc3QgW2VkaXRDb3Vyc2VGb3JtXSA9IEZvcm0udXNlRm9ybSgpO1xuICBjb25zdCBbYWRkU2VyaWVzRm9ybV0gPSBGb3JtLnVzZUZvcm0oKTtcbiAgY29uc3QgW2FkZFRhZ0Zvcm1dID0gRm9ybS51c2VGb3JtKCk7XG4gIGNvbnN0IFtwdWJsaXNoU2VyaWVzRm9ybV0gPSBGb3JtLnVzZUZvcm0oKTtcbiAgY29uc3QgW3B1Ymxpc2hDb3Vyc2VGb3JtXSA9IEZvcm0udXNlRm9ybSgpO1xuICBjb25zdCBub3RpZmljYXRpb24gPSBHZXROb3RpZmljYXRpb24oKTtcblxuXG5cbiAgLy8g6I635Y+W57O75YiX6K++56iL5YiX6KGoXG4gIGNvbnN0IGZldGNoU2VyaWVzTGlzdCA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0U2VyaWVzTG9hZGluZyh0cnVlKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OdIOiOt+WPluezu+WIl+ivvueoi+WIl+ihqC4uLicpO1xuXG4gICAgICBjb25zdCB7IGRhdGE6IHJlcyB9ID0gYXdhaXQgY291cnNlQXBpLmdldE1hcmtldHBsYWNlU2VyaWVzKHtcbiAgICAgICAgcGFnZTogMSxcbiAgICAgICAgcGFnZVNpemU6IDUwXG4gICAgICB9KTtcblxuICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDAgJiYgcmVzLmRhdGE/Lmxpc3QpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDojrflj5bns7vliJfor77nqIvliJfooajmiJDlip86JywgcmVzLmRhdGEubGlzdCk7XG4gICAgICAgIHNldFNlcmllc0xpc3QocmVzLmRhdGEubGlzdCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg6I635Y+W57O75YiX6K++56iL5YiX6KGo5aSx6LSlOicsIHJlcy5tc2cpO1xuICAgICAgICBtZXNzYWdlLmVycm9yKHJlcy5tc2cgfHwgJ+iOt+WPluezu+WIl+ivvueoi+WIl+ihqOWksei0pScpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg6I635Y+W57O75YiX6K++56iL5YiX6KGo5byC5bi4OicsIGVycm9yKTtcbiAgICAgIG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluezu+WIl+ivvueoi+WIl+ihqOWksei0pe+8jOivt+mHjeivlScpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRTZXJpZXNMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g6I635Y+W5oyH5a6a57O75YiX5LiL55qE5a2Q6K++56iL5YiX6KGoXG4gIGNvbnN0IGZldGNoU2VyaWVzQ291cnNlcyA9IGFzeW5jIChzZXJpZXNJZDogbnVtYmVyKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OdIOiOt+WPluezu+WIl+WtkOivvueoi+WIl+ihqO+8jOezu+WIl0lEOicsIHNlcmllc0lkKTtcblxuICAgICAgY29uc3QgeyBkYXRhOiByZXMgfSA9IGF3YWl0IGNvdXJzZUFwaS5nZXRTZXJpZXNDb3Vyc2VMaXN0KHNlcmllc0lkLCB7XG4gICAgICAgIHBhZ2U6IDEsXG4gICAgICAgIHBhZ2VTaXplOiA1MFxuICAgICAgfSk7XG5cbiAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwICYmIHJlcy5kYXRhPy5saXN0KSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg6I635Y+W57O75YiX5a2Q6K++56iL5YiX6KGo5oiQ5YqfOicsIHJlcy5kYXRhLmxpc3QpO1xuICAgICAgICBzZXRTZXJpZXNDb3Vyc2VzTWFwKHByZXYgPT4gbmV3IE1hcChwcmV2LnNldChzZXJpZXNJZCwgcmVzLmRhdGEubGlzdCkpKTtcbiAgICAgICAgc2V0RXhwYW5kZWRTZXJpZXMocHJldiA9PiBuZXcgU2V0KHByZXYuYWRkKHNlcmllc0lkKSkpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOiOt+WPluezu+WIl+WtkOivvueoi+WIl+ihqOWksei0pTonLCByZXMubXNnKTtcbiAgICAgICAgbWVzc2FnZS5lcnJvcihyZXMubXNnIHx8ICfojrflj5blrZDor77nqIvliJfooajlpLHotKUnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOiOt+WPluezu+WIl+WtkOivvueoi+WIl+ihqOW8guW4uDonLCBlcnJvcik7XG4gICAgICBtZXNzYWdlLmVycm9yKCfojrflj5blrZDor77nqIvliJfooajlpLHotKXvvIzor7fph43or5UnKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g6I635Y+W6K++56iL5YiX6KGo77yI5L+d55WZ5Y6f5pyJ5Yqf6IO977yJXG4gIGNvbnN0IGZldGNoQ291cnNlTGlzdCA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ/Cfk50g6I635Y+W6K++56iL5YiX6KGoLi4uJyk7XG5cbiAgICAgIC8vIOiOt+WPluezu+WIl+ivvueoi+WIl+ihqFxuICAgICAgYXdhaXQgZmV0Y2hTZXJpZXNMaXN0KCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDojrflj5bor77nqIvliJfooajlpLHotKU6JywgZXJyb3IpO1xuICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfojrflj5bor77nqIvliJfooajlpLHotKXvvIzor7fph43or5UnKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5re75Yqg6K++56iLXG4gIGNvbnN0IGhhbmRsZUFkZENvdXJzZSA9IGFzeW5jICh2YWx1ZXM6IGFueSkgPT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyDmnoTlu7rlhoXlrrnphY3nva7vvIzlj6rljIXlkKvmnInmlYjnmoTlqpLkvZPmlofku7ZcbiAgICAgIGNvbnN0IGNvbnRlbnRDb25maWc6IGFueSA9IHtcbiAgICAgICAgaGFzVmlkZW86IGNvdXJzZVZpZGVvVXJsID8gMSA6IDAsXG4gICAgICAgIGhhc0RvY3VtZW50OiBjb3Vyc2VEb2N1bWVudFVybCA/IDEgOiAwLFxuICAgICAgICBoYXNBdWRpbzogY291cnNlQXVkaW9VcmwgPyAxIDogMCxcbiAgICAgIH07XG5cbiAgICAgIGlmIChjb3Vyc2VWaWRlb1VybCkge1xuICAgICAgICBjb250ZW50Q29uZmlnLnZpZGVvID0ge1xuICAgICAgICAgIHVybDogY291cnNlVmlkZW9VcmwsXG4gICAgICAgICAgbmFtZTogY291cnNlVmlkZW9OYW1lIHx8ICfor77nqIvop4bpopEubXA0J1xuICAgICAgICB9O1xuICAgICAgfVxuXG4gICAgICBpZiAoY291cnNlRG9jdW1lbnRVcmwpIHtcbiAgICAgICAgY29udGVudENvbmZpZy5kb2N1bWVudCA9IHtcbiAgICAgICAgICB1cmw6IGNvdXJzZURvY3VtZW50VXJsLFxuICAgICAgICAgIG5hbWU6IGNvdXJzZURvY3VtZW50TmFtZSB8fCAn6K++56iL5paH5qGjLnBkZidcbiAgICAgICAgfTtcbiAgICAgIH1cblxuICAgICAgaWYgKGNvdXJzZUF1ZGlvVXJsKSB7XG4gICAgICAgIGNvbnRlbnRDb25maWcuYXVkaW8gPSB7XG4gICAgICAgICAgdXJsOiBjb3Vyc2VBdWRpb1VybCxcbiAgICAgICAgICBuYW1lOiBjb3Vyc2VBdWRpb05hbWUgfHwgJ+ivvueoi+mfs+mikS5tcDMnXG4gICAgICAgIH07XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGNvdXJzZURhdGEgPSB7XG4gICAgICAgIHNlcmllc0lkOiBwYXJzZUludCh2YWx1ZXMuc2VyaWVzSWQpLFxuICAgICAgICB0aXRsZTogdmFsdWVzLnRpdGxlLnRyaW0oKSxcbiAgICAgICAgZGVzY3JpcHRpb246IHZhbHVlcy5kZXNjcmlwdGlvbi50cmltKCksXG4gICAgICAgIGNvdmVySW1hZ2U6IGNvdXJzZUNvdmVySW1hZ2VVcmwsXG4gICAgICAgIGhhc1ZpZGVvOiBjb3Vyc2VWaWRlb1VybCA/IDEgOiAwLFxuICAgICAgICBoYXNEb2N1bWVudDogY291cnNlRG9jdW1lbnRVcmwgPyAxIDogMCxcbiAgICAgICAgaGFzQXVkaW86IGNvdXJzZUF1ZGlvVXJsID8gMSA6IDAsXG4gICAgICAgIHZpZGVvRHVyYXRpb246IHZpZGVvRHVyYXRpb24gfHwgMCxcbiAgICAgICAgY29udGVudENvbmZpZyxcbiAgICAgICAgdGVhY2hpbmdJbmZvOiB2YWx1ZXMudGVhY2hpbmdPYmplY3RpdmVzICYmIHZhbHVlcy50ZWFjaGluZ09iamVjdGl2ZXMubGVuZ3RoID4gMCA/IFt7XG4gICAgICAgICAgdGl0bGU6IFwi5pWZ5a2m55uu5qCHXCIsXG4gICAgICAgICAgY29udGVudDogQXJyYXkuaXNBcnJheSh2YWx1ZXMudGVhY2hpbmdPYmplY3RpdmVzKSA/IHZhbHVlcy50ZWFjaGluZ09iamVjdGl2ZXMgOiBbdmFsdWVzLnRlYWNoaW5nT2JqZWN0aXZlc11cbiAgICAgICAgfV0gOiBbXSxcbiAgICAgICAgYWRkaXRpb25hbFJlc291cmNlczogYWRkaXRpb25hbEZpbGVzLm1hcChmaWxlID0+ICh7XG4gICAgICAgICAgdGl0bGU6IGZpbGUuc3BsaXQoJy8nKS5wb3AoKSB8fCAnZmlsZScsXG4gICAgICAgICAgdXJsOiBmaWxlLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAn6K++56iL6ZmE5Lu26LWE5rqQJ1xuICAgICAgICB9KSksXG4gICAgICAgIG9yZGVySW5kZXg6IHBhcnNlSW50KHZhbHVlcy5vcmRlckluZGV4KSB8fCAwXG4gICAgICB9O1xuXG4gICAgICAvLyDpqozor4Hlv4XopoHlrZfmrrVcbiAgICAgIGlmICghY291cnNlRGF0YS5zZXJpZXNJZCkge1xuICAgICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+ivt+mAieaLqeaJgOWxnuezu+WIl+ivvueoiycpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgICBpZiAoIWNvdXJzZURhdGEudGl0bGUpIHtcbiAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfor7fovpPlhaXor77nqIvlkI3np7AnKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgaWYgKCFjb3Vyc2VEYXRhLmNvdmVySW1hZ2UpIHtcbiAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfor7fkuIrkvKDor77nqIvlsIHpnaInKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZygn8J+TpCDmj5DkuqTor77nqIvmlbDmja46JywgY291cnNlRGF0YSk7XG4gICAgICBjb25zb2xlLmxvZygn8J+TiiDmlbDmja7lpKflsI/kvLDnrpc6JywgSlNPTi5zdHJpbmdpZnkoY291cnNlRGF0YSkubGVuZ3RoLCAn5a2X56ymJyk7XG5cbiAgICAgIC8vIOa3u+WKoOmHjeivleacuuWItlxuICAgICAgbGV0IHJldHJ5Q291bnQgPSAwO1xuICAgICAgY29uc3QgbWF4UmV0cmllcyA9IDI7XG4gICAgICBsZXQgbGFzdEVycm9yO1xuXG4gICAgICB3aGlsZSAocmV0cnlDb3VudCA8PSBtYXhSZXRyaWVzKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc3QgeyBkYXRhOiByZXMgfSA9IGF3YWl0IGNvdXJzZUFwaS5jcmVhdGVDb3Vyc2UoY291cnNlRGF0YSk7XG5cbiAgICAgICAgICAvLyDlpoLmnpzmiJDlip/vvIzot7Plh7rph43or5Xlvqrnjq9cbiAgICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkge1xuICAgICAgICAgICAgbm90aWZpY2F0aW9uLnN1Y2Nlc3MoJ+WIm+W7uuivvueoi+aIkOWKnycpO1xuICAgICAgICAgICAgZmV0Y2hDb3Vyc2VMaXN0KCk7XG4gICAgICAgICAgICBzZXRJc0FkZENvdXJzZU1vZGFsVmlzaWJsZShmYWxzZSk7XG4gICAgICAgICAgICBhZGRDb3Vyc2VGb3JtLnJlc2V0RmllbGRzKCk7XG4gICAgICAgICAgICBzZXRDb3Vyc2VDb3ZlckltYWdlVXJsKCcnKTtcbiAgICAgICAgICAgIHNldEFkZGl0aW9uYWxGaWxlcyhbXSk7XG4gICAgICAgICAgICBzZXRDb3Vyc2VWaWRlb1VybCgnJyk7XG4gICAgICAgICAgICBzZXRDb3Vyc2VWaWRlb05hbWUoJycpO1xuICAgICAgICAgICAgc2V0Q291cnNlRG9jdW1lbnRVcmwoJycpO1xuICAgICAgICAgICAgc2V0Q291cnNlRG9jdW1lbnROYW1lKCcnKTtcbiAgICAgICAgICAgIHNldENvdXJzZUF1ZGlvVXJsKCcnKTtcbiAgICAgICAgICAgIHNldENvdXJzZUF1ZGlvTmFtZSgnJyk7XG4gICAgICAgICAgICBzZXRWaWRlb0R1cmF0aW9uKDApO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBub3RpZmljYXRpb24uZXJyb3IocmVzLm1zZyB8fCAn5Yib5bu66K++56iL5aSx6LSlJyk7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgfVxuICAgICAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICAgICAgbGFzdEVycm9yID0gZXJyb3I7XG4gICAgICAgICAgcmV0cnlDb3VudCsrO1xuXG4gICAgICAgICAgaWYgKHJldHJ5Q291bnQgPD0gbWF4UmV0cmllcykge1xuICAgICAgICAgICAgY29uc29sZS5sb2coYPCflIQg56ysJHtyZXRyeUNvdW50feasoemHjeivlS4uLmApO1xuICAgICAgICAgICAgbm90aWZpY2F0aW9uLndhcm5pbmcoYOe9kee7nOW8guW4uO+8jOato+WcqOmHjeivlSAoJHtyZXRyeUNvdW50fS8ke21heFJldHJpZXN9KWApO1xuICAgICAgICAgICAgLy8g562J5b6FMeenkuWQjumHjeivlVxuICAgICAgICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDEwMDApKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8g5aaC5p6c5omA5pyJ6YeN6K+V6YO95aSx6LSl5LqG77yM5oqb5Ye65pyA5ZCO55qE6ZSZ6K+vXG4gICAgICB0aHJvdyBsYXN0RXJyb3I7XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOWIm+W7uuivvueoi+Wksei0pTonLCBlcnJvcik7XG5cbiAgICAgIC8vIOabtOivpue7hueahOmUmeivr+WkhOeQhlxuICAgICAgaWYgKGVycm9yLmNvZGUgPT09ICdFQ09OTlJFU0VUJyB8fCBlcnJvci5tZXNzYWdlPy5pbmNsdWRlcygnRUNPTk5SRVNFVCcpIHx8XG4gICAgICAgICAgKGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlICYmIGVycm9yLnJlc3BvbnNlLmRhdGEubWVzc2FnZS5pbmNsdWRlcygnRUNPTk5SRVNFVCcpKSkge1xuICAgICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+e9kee7nOi/nuaOpeS4reaWre+8jOWPr+iDveaYr+e9kee7nOS4jeeos+WumuaIluacjeWKoeWZqOe5geW/meOAguivt+eojeWQjumHjeivleaIluiBlOezu+euoeeQhuWRmOOAgicpO1xuICAgICAgfSBlbHNlIGlmIChlcnJvci5jb2RlID09PSAnTkVUV09SS19FUlJPUicpIHtcbiAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfnvZHnu5zplJnor6/vvIzor7fmo4Dmn6XnvZHnu5zov57mjqUnKTtcbiAgICAgIH0gZWxzZSBpZiAoZXJyb3IucmVzcG9uc2U/LnN0YXR1cyA9PT0gNDEzKSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign5LiK5Lyg5paH5Lu26L+H5aSn77yM6K+35Y6L57yp5ZCO6YeN6K+VJyk7XG4gICAgICB9IGVsc2UgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwMCkge1xuICAgICAgICBjb25zdCBlcnJvck1zZyA9IGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8IGVycm9yLm1lc3NhZ2U7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcihg6K+35rGC5Y+C5pWw6ZSZ6K+vOiAke2Vycm9yTXNnfWApO1xuICAgICAgfSBlbHNlIGlmIChlcnJvci5yZXNwb25zZT8uc3RhdHVzID09PSA1MDApIHtcbiAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfmnI3liqHlmajlhoXpg6jplJnor6/vvIzor7fogZTns7vnrqHnkIblkZgnKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcihg5Yib5bu66K++56iL5aSx6LSlOiAke2Vycm9yLm1lc3NhZ2UgfHwgJ+ivt+eojeWQjumHjeivlSd9YCk7XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5SNIOWujOaVtOmUmeivr+S/oeaBrzonLCB7XG4gICAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2UsXG4gICAgICAgIGNvZGU6IGVycm9yLmNvZGUsXG4gICAgICAgIHN0YXR1czogZXJyb3IucmVzcG9uc2U/LnN0YXR1cyxcbiAgICAgICAgZGF0YTogZXJyb3IucmVzcG9uc2U/LmRhdGFcbiAgICAgIH0pO1xuICAgIH1cbiAgfTtcblxuICAvLyDnvJbovpHor77nqItcbiAgY29uc3QgaGFuZGxlRWRpdENvdXJzZSA9IGFzeW5jICh2YWx1ZXM6IFVwZGF0ZUNvdXJzZVJlcXVlc3QpID0+IHtcbiAgICBpZiAoIWVkaXRpbmdDb3Vyc2UpIHJldHVybjtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCB7IGRhdGE6IHJlcyB9ID0gYXdhaXQgY291cnNlQXBpLnVwZGF0ZUNvdXJzZShlZGl0aW5nQ291cnNlLmlkLCB2YWx1ZXMpO1xuICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHtcbiAgICAgICAgbm90aWZpY2F0aW9uLnN1Y2Nlc3MoJ+abtOaWsOivvueoi+aIkOWKnycpO1xuICAgICAgICBmZXRjaENvdXJzZUxpc3QoKTtcbiAgICAgICAgc2V0SXNFZGl0Q291cnNlTW9kYWxWaXNpYmxlKGZhbHNlKTtcbiAgICAgICAgc2V0RWRpdGluZ0NvdXJzZShudWxsKTtcbiAgICAgICAgZWRpdENvdXJzZUZvcm0ucmVzZXRGaWVsZHMoKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcihyZXMubXNnIHx8ICfmm7TmlrDor77nqIvlpLHotKUnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOabtOaWsOivvueoi+Wksei0pTonLCBlcnJvcik7XG4gICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+abtOaWsOivvueoi+Wksei0pe+8jOivt+mHjeivlScpO1xuICAgIH1cbiAgfTtcblxuICAvLyDliKDpmaTor77nqItcbiAgY29uc3QgaGFuZGxlRGVsZXRlQ291cnNlID0gYXN5bmMgKGNvdXJzZUlkOiBudW1iZXIpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBkYXRhOiByZXMgfSA9IGF3YWl0IGNvdXJzZUFwaS5kZWxldGVDb3Vyc2UoY291cnNlSWQpO1xuICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHtcbiAgICAgICAgbm90aWZpY2F0aW9uLnN1Y2Nlc3MoJ+WIoOmZpOivvueoi+aIkOWKnycpO1xuICAgICAgICBmZXRjaENvdXJzZUxpc3QoKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcihyZXMubXNnIHx8ICfliKDpmaTor77nqIvlpLHotKUnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOWIoOmZpOivvueoi+Wksei0pTonLCBlcnJvcik7XG4gICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+WIoOmZpOivvueoi+Wksei0pe+8jOivt+mHjeivlScpO1xuICAgIH1cbiAgfTtcblxuICAvLyDliKDpmaTlrZDor77nqItcbiAgY29uc3QgaGFuZGxlRGVsZXRlU3ViQ291cnNlID0gYXN5bmMgKGNvdXJzZUlkOiBudW1iZXIsIHNlcmllc0lkOiBudW1iZXIpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ/Cfl5HvuI8g5Yig6Zmk5a2Q6K++56iL77yM6K++56iLSUQ6JywgY291cnNlSWQsICfns7vliJdJRDonLCBzZXJpZXNJZCk7XG5cbiAgICAgIGNvbnN0IHsgZGF0YTogcmVzIH0gPSBhd2FpdCBjb3Vyc2VBcGkuZGVsZXRlQ291cnNlKGNvdXJzZUlkKTtcblxuICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHtcbiAgICAgICAgbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTlrZDor77nqIvmiJDlip8nKTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDlrZDor77nqIvliKDpmaTmiJDlip/vvIzph43mlrDojrflj5bns7vliJflrZDor77nqIvliJfooagnKTtcblxuICAgICAgICAvLyDph43mlrDojrflj5bor6Xns7vliJfnmoTlrZDor77nqIvliJfooahcbiAgICAgICAgYXdhaXQgZmV0Y2hTZXJpZXNDb3Vyc2VzKHNlcmllc0lkKTtcblxuICAgICAgICBjb25zb2xlLmxvZygn8J+UhCDlrZDor77nqIvliJfooajlt7LliLfmlrAnKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDliKDpmaTlrZDor77nqIvlpLHotKU6JywgcmVzLm1zZyk7XG4gICAgICAgIG1lc3NhZ2UuZXJyb3IocmVzLm1zZyB8fCAn5Yig6Zmk5a2Q6K++56iL5aSx6LSlJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDliKDpmaTlrZDor77nqIvlvILluLg6JywgZXJyb3IpO1xuICAgICAgbWVzc2FnZS5lcnJvcign5Yig6Zmk5a2Q6K++56iL5aSx6LSl77yM6K+36YeN6K+VJyk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWIh+aNouezu+WIl+WxleW8gC/mlLbotbfnirbmgIFcbiAgY29uc3QgdG9nZ2xlU2VyaWVzRXhwYW5zaW9uID0gYXN5bmMgKHNlcmllc0lkOiBudW1iZXIpID0+IHtcbiAgICBjb25zb2xlLmxvZygn8J+UhCDliIfmjaLns7vliJflsZXlvIDnirbmgIHvvIzns7vliJdJRDonLCBzZXJpZXNJZCk7XG4gICAgY29uc29sZS5sb2coJ/Cfk4og5b2T5YmN5bGV5byA54q25oCBOicsIGV4cGFuZGVkU2VyaWVzLmhhcyhzZXJpZXNJZCkpO1xuXG4gICAgaWYgKGV4cGFuZGVkU2VyaWVzLmhhcyhzZXJpZXNJZCkpIHtcbiAgICAgIC8vIOaUtui1t1xuICAgICAgY29uc29sZS5sb2coJ/Cfk4Eg5pS26LW357O75YiXOicsIHNlcmllc0lkKTtcbiAgICAgIHNldEV4cGFuZGVkU2VyaWVzKHByZXYgPT4ge1xuICAgICAgICBjb25zdCBuZXdTZXQgPSBuZXcgU2V0KHByZXYpO1xuICAgICAgICBuZXdTZXQuZGVsZXRlKHNlcmllc0lkKTtcbiAgICAgICAgcmV0dXJuIG5ld1NldDtcbiAgICAgIH0pO1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyDlsZXlvIDvvIzpnIDopoHojrflj5blrZDor77nqIvmlbDmja5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5OCIOWxleW8gOezu+WIl++8jOiOt+WPluWtkOivvueoizonLCBzZXJpZXNJZCk7XG4gICAgICBhd2FpdCBmZXRjaFNlcmllc0NvdXJzZXMoc2VyaWVzSWQpO1xuICAgIH1cbiAgfTtcblxuICAvLyDlsZXlvIDmiYDmnInns7vliJdcbiAgY29uc3QgZXhwYW5kQWxsU2VyaWVzID0gYXN5bmMgKCkgPT4ge1xuICAgIGNvbnNvbGUubG9nKCfwn5OCIOWxleW8gOaJgOacieezu+WIl+ivvueoiycpO1xuICAgIGZvciAoY29uc3Qgc2VyaWVzIG9mIHNlcmllc0xpc3QpIHtcbiAgICAgIGlmICghZXhwYW5kZWRTZXJpZXMuaGFzKHNlcmllcy5pZCkpIHtcbiAgICAgICAgYXdhaXQgZmV0Y2hTZXJpZXNDb3Vyc2VzKHNlcmllcy5pZCk7XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIC8vIOaUtui1t+aJgOacieezu+WIl1xuICBjb25zdCBjb2xsYXBzZUFsbFNlcmllcyA9ICgpID0+IHtcbiAgICBjb25zb2xlLmxvZygn8J+TgSDmlLbotbfmiYDmnInns7vliJfor77nqIsnKTtcbiAgICBzZXRFeHBhbmRlZFNlcmllcyhuZXcgU2V0KCkpO1xuICB9O1xuXG4gIC8vIOa3u+WKoOezu+WIl+ivvueoi1xuICBjb25zdCBoYW5kbGVBZGRTZXJpZXMgPSBhc3luYyAodmFsdWVzOiBDcmVhdGVDb3Vyc2VTZXJpZXNSZXF1ZXN0KSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHNlcmllc0RhdGEgPSB7XG4gICAgICAgIC4uLnZhbHVlcyxcbiAgICAgICAgY292ZXJJbWFnZTogY292ZXJJbWFnZVVybFxuICAgICAgfTtcblxuICAgICAgY29uc29sZS5sb2coJ+WIm+W7uuezu+WIl+ivvueoi+aVsOaNrjonLCBzZXJpZXNEYXRhKTtcblxuICAgICAgY29uc3QgeyBkYXRhOiByZXMgfSA9IGF3YWl0IGNvdXJzZUFwaS5jcmVhdGVDb3Vyc2VTZXJpZXMoc2VyaWVzRGF0YSk7XG5cbiAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5zdWNjZXNzKCfliJvlu7rns7vliJfor77nqIvmiJDlip8nKTtcbiAgICAgICAgZmV0Y2hDb3Vyc2VMaXN0KCk7XG4gICAgICAgIHNldElzQWRkU2VyaWVzTW9kYWxWaXNpYmxlKGZhbHNlKTtcbiAgICAgICAgYWRkU2VyaWVzRm9ybS5yZXNldEZpZWxkcygpO1xuICAgICAgICBzZXRDb3ZlckltYWdlVXJsKCcnKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcihyZXMubXNnIHx8ICfliJvlu7rns7vliJfor77nqIvlpLHotKUnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOWIm+W7uuezu+WIl+ivvueoi+Wksei0pTonLCBlcnJvcik7XG4gICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+WIm+W7uuezu+WIl+ivvueoi+Wksei0pe+8jOivt+mHjeivlScpO1xuICAgIH1cbiAgfTtcblxuICAvLyDliJvlu7ror77nqIvmoIfnrb5cbiAgY29uc3QgaGFuZGxlQWRkVGFnID0gYXN5bmMgKHZhbHVlczogYW55KSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn4+377iPIOWIm+W7uuivvueoi+agh+etvuaVsOaNrjonLCB2YWx1ZXMpO1xuXG4gICAgICBjb25zdCB7IGRhdGE6IHJlcyB9ID0gYXdhaXQgY291cnNlQXBpLmNyZWF0ZUNvdXJzZVRhZyh2YWx1ZXMpO1xuXG4gICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkge1xuICAgICAgICBub3RpZmljYXRpb24uc3VjY2Vzcygn5Yib5bu65qCH562+5oiQ5YqfJyk7XG4gICAgICAgIHNldElzQWRkVGFnTW9kYWxWaXNpYmxlKGZhbHNlKTtcbiAgICAgICAgYWRkVGFnRm9ybS5yZXNldEZpZWxkcygpO1xuICAgICAgICAvLyDph43mlrDojrflj5bmoIfnrb7liJfooahcbiAgICAgICAgZmV0Y2hDb3Vyc2VUYWdzKCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBub3RpZmljYXRpb24uZXJyb3IocmVzLm1zZyB8fCAn5Yib5bu65qCH562+5aSx6LSlJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDliJvlu7rmoIfnrb7lpLHotKU6JywgZXJyb3IpO1xuICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfliJvlu7rmoIfnrb7lpLHotKXvvIzor7fph43or5UnKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5Y+R5biD57O75YiX6K++56iLXG4gIGNvbnN0IGhhbmRsZVB1Ymxpc2hTZXJpZXMgPSBhc3luYyAodmFsdWVzOiBhbnkpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ/Cfk6Ig5Y+R5biD57O75YiX6K++56iL5pWw5o2uOicsIHZhbHVlcyk7XG5cbiAgICAgIGNvbnN0IHsgZGF0YTogcmVzIH0gPSBhd2FpdCBjb3Vyc2VBcGkucHVibGlzaENvdXJzZVNlcmllcyh2YWx1ZXMuc2VyaWVzSWQpO1xuXG4gICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkge1xuICAgICAgICBub3RpZmljYXRpb24uc3VjY2Vzcygn5Y+R5biD57O75YiX6K++56iL5oiQ5YqfJyk7XG4gICAgICAgIHNldElzUHVibGlzaFNlcmllc01vZGFsVmlzaWJsZShmYWxzZSk7XG4gICAgICAgIHB1Ymxpc2hTZXJpZXNGb3JtLnJlc2V0RmllbGRzKCk7XG5cbiAgICAgICAgLy8g5pi+56S65Y+R5biD57uT5p6c5L+h5oGvXG4gICAgICAgIGNvbnN0IHB1Ymxpc2hEYXRhID0gcmVzLmRhdGE7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg5Y+R5biD5oiQ5Yqf77yM57O75YiX5L+h5oGvOicsIHB1Ymxpc2hEYXRhKTtcblxuICAgICAgICAvLyDlj6/ku6XpgInmi6nmmL7npLrlj5HluIPnu5/orqHkv6Hmga9cbiAgICAgICAgaWYgKHB1Ymxpc2hEYXRhLnB1Ymxpc2hTdGF0cykge1xuICAgICAgICAgIGNvbnN0IHN0YXRzID0gcHVibGlzaERhdGEucHVibGlzaFN0YXRzO1xuICAgICAgICAgIGNvbnN0IHN0YXRzTWVzc2FnZSA9IGDlt7Llj5HluIMgJHtwdWJsaXNoRGF0YS5wdWJsaXNoZWRDb3Vyc2VzfS8ke3B1Ymxpc2hEYXRhLnRvdGFsQ291cnNlc30g5Liq6K++56iL77yM5YyF5ZCrICR7c3RhdHMudmlkZW9Db3Vyc2VDb3VudH0g5Liq6KeG6aKR6K++56iL77yM5oC75pe26ZW/ICR7TWF0aC5yb3VuZChzdGF0cy50b3RhbFZpZGVvRHVyYXRpb24gLyA2MCl9IOWIhumSn2A7XG4gICAgICAgICAgbm90aWZpY2F0aW9uLmluZm8oc3RhdHNNZXNzYWdlKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKHJlcy5tc2cgfHwgJ+WPkeW4g+ezu+WIl+ivvueoi+Wksei0pScpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg5Y+R5biD57O75YiX6K++56iL5aSx6LSlOicsIGVycm9yKTtcbiAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign5Y+R5biD57O75YiX6K++56iL5aSx6LSl77yM6K+36YeN6K+VJyk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOiOt+WPluWPkeW4g+eUqOeahOezu+WIl+ivvueoi+WIl+ihqFxuICBjb25zdCBmZXRjaFNlcmllc0ZvclB1Ymxpc2ggPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldFB1Ymxpc2hMb2FkaW5nKHRydWUpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk50g6I635Y+W5Y+R5biD55So57O75YiX6K++56iL5YiX6KGoLi4uJyk7XG5cbiAgICAgIGNvbnN0IHsgZGF0YTogcmVzIH0gPSBhd2FpdCBjb3Vyc2VBcGkuZ2V0TWFya2V0cGxhY2VTZXJpZXMoe1xuICAgICAgICBwYWdlOiAxLFxuICAgICAgICBwYWdlU2l6ZTogNTBcbiAgICAgIH0pO1xuXG4gICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCAmJiByZXMuZGF0YT8ubGlzdCkge1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOiOt+WPluWPkeW4g+eUqOezu+WIl+ivvueoi+WIl+ihqOaIkOWKnzonLCByZXMuZGF0YS5saXN0KTtcbiAgICAgICAgcmV0dXJuIHJlcy5kYXRhLmxpc3Q7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg6I635Y+W5Y+R5biD55So57O75YiX6K++56iL5YiX6KGo5aSx6LSlOicsIHJlcy5tc2cpO1xuICAgICAgICBtZXNzYWdlLmVycm9yKHJlcy5tc2cgfHwgJ+iOt+WPluezu+WIl+ivvueoi+WIl+ihqOWksei0pScpO1xuICAgICAgICByZXR1cm4gW107XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDojrflj5blj5HluIPnlKjns7vliJfor77nqIvliJfooajlvILluLg6JywgZXJyb3IpO1xuICAgICAgbWVzc2FnZS5lcnJvcign6I635Y+W57O75YiX6K++56iL5YiX6KGo5aSx6LSl77yM6K+36YeN6K+VJyk7XG4gICAgICByZXR1cm4gW107XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldFB1Ymxpc2hMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g6I635Y+W5oyH5a6a57O75YiX55qE6K++56iL6K+m5oOFXG4gIGNvbnN0IGZldGNoU2VyaWVzRGV0YWlsRm9yUHVibGlzaCA9IGFzeW5jIChzZXJpZXNJZDogbnVtYmVyKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldFB1Ymxpc2hMb2FkaW5nKHRydWUpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk50g6I635Y+W57O75YiX6K++56iL6K+m5oOF77yM57O75YiXSUQ6Jywgc2VyaWVzSWQpO1xuXG4gICAgICBjb25zdCB7IGRhdGE6IHJlcyB9ID0gYXdhaXQgY291cnNlQXBpLmdldE1hcmtldHBsYWNlU2VyaWVzRGV0YWlsKHNlcmllc0lkKTtcblxuICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDAgJiYgcmVzLmRhdGEpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDojrflj5bns7vliJfor77nqIvor6bmg4XmiJDlip86JywgcmVzLmRhdGEpO1xuICAgICAgICByZXR1cm4gcmVzLmRhdGE7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg6I635Y+W57O75YiX6K++56iL6K+m5oOF5aSx6LSlOicsIHJlcy5tc2cpO1xuICAgICAgICBtZXNzYWdlLmVycm9yKHJlcy5tc2cgfHwgJ+iOt+WPluezu+WIl+ivvueoi+ivpuaDheWksei0pScpO1xuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOiOt+WPluezu+WIl+ivvueoi+ivpuaDheW8guW4uDonLCBlcnJvcik7XG4gICAgICBtZXNzYWdlLmVycm9yKCfojrflj5bns7vliJfor77nqIvor6bmg4XlpLHotKXvvIzor7fph43or5UnKTtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRQdWJsaXNoTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOiOt+WPluWPkeW4g+W8ueeql+eahOezu+WIl+ivvueoi+WIl+ihqFxuICBjb25zdCBmZXRjaFB1Ymxpc2hTZXJpZXNMaXN0ID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRQdWJsaXNoRm9ybUxvYWRpbmcodHJ1ZSk7XG4gICAgICBjb25zb2xlLmxvZygn8J+TnSDojrflj5blj5HluIPlvLnnqpfnmoTns7vliJfor77nqIvliJfooaguLi4nKTtcblxuICAgICAgY29uc3QgeyBkYXRhOiByZXMgfSA9IGF3YWl0IGNvdXJzZUFwaS5nZXRNYXJrZXRwbGFjZVNlcmllcyh7XG4gICAgICAgIHBhZ2U6IDEsXG4gICAgICAgIHBhZ2VTaXplOiA1MFxuICAgICAgfSk7XG5cbiAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwICYmIHJlcy5kYXRhPy5saXN0KSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg6I635Y+W5Y+R5biD5by556qX57O75YiX6K++56iL5YiX6KGo5oiQ5YqfOicsIHJlcy5kYXRhLmxpc3QpO1xuICAgICAgICBzZXRQdWJsaXNoU2VyaWVzTGlzdEZvck1vZGFsKHJlcy5kYXRhLmxpc3QpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOiOt+WPluWPkeW4g+W8ueeql+ezu+WIl+ivvueoi+WIl+ihqOWksei0pTonLCByZXMubXNnKTtcbiAgICAgICAgbWVzc2FnZS5lcnJvcihyZXMubXNnIHx8ICfojrflj5bns7vliJfor77nqIvliJfooajlpLHotKUnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOiOt+WPluWPkeW4g+W8ueeql+ezu+WIl+ivvueoi+WIl+ihqOW8guW4uDonLCBlcnJvcik7XG4gICAgICBtZXNzYWdlLmVycm9yKCfojrflj5bns7vliJfor77nqIvliJfooajlpLHotKXvvIzor7fph43or5UnKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0UHVibGlzaEZvcm1Mb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g6I635Y+W5oyH5a6a57O75YiX5LiL55qE5a2Q6K++56iL5YiX6KGo77yI55So5LqO5Y+R5biD5by556qX77yJXG4gIGNvbnN0IGZldGNoUHVibGlzaENvdXJzZUxpc3QgPSBhc3luYyAoc2VyaWVzSWQ6IG51bWJlcikgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+TnSDojrflj5blj5HluIPlvLnnqpfnmoTlrZDor77nqIvliJfooajvvIzns7vliJdJRDonLCBzZXJpZXNJZCk7XG5cbiAgICAgIC8vIOmmluWFiOWwneivleS9v+eUqOivvueoi+W4guWcukFQSVxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc29sZS5sb2coJ/CflIQg5bCd6K+V5L2/55So6K++56iL5biC5Zy6QVBJ6I635Y+W57O75YiX6K+m5oOFLi4uJyk7XG4gICAgICAgIGNvbnN0IHsgZGF0YTogcmVzIH0gPSBhd2FpdCBjb3Vyc2VBcGkuZ2V0TWFya2V0cGxhY2VTZXJpZXNEZXRhaWwoc2VyaWVzSWQpO1xuXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIOivvueoi+W4guWcukFQSeWTjeW6lDonLCByZXMpO1xuXG4gICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwICYmIHJlcy5kYXRhKSB7XG4gICAgICAgICAgLy8g5qOA5p+l5LiN5ZCM5Y+v6IO955qE5pWw5o2u57uT5p6EXG4gICAgICAgICAgbGV0IGNvdXJzZUxpc3QgPSBbXTtcblxuICAgICAgICAgIGlmIChyZXMuZGF0YS5jb3Vyc2VMaXN0KSB7XG4gICAgICAgICAgICBjb3Vyc2VMaXN0ID0gcmVzLmRhdGEuY291cnNlTGlzdDtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUg5LuOY291cnNlTGlzdOWtl+auteiOt+WPluWtkOivvueoizonLCBjb3Vyc2VMaXN0KTtcbiAgICAgICAgICB9IGVsc2UgaWYgKHJlcy5kYXRhLmNvdXJzZXMpIHtcbiAgICAgICAgICAgIGNvdXJzZUxpc3QgPSByZXMuZGF0YS5jb3Vyc2VzO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSDku45jb3Vyc2Vz5a2X5q616I635Y+W5a2Q6K++56iLOicsIGNvdXJzZUxpc3QpO1xuICAgICAgICAgIH0gZWxzZSBpZiAoQXJyYXkuaXNBcnJheShyZXMuZGF0YSkpIHtcbiAgICAgICAgICAgIGNvdXJzZUxpc3QgPSByZXMuZGF0YTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUg55u05o6l5LuOZGF0YeaVsOe7hOiOt+WPluWtkOivvueoizonLCBjb3Vyc2VMaXN0KTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KaoO+4jyDor77nqIvluILlnLpBUEnmnKrmib7liLDlrZDor77nqIvmlbDmja7vvIzlsJ3or5XlpIfnlKhBUEkuLi4nKTtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignTm8gY291cnNlIGxpc3QgZm91bmQgaW4gbWFya2V0cGxhY2UgQVBJJyk7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgaWYgKGNvdXJzZUxpc3QgJiYgY291cnNlTGlzdC5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIOiuvue9ruWtkOivvueoi+WIl+ihqO+8jOaVsOmHjzonLCBjb3Vyc2VMaXN0Lmxlbmd0aCk7XG4gICAgICAgICAgICBzZXRQdWJsaXNoQ291cnNlTGlzdEZvck1vZGFsKGNvdXJzZUxpc3QpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAobWFya2V0cGxhY2VFcnJvcikge1xuICAgICAgICBjb25zb2xlLmxvZygn4pqg77iPIOivvueoi+W4guWcukFQSeWksei0pe+8jOWwneivleS9v+eUqOivvueoi+euoeeQhkFQSS4uLicpO1xuXG4gICAgICAgIC8vIOWkh+eUqOaWueahiO+8muS9v+eUqOivvueoi+euoeeQhkFQSVxuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IHsgZGF0YTogcmVzMiB9ID0gYXdhaXQgY291cnNlQXBpLmdldFNlcmllc0NvdXJzZUxpc3Qoc2VyaWVzSWQsIHtcbiAgICAgICAgICAgIHBhZ2U6IDEsXG4gICAgICAgICAgICBwYWdlU2l6ZTogNTBcbiAgICAgICAgICB9KTtcblxuICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIOivvueoi+euoeeQhkFQSeWTjeW6lDonLCByZXMyKTtcblxuICAgICAgICAgIGlmIChyZXMyLmNvZGUgPT09IDIwMCAmJiByZXMyLmRhdGE/Lmxpc3QpIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUg5LuO6K++56iL566h55CGQVBJ6I635Y+W5a2Q6K++56iL5YiX6KGo5oiQ5YqfOicsIHJlczIuZGF0YS5saXN0KTtcbiAgICAgICAgICAgIHNldFB1Ymxpc2hDb3Vyc2VMaXN0Rm9yTW9kYWwocmVzMi5kYXRhLmxpc3QpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgIH1cbiAgICAgICAgfSBjYXRjaCAobWFuYWdlbWVudEVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOivvueoi+euoeeQhkFQSeS5n+Wksei0pTonLCBtYW5hZ2VtZW50RXJyb3IpO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIOWmguaenOaJgOaciUFQSemDveWksei0pVxuICAgICAgY29uc29sZS5sb2coJ+KaoO+4jyDor6Xns7vliJfmmoLml6DlrZDor77nqIvmiJZBUEnosIPnlKjlpLHotKUnKTtcbiAgICAgIHNldFB1Ymxpc2hDb3Vyc2VMaXN0Rm9yTW9kYWwoW10pO1xuICAgICAgbWVzc2FnZS5pbmZvKCfor6Xns7vliJfmmoLml6DlrZDor77nqIsnKTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg6I635Y+W5Y+R5biD5by556qX5a2Q6K++56iL5YiX6KGo5byC5bi4OicsIGVycm9yKTtcbiAgICAgIG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluWtkOivvueoi+WIl+ihqOWksei0pe+8jOivt+mHjeivlScpO1xuICAgICAgc2V0UHVibGlzaENvdXJzZUxpc3RGb3JNb2RhbChbXSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWkhOeQhuezu+WIl+mAieaLqe+8iOWPkeW4g+W8ueeql++8iVxuICBjb25zdCBoYW5kbGVQdWJsaXNoU2VyaWVzQ2hhbmdlID0gYXN5bmMgKHNlcmllc0lkOiBudW1iZXIpID0+IHtcbiAgICBjb25zb2xlLmxvZygn8J+TmiDlj5HluIPlvLnnqpfpgInmi6nns7vliJdJRDonLCBzZXJpZXNJZCk7XG4gICAgY29uc29sZS5sb2coJ/Cfk5og5b2T5YmN57O75YiX5YiX6KGoOicsIHB1Ymxpc2hTZXJpZXNMaXN0Rm9yTW9kYWwpO1xuXG4gICAgc2V0U2VsZWN0ZWRTZXJpZXNGb3JQdWJsaXNoKHNlcmllc0lkKTtcbiAgICBzZXRTZWxlY3RlZENvdXJzZUZvclB1Ymxpc2godW5kZWZpbmVkKTtcbiAgICBzZXRQdWJsaXNoQ291cnNlTGlzdEZvck1vZGFsKFtdKTtcblxuICAgIC8vIOmHjee9ruihqOWNleS4reeahOivvueoi+mAieaLqVxuICAgIHB1Ymxpc2hDb3Vyc2VGb3JtLnNldEZpZWxkc1ZhbHVlKHsgY291cnNlSWQ6IHVuZGVmaW5lZCB9KTtcblxuICAgIC8vIOiOt+WPluivpeezu+WIl+S4i+eahOWtkOivvueoi1xuICAgIGlmIChzZXJpZXNJZCkge1xuICAgICAgY29uc29sZS5sb2coJ/CflIQg5byA5aeL6I635Y+W57O75YiX5a2Q6K++56iLLi4uJyk7XG4gICAgICBzZXRQdWJsaXNoRm9ybUxvYWRpbmcodHJ1ZSk7XG5cbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IGZldGNoUHVibGlzaENvdXJzZUxpc3Qoc2VyaWVzSWQpO1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOWtkOivvueoi+iOt+WPluWujOaIkCcpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOWtkOivvueoi+iOt+WPluWksei0pTonLCBlcnJvcik7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRQdWJsaXNoRm9ybUxvYWRpbmcoZmFsc2UpO1xuICAgICAgfVxuICAgIH1cbiAgfTtcblxuICAvLyDlpITnkIbor77nqIvpgInmi6nvvIjlj5HluIPlvLnnqpfvvIlcbiAgY29uc3QgaGFuZGxlUHVibGlzaENvdXJzZUNoYW5nZSA9IChjb3Vyc2VJZDogbnVtYmVyKSA9PiB7XG4gICAgY29uc29sZS5sb2coJ/Cfk5Yg5Y+R5biD5by556qX6YCJ5oup6K++56iLSUQ6JywgY291cnNlSWQpO1xuICAgIHNldFNlbGVjdGVkQ291cnNlRm9yUHVibGlzaChjb3Vyc2VJZCk7XG4gIH07XG5cbiAgLy8g6YeN572u5Y+R5biD6K++56iL5by556qX54q25oCBXG4gIGNvbnN0IHJlc2V0UHVibGlzaENvdXJzZU1vZGFsID0gKCkgPT4ge1xuICAgIHNldElzUHVibGlzaENvdXJzZU1vZGFsVmlzaWJsZShmYWxzZSk7XG4gICAgc2V0U2VsZWN0ZWRTZXJpZXNGb3JQdWJsaXNoKHVuZGVmaW5lZCk7XG4gICAgc2V0U2VsZWN0ZWRDb3Vyc2VGb3JQdWJsaXNoKHVuZGVmaW5lZCk7XG4gICAgc2V0UHVibGlzaFNlcmllc0xpc3RGb3JNb2RhbChbXSk7XG4gICAgc2V0UHVibGlzaENvdXJzZUxpc3RGb3JNb2RhbChbXSk7XG4gICAgcHVibGlzaENvdXJzZUZvcm0ucmVzZXRGaWVsZHMoKTtcbiAgfTtcblxuICAvLyDmiZPlvIDlj5HluIPor77nqIvlvLnnqpdcbiAgY29uc3Qgb3BlblB1Ymxpc2hDb3Vyc2VNb2RhbCA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRJc1B1Ymxpc2hDb3Vyc2VNb2RhbFZpc2libGUodHJ1ZSk7XG4gICAgYXdhaXQgZmV0Y2hQdWJsaXNoU2VyaWVzTGlzdCgpO1xuICB9O1xuXG4gIC8vIOWPkeW4g+ivvueoi1xuICBjb25zdCBoYW5kbGVQdWJsaXNoQ291cnNlID0gYXN5bmMgKHZhbHVlczogYW55KSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGlmICghc2VsZWN0ZWRDb3Vyc2VGb3JQdWJsaXNoIHx8ICFzZWxlY3RlZFNlcmllc0ZvclB1Ymxpc2gpIHtcbiAgICAgICAgbWVzc2FnZS5lcnJvcign6K+36YCJ5oup57O75YiX6K++56iL5ZKM5a2Q6K++56iLJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgc2V0UHVibGlzaEZvcm1Mb2FkaW5nKHRydWUpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk6Ig5Y+R5biD6K++56iL77yM6K++56iLSUQ6Jywgc2VsZWN0ZWRDb3Vyc2VGb3JQdWJsaXNoKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OiIOezu+WIl0lEOicsIHNlbGVjdGVkU2VyaWVzRm9yUHVibGlzaCk7XG4gICAgICBjb25zb2xlLmxvZygn8J+TpCDooajljZXmlbDmja46JywgdmFsdWVzKTtcblxuICAgICAgLy8g6I635Y+W5b2T5YmN6YCJ5Lit55qE6K++56iL5L+h5oGvXG4gICAgICBjb25zdCBzZWxlY3RlZENvdXJzZSA9IHB1Ymxpc2hDb3Vyc2VMaXN0Rm9yTW9kYWwuZmluZChjID0+IGMuaWQgPT09IHNlbGVjdGVkQ291cnNlRm9yUHVibGlzaCk7XG4gICAgICBpZiAoIXNlbGVjdGVkQ291cnNlKSB7XG4gICAgICAgIG1lc3NhZ2UuZXJyb3IoJ+acquaJvuWIsOmAieS4reeahOivvueoi+S/oeaBrycpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5OWIOW9k+WJjeivvueoi+S/oeaBrzonLCBzZWxlY3RlZENvdXJzZSk7XG5cbiAgICAgIC8vIOS9v+eUqOS4k+mXqOeahOWPkeW4g+ivvueoi0FQSVxuICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg6LCD55So5Y+R5biD6K++56iLQVBJ77yM6K++56iLSUQ6Jywgc2VsZWN0ZWRDb3Vyc2VGb3JQdWJsaXNoKTtcbiAgICAgIGNvbnN0IHsgZGF0YTogcmVzIH0gPSBhd2FpdCBjb3Vyc2VBcGkucHVibGlzaENvdXJzZShzZWxlY3RlZENvdXJzZUZvclB1Ymxpc2gpO1xuXG4gICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkge1xuICAgICAgICBtZXNzYWdlLnN1Y2Nlc3MoJ+WPkeW4g+ivvueoi+aIkOWKnycpO1xuICAgICAgICByZXNldFB1Ymxpc2hDb3Vyc2VNb2RhbCgpO1xuXG4gICAgICAgIC8vIOaYvuekuuWPkeW4g+e7k+aenOS/oeaBr1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOWPkeW4g+aIkOWKn++8jOivvueoi+S/oeaBrzonLCByZXMuZGF0YSk7XG5cbiAgICAgICAgLy8g5Yi35paw6K++56iL5YiX6KGoXG4gICAgICAgIGF3YWl0IGZldGNoQ291cnNlTGlzdCgpO1xuXG4gICAgICAgIC8vIOWmguaenOW9k+WJjeezu+WIl+W3suWxleW8gO+8jOWIt+aWsOWtkOivvueoi+WIl+ihqFxuICAgICAgICBpZiAoc2VsZWN0ZWRTZXJpZXNGb3JQdWJsaXNoICYmIGV4cGFuZGVkU2VyaWVzLmhhcyhzZWxlY3RlZFNlcmllc0ZvclB1Ymxpc2gpKSB7XG4gICAgICAgICAgYXdhaXQgZmV0Y2hTZXJpZXNDb3Vyc2VzKHNlbGVjdGVkU2VyaWVzRm9yUHVibGlzaCk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDlj5HluIPor77nqIvlpLHotKU6JywgcmVzLm1zZyk7XG4gICAgICAgIG1lc3NhZ2UuZXJyb3IocmVzLm1zZyB8fCAn5Y+R5biD6K++56iL5aSx6LSlJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOWPkeW4g+ivvueoi+W8guW4uDonLCBlcnJvcik7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg6ZSZ6K+v6K+m5oOFOicsIGVycm9yLnJlc3BvbnNlPy5kYXRhKTtcbiAgICAgIG1lc3NhZ2UuZXJyb3IoJ+WPkeW4g+ivvueoi+Wksei0pe+8jOivt+mHjeivlScpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRQdWJsaXNoRm9ybUxvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyDph43nva7lj5HluIPlvLnnqpfnirbmgIFcbiAgY29uc3QgcmVzZXRQdWJsaXNoTW9kYWwgPSAoKSA9PiB7XG4gICAgc2V0SXNQdWJsaXNoQ291cnNlTW9kYWxWaXNpYmxlKGZhbHNlKTtcbiAgICBzZXRTZWxlY3RlZFNlcmllc0ZvclB1Ymxpc2godW5kZWZpbmVkKTtcbiAgICBzZXRTZWxlY3RlZENvdXJzZUZvclB1Ymxpc2godW5kZWZpbmVkKTtcbiAgICBzZXRQdWJsaXNoU2VyaWVzQ291cnNlcyhbXSk7XG4gICAgc2V0UHVibGlzaFNlcmllc09wdGlvbnMoW10pO1xuICAgIHB1Ymxpc2hDb3Vyc2VGb3JtLnJlc2V0RmllbGRzKCk7XG4gIH07XG5cblxuXG5cblxuICAvLyDlpITnkIblm77niYfkuIrkvKBcbiAgY29uc3QgaGFuZGxlSW1hZ2VVcGxvYWQ6IFVwbG9hZFByb3BzWydjdXN0b21SZXF1ZXN0J10gPSBhc3luYyAob3B0aW9ucykgPT4ge1xuICAgIGNvbnN0IHsgZmlsZSwgb25TdWNjZXNzLCBvbkVycm9yIH0gPSBvcHRpb25zO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIOiwg+eUqOWunumZheeahE9TU+S4iuS8oEFQSVxuICAgICAgY29uc3QgdXJsID0gYXdhaXQgdXBsb2FkQXBpLnVwbG9hZFRvT3NzKGZpbGUgYXMgRmlsZSk7XG4gICAgICBjb25zb2xlLmxvZygn57O75YiX5bCB6Z2i5Zu+54mH5LiK5Lyg5oiQ5Yqf77yMVVJMOicsIHVybCk7XG5cbiAgICAgIHNldENvdmVySW1hZ2VVcmwodXJsKTtcbiAgICAgIG9uU3VjY2Vzcz8uKHsgdXJsOiB1cmwgfSk7XG4gICAgICBtZXNzYWdlLnN1Y2Nlc3MoJ+WbvueJh+S4iuS8oOaIkOWKnycpO1xuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+ezu+WIl+WwgemdouWbvueJh+S4iuS8oOWksei0pTonLCBlcnJvcik7XG4gICAgICBtZXNzYWdlLmVycm9yKGDkuIrkvKDlpLHotKU6ICR7ZXJyb3IubWVzc2FnZSB8fCAn6K+356iN5ZCO6YeN6K+VJ31gKTtcbiAgICAgIG9uRXJyb3I/LihlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWkhOeQhuWbvueJh+WIoOmZpFxuICBjb25zdCBoYW5kbGVJbWFnZVJlbW92ZSA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRDb3ZlckltYWdlVXJsKCcnKTtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfTtcblxuICAvLyDlpITnkIbor77nqIvlsIHpnaLlm77niYfkuIrkvKBcbiAgY29uc3QgaGFuZGxlQ291cnNlQ292ZXJVcGxvYWQ6IFVwbG9hZFByb3BzWydjdXN0b21SZXF1ZXN0J10gPSBhc3luYyAob3B0aW9ucykgPT4ge1xuICAgIGNvbnN0IHsgZmlsZSwgb25TdWNjZXNzLCBvbkVycm9yIH0gPSBvcHRpb25zO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIOiwg+eUqOWunumZheeahE9TU+S4iuS8oEFQSVxuICAgICAgY29uc3QgdXJsID0gYXdhaXQgdXBsb2FkQXBpLnVwbG9hZFRvT3NzKGZpbGUgYXMgRmlsZSk7XG4gICAgICBjb25zb2xlLmxvZygn6K++56iL5bCB6Z2i5Zu+54mH5LiK5Lyg5oiQ5Yqf77yMVVJMOicsIHVybCk7XG5cbiAgICAgIHNldENvdXJzZUNvdmVySW1hZ2VVcmwodXJsKTtcbiAgICAgIG9uU3VjY2Vzcz8uKHsgdXJsOiB1cmwgfSk7XG4gICAgICBtZXNzYWdlLnN1Y2Nlc3MoJ+ivvueoi+WwgemdouS4iuS8oOaIkOWKnycpO1xuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+ivvueoi+WwgemdouWbvueJh+S4iuS8oOWksei0pTonLCBlcnJvcik7XG4gICAgICBtZXNzYWdlLmVycm9yKGDkuIrkvKDlpLHotKU6ICR7ZXJyb3IubWVzc2FnZSB8fCAn6K+356iN5ZCO6YeN6K+VJ31gKTtcbiAgICAgIG9uRXJyb3I/LihlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWkhOeQhuivvueoi+WwgemdouWIoOmZpFxuICBjb25zdCBoYW5kbGVDb3Vyc2VDb3ZlclJlbW92ZSA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRDb3Vyc2VDb3ZlckltYWdlVXJsKCcnKTtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfTtcblxuICAvLyDlpITnkIbpmYTku7botYTmupDkuIrkvKBcbiAgY29uc3QgaGFuZGxlQWRkaXRpb25hbFJlc291cmNlVXBsb2FkOiBVcGxvYWRQcm9wc1snY3VzdG9tUmVxdWVzdCddID0gYXN5bmMgKG9wdGlvbnMpID0+IHtcbiAgICBjb25zdCB7IGZpbGUsIG9uU3VjY2Vzcywgb25FcnJvciB9ID0gb3B0aW9ucztcblxuICAgIHRyeSB7XG4gICAgICAvLyDosIPnlKjlrp7pmYXnmoRPU1PkuIrkvKBBUElcbiAgICAgIGNvbnN0IHVybCA9IGF3YWl0IHVwbG9hZEFwaS51cGxvYWRUb09zcyhmaWxlIGFzIEZpbGUpO1xuICAgICAgY29uc29sZS5sb2coJ+mZhOS7tui1hOa6kOS4iuS8oOaIkOWKn++8jFVSTDonLCB1cmwpO1xuXG4gICAgICBzZXRBZGRpdGlvbmFsRmlsZXMocHJldiA9PiBbLi4ucHJldiwgdXJsXSk7XG4gICAgICBvblN1Y2Nlc3M/Lih7IHVybDogdXJsLCBuYW1lOiAoZmlsZSBhcyBGaWxlKS5uYW1lIH0pO1xuICAgICAgbWVzc2FnZS5zdWNjZXNzKGDpmYTku7YgJHsoZmlsZSBhcyBGaWxlKS5uYW1lfSDkuIrkvKDmiJDlip9gKTtcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfpmYTku7botYTmupDkuIrkvKDlpLHotKU6JywgZXJyb3IpO1xuICAgICAgbWVzc2FnZS5lcnJvcihg6ZmE5Lu2ICR7KGZpbGUgYXMgRmlsZSkubmFtZX0g5LiK5Lyg5aSx6LSlOiAke2Vycm9yLm1lc3NhZ2UgfHwgJ+ivt+eojeWQjumHjeivlSd9YCk7XG4gICAgICBvbkVycm9yPy4oZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICAvLyDlpITnkIbpmYTku7bliKDpmaRcbiAgY29uc3QgaGFuZGxlQWRkaXRpb25hbFJlc291cmNlUmVtb3ZlID0gYXN5bmMgKGZpbGU6IGFueSkgPT4ge1xuICAgIGNvbnN0IHVybCA9IGZpbGUudXJsIHx8IGZpbGUucmVzcG9uc2U/LnVybDtcbiAgICBzZXRBZGRpdGlvbmFsRmlsZXMocHJldiA9PiBwcmV2LmZpbHRlcihmID0+IGYgIT09IHVybCkpO1xuICAgIHJldHVybiB0cnVlO1xuICB9O1xuICAvLyDlpITnkIbop4bpopHkuIrkvKBcbiAgY29uc3QgaGFuZGxlVmlkZW9VcGxvYWQ6IFVwbG9hZFByb3BzWydjdXN0b21SZXF1ZXN0J10gPSBhc3luYyAob3B0aW9ucykgPT4ge1xuICAgIGNvbnN0IHsgZmlsZSwgb25TdWNjZXNzLCBvbkVycm9yIH0gPSBvcHRpb25zO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVybCA9IGF3YWl0IHVwbG9hZEFwaS51cGxvYWRUb09zcyhmaWxlIGFzIEZpbGUpO1xuICAgICAgY29uc29sZS5sb2coJ+ivvueoi+inhumikeS4iuS8oOaIkOWKn++8jFVSTDonLCB1cmwpO1xuXG4gICAgICBzZXRDb3Vyc2VWaWRlb1VybCh1cmwpO1xuICAgICAgc2V0Q291cnNlVmlkZW9OYW1lKChmaWxlIGFzIEZpbGUpLm5hbWUpO1xuXG4gICAgICAvLyDlpoLmnpzmmK/op4bpopHmlofku7bvvIzlsJ3or5Xojrflj5bml7bplb9cbiAgICAgIGNvbnN0IHZpZGVvRWxlbWVudCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ3ZpZGVvJyk7XG4gICAgICB2aWRlb0VsZW1lbnQuc3JjID0gdXJsO1xuICAgICAgdmlkZW9FbGVtZW50Lm9ubG9hZGVkbWV0YWRhdGEgPSAoKSA9PiB7XG4gICAgICAgIHNldFZpZGVvRHVyYXRpb24oTWF0aC5mbG9vcih2aWRlb0VsZW1lbnQuZHVyYXRpb24pKTtcbiAgICAgIH07XG5cbiAgICAgIG9uU3VjY2Vzcz8uKHsgdXJsOiB1cmwgfSk7XG4gICAgICBtZXNzYWdlLnN1Y2Nlc3MoJ+ivvueoi+inhumikeS4iuS8oOaIkOWKnycpO1xuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+ivvueoi+inhumikeS4iuS8oOWksei0pTonLCBlcnJvcik7XG4gICAgICBtZXNzYWdlLmVycm9yKGDop4bpopHkuIrkvKDlpLHotKU6ICR7ZXJyb3IubWVzc2FnZSB8fCAn6K+356iN5ZCO6YeN6K+VJ31gKTtcbiAgICAgIG9uRXJyb3I/LihlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWkhOeQhuinhumikeWIoOmZpFxuICBjb25zdCBoYW5kbGVWaWRlb1JlbW92ZSA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRDb3Vyc2VWaWRlb1VybCgnJyk7XG4gICAgc2V0Q291cnNlVmlkZW9OYW1lKCcnKTtcbiAgICBzZXRWaWRlb0R1cmF0aW9uKDApO1xuICAgIHJldHVybiB0cnVlO1xuICB9O1xuXG4gIC8vIOWkhOeQhuaWh+aho+S4iuS8oFxuICBjb25zdCBoYW5kbGVEb2N1bWVudFVwbG9hZDogVXBsb2FkUHJvcHNbJ2N1c3RvbVJlcXVlc3QnXSA9IGFzeW5jIChvcHRpb25zKSA9PiB7XG4gICAgY29uc3QgeyBmaWxlLCBvblN1Y2Nlc3MsIG9uRXJyb3IgfSA9IG9wdGlvbnM7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgdXJsID0gYXdhaXQgdXBsb2FkQXBpLnVwbG9hZFRvT3NzKGZpbGUgYXMgRmlsZSk7XG4gICAgICBjb25zb2xlLmxvZygn6K++56iL5paH5qGj5LiK5Lyg5oiQ5Yqf77yMVVJMOicsIHVybCk7XG5cbiAgICAgIHNldENvdXJzZURvY3VtZW50VXJsKHVybCk7XG4gICAgICBzZXRDb3Vyc2VEb2N1bWVudE5hbWUoKGZpbGUgYXMgRmlsZSkubmFtZSk7XG4gICAgICBvblN1Y2Nlc3M/Lih7IHVybDogdXJsIH0pO1xuICAgICAgbWVzc2FnZS5zdWNjZXNzKCfor77nqIvmlofmoaPkuIrkvKDmiJDlip8nKTtcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfor77nqIvmlofmoaPkuIrkvKDlpLHotKU6JywgZXJyb3IpO1xuICAgICAgbWVzc2FnZS5lcnJvcihg5paH5qGj5LiK5Lyg5aSx6LSlOiAke2Vycm9yLm1lc3NhZ2UgfHwgJ+ivt+eojeWQjumHjeivlSd9YCk7XG4gICAgICBvbkVycm9yPy4oZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICAvLyDlpITnkIbmlofmoaPliKDpmaRcbiAgY29uc3QgaGFuZGxlRG9jdW1lbnRSZW1vdmUgPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0Q291cnNlRG9jdW1lbnRVcmwoJycpO1xuICAgIHNldENvdXJzZURvY3VtZW50TmFtZSgnJyk7XG4gICAgcmV0dXJuIHRydWU7XG4gIH07XG5cbiAgLy8g5aSE55CG6Z+z6aKR5LiK5LygXG4gIGNvbnN0IGhhbmRsZUF1ZGlvVXBsb2FkOiBVcGxvYWRQcm9wc1snY3VzdG9tUmVxdWVzdCddID0gYXN5bmMgKG9wdGlvbnMpID0+IHtcbiAgICBjb25zdCB7IGZpbGUsIG9uU3VjY2Vzcywgb25FcnJvciB9ID0gb3B0aW9ucztcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCB1cmwgPSBhd2FpdCB1cGxvYWRBcGkudXBsb2FkVG9Pc3MoZmlsZSBhcyBGaWxlKTtcbiAgICAgIGNvbnNvbGUubG9nKCfor77nqIvpn7PpopHkuIrkvKDmiJDlip/vvIxVUkw6JywgdXJsKTtcblxuICAgICAgc2V0Q291cnNlQXVkaW9VcmwodXJsKTtcbiAgICAgIHNldENvdXJzZUF1ZGlvTmFtZSgoZmlsZSBhcyBGaWxlKS5uYW1lKTtcbiAgICAgIG9uU3VjY2Vzcz8uKHsgdXJsOiB1cmwgfSk7XG4gICAgICBtZXNzYWdlLnN1Y2Nlc3MoJ+ivvueoi+mfs+mikeS4iuS8oOaIkOWKnycpO1xuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+ivvueoi+mfs+mikeS4iuS8oOWksei0pTonLCBlcnJvcik7XG4gICAgICBtZXNzYWdlLmVycm9yKGDpn7PpopHkuIrkvKDlpLHotKU6ICR7ZXJyb3IubWVzc2FnZSB8fCAn6K+356iN5ZCO6YeN6K+VJ31gKTtcbiAgICAgIG9uRXJyb3I/LihlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWkhOeQhumfs+mikeWIoOmZpFxuICBjb25zdCBoYW5kbGVBdWRpb1JlbW92ZSA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRDb3Vyc2VBdWRpb1VybCgnJyk7XG4gICAgc2V0Q291cnNlQXVkaW9OYW1lKCcnKTtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfTtcblxuXG5cblxuXG4gIC8vIOaJk+W8gOe8lui+keaooeaAgeahhlxuICBjb25zdCBvcGVuRWRpdE1vZGFsID0gYXN5bmMgKGNvdXJzZTogQ291cnNlKSA9PiB7XG4gICAgc2V0RWRpdGluZ0NvdXJzZShjb3Vyc2UpO1xuICAgIGVkaXRDb3Vyc2VGb3JtLnNldEZpZWxkc1ZhbHVlKGNvdXJzZSk7XG4gICAgc2V0SXNFZGl0Q291cnNlTW9kYWxWaXNpYmxlKHRydWUpO1xuICB9O1xuXG4gIC8vIOi/h+a7pOivvueoi+WIl+ihqFxuICBjb25zdCBmaWx0ZXJlZENvdXJzZXMgPSAoY291cnNlTGlzdCB8fCBbXSkuZmlsdGVyKGNvdXJzZSA9PlxuICAgIGNvdXJzZS5uYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoS2V5d29yZC50b0xvd2VyQ2FzZSgpKSB8fFxuICAgIGNvdXJzZS5kZXNjcmlwdGlvbi50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaEtleXdvcmQudG9Mb3dlckNhc2UoKSkgfHxcbiAgICBjb3Vyc2UuY2F0ZWdvcnkudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hLZXl3b3JkLnRvTG93ZXJDYXNlKCkpXG4gICk7XG5cbiAgLy8g5YeG5aSH6KGo5qC85pWw5o2u77ya5bCG57O75YiX6K++56iL5ZKM5a2Q6K++56iL5ZCI5bm25Li65LiA5Liq5omB5bmz5YiX6KGoXG4gIGNvbnN0IHByZXBhcmVUYWJsZURhdGEgPSAoKSA9PiB7XG4gICAgY29uc3QgdGFibGVEYXRhOiBhbnlbXSA9IFtdO1xuXG4gICAgY29uc29sZS5sb2coJ/CflIQg5YeG5aSH6KGo5qC85pWw5o2uLi4uJyk7XG4gICAgY29uc29sZS5sb2coJ/Cfk4og57O75YiX6K++56iL5YiX6KGoOicsIHNlcmllc0xpc3QpO1xuICAgIGNvbnNvbGUubG9nKCfwn5OKIOWxleW8gOeahOezu+WIlzonLCBBcnJheS5mcm9tKGV4cGFuZGVkU2VyaWVzKSk7XG4gICAgY29uc29sZS5sb2coJ/Cfk4og5a2Q6K++56iL5pig5bCEOicsIHNlcmllc0NvdXJzZXNNYXApO1xuXG4gICAgc2VyaWVzTGlzdC5mb3JFYWNoKHNlcmllcyA9PiB7XG4gICAgICAvLyDmt7vliqDns7vliJfor77nqIvooYxcbiAgICAgIHRhYmxlRGF0YS5wdXNoKHtcbiAgICAgICAga2V5OiBgc2VyaWVzLSR7c2VyaWVzLmlkfWAsXG4gICAgICAgIGlkOiBzZXJpZXMuaWQsXG4gICAgICAgIHRpdGxlOiBzZXJpZXMudGl0bGUsXG4gICAgICAgIHN0YXR1czogc2VyaWVzLnN0YXR1cyxcbiAgICAgICAgdHlwZTogJ3NlcmllcycsXG4gICAgICAgIGlzRXhwYW5kZWQ6IGV4cGFuZGVkU2VyaWVzLmhhcyhzZXJpZXMuaWQpLFxuICAgICAgICBzZXJpZXNJZDogc2VyaWVzLmlkXG4gICAgICB9KTtcblxuICAgICAgLy8g5aaC5p6c57O75YiX5bey5bGV5byA77yM5re75Yqg5a2Q6K++56iL6KGMXG4gICAgICBpZiAoZXhwYW5kZWRTZXJpZXMuaGFzKHNlcmllcy5pZCkpIHtcbiAgICAgICAgY29uc3Qgc3ViQ291cnNlcyA9IHNlcmllc0NvdXJzZXNNYXAuZ2V0KHNlcmllcy5pZCkgfHwgW107XG4gICAgICAgIGNvbnNvbGUubG9nKGDwn5OaIOezu+WIlyAke3Nlcmllcy5pZH0g55qE5a2Q6K++56iLOmAsIHN1YkNvdXJzZXMpO1xuXG4gICAgICAgIHN1YkNvdXJzZXMuZm9yRWFjaChjb3Vyc2UgPT4ge1xuICAgICAgICAgIHRhYmxlRGF0YS5wdXNoKHtcbiAgICAgICAgICAgIGtleTogYGNvdXJzZS0ke2NvdXJzZS5pZH1gLFxuICAgICAgICAgICAgaWQ6IGNvdXJzZS5pZCxcbiAgICAgICAgICAgIHRpdGxlOiBjb3Vyc2UudGl0bGUsXG4gICAgICAgICAgICBzdGF0dXM6IGNvdXJzZS5zdGF0dXMsXG4gICAgICAgICAgICB0eXBlOiAnY291cnNlJyxcbiAgICAgICAgICAgIHNlcmllc0lkOiBzZXJpZXMuaWQsXG4gICAgICAgICAgICBwYXJlbnRTZXJpZXNUaXRsZTogc2VyaWVzLnRpdGxlXG4gICAgICAgICAgfSk7XG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH0pO1xuXG4gICAgY29uc29sZS5sb2coJ/Cfk4sg5pyA57uI6KGo5qC85pWw5o2uOicsIHRhYmxlRGF0YSk7XG4gICAgcmV0dXJuIHRhYmxlRGF0YTtcbiAgfTtcblxuICAvLyDooajmoLzliJflrprkuYlcbiAgY29uc3QgY29sdW1ucyA9IFtcbiAgICB7XG4gICAgICB0aXRsZTogJ+ezu+WIl+ivvueoi0lEJyxcbiAgICAgIGRhdGFJbmRleDogJ2lkJyxcbiAgICAgIGtleTogJ2lkJyxcbiAgICAgIHdpZHRoOiAxMjAsXG4gICAgfSxcbiAgICB7XG4gICAgICB0aXRsZTogJ+ezu+WIl+ivvueoiy/lrZDor77nqIvlkI3np7AnLFxuICAgICAgZGF0YUluZGV4OiAndGl0bGUnLFxuICAgICAga2V5OiAndGl0bGUnLFxuICAgICAgcmVuZGVyOiAodGV4dDogc3RyaW5nLCByZWNvcmQ6IGFueSkgPT4ge1xuICAgICAgICBpZiAocmVjb3JkLnR5cGUgPT09ICdzZXJpZXMnKSB7XG4gICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICBzaXplPVwic21hbGxcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHRvZ2dsZVNlcmllc0V4cGFuc2lvbihyZWNvcmQuaWQpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMCBtaW4tdy0wIGhvdmVyOmJnLWJsdWUtNTBcIlxuICAgICAgICAgICAgICAgIHN0eWxlPXt7IG1pbldpZHRoOiAnMjBweCcsIGhlaWdodDogJzIwcHgnIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7cmVjb3JkLmlzRXhwYW5kZWQgPyAn4pa8JyA6ICfilrYnfVxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ibHVlLTYwMCB0ZXh0LWJhc2VcIj57dGV4dH08L3NwYW4+XG4gICAgICAgICAgICAgIDxUYWcgY29sb3I9XCJibHVlXCIgY2xhc3NOYW1lPVwidGV4dC14c1wiPuezu+WIlzwvVGFnPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC04IGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj7ilJTilIA8L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDBcIj57dGV4dH08L3NwYW4+XG4gICAgICAgICAgICAgIDxUYWcgY29sb3I9XCJncmVlblwiIGNsYXNzTmFtZT1cInRleHQteHNcIj7lrZDor77nqIs8L1RhZz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICk7XG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgfSxcbiAgICB7XG4gICAgICB0aXRsZTogJ+WPkeW4g+eKtuaAgScsXG4gICAgICBkYXRhSW5kZXg6ICdzdGF0dXMnLFxuICAgICAga2V5OiAnc3RhdHVzJyxcbiAgICAgIHdpZHRoOiAxMDAsXG4gICAgICByZW5kZXI6IChzdGF0dXM6IG51bWJlciwgcmVjb3JkOiBhbnkpID0+IHtcbiAgICAgICAgY29uc3QgZ2V0U3RhdHVzQ29uZmlnID0gKHN0YXR1czogbnVtYmVyKSA9PiB7XG4gICAgICAgICAgc3dpdGNoIChzdGF0dXMpIHtcbiAgICAgICAgICAgIGNhc2UgMTogcmV0dXJuIHsgY29sb3I6ICdncmVlbicsIHRleHQ6ICflt7Llj5HluIMnIH07XG4gICAgICAgICAgICBjYXNlIDA6IHJldHVybiB7IGNvbG9yOiAnb3JhbmdlJywgdGV4dDogJ+iNieeovycgfTtcbiAgICAgICAgICAgIGNhc2UgMjogcmV0dXJuIHsgY29sb3I6ICdyZWQnLCB0ZXh0OiAn5bey5b2S5qGjJyB9O1xuICAgICAgICAgICAgZGVmYXVsdDogcmV0dXJuIHsgY29sb3I6ICdncmF5JywgdGV4dDogJ+acquefpScgfTtcbiAgICAgICAgICB9XG4gICAgICAgIH07XG5cbiAgICAgICAgY29uc3QgY29uZmlnID0gZ2V0U3RhdHVzQ29uZmlnKHN0YXR1cyk7XG4gICAgICAgIHJldHVybiA8VGFnIGNvbG9yPXtjb25maWcuY29sb3J9Pntjb25maWcudGV4dH08L1RhZz47XG4gICAgICB9LFxuICAgIH0sXG4gICAge1xuICAgICAgdGl0bGU6ICfmk43kvZwnLFxuICAgICAga2V5OiAnYWN0aW9uJyxcbiAgICAgIHdpZHRoOiAxNTAsXG4gICAgICByZW5kZXI6IChyZWNvcmQ6IGFueSkgPT4ge1xuICAgICAgICBpZiAocmVjb3JkLnR5cGUgPT09ICdzZXJpZXMnKSB7XG4gICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgIDxTcGFjZSBzaXplPVwic21hbGxcIj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHR5cGU9XCJsaW5rXCJcbiAgICAgICAgICAgICAgICBzaXplPVwic21hbGxcIlxuICAgICAgICAgICAgICAgIGljb249ezxFZGl0T3V0bGluZWQgLz59XG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgbWVzc2FnZS5pbmZvKCfns7vliJfor77nqIvnvJbovpHlip/og73lvoXlrp7njrAnKTtcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg57yW6L6RXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9TcGFjZT5cbiAgICAgICAgICApO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICA8U3BhY2Ugc2l6ZT1cInNtYWxsXCI+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICB0eXBlPVwibGlua1wiXG4gICAgICAgICAgICAgICAgc2l6ZT1cInNtYWxsXCJcbiAgICAgICAgICAgICAgICBpY29uPXs8RWRpdE91dGxpbmVkIC8+fVxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgIG1lc3NhZ2UuaW5mbygn5a2Q6K++56iL57yW6L6R5Yqf6IO95b6F5a6e546wJyk7XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwIGhvdmVyOnRleHQtYmx1ZS04MDBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg57yW6L6RXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8UG9wY29uZmlybVxuICAgICAgICAgICAgICAgIHRpdGxlPXtcbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxwPuehruWumuimgeWIoOmZpOi/meS4quWtkOivvueoi+WQl++8nzwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCB0ZXh0LXNtXCI+6K++56iL5ZCN56ew77yae3JlY29yZC50aXRsZX08L3A+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgdGV4dC1zbVwiPuaJgOWxnuezu+WIl++8mntyZWNvcmQucGFyZW50U2VyaWVzVGl0bGV9PC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIG9uQ29uZmlybT17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfl5HvuI8g55So5oi356Gu6K6k5Yig6Zmk5a2Q6K++56iLOicsIHJlY29yZCk7XG4gICAgICAgICAgICAgICAgICBoYW5kbGVEZWxldGVTdWJDb3Vyc2UocmVjb3JkLmlkLCByZWNvcmQuc2VyaWVzSWQpO1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgb2tUZXh0PVwi56Gu5a6a5Yig6ZmkXCJcbiAgICAgICAgICAgICAgICBjYW5jZWxUZXh0PVwi5Y+W5raIXCJcbiAgICAgICAgICAgICAgICBva1R5cGU9XCJkYW5nZXJcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgdHlwZT1cImxpbmtcIlxuICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtYWxsXCJcbiAgICAgICAgICAgICAgICAgIGRhbmdlclxuICAgICAgICAgICAgICAgICAgaWNvbj17PERlbGV0ZU91dGxpbmVkIC8+fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwIGhvdmVyOnRleHQtcmVkLTgwMFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAg5Yig6ZmkXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDwvUG9wY29uZmlybT5cbiAgICAgICAgICAgIDwvU3BhY2U+XG4gICAgICAgICAgKTtcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICB9LFxuICBdO1xuXG4gIC8vIOiOt+WPluaVmeW4iOWIl+ihqFxuICAvLyBjb25zdCBmZXRjaFRlYWNoZXJzID0gYXN5bmMgKCkgPT4ge1xuICAvLyAgIHRyeSB7XG4gIC8vICAgICBjb25zdCB7IGRhdGE6IHJlcyB9ID0gYXdhaXQgY291cnNlQXBpLmdldFRlYWNoZXJzKCk7XG4gIC8vICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkge1xuICAvLyAgICAgICBzZXRUZWFjaGVycyhyZXMuZGF0YSk7XG4gIC8vICAgICAgIGNvbnNvbGUubG9nKCfmiJDlip/ojrflj5bmlZnluIjliJfooag6JywgcmVzLmRhdGEpO1xuICAvLyAgICAgfSBlbHNlIHtcbiAgLy8gICAgICAgY29uc29sZS5sb2coJ0FQSei/lOWbnuaXoOaVsOaNru+8jOS9v+eUqOaooeaLn+aVmeW4iOaVsOaNricpO1xuICAvLyAgICAgICAvLyDkvb/nlKjmqKHmi5/mlbDmja5cbiAgLy8gICAgICAgY29uc3QgbW9ja1RlYWNoZXJzID0gW1xuICAvLyAgICAgICAgIHsgaWQ6IDEsIG5hbWU6ICflvKDogIHluIgnLCBlbWFpbDogJ3poYW5nQHNjaG9vbC5jb20nLCBzdWJqZWN0OiAn5pWw5a2mJywgc2Nob29sOiAn5a6e6aqM5bCP5a2mJywgYXZhdGFyOiAnJywgcGhvbmU6ICcxMzgwMDEzODAwMScgfSxcbiAgLy8gICAgICAgICB7IGlkOiAyLCBuYW1lOiAn5p2O6ICB5biIJywgZW1haWw6ICdsaUBzY2hvb2wuY29tJywgc3ViamVjdDogJ+ivreaWhycsIHNjaG9vbDogJ+WunumqjOWwj+WtpicsIGF2YXRhcjogJycsIHBob25lOiAnMTM4MDAxMzgwMDInIH0sXG4gIC8vICAgICAgICAgeyBpZDogMywgbmFtZTogJ+eOi+iAgeW4iCcsIGVtYWlsOiAnd2FuZ0BzY2hvb2wuY29tJywgc3ViamVjdDogJ+iLseivrScsIHNjaG9vbDogJ+esrOS6jOWwj+WtpicsIGF2YXRhcjogJycsIHBob25lOiAnMTM4MDAxMzgwMDMnIH0sXG4gIC8vICAgICAgICAgeyBpZDogNCwgbmFtZTogJ+i1teiAgeW4iCcsIGVtYWlsOiAnemhhb0BzY2hvb2wuY29tJywgc3ViamVjdDogJ+enkeWtpicsIHNjaG9vbDogJ+esrOS6jOWwj+WtpicsIGF2YXRhcjogJycsIHBob25lOiAnMTM4MDAxMzgwMDQnIH0sXG4gIC8vICAgICAgICAgeyBpZDogNSwgbmFtZTogJ+WImOiAgeW4iCcsIGVtYWlsOiAnbGl1QHNjaG9vbC5jb20nLCBzdWJqZWN0OiAn57yW56iLJywgc2Nob29sOiAn5a6e6aqM5Lit5a2mJywgYXZhdGFyOiAnJywgcGhvbmU6ICcxMzgwMDEzODAwNScgfSxcbiAgLy8gICAgICAgICB7IGlkOiA2LCBuYW1lOiAn6ZmI6ICB5biIJywgZW1haWw6ICdjaGVuQHNjaG9vbC5jb20nLCBzdWJqZWN0OiAn5L+h5oGv5oqA5pyvJywgc2Nob29sOiAn5a6e6aqM5Lit5a2mJywgYXZhdGFyOiAnJywgcGhvbmU6ICcxMzgwMDEzODAwNicgfVxuICAvLyAgICAgICBdO1xuICAvLyAgICAgICBzZXRUZWFjaGVycyhtb2NrVGVhY2hlcnMpO1xuICAvLyAgICAgfVxuICAvLyAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gIC8vICAgICBjb25zb2xlLmVycm9yKCfojrflj5bmlZnluIjliJfooajlpLHotKU6JywgZXJyb3IpO1xuICAvLyAgICAgLy8g5L2/55So5qih5ouf5pWw5o2uXG4gIC8vICAgICBjb25zdCBtb2NrVGVhY2hlcnMgPSBbXG4gIC8vICAgICAgIHsgaWQ6IDEsIG5hbWU6ICflvKDogIHluIgnLCBlbWFpbDogJ3poYW5nQHNjaG9vbC5jb20nLCBzdWJqZWN0OiAn5pWw5a2mJywgc2Nob29sOiAn5a6e6aqM5bCP5a2mJywgYXZhdGFyOiAnJywgcGhvbmU6ICcxMzgwMDEzODAwMScgfSxcbiAgLy8gICAgICAgeyBpZDogMiwgbmFtZTogJ+adjuiAgeW4iCcsIGVtYWlsOiAnbGlAc2Nob29sLmNvbScsIHN1YmplY3Q6ICfor63mlocnLCBzY2hvb2w6ICflrp7pqozlsI/lraYnLCBhdmF0YXI6ICcnLCBwaG9uZTogJzEzODAwMTM4MDAyJyB9LFxuICAvLyAgICAgICB7IGlkOiAzLCBuYW1lOiAn546L6ICB5biIJywgZW1haWw6ICd3YW5nQHNjaG9vbC5jb20nLCBzdWJqZWN0OiAn6Iux6K+tJywgc2Nob29sOiAn56ys5LqM5bCP5a2mJywgYXZhdGFyOiAnJywgcGhvbmU6ICcxMzgwMDEzODAwMycgfSxcbiAgLy8gICAgICAgeyBpZDogNCwgbmFtZTogJ+i1teiAgeW4iCcsIGVtYWlsOiAnemhhb0BzY2hvb2wuY29tJywgc3ViamVjdDogJ+enkeWtpicsIHNjaG9vbDogJ+esrOS6jOWwj+WtpicsIGF2YXRhcjogJycsIHBob25lOiAnMTM4MDAxMzgwMDQnIH0sXG4gIC8vICAgICAgIHsgaWQ6IDUsIG5hbWU6ICfliJjogIHluIgnLCBlbWFpbDogJ2xpdUBzY2hvb2wuY29tJywgc3ViamVjdDogJ+e8lueoiycsIHNjaG9vbDogJ+WunumqjOS4reWtpicsIGF2YXRhcjogJycsIHBob25lOiAnMTM4MDAxMzgwMDUnIH0sXG4gIC8vICAgICAgIHsgaWQ6IDYsIG5hbWU6ICfpmYjogIHluIgnLCBlbWFpbDogJ2NoZW5Ac2Nob29sLmNvbScsIHN1YmplY3Q6ICfkv6Hmga/mioDmnK8nLCBzY2hvb2w6ICflrp7pqozkuK3lraYnLCBhdmF0YXI6ICcnLCBwaG9uZTogJzEzODAwMTM4MDA2JyB9XG4gIC8vICAgICBdO1xuICAvLyAgICAgc2V0VGVhY2hlcnMobW9ja1RlYWNoZXJzKTtcbiAgLy8gICAgIGNvbnNvbGUubG9nKCfkvb/nlKjmqKHmi5/mlZnluIjmlbDmja46JywgbW9ja1RlYWNoZXJzKTtcbiAgLy8gICB9XG4gIC8vIH07XG5cbiAgLy8g6I635Y+W6K++56iL5qCH562+5YiX6KGoIC0g5L2/55So6K++56iL5biC5Zy6QVBJXG4gIGNvbnN0IGZldGNoQ291cnNlVGFncyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ/Cfj7fvuI8g5byA5aeL6I635Y+W6K++56iL5qCH562+5YiX6KGoLi4uJyk7XG4gICAgICBjb25zdCB7IGRhdGE6IHJlcyB9ID0gYXdhaXQgY291cnNlQXBpLmdldENvdXJzZVRhZ3Moe1xuICAgICAgICBwYWdlOiAxLFxuICAgICAgICBwYWdlU2l6ZTogMTAwLCAvLyDojrflj5bmm7TlpJrmoIfnrb7nlKjkuo7pgInmi6lcbiAgICAgICAgc3RhdHVzOiAxICAgICAgLy8g5Y+q6I635Y+W5ZCv55So55qE5qCH562+XG4gICAgICB9KTtcblxuICAgICAgY29uc29sZS5sb2coJ/Cfk6ggZ2V0Q291cnNlVGFncyBBUEnlk43lupQ6JywgcmVzKTtcblxuICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDAgJiYgcmVzLmRhdGEgJiYgcmVzLmRhdGEubGlzdCkge1xuICAgICAgICBjb25zdCB0YWdzID0gcmVzLmRhdGEubGlzdC5tYXAoKHRhZzogYW55KSA9PiAoe1xuICAgICAgICAgIGlkOiB0YWcuaWQsXG4gICAgICAgICAgbmFtZTogdGFnLm5hbWUsXG4gICAgICAgICAgY29sb3I6IHRhZy5jb2xvcixcbiAgICAgICAgICBjYXRlZ29yeTogdGFnLmNhdGVnb3J5LFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiB0YWcuZGVzY3JpcHRpb24gfHwgJydcbiAgICAgICAgfSkpO1xuXG4gICAgICAgIHNldENvdXJzZVRhZ3ModGFncyk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg5oiQ5Yqf6I635Y+W6K++56iL5qCH562+5YiX6KGoOicsIHRhZ3MpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS53YXJuKCfimqDvuI8gQVBJ6L+U5Zue5pWw5o2u5qC85byP5byC5bi4OicsIHJlcyk7XG4gICAgICAgIHNldENvdXJzZVRhZ3MoW10pO1xuICAgICAgICBub3RpZmljYXRpb24ud2FybmluZygn6I635Y+W5qCH562+5YiX6KGo5aSx6LSl77yM6K+35qOA5p+l572R57uc6L+e5o6lJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDojrflj5bor77nqIvmoIfnrb7lpLHotKU6JywgZXJyb3IpO1xuICAgICAgc2V0Q291cnNlVGFncyhbXSk7XG4gICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+iOt+WPluagh+etvuWIl+ihqOWksei0pe+8jOivt+mHjeivlScpO1xuICAgIH1cbiAgfTtcblxuICAvLyDojrflj5bor77nqIvns7vliJfliJfooaggLSDkvb/nlKjor77nqIvluILlnLpBUElcbiAgY29uc3QgZmV0Y2hDb3Vyc2VTZXJpZXMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SEIOW8gOWni+iOt+WPluivvueoi+W4guWcuuezu+WIl+ivvueoi+WIl+ihqC4uLicpO1xuICAgICAgY29uc3QgeyBkYXRhOiByZXMgfSA9IGF3YWl0IGNvdXJzZUFwaS5nZXRNYXJrZXRwbGFjZVNlcmllcyh7XG4gICAgICAgIHBhZ2U6IDEsXG4gICAgICAgIHBhZ2VTaXplOiA1MCAvLyDor77nqIvluILlnLpBUEnpmZDliLbmnIDlpKc1MFxuICAgICAgfSk7XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5OoIGdldE1hcmtldHBsYWNlU2VyaWVzIEFQSeWTjeW6lDonLCByZXMpO1xuXG4gICAgICAvLyDmo4Dmn6XmmK/lkKbmnInmm7TlpJrmlbDmja5cbiAgICAgIGlmIChyZXMuZGF0YT8ucGFnaW5hdGlvbj8udG90YWwgPiA1MCkge1xuICAgICAgICBjb25zb2xlLmxvZyhg4pqg77iPIOazqOaEj++8muaAu+WFseaciSAke3Jlcy5kYXRhLnBhZ2luYXRpb24udG90YWx9IOS4quezu+WIl+ivvueoi++8jOW9k+WJjeWPquaYvuekuuWJjTUw5LiqYCk7XG4gICAgICB9XG5cbiAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwICYmIHJlcy5kYXRhKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5OKIEFQSei/lOWbnueahOWujOaVtOaVsOaNrue7k+aehDonLCByZXMuZGF0YSk7XG5cbiAgICAgICAgaWYgKHJlcy5kYXRhLmxpc3QgJiYgQXJyYXkuaXNBcnJheShyZXMuZGF0YS5saXN0KSkge1xuICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5OLIOiOt+WPluWIsCAke3Jlcy5kYXRhLmxpc3QubGVuZ3RofSDkuKrns7vliJfor77nqItgKTtcblxuICAgICAgICAgIC8vIOWwhuivvueoi+W4guWcukFQSei/lOWbnueahOaVsOaNrui9rOaNouS4uue7hOS7tumcgOimgeeahOagvOW8j1xuICAgICAgICAgIGNvbnN0IGZvcm1hdHRlZFNlcmllcyA9IHJlcy5kYXRhLmxpc3QubWFwKChpdGVtOiBhbnksIGluZGV4OiBudW1iZXIpID0+IHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5SNIOWkhOeQhuesrCAke2luZGV4ICsgMX0g5Liq57O75YiXOmAsIHtcbiAgICAgICAgICAgICAgaWQ6IGl0ZW0uaWQsXG4gICAgICAgICAgICAgIHRpdGxlOiBpdGVtLnRpdGxlLFxuICAgICAgICAgICAgICBjYXRlZ29yeTogaXRlbS5jYXRlZ29yeSxcbiAgICAgICAgICAgICAgY2F0ZWdvcnlMYWJlbDogaXRlbS5jYXRlZ29yeUxhYmVsLFxuICAgICAgICAgICAgICB0YWdzOiBpdGVtLnRhZ3NcbiAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICBpZDogaXRlbS5pZCxcbiAgICAgICAgICAgICAgdGl0bGU6IGl0ZW0udGl0bGUsXG4gICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBpdGVtLmRlc2NyaXB0aW9uLFxuICAgICAgICAgICAgICBjb3ZlckltYWdlOiBpdGVtLmNvdmVySW1hZ2UgfHwgJycsXG4gICAgICAgICAgICAgIGNhdGVnb3J5OiBpdGVtLmNhdGVnb3J5TGFiZWwgfHwgKGl0ZW0uY2F0ZWdvcnkgPT09IDAgPyAn5a6Y5pa5JyA6ICfnpL7ljLonKSwgLy8g5L2/55SoY2F0ZWdvcnlMYWJlbOaIlui9rOaNomNhdGVnb3J5XG4gICAgICAgICAgICAgIHRlYWNoZXJJZHM6IFtdLCAvLyDor77nqIvluILlnLpBUEnkuI3ov5Tlm550ZWFjaGVySWRzXG4gICAgICAgICAgICAgIHRhZ0lkczogaXRlbS50YWdzPy5tYXAoKHRhZzogYW55KSA9PiB0YWcuaWQpIHx8IFtdLCAvLyDku450YWdz5pWw57uE5Lit5o+Q5Y+WSURcbiAgICAgICAgICAgICAgY3JlYXRlZEF0OiBpdGVtLmNyZWF0ZWRBdCB8fCBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICAgIHVwZGF0ZWRBdDogaXRlbS51cGRhdGVkQXQgfHwgbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgICB9O1xuICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgc2V0Q291cnNlU2VyaWVzKGZvcm1hdHRlZFNlcmllcyk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ+KchSDmiJDlip/ojrflj5bns7vliJfor77nqIvliJfooag6JywgZm9ybWF0dGVkU2VyaWVzKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBBUEnov5Tlm57mlbDmja7kuK3msqHmnIlsaXN05a2X5q615oiWbGlzdOS4jeaYr+aVsOe7hDonLCByZXMuZGF0YSk7XG4gICAgICAgICAgc2V0Q291cnNlU2VyaWVzKFtdKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS53YXJuKCfimqDvuI8gQVBJ6L+U5Zue5pWw5o2u5qC85byP5byC5bi4OicsIHtcbiAgICAgICAgICBjb2RlOiByZXMuY29kZSxcbiAgICAgICAgICBtZXNzYWdlOiByZXMubWVzc2FnZSxcbiAgICAgICAgICBkYXRhOiByZXMuZGF0YVxuICAgICAgICB9KTtcbiAgICAgICAgc2V0Q291cnNlU2VyaWVzKFtdKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOiOt+WPluivvueoi+ezu+WIl+Wksei0pTonLCBlcnJvcik7XG4gICAgICBzZXRDb3Vyc2VTZXJpZXMoW10pO1xuICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfojrflj5bns7vliJfor77nqIvliJfooajlpLHotKXvvIzor7fph43or5UnKTtcbiAgICB9XG4gIH07XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmZXRjaENvdXJzZUxpc3QoKTtcblxuICAgIGZldGNoQ291cnNlVGFncygpO1xuICAgIGZldGNoQ291cnNlU2VyaWVzKCk7XG4gIH0sIFtdKTtcblxuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICA8Q2FyZFxuICAgICAgICB0aXRsZT1cIuivvueoi+euoeeQhlwiXG4gICAgICAgIGV4dHJhPXs8QnV0dG9uIHR5cGU9XCJwcmltYXJ5XCIgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgIGZldGNoQ291cnNlTGlzdCgpO1xuICAgICAgICAgIHNldElzQ291cnNlTW9kYWxWaXNpYmxlKHRydWUpO1xuICAgICAgICB9fT7mn6XnnIvlhajpg6g8L0J1dHRvbj59XG4gICAgICAgIGNsYXNzTmFtZT1cInNoYWRvdy1zbVwiXG4gICAgICA+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgPEJ1dHRvbiBibG9jayBvbkNsaWNrPXsoKSA9PiBzZXRJc0FkZENvdXJzZU1vZGFsVmlzaWJsZSh0cnVlKX0+XG4gICAgICAgICAgICDmt7vliqDor77nqItcbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8QnV0dG9uIGJsb2NrIG9uQ2xpY2s9eygpID0+IHNldElzQWRkU2VyaWVzTW9kYWxWaXNpYmxlKHRydWUpfT5cbiAgICAgICAgICAgIOa3u+WKoOezu+WIl+ivvueoi1xuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDxCdXR0b24gYmxvY2sgb25DbGljaz17KCkgPT4gc2V0SXNBZGRUYWdNb2RhbFZpc2libGUodHJ1ZSl9IHR5cGU9XCJkYXNoZWRcIj5cbiAgICAgICAgICAgIOa3u+WKoOivvueoi+agh+etvlxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDxCdXR0b24gYmxvY2sgb25DbGljaz17b3BlblB1Ymxpc2hDb3Vyc2VNb2RhbH0gc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiAnd2hpdGUnLCBib3JkZXJDb2xvcjogJyNkOWQ5ZDknLCBjb2xvcjogJyMwMDAwMDBkOScgfX0+XG4gICAgICAgICAgICDlj5HluIPor77nqItcbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8QnV0dG9uIGJsb2NrIG9uQ2xpY2s9eygpID0+IHNldElzUHVibGlzaFNlcmllc01vZGFsVmlzaWJsZSh0cnVlKX0gc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiAnd2hpdGUnLCBib3JkZXJDb2xvcjogJyNkOWQ5ZDknLCBjb2xvcjogJyMwMDAwMDBkOScgfX0+XG4gICAgICAgICAgICDlj5HluIPns7vliJfor77nqItcbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L0NhcmQ+XG5cbiAgICAgIHsvKiDor77nqIvnrqHnkIbkuLvmqKHmgIHmoYYgKi99XG4gICAgICA8TW9kYWxcbiAgICAgICAgdGl0bGU9XCLor77nqIvnrqHnkIZcIlxuICAgICAgICBvcGVuPXtpc0NvdXJzZU1vZGFsVmlzaWJsZX1cbiAgICAgICAgb25DYW5jZWw9eygpID0+IHNldElzQ291cnNlTW9kYWxWaXNpYmxlKGZhbHNlKX1cbiAgICAgICAgZm9vdGVyPXtudWxsfVxuICAgICAgICB3aWR0aD17MTAwMH1cbiAgICAgID5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgbWItM1wiPlxuICAgICAgICAgICAgPFNlYXJjaFxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuaQnOe0ouezu+WIl+ivvueoi+WQjeensFwiXG4gICAgICAgICAgICAgIGFsbG93Q2xlYXJcbiAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IDMwMCB9fVxuICAgICAgICAgICAgICBvblNlYXJjaD17c2V0U2VhcmNoS2V5d29yZH1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWFyY2hLZXl3b3JkKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTJcIj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHR5cGU9XCJwcmltYXJ5XCJcbiAgICAgICAgICAgICAgICBpY29uPXs8UGx1c091dGxpbmVkIC8+fVxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzQWRkQ291cnNlTW9kYWxWaXNpYmxlKHRydWUpfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg5re75Yqg6K++56iLXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdHlwZT1cImRlZmF1bHRcIlxuICAgICAgICAgICAgICAgIGljb249ezxQbHVzT3V0bGluZWQgLz59XG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNBZGRTZXJpZXNNb2RhbFZpc2libGUodHJ1ZSl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDmt7vliqDns7vliJfor77nqItcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiDnu5/orqHkv6Hmga/lkozlv6vmjbfmk43kvZwgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgYmctZ3JheS01MCBwLTMgcm91bmRlZFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC00IHRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICA8c3Bhbj7ns7vliJfor77nqIvmgLvmlbA6IDxzdHJvbmcgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMFwiPntzZXJpZXNMaXN0Lmxlbmd0aH08L3N0cm9uZz48L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuPuW3suWxleW8gOezu+WIlzogPHN0cm9uZyBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTYwMFwiPntleHBhbmRlZFNlcmllcy5zaXplfTwvc3Ryb25nPjwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4+5bey5Yqg6L295a2Q6K++56iLOiA8c3Ryb25nIGNsYXNzTmFtZT1cInRleHQtb3JhbmdlLTYwMFwiPlxuICAgICAgICAgICAgICAgIHtBcnJheS5mcm9tKHNlcmllc0NvdXJzZXNNYXAudmFsdWVzKCkpLnJlZHVjZSgodG90YWwsIGNvdXJzZXMpID0+IHRvdGFsICsgY291cnNlcy5sZW5ndGgsIDApfVxuICAgICAgICAgICAgICA8L3N0cm9uZz48L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yXCI+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICBzaXplPVwic21hbGxcIlxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtleHBhbmRBbGxTZXJpZXN9XG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3Nlcmllc0xvYWRpbmd9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMCBob3Zlcjp0ZXh0LWJsdWUtODAwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIOWxleW8gOaJgOaciVxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHNpemU9XCJzbWFsbFwiXG4gICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2NvbGxhcHNlQWxsU2VyaWVzfVxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtzZXJpZXNMb2FkaW5nfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC1ncmF5LTgwMFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDmlLbotbfmiYDmnIlcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPFRhYmxlXG4gICAgICAgICAgY29sdW1ucz17Y29sdW1uc31cbiAgICAgICAgICBkYXRhU291cmNlPXtwcmVwYXJlVGFibGVEYXRhKCl9XG4gICAgICAgICAgcm93S2V5PVwia2V5XCJcbiAgICAgICAgICBsb2FkaW5nPXtzZXJpZXNMb2FkaW5nfVxuICAgICAgICAgIHBhZ2luYXRpb249e3tcbiAgICAgICAgICAgIHBhZ2VTaXplOiAyMCxcbiAgICAgICAgICAgIHNob3dTaXplQ2hhbmdlcjogZmFsc2UsXG4gICAgICAgICAgICBzaG93VG90YWw6ICh0b3RhbCkgPT4gYOWFsSAke3RvdGFsfSDmnaHorrDlvZVgLFxuICAgICAgICAgIH19XG4gICAgICAgICAgc2l6ZT1cInNtYWxsXCJcbiAgICAgICAgLz5cbiAgICAgIDwvTW9kYWw+XG5cbiAgICAgIHsvKiDmt7vliqDor77nqIvmqKHmgIHmoYYgKi99XG4gICAgICA8TW9kYWxcbiAgICAgICAgdGl0bGU9XCLmt7vliqDor77nqItcIlxuICAgICAgICBvcGVuPXtpc0FkZENvdXJzZU1vZGFsVmlzaWJsZX1cbiAgICAgICAgb25DYW5jZWw9eygpID0+IHtcbiAgICAgICAgICBzZXRJc0FkZENvdXJzZU1vZGFsVmlzaWJsZShmYWxzZSk7XG4gICAgICAgICAgYWRkQ291cnNlRm9ybS5yZXNldEZpZWxkcygpO1xuICAgICAgICAgIHNldENvdXJzZUNvdmVySW1hZ2VVcmwoJycpO1xuICAgICAgICAgIHNldEFkZGl0aW9uYWxGaWxlcyhbXSk7XG4gICAgICAgICAgc2V0Q291cnNlVmlkZW9VcmwoJycpO1xuICAgICAgICAgIHNldENvdXJzZVZpZGVvTmFtZSgnJyk7XG4gICAgICAgICAgc2V0Q291cnNlRG9jdW1lbnRVcmwoJycpO1xuICAgICAgICAgIHNldENvdXJzZURvY3VtZW50TmFtZSgnJyk7XG4gICAgICAgICAgc2V0Q291cnNlQXVkaW9VcmwoJycpO1xuICAgICAgICAgIHNldENvdXJzZUF1ZGlvTmFtZSgnJyk7XG4gICAgICAgICAgc2V0VmlkZW9EdXJhdGlvbigwKTtcbiAgICAgICAgfX1cbiAgICAgICAgb25Paz17KCkgPT4gYWRkQ291cnNlRm9ybS5zdWJtaXQoKX1cbiAgICAgICAgb2tUZXh0PVwi56Gu5a6aXCJcbiAgICAgICAgY2FuY2VsVGV4dD1cIuWPlua2iFwiXG4gICAgICA+XG4gICAgICAgIDxGb3JtXG4gICAgICAgICAgZm9ybT17YWRkQ291cnNlRm9ybX1cbiAgICAgICAgICBsYXlvdXQ9XCJ2ZXJ0aWNhbFwiXG4gICAgICAgICAgb25GaW5pc2g9e2hhbmRsZUFkZENvdXJzZX1cbiAgICAgICAgPlxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJzZXJpZXNJZFwiXG4gICAgICAgICAgICBsYWJlbD1cIuaJgOWxnuezu+WIl+ivvueoi1wiXG4gICAgICAgICAgICBydWxlcz17W3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nmiYDlsZ7ns7vliJfor77nqIsnIH1dfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTZWxlY3RcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fpgInmi6nns7vliJfor77nqItcIlxuICAgICAgICAgICAgICBzaG93U2VhcmNoXG4gICAgICAgICAgICAgIG9wdGlvbkZpbHRlclByb3A9XCJjaGlsZHJlblwiXG4gICAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiAnMTAwJScgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2NvdXJzZVNlcmllcy5tYXAoc2VyaWVzID0+IChcbiAgICAgICAgICAgICAgICA8T3B0aW9uIGtleT17c2VyaWVzLmlkfSB2YWx1ZT17c2VyaWVzLmlkfSB0aXRsZT17YCR7c2VyaWVzLnRpdGxlfSAtICR7c2VyaWVzLmRlc2NyaXB0aW9ufWB9PlxuICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICBvdmVyZmxvdzogJ2hpZGRlbicsXG4gICAgICAgICAgICAgICAgICAgIHRleHRPdmVyZmxvdzogJ2VsbGlwc2lzJyxcbiAgICAgICAgICAgICAgICAgICAgd2hpdGVTcGFjZTogJ25vd3JhcCcsXG4gICAgICAgICAgICAgICAgICAgIG1heFdpZHRoOiAnMTAwJSdcbiAgICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT17eyBmb250V2VpZ2h0OiA1MDAgfX0+e3Nlcmllcy50aXRsZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPXt7IGZvbnRTaXplOiAnMTJweCcsIGNvbG9yOiAnIzY2NicsIG1hcmdpbkxlZnQ6ICc4cHgnIH19PlxuICAgICAgICAgICAgICAgICAgICAgICh7c2VyaWVzLmNhdGVnb3J5fSlcbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9PcHRpb24+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9TZWxlY3Q+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwidGl0bGVcIlxuICAgICAgICAgICAgbGFiZWw9XCLor77nqIvlkI3np7BcIlxuICAgICAgICAgICAgcnVsZXM9e1t7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl6K++56iL5ZCN56ewJyB9XX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8SW5wdXQgcGxhY2Vob2xkZXI9XCLor7fovpPlhaXor77nqIvlkI3np7BcIiAvPlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbmFtZT1cImRlc2NyaXB0aW9uXCJcbiAgICAgICAgICAgIGxhYmVsPVwi6K++56iL5o+P6L+wXCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeivvueoi+aPj+i/sCcgfV19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPElucHV0LlRleHRBcmVhXG4gICAgICAgICAgICAgIHJvd3M9ezR9XG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36K+m57uG5o+P6L+w6K++56iL5YaF5a6544CB55uu5qCH5ZKM54m56ImyLi4uXCJcbiAgICAgICAgICAgICAgc2hvd0NvdW50XG4gICAgICAgICAgICAgIG1heExlbmd0aD17NTAwfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIGxhYmVsPVwi6K++56iL5bCB6Z2iXCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+S4iuS8oOivvueoi+WwgemdoicgfV19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFVwbG9hZC5EcmFnZ2VyXG4gICAgICAgICAgICAgIG5hbWU9XCJjb3Vyc2VDb3ZlclwiXG4gICAgICAgICAgICAgIGN1c3RvbVJlcXVlc3Q9e2hhbmRsZUNvdXJzZUNvdmVyVXBsb2FkfVxuICAgICAgICAgICAgICBvblJlbW92ZT17aGFuZGxlQ291cnNlQ292ZXJSZW1vdmV9XG4gICAgICAgICAgICAgIGFjY2VwdD1cImltYWdlLypcIlxuICAgICAgICAgICAgICBtYXhDb3VudD17MX1cbiAgICAgICAgICAgICAgbGlzdFR5cGU9XCJwaWN0dXJlXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2NvdXJzZUNvdmVySW1hZ2VVcmwgPyAoXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxpbWcgc3JjPXtjb3Vyc2VDb3ZlckltYWdlVXJsfSBhbHQ9XCLor77nqIvlsIHpnaLpooTop4hcIiBzdHlsZT17eyB3aWR0aDogJzEwMCUnLCBtYXhIZWlnaHQ6ICcyMDBweCcsIG9iamVjdEZpdDogJ2NvdmVyJyB9fSAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJhbnQtdXBsb2FkLWRyYWctaWNvblwiPlxuICAgICAgICAgICAgICAgICAgICA8SW5ib3hPdXRsaW5lZCAvPlxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiYW50LXVwbG9hZC10ZXh0XCI+54K55Ye75oiW5ouW5ou95paH5Lu25Yiw5q2k5Yy65Z+f5LiK5LygPC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiYW50LXVwbG9hZC1oaW50XCI+XG4gICAgICAgICAgICAgICAgICAgIOaUr+aMgeWNleS4quaWh+S7tuS4iuS8oO+8jOW7uuiuruS4iuS8oGpwZ+OAgXBuZ+agvOW8j+WbvueJh++8jOWkp+Wwj+S4jei2hei/hzJNQlxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9VcGxvYWQuRHJhZ2dlcj5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJvcmRlckluZGV4XCJcbiAgICAgICAgICAgIGxhYmVsPVwi6K++56iL5bqP5Y+3XCJcbiAgICAgICAgICAgIHJ1bGVzPXtbXG4gICAgICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXor77nqIvluo/lj7cnIH0sXG4gICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICB0eXBlOiAnbnVtYmVyJyxcbiAgICAgICAgICAgICAgICBtaW46IDAsXG4gICAgICAgICAgICAgICAgbWVzc2FnZTogJ+ivvueoi+W6j+WPt+W/hemhu+Wkp+S6juetieS6jjAnLFxuICAgICAgICAgICAgICAgIHRyYW5zZm9ybTogKHZhbHVlKSA9PiBOdW1iZXIodmFsdWUpXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIF19XG4gICAgICAgICAgICB0b29sdGlwPVwi5Zyo57O75YiX6K++56iL5Lit55qE5o6S5bqP5L2N572u77yM5pWw5a2X6LaK5bCP5o6S5bqP6LaK6Z2g5YmN77yM5LuOMOW8gOWni1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPElucHV0IHR5cGU9XCJudW1iZXJcIiBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpeivvueoi+WcqOezu+WIl+S4reeahOW6j+WPt++8iOS7jjDlvIDlp4vvvIlcIiBtaW49ezB9IC8+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cblxuXG4gICAgICAgICAgey8qIOinhumikeS4iuS8oCAqL31cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBsYWJlbD1cIuivvueoi+inhumikVwiXG4gICAgICAgICAgICB0b29sdGlwPVwi5LiK5Lyg6K++56iL6KeG6aKR5paH5Lu277yM57O757uf5bCG6Ieq5Yqo6K+G5Yir5pe26ZW/562J5L+h5oGvXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8VXBsb2FkLkRyYWdnZXJcbiAgICAgICAgICAgICAgbmFtZT1cImNvdXJzZVZpZGVvXCJcbiAgICAgICAgICAgICAgY3VzdG9tUmVxdWVzdD17aGFuZGxlVmlkZW9VcGxvYWR9XG4gICAgICAgICAgICAgIG9uUmVtb3ZlPXtoYW5kbGVWaWRlb1JlbW92ZX1cbiAgICAgICAgICAgICAgYWNjZXB0PVwidmlkZW8vKlwiXG4gICAgICAgICAgICAgIG1heENvdW50PXsxfVxuICAgICAgICAgICAgICBsaXN0VHlwZT1cInBpY3R1cmVcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7Y291cnNlVmlkZW9VcmwgPyAoXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDx2aWRlb1xuICAgICAgICAgICAgICAgICAgICBzcmM9e2NvdXJzZVZpZGVvVXJsfVxuICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogJzEwMCUnLCBtYXhIZWlnaHQ6ICcyMDBweCcgfX1cbiAgICAgICAgICAgICAgICAgICAgY29udHJvbHNcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8cCBzdHlsZT17eyBtYXJnaW5Ub3A6IDgsIGNvbG9yOiAnIzY2NicgfX0+XG4gICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VWaWRlb05hbWUgfHwgJ+ivvueoi+inhumikSd9XG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImFudC11cGxvYWQtZHJhZy1pY29uXCI+XG4gICAgICAgICAgICAgICAgICAgIDxJbmJveE91dGxpbmVkIC8+XG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJhbnQtdXBsb2FkLXRleHRcIj7ngrnlh7vmiJbmi5bmi73op4bpopHmlofku7bliLDmraTljLrln5/kuIrkvKA8L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJhbnQtdXBsb2FkLWhpbnRcIj5cbiAgICAgICAgICAgICAgICAgICAg5pSv5oyBTVA044CBQVZJ44CBTU9W562J5qC85byP77yM5aSn5bCP5LiN6LaF6L+HMTAwTUJcbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvVXBsb2FkLkRyYWdnZXI+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICB7Lyog5paH5qGj5LiK5LygICovfVxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIGxhYmVsPVwi6K++56iL5paH5qGjXCJcbiAgICAgICAgICAgIHRvb2x0aXA9XCLkuIrkvKDor77nqIvnm7jlhbPmlofmoaPvvIzlpoJQUFTjgIFQREbnrYlcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxVcGxvYWQuRHJhZ2dlclxuICAgICAgICAgICAgICBuYW1lPVwiY291cnNlRG9jdW1lbnRcIlxuICAgICAgICAgICAgICBjdXN0b21SZXF1ZXN0PXtoYW5kbGVEb2N1bWVudFVwbG9hZH1cbiAgICAgICAgICAgICAgb25SZW1vdmU9e2hhbmRsZURvY3VtZW50UmVtb3ZlfVxuICAgICAgICAgICAgICBhY2NlcHQ9XCIucGRmLC5kb2MsLmRvY3gsLnBwdCwucHB0eFwiXG4gICAgICAgICAgICAgIG1heENvdW50PXsxfVxuICAgICAgICAgICAgICBsaXN0VHlwZT1cInBpY3R1cmVcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7Y291cnNlRG9jdW1lbnRVcmwgPyAoXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgcGFkZGluZzogJzIwcHgnLCB0ZXh0QWxpZ246ICdjZW50ZXInIH19PlxuICAgICAgICAgICAgICAgICAgICA8SW5ib3hPdXRsaW5lZCBzdHlsZT17eyBmb250U2l6ZTogJzQ4cHgnLCBjb2xvcjogJyMxODkwZmYnIH19IC8+XG4gICAgICAgICAgICAgICAgICAgIDxwIHN0eWxlPXt7IG1hcmdpblRvcDogOCwgY29sb3I6ICcjNjY2JyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICB7Y291cnNlRG9jdW1lbnROYW1lIHx8ICfor77nqIvmlofmoaMnfVxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiYW50LXVwbG9hZC1kcmFnLWljb25cIj5cbiAgICAgICAgICAgICAgICAgICAgPEluYm94T3V0bGluZWQgLz5cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImFudC11cGxvYWQtdGV4dFwiPueCueWHu+aIluaLluaLveaWh+aho+aWh+S7tuWIsOatpOWMuuWfn+S4iuS8oDwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImFudC11cGxvYWQtaGludFwiPlxuICAgICAgICAgICAgICAgICAgICDmlK/mjIFQREbjgIFXb3Jk44CBUFBU5qC85byP77yM5aSn5bCP5LiN6LaF6L+HNTBNQlxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9VcGxvYWQuRHJhZ2dlcj5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIHsvKiDpn7PpopHkuIrkvKAgKi99XG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbGFiZWw9XCLor77nqIvpn7PpopFcIlxuICAgICAgICAgICAgdG9vbHRpcD1cIuS4iuS8oOivvueoi+mfs+mikeaWh+S7tlwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFVwbG9hZC5EcmFnZ2VyXG4gICAgICAgICAgICAgIG5hbWU9XCJjb3Vyc2VBdWRpb1wiXG4gICAgICAgICAgICAgIGN1c3RvbVJlcXVlc3Q9e2hhbmRsZUF1ZGlvVXBsb2FkfVxuICAgICAgICAgICAgICBvblJlbW92ZT17aGFuZGxlQXVkaW9SZW1vdmV9XG4gICAgICAgICAgICAgIGFjY2VwdD1cImF1ZGlvLypcIlxuICAgICAgICAgICAgICBtYXhDb3VudD17MX1cbiAgICAgICAgICAgICAgbGlzdFR5cGU9XCJwaWN0dXJlXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2NvdXJzZUF1ZGlvVXJsID8gKFxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8YXVkaW9cbiAgICAgICAgICAgICAgICAgICAgc3JjPXtjb3Vyc2VBdWRpb1VybH1cbiAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6ICcxMDAlJyB9fVxuICAgICAgICAgICAgICAgICAgICBjb250cm9sc1xuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDxwIHN0eWxlPXt7IG1hcmdpblRvcDogOCwgY29sb3I6ICcjNjY2JyB9fT5cbiAgICAgICAgICAgICAgICAgICAge2NvdXJzZUF1ZGlvTmFtZSB8fCAn6K++56iL6Z+z6aKRJ31cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiYW50LXVwbG9hZC1kcmFnLWljb25cIj5cbiAgICAgICAgICAgICAgICAgICAgPEluYm94T3V0bGluZWQgLz5cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImFudC11cGxvYWQtdGV4dFwiPueCueWHu+aIluaLluaLvemfs+mikeaWh+S7tuWIsOatpOWMuuWfn+S4iuS8oDwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImFudC11cGxvYWQtaGludFwiPlxuICAgICAgICAgICAgICAgICAgICDmlK/mjIFNUDPjgIFXQVbjgIFBQUPnrYnmoLzlvI/vvIzlpKflsI/kuI3otoXov4c1ME1CXG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L1VwbG9hZC5EcmFnZ2VyPlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbmFtZT1cInRlYWNoaW5nT2JqZWN0aXZlc1wiXG4gICAgICAgICAgICBsYWJlbD1cIuaVmeWtpuebruagh1wiXG4gICAgICAgICAgICB0b29sdGlwPVwi5a2m5ZGY5a6M5oiQ5pys6K++56iL5ZCO5bqU6K+l6L6+5Yiw55qE5a2m5Lmg55uu5qCHXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8U2VsZWN0XG4gICAgICAgICAgICAgIG1vZGU9XCJ0YWdzXCJcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLnkIbop6NOb2RlLmpz55qE5Z+65pys5qaC5b+15ZKM54m554K577yM5o6M5o+hTm9kZS5qc+eahOWuieijheWSjOeOr+Wig+mFjee9rlwiXG4gICAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiAnMTAwJScgfX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBsYWJlbD1cIumZhOS7tui1hOa6kFwiXG4gICAgICAgICAgICB0b29sdGlwPVwi5LiK5Lyg6K++56iL55u45YWz55qE6ZmE5Lu26LWE5rqQ77yM5aaCUFBU44CB5paH5qGj44CB5Luj56CB562JXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8VXBsb2FkXG4gICAgICAgICAgICAgIG5hbWU9XCJhZGRpdGlvbmFsUmVzb3VyY2VzXCJcbiAgICAgICAgICAgICAgY3VzdG9tUmVxdWVzdD17aGFuZGxlQWRkaXRpb25hbFJlc291cmNlVXBsb2FkfVxuICAgICAgICAgICAgICBvblJlbW92ZT17aGFuZGxlQWRkaXRpb25hbFJlc291cmNlUmVtb3ZlfVxuICAgICAgICAgICAgICBtdWx0aXBsZVxuICAgICAgICAgICAgICBhY2NlcHQ9XCIucGRmLC5kb2MsLmRvY3gsLnBwdCwucHB0eCwueGxzLC54bHN4LC56aXAsLnJhciwudHh0XCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPEJ1dHRvbiBpY29uPXs8VXBsb2FkT3V0bGluZWQgLz59PuS4iuS8oOmZhOS7tui1hOa6kDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9VcGxvYWQ+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGZvbnRTaXplOiAnMTJweCcsIGNvbG9yOiAnIzY2NicsIG1hcmdpblRvcDogNCB9fT5cbiAgICAgICAgICAgICAg5pSv5oyB5LiK5LygUERG44CBT2ZmaWNl5paH5qGj44CB5Y6L57yp5YyF562J5qC85byP5paH5Lu277yM5Y2V5Liq5paH5Lu25LiN6LaF6L+HMTBNQlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG4gICAgICAgIDwvRm9ybT5cbiAgICAgIDwvTW9kYWw+XG5cbiAgICAgIHsvKiDnvJbovpHor77nqIvmqKHmgIHmoYYgKi99XG4gICAgICA8TW9kYWxcbiAgICAgICAgdGl0bGU9XCLnvJbovpHor77nqItcIlxuICAgICAgICBvcGVuPXtpc0VkaXRDb3Vyc2VNb2RhbFZpc2libGV9XG4gICAgICAgIG9uQ2FuY2VsPXsoKSA9PiB7XG4gICAgICAgICAgc2V0SXNFZGl0Q291cnNlTW9kYWxWaXNpYmxlKGZhbHNlKTtcbiAgICAgICAgICBzZXRFZGl0aW5nQ291cnNlKG51bGwpO1xuICAgICAgICAgIGVkaXRDb3Vyc2VGb3JtLnJlc2V0RmllbGRzKCk7XG4gICAgICAgIH19XG4gICAgICAgIG9uT2s9eygpID0+IGVkaXRDb3Vyc2VGb3JtLnN1Ym1pdCgpfVxuICAgICAgICBva1RleHQ9XCLnoa7lrppcIlxuICAgICAgICBjYW5jZWxUZXh0PVwi5Y+W5raIXCJcbiAgICAgID5cbiAgICAgICAgPEZvcm1cbiAgICAgICAgICBmb3JtPXtlZGl0Q291cnNlRm9ybX1cbiAgICAgICAgICBsYXlvdXQ9XCJ2ZXJ0aWNhbFwiXG4gICAgICAgICAgb25GaW5pc2g9e2hhbmRsZUVkaXRDb3Vyc2V9XG4gICAgICAgID5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwibmFtZVwiXG4gICAgICAgICAgICBsYWJlbD1cIuivvueoi+WQjeensFwiXG4gICAgICAgICAgICBydWxlcz17W3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXor77nqIvlkI3np7AnIH1dfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxJbnB1dCBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpeivvueoi+WQjeensFwiIC8+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwiZGVzY3JpcHRpb25cIlxuICAgICAgICAgICAgbGFiZWw9XCLor77nqIvmj4/ov7BcIlxuICAgICAgICAgICAgcnVsZXM9e1t7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl6K++56iL5o+P6L+wJyB9XX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8SW5wdXQuVGV4dEFyZWEgcm93cz17M30gcGxhY2Vob2xkZXI9XCLor7fovpPlhaXor77nqIvmj4/ov7BcIiAvPlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbmFtZT1cImNhdGVnb3J5XCJcbiAgICAgICAgICAgIGxhYmVsPVwi6K++56iL5YiG57G7XCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqeivvueoi+WIhuexuycgfV19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFNlbGVjdCBwbGFjZWhvbGRlcj1cIuivt+mAieaLqeivvueoi+WIhuexu1wiPlxuICAgICAgICAgICAgICA8T3B0aW9uIHZhbHVlPVwi57yW56iL5Z+656GAXCI+57yW56iL5Z+656GAPC9PcHRpb24+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9XCLnvJbnqIvov5vpmLZcIj7nvJbnqIvov5vpmLY8L09wdGlvbj5cbiAgICAgICAgICAgICAgPE9wdGlvbiB2YWx1ZT1cIueul+azleaAnee7tFwiPueul+azleaAnee7tDwvT3B0aW9uPlxuICAgICAgICAgICAgICA8T3B0aW9uIHZhbHVlPVwi6aG555uu5a6e5oiYXCI+6aG555uu5a6e5oiYPC9PcHRpb24+XG4gICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJzdGF0dXNcIlxuICAgICAgICAgICAgbGFiZWw9XCLor77nqIvnirbmgIFcIlxuICAgICAgICAgICAgcnVsZXM9e1t7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup6K++56iL54q25oCBJyB9XX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8U2VsZWN0PlxuICAgICAgICAgICAgICA8T3B0aW9uIHZhbHVlPVwiYWN0aXZlXCI+5ZCv55SoPC9PcHRpb24+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9XCJpbmFjdGl2ZVwiPuemgeeUqDwvT3B0aW9uPlxuICAgICAgICAgICAgPC9TZWxlY3Q+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG4gICAgICAgIDwvRm9ybT5cbiAgICAgIDwvTW9kYWw+XG5cbiAgICAgIHsvKiDmt7vliqDns7vliJfor77nqIvmqKHmgIHmoYYgKi99XG4gICAgICA8TW9kYWxcbiAgICAgICAgdGl0bGU9XCLliJvlu7rns7vliJfor77nqItcIlxuICAgICAgICBvcGVuPXtpc0FkZFNlcmllc01vZGFsVmlzaWJsZX1cbiAgICAgICAgb25DYW5jZWw9eygpID0+IHtcbiAgICAgICAgICBzZXRJc0FkZFNlcmllc01vZGFsVmlzaWJsZShmYWxzZSk7XG4gICAgICAgICAgYWRkU2VyaWVzRm9ybS5yZXNldEZpZWxkcygpO1xuICAgICAgICAgIHNldENvdmVySW1hZ2VVcmwoJycpO1xuICAgICAgICB9fVxuICAgICAgICBvbk9rPXsoKSA9PiBhZGRTZXJpZXNGb3JtLnN1Ym1pdCgpfVxuICAgICAgICBva1RleHQ9XCLliJvlu7rns7vliJfor77nqItcIlxuICAgICAgICBjYW5jZWxUZXh0PVwi5Y+W5raIXCJcbiAgICAgICAgd2lkdGg9ezgwMH1cbiAgICAgID5cbiAgICAgICAgPEZvcm1cbiAgICAgICAgICBmb3JtPXthZGRTZXJpZXNGb3JtfVxuICAgICAgICAgIGxheW91dD1cInZlcnRpY2FsXCJcbiAgICAgICAgICBvbkZpbmlzaD17aGFuZGxlQWRkU2VyaWVzfVxuICAgICAgICA+XG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbmFtZT1cInRpdGxlXCJcbiAgICAgICAgICAgIGxhYmVsPVwi57O75YiX6K++56iL5ZCN56ewXCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeezu+WIl+ivvueoi+WQjeensCcgfV19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPElucHV0IHBsYWNlaG9sZGVyPVwi5L6L5aaC77yaUmVhY3TlhajmoIjlvIDlj5Hlrp7miJhcIiAvPlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbmFtZT1cImRlc2NyaXB0aW9uXCJcbiAgICAgICAgICAgIGxhYmVsPVwi6K++56iL5LuL57uNXCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeivvueoi+S7i+e7jScgfV19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPElucHV0LlRleHRBcmVhXG4gICAgICAgICAgICAgIHJvd3M9ezR9XG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36K+m57uG5o+P6L+w57O75YiX6K++56iL55qE5YaF5a6544CB55uu5qCH5ZKM54m56ImyLi4uXCJcbiAgICAgICAgICAgICAgc2hvd0NvdW50XG4gICAgICAgICAgICAgIG1heExlbmd0aD17NTAwfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIGxhYmVsPVwi5bCB6Z2i5Zu+54mHXCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+S4iuS8oOWwgemdouWbvueJhycgfV19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFVwbG9hZC5EcmFnZ2VyXG4gICAgICAgICAgICAgIG5hbWU9XCJjb3ZlckltYWdlXCJcbiAgICAgICAgICAgICAgY3VzdG9tUmVxdWVzdD17aGFuZGxlSW1hZ2VVcGxvYWR9XG4gICAgICAgICAgICAgIG9uUmVtb3ZlPXtoYW5kbGVJbWFnZVJlbW92ZX1cbiAgICAgICAgICAgICAgYWNjZXB0PVwiaW1hZ2UvKlwiXG4gICAgICAgICAgICAgIG1heENvdW50PXsxfVxuICAgICAgICAgICAgICBsaXN0VHlwZT1cInBpY3R1cmVcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7Y292ZXJJbWFnZVVybCA/IChcbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGltZyBzcmM9e2NvdmVySW1hZ2VVcmx9IGFsdD1cIuWwgemdoumihOiniFwiIHN0eWxlPXt7IHdpZHRoOiAnMTAwJScsIG1heEhlaWdodDogJzIwMHB4Jywgb2JqZWN0Rml0OiAnY292ZXInIH19IC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImFudC11cGxvYWQtZHJhZy1pY29uXCI+XG4gICAgICAgICAgICAgICAgICAgIDxJbmJveE91dGxpbmVkIC8+XG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJhbnQtdXBsb2FkLXRleHRcIj7ngrnlh7vmiJbmi5bmi73mlofku7bliLDmraTljLrln5/kuIrkvKA8L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJhbnQtdXBsb2FkLWhpbnRcIj5cbiAgICAgICAgICAgICAgICAgICAg5pSv5oyB5Y2V5Liq5paH5Lu25LiK5Lyg77yM5bu66K6u5LiK5LyganBn44CBcG5n5qC85byP5Zu+54mH77yM5aSn5bCP5LiN6LaF6L+HMk1CXG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L1VwbG9hZC5EcmFnZ2VyPlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG5cblxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJjYXRlZ29yeVwiXG4gICAgICAgICAgICBsYWJlbD1cIuaYr+WQpuS4uuWumOaWueezu+WIl+ivvueoi1wiXG4gICAgICAgICAgICBydWxlcz17W3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nmmK/lkKbkuLrlrpjmlrnns7vliJfor77nqIsnIH1dfVxuICAgICAgICAgICAgaW5pdGlhbFZhbHVlPXswfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTZWxlY3QgcGxhY2Vob2xkZXI9XCLor7fpgInmi6lcIj5cbiAgICAgICAgICAgICAgPE9wdGlvbiB2YWx1ZT17MX0+5piv77yI5a6Y5pa577yJPC9PcHRpb24+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9ezB9PuWQpu+8iOekvuWMuu+8iTwvT3B0aW9uPlxuICAgICAgICAgICAgPC9TZWxlY3Q+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwicHJvamVjdE1lbWJlcnNcIlxuICAgICAgICAgICAgbGFiZWw9XCLor77nqIvmiJDlkZhcIlxuICAgICAgICAgICAgcnVsZXM9e1t7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl6K++56iL5oiQ5ZGYJyB9XX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fovpPlhaXor77nqIvmiJDlkZjvvIzlpoLvvJrnjovogIHluIjjgIHmnY7liqnmlZnjgIHlvKDlkIzlraZcIlxuICAgICAgICAgICAgICBzaG93Q291bnRcbiAgICAgICAgICAgICAgbWF4TGVuZ3RoPXsyMDB9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbmFtZT1cInRhZ0lkc1wiXG4gICAgICAgICAgICBsYWJlbD1cIuagh+etvumAieaLqVwiXG4gICAgICAgICAgICBydWxlcz17W3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nmoIfnrb4nIH1dfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTZWxlY3RcbiAgICAgICAgICAgICAgbW9kZT1cIm11bHRpcGxlXCJcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fpgInmi6nnm7jlhbPmoIfnrb5cIlxuICAgICAgICAgICAgICBvcHRpb25MYWJlbFByb3A9XCJsYWJlbFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtjb3Vyc2VUYWdzLm1hcCh0YWcgPT4gKFxuICAgICAgICAgICAgICAgIDxPcHRpb24ga2V5PXt0YWcuaWR9IHZhbHVlPXt0YWcuaWR9IGxhYmVsPXt0YWcubmFtZX0+XG4gICAgICAgICAgICAgICAgICA8VGFnIGNvbG9yPXt0YWcuY29sb3J9Pnt0YWcubmFtZX08L1RhZz5cbiAgICAgICAgICAgICAgICA8L09wdGlvbj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cbiAgICAgICAgPC9Gb3JtPlxuICAgICAgPC9Nb2RhbD5cblxuICAgICAgey8qIOa3u+WKoOivvueoi+agh+etvuaooeaAgeahhiAqL31cbiAgICAgIDxNb2RhbFxuICAgICAgICB0aXRsZT1cIuWIm+W7uuivvueoi+agh+etvlwiXG4gICAgICAgIG9wZW49e2lzQWRkVGFnTW9kYWxWaXNpYmxlfVxuICAgICAgICBvbkNhbmNlbD17KCkgPT4ge1xuICAgICAgICAgIHNldElzQWRkVGFnTW9kYWxWaXNpYmxlKGZhbHNlKTtcbiAgICAgICAgICBhZGRUYWdGb3JtLnJlc2V0RmllbGRzKCk7XG4gICAgICAgIH19XG4gICAgICAgIG9uT2s9eygpID0+IGFkZFRhZ0Zvcm0uc3VibWl0KCl9XG4gICAgICAgIG9rVGV4dD1cIuWIm+W7uuagh+etvlwiXG4gICAgICAgIGNhbmNlbFRleHQ9XCLlj5bmtohcIlxuICAgICAgICB3aWR0aD17NjAwfVxuICAgICAgPlxuICAgICAgICA8Rm9ybVxuICAgICAgICAgIGZvcm09e2FkZFRhZ0Zvcm19XG4gICAgICAgICAgbGF5b3V0PVwidmVydGljYWxcIlxuICAgICAgICAgIG9uRmluaXNoPXtoYW5kbGVBZGRUYWd9XG4gICAgICAgID5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwibmFtZVwiXG4gICAgICAgICAgICBsYWJlbD1cIuagh+etvuWQjeensFwiXG4gICAgICAgICAgICBydWxlcz17W1xuICAgICAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5qCH562+5ZCN56ewJyB9LFxuICAgICAgICAgICAgICB7IG1heDogMjAsIG1lc3NhZ2U6ICfmoIfnrb7lkI3np7DkuI3og73otoXov4cyMOS4quWtl+espicgfVxuICAgICAgICAgICAgXX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8SW5wdXQgcGxhY2Vob2xkZXI9XCLkvovlpoLvvJrpq5jnuqfjgIHnvJbnqIvjgIHlrp7miJhcIiAvPlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG4gICAgICAgICAgPEZvcm0uSXRlbVxuICAgICAgICAgICAgbmFtZT1cImNvbG9yXCJcbiAgICAgICAgICAgIGxhYmVsPVwi5qCH562+6aKc6ImyXCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqeagh+etvuminOiJsicgfV19XG4gICAgICAgICAgICBpbml0aWFsVmFsdWU9XCIjMDA3YmZmXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8U2VsZWN0IHBsYWNlaG9sZGVyPVwi6K+36YCJ5oup5qCH562+6aKc6ImyXCI+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9XCIjMDA3YmZmXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTQgaC00IHJvdW5kZWRcIiBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICcjMDA3YmZmJyB9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIOiTneiJsiAoIzAwN2JmZilcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9PcHRpb24+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9XCIjMjhhNzQ1XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTQgaC00IHJvdW5kZWRcIiBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICcjMjhhNzQ1JyB9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIOe7v+iJsiAoIzI4YTc0NSlcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9PcHRpb24+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9XCIjZGMzNTQ1XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTQgaC00IHJvdW5kZWRcIiBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICcjZGMzNTQ1JyB9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIOe6ouiJsiAoI2RjMzU0NSlcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9PcHRpb24+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9XCIjZmZjMTA3XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTQgaC00IHJvdW5kZWRcIiBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICcjZmZjMTA3JyB9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIOm7hOiJsiAoI2ZmYzEwNylcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9PcHRpb24+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9XCIjNmY0MmMxXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTQgaC00IHJvdW5kZWRcIiBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICcjNmY0MmMxJyB9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIOe0q+iJsiAoIzZmNDJjMSlcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9PcHRpb24+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9XCIjZmQ3ZTE0XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTQgaC00IHJvdW5kZWRcIiBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICcjZmQ3ZTE0JyB9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIOapmeiJsiAoI2ZkN2UxNClcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9PcHRpb24+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9XCIjMjBjOTk3XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTQgaC00IHJvdW5kZWRcIiBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICcjMjBjOTk3JyB9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIOmdkuiJsiAoIzIwYzk5NylcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9PcHRpb24+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9XCIjNmM3NTdkXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTQgaC00IHJvdW5kZWRcIiBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICcjNmM3NTdkJyB9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIOeBsOiJsiAoIzZjNzU3ZClcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9PcHRpb24+XG4gICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJjYXRlZ29yeVwiXG4gICAgICAgICAgICBsYWJlbD1cIuagh+etvuWIhuexu1wiXG4gICAgICAgICAgICBydWxlcz17W3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nmoIfnrb7liIbnsbsnIH1dfVxuICAgICAgICAgICAgaW5pdGlhbFZhbHVlPXsxfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTZWxlY3QgcGxhY2Vob2xkZXI9XCLor7fpgInmi6nmoIfnrb7liIbnsbtcIj5cbiAgICAgICAgICAgICAgPE9wdGlvbiB2YWx1ZT17MH0+6Zq+5bqm5qCH562+PC9PcHRpb24+XG4gICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9ezF9Puexu+Wei+agh+etvjwvT3B0aW9uPlxuICAgICAgICAgICAgICA8T3B0aW9uIHZhbHVlPXsyfT7nibnoibLmoIfnrb48L09wdGlvbj5cbiAgICAgICAgICAgICAgPE9wdGlvbiB2YWx1ZT17M30+5YW25LuW5qCH562+PC9PcHRpb24+XG4gICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJkZXNjcmlwdGlvblwiXG4gICAgICAgICAgICBsYWJlbD1cIuagh+etvuaPj+i/sFwiXG4gICAgICAgICAgICBydWxlcz17W3sgbWF4OiAxMDAsIG1lc3NhZ2U6ICfmoIfnrb7mj4/ov7DkuI3og73otoXov4cxMDDkuKrlrZfnrKYnIH1dfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxJbnB1dC5UZXh0QXJlYVxuICAgICAgICAgICAgICByb3dzPXszfVxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpeagh+etvueahOivpue7huaPj+i/sC4uLlwiXG4gICAgICAgICAgICAgIHNob3dDb3VudFxuICAgICAgICAgICAgICBtYXhMZW5ndGg9ezEwMH1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwic3RhdHVzXCJcbiAgICAgICAgICAgIGxhYmVsPVwi5qCH562+54q25oCBXCJcbiAgICAgICAgICAgIHJ1bGVzPXtbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqeagh+etvueKtuaAgScgfV19XG4gICAgICAgICAgICBpbml0aWFsVmFsdWU9ezF9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFNlbGVjdCBwbGFjZWhvbGRlcj1cIuivt+mAieaLqeagh+etvueKtuaAgVwiPlxuICAgICAgICAgICAgICA8T3B0aW9uIHZhbHVlPXsxfT7lkK/nlKg8L09wdGlvbj5cbiAgICAgICAgICAgICAgPE9wdGlvbiB2YWx1ZT17MH0+56aB55SoPC9PcHRpb24+XG4gICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cbiAgICAgICAgPC9Gb3JtPlxuICAgICAgPC9Nb2RhbD5cblxuICAgICAgey8qIOWPkeW4g+ezu+WIl+ivvueoi+aooeaAgeahhiAqL31cbiAgICAgIDxNb2RhbFxuICAgICAgICB0aXRsZT1cIuWPkeW4g+ezu+WIl+ivvueoi1wiXG4gICAgICAgIG9wZW49e2lzUHVibGlzaFNlcmllc01vZGFsVmlzaWJsZX1cbiAgICAgICAgb25DYW5jZWw9eygpID0+IHtcbiAgICAgICAgICBzZXRJc1B1Ymxpc2hTZXJpZXNNb2RhbFZpc2libGUoZmFsc2UpO1xuICAgICAgICAgIHB1Ymxpc2hTZXJpZXNGb3JtLnJlc2V0RmllbGRzKCk7XG4gICAgICAgIH19XG4gICAgICAgIG9uT2s9eygpID0+IHB1Ymxpc2hTZXJpZXNGb3JtLnN1Ym1pdCgpfVxuICAgICAgICBva1RleHQ9XCLlj5HluIPns7vliJdcIlxuICAgICAgICBjYW5jZWxUZXh0PVwi5Y+W5raIXCJcbiAgICAgICAgd2lkdGg9ezYwMH1cbiAgICAgID5cbiAgICAgICAgPEZvcm1cbiAgICAgICAgICBmb3JtPXtwdWJsaXNoU2VyaWVzRm9ybX1cbiAgICAgICAgICBsYXlvdXQ9XCJ2ZXJ0aWNhbFwiXG4gICAgICAgICAgb25GaW5pc2g9e2hhbmRsZVB1Ymxpc2hTZXJpZXN9XG4gICAgICAgID5cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwic2VyaWVzSWRcIlxuICAgICAgICAgICAgbGFiZWw9XCLpgInmi6nopoHlj5HluIPnmoTns7vliJfor77nqItcIlxuICAgICAgICAgICAgcnVsZXM9e1t7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup6KaB5Y+R5biD55qE57O75YiX6K++56iLJyB9XX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8U2VsZWN0XG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36YCJ5oup57O75YiX6K++56iLXCJcbiAgICAgICAgICAgICAgc2hvd1NlYXJjaFxuICAgICAgICAgICAgICBmaWx0ZXJPcHRpb249eyhpbnB1dCwgb3B0aW9uKSA9PlxuICAgICAgICAgICAgICAgIChvcHRpb24/LmNoaWxkcmVuIGFzIHVua25vd24gYXMgc3RyaW5nKT8udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhpbnB1dC50b0xvd2VyQ2FzZSgpKVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtjb3Vyc2VTZXJpZXMubWFwKChzZXJpZXMpID0+IChcbiAgICAgICAgICAgICAgICA8T3B0aW9uIGtleT17c2VyaWVzLmlkfSB2YWx1ZT17c2VyaWVzLmlkfT5cbiAgICAgICAgICAgICAgICAgIHtzZXJpZXMudGl0bGV9ICh7c2VyaWVzLmNhdGVnb3J5fSlcbiAgICAgICAgICAgICAgICA8L09wdGlvbj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJwdWJsaXNoTm90ZVwiXG4gICAgICAgICAgICBsYWJlbD1cIuWPkeW4g+ivtOaYjlwiXG4gICAgICAgICAgICBydWxlcz17W3sgcmVxdWlyZWQ6IGZhbHNlIH1dfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxJbnB1dC5UZXh0QXJlYVxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpeWPkeW4g+ivtOaYju+8iOWPr+mAie+8iVwiXG4gICAgICAgICAgICAgIHJvd3M9ezN9XG4gICAgICAgICAgICAgIG1heExlbmd0aD17MjAwfVxuICAgICAgICAgICAgICBzaG93Q291bnRcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNTAgcC00IHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPuWPkeW4g+ivtOaYju+8mjwvaDQ+XG4gICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIHNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICA8bGk+4oCiIOWPkeW4g+WQjuezu+WIl+ivvueoi+WwhuWcqOivvueoi+W4guWcuuS4reWFrOW8gOaYvuekujwvbGk+XG4gICAgICAgICAgICAgIDxsaT7igKIg5Y+q5pyJ5bey5a6M5oiQ55qE6K++56iL5omN5Lya6KKr5Y+R5biDPC9saT5cbiAgICAgICAgICAgICAgPGxpPuKAoiDlj5HluIPlkI7lj6/ku6Xmn6XnnIvor6bnu4bnmoTlj5HluIPnu5/orqHkv6Hmga88L2xpPlxuICAgICAgICAgICAgICA8bGk+4oCiIOWPkeW4g+eKtuaAgeWPr+S7pemaj+aXtuS/ruaUuTwvbGk+XG4gICAgICAgICAgICA8L3VsPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0Zvcm0+XG4gICAgICA8L01vZGFsPlxuXG4gICAgICB7Lyog5Y+R5biD6K++56iL5qih5oCB5qGGICovfVxuICAgICAgPE1vZGFsXG4gICAgICAgIHRpdGxlPVwi5Y+R5biD6K++56iLXCJcbiAgICAgICAgb3Blbj17aXNQdWJsaXNoQ291cnNlTW9kYWxWaXNpYmxlfVxuICAgICAgICBvbkNhbmNlbD17cmVzZXRQdWJsaXNoQ291cnNlTW9kYWx9XG4gICAgICAgIGZvb3Rlcj17bnVsbH1cbiAgICAgICAgd2lkdGg9ezcwMH1cbiAgICAgICAgZGVzdHJveU9uQ2xvc2VcbiAgICAgID5cbiAgICAgICAgPEZvcm1cbiAgICAgICAgICBmb3JtPXtwdWJsaXNoQ291cnNlRm9ybX1cbiAgICAgICAgICBsYXlvdXQ9XCJ2ZXJ0aWNhbFwiXG4gICAgICAgICAgb25GaW5pc2g9e2hhbmRsZVB1Ymxpc2hDb3Vyc2V9XG4gICAgICAgICAgY2xhc3NOYW1lPVwibXQtNFwiXG4gICAgICAgID5cbiAgICAgICAgICB7Lyog56ys5LiA5Liq5LiL5ouJ5qGG77ya6YCJ5oup57O75YiX6K++56iLICovfVxuICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgIG5hbWU9XCJzZXJpZXNJZFwiXG4gICAgICAgICAgICBsYWJlbD1cIumAieaLqeezu+WIl+ivvueoi1wiXG4gICAgICAgICAgICBydWxlcz17W3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fpgInmi6nns7vliJfor77nqIsnIH1dfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTZWxlY3RcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fpgInmi6nns7vliJfor77nqItcIlxuICAgICAgICAgICAgICBsb2FkaW5nPXtwdWJsaXNoRm9ybUxvYWRpbmd9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVQdWJsaXNoU2VyaWVzQ2hhbmdlfVxuICAgICAgICAgICAgICBzaG93U2VhcmNoXG4gICAgICAgICAgICAgIGZpbHRlck9wdGlvbj17KGlucHV0LCBvcHRpb24pID0+XG4gICAgICAgICAgICAgICAgKG9wdGlvbj8uY2hpbGRyZW4gYXMgdW5rbm93biBhcyBzdHJpbmcpPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGlucHV0LnRvTG93ZXJDYXNlKCkpXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge3B1Ymxpc2hTZXJpZXNMaXN0Rm9yTW9kYWwubWFwKHNlcmllcyA9PiAoXG4gICAgICAgICAgICAgICAgPE9wdGlvbiBrZXk9e3Nlcmllcy5pZH0gdmFsdWU9e3Nlcmllcy5pZH0+XG4gICAgICAgICAgICAgICAgICB7c2VyaWVzLnRpdGxlfSAoSUQ6IHtzZXJpZXMuaWR9KVxuICAgICAgICAgICAgICAgIDwvT3B0aW9uPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvU2VsZWN0PlxuICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG4gICAgICAgICAgey8qIOesrOS6jOS4quS4i+aLieahhu+8mumAieaLqeWtkOivvueoiyAqL31cbiAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICBuYW1lPVwiY291cnNlSWRcIlxuICAgICAgICAgICAgbGFiZWw9XCLpgInmi6nlrZDor77nqItcIlxuICAgICAgICAgICAgcnVsZXM9e1t7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup6KaB5Y+R5biD55qE5a2Q6K++56iLJyB9XX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8U2VsZWN0XG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPXtzZWxlY3RlZFNlcmllc0ZvclB1Ymxpc2ggPyBcIuivt+mAieaLqeimgeWPkeW4g+eahOWtkOivvueoi1wiIDogXCLor7flhYjpgInmi6nns7vliJfor77nqItcIn1cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFzZWxlY3RlZFNlcmllc0ZvclB1Ymxpc2h9XG4gICAgICAgICAgICAgIGxvYWRpbmc9e3B1Ymxpc2hGb3JtTG9hZGluZyAmJiAhIXNlbGVjdGVkU2VyaWVzRm9yUHVibGlzaH1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZVB1Ymxpc2hDb3Vyc2VDaGFuZ2V9XG4gICAgICAgICAgICAgIHNob3dTZWFyY2hcbiAgICAgICAgICAgICAgZmlsdGVyT3B0aW9uPXsoaW5wdXQsIG9wdGlvbikgPT5cbiAgICAgICAgICAgICAgICAob3B0aW9uPy5jaGlsZHJlbiBhcyB1bmtub3duIGFzIHN0cmluZyk/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoaW5wdXQudG9Mb3dlckNhc2UoKSlcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICBub3RGb3VuZENvbnRlbnQ9e1xuICAgICAgICAgICAgICAgIHB1Ymxpc2hGb3JtTG9hZGluZyAmJiBzZWxlY3RlZFNlcmllc0ZvclB1Ymxpc2hcbiAgICAgICAgICAgICAgICAgID8gXCLmraPlnKjliqDovb3lrZDor77nqIsuLi5cIlxuICAgICAgICAgICAgICAgICAgOiBzZWxlY3RlZFNlcmllc0ZvclB1Ymxpc2hcbiAgICAgICAgICAgICAgICAgICAgPyBcIuivpeezu+WIl+aaguaXoOWtkOivvueoi1wiXG4gICAgICAgICAgICAgICAgICAgIDogXCLor7flhYjpgInmi6nns7vliJfor77nqItcIlxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtwdWJsaXNoQ291cnNlTGlzdEZvck1vZGFsLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICAgICAgcHVibGlzaENvdXJzZUxpc3RGb3JNb2RhbC5tYXAoY291cnNlID0+IChcbiAgICAgICAgICAgICAgICAgIDxPcHRpb24ga2V5PXtjb3Vyc2UuaWR9IHZhbHVlPXtjb3Vyc2UuaWR9PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntjb3Vyc2UudGl0bGV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRhZyBjb2xvcj17Y291cnNlLnN0YXR1cyA9PT0gMSA/ICdncmVlbicgOiBjb3Vyc2Uuc3RhdHVzID09PSAwID8gJ29yYW5nZScgOiAncmVkJ30gY2xhc3NOYW1lPVwidGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Y291cnNlLnN0YXR1cyA9PT0gMSA/ICflt7Llj5HluIMnIDogY291cnNlLnN0YXR1cyA9PT0gMCA/ICfojYnnqL8nIDogJ+W3suW9kuahoyd9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L1RhZz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC14c1wiPklEOiB7Y291cnNlLmlkfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L09wdGlvbj5cbiAgICAgICAgICAgICAgICApKVxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIHNlbGVjdGVkU2VyaWVzRm9yUHVibGlzaCAmJiAhcHVibGlzaEZvcm1Mb2FkaW5nID8gKFxuICAgICAgICAgICAgICAgICAgPE9wdGlvbiBkaXNhYmxlZCB2YWx1ZT1cIm5vLWNvdXJzZXNcIj5cbiAgICAgICAgICAgICAgICAgICAg6K+l57O75YiX5pqC5peg5a2Q6K++56iLXG4gICAgICAgICAgICAgICAgICA8L09wdGlvbj5cbiAgICAgICAgICAgICAgICApIDogbnVsbFxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9TZWxlY3Q+XG4gICAgICAgICAgPC9Gb3JtLkl0ZW0+XG5cbiAgICAgICAgICB7Lyog6LCD6K+V5L+h5oGv5pi+56S6ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS01MCBwLTMgcm91bmRlZC1sZyBtYi00IHRleHQteHNcIj5cbiAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj7osIPor5Xkv6Hmga88L2g0PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTEgdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICA8cD7lt7LpgInmi6nns7vliJdJRDoge3NlbGVjdGVkU2VyaWVzRm9yUHVibGlzaCB8fCAn5pyq6YCJ5oupJ308L3A+XG4gICAgICAgICAgICAgIDxwPuW3sumAieaLqeivvueoi0lEOiB7c2VsZWN0ZWRDb3Vyc2VGb3JQdWJsaXNoIHx8ICfmnKrpgInmi6knfTwvcD5cbiAgICAgICAgICAgICAgPHA+57O75YiX5YiX6KGo5pWw6YePOiB7cHVibGlzaFNlcmllc0xpc3RGb3JNb2RhbC5sZW5ndGh9PC9wPlxuICAgICAgICAgICAgICA8cD7lrZDor77nqIvliJfooajmlbDph486IHtwdWJsaXNoQ291cnNlTGlzdEZvck1vZGFsLmxlbmd0aH08L3A+XG4gICAgICAgICAgICAgIDxwPuWKoOi9veeKtuaAgToge3B1Ymxpc2hGb3JtTG9hZGluZyA/ICfliqDovb3kuK0nIDogJ+epuumXsid9PC9wPlxuICAgICAgICAgICAgICB7cHVibGlzaENvdXJzZUxpc3RGb3JNb2RhbC5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPHA+5a2Q6K++56iL5YiX6KGoOjwvcD5cbiAgICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJtbC00IGxpc3QtZGlzY1wiPlxuICAgICAgICAgICAgICAgICAgICB7cHVibGlzaENvdXJzZUxpc3RGb3JNb2RhbC5tYXAoY291cnNlID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8bGkga2V5PXtjb3Vyc2UuaWR9PklEOiB7Y291cnNlLmlkfSwg5ZCN56ewOiB7Y291cnNlLnRpdGxlfTwvbGk+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIOmAieS4reivvueoi+S/oeaBr+aYvuekuiAqL31cbiAgICAgICAgICB7c2VsZWN0ZWRDb3Vyc2VGb3JQdWJsaXNoICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS01MCBwLTQgcm91bmRlZC1sZyBtYi00XCI+XG4gICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS05MDAgbWItMlwiPuWNs+WwhuWPkeW4g+eahOivvueoizwvaDQ+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtNzAwXCI+XG4gICAgICAgICAgICAgICAgPHA+6K++56iLSUQ6IHtzZWxlY3RlZENvdXJzZUZvclB1Ymxpc2h9PC9wPlxuICAgICAgICAgICAgICAgIDxwPuaJgOWxnuezu+WIlzoge3B1Ymxpc2hTZXJpZXNMaXN0Rm9yTW9kYWwuZmluZChzID0+IHMuaWQgPT09IHNlbGVjdGVkU2VyaWVzRm9yUHVibGlzaCk/LnRpdGxlfTwvcD5cbiAgICAgICAgICAgICAgICA8cD7or77nqIvlkI3np7A6IHtwdWJsaXNoQ291cnNlTGlzdEZvck1vZGFsLmZpbmQoYyA9PiBjLmlkID09PSBzZWxlY3RlZENvdXJzZUZvclB1Ymxpc2gpPy50aXRsZX08L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMiB0ZXh0LWJsdWUtNjAwIGZvbnQtbWVkaXVtXCI+54K55Ye7XCLlj5HluIPmraTor77nqItcIuWwhuaKiuivpeivvueoi+eKtuaAgeiuvue9ruS4uuW3suWPkeW4gzwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgcHQtNCBib3JkZXItdFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAge3NlbGVjdGVkQ291cnNlRm9yUHVibGlzaCA/IChcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTYwMFwiPuKckyDlt7LpgInmi6nor77nqIvvvIzlj6/ku6Xlj5HluIM8L3NwYW4+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPHNwYW4+6K+35YWI6YCJ5oup57O75YiX6K++56iL5ZKM5a2Q6K++56iLPC9zcGFuPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTJcIj5cbiAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtyZXNldFB1Ymxpc2hDb3Vyc2VNb2RhbH0+XG4gICAgICAgICAgICAgICAg5Y+W5raIXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdHlwZT1cInByaW1hcnlcIlxuICAgICAgICAgICAgICAgIGh0bWxUeXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgICAgICBsb2FkaW5nPXtwdWJsaXNoRm9ybUxvYWRpbmd9XG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFzZWxlY3RlZENvdXJzZUZvclB1Ymxpc2h9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtzZWxlY3RlZENvdXJzZUZvclB1Ymxpc2ggPyAnYmctZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTcwMCBib3JkZXItZ3JlZW4tNjAwJyA6ICcnfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge3B1Ymxpc2hGb3JtTG9hZGluZyA/ICflj5HluIPkuK0uLi4nIDogJ+WPkeW4g+atpOivvueoiyd9XG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvRm9ybT5cbiAgICAgIDwvTW9kYWw+XG4gICAgPC8+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBDb3Vyc2VNYW5hZ2VtZW50O1xuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiQ2FyZCIsIkJ1dHRvbiIsIlRhYmxlIiwiTW9kYWwiLCJGb3JtIiwiSW5wdXQiLCJTZWxlY3QiLCJTcGFjZSIsIlRhZyIsIlBvcGNvbmZpcm0iLCJVcGxvYWQiLCJtZXNzYWdlIiwiUGx1c091dGxpbmVkIiwiRWRpdE91dGxpbmVkIiwiRGVsZXRlT3V0bGluZWQiLCJVcGxvYWRPdXRsaW5lZCIsIkluYm94T3V0bGluZWQiLCJHZXROb3RpZmljYXRpb24iLCJjb3Vyc2VBcGkiLCJ1cGxvYWRBcGkiLCJTZWFyY2giLCJPcHRpb24iLCJDb3Vyc2VNYW5hZ2VtZW50IiwicHVibGlzaFNlcmllc0xpc3RGb3JNb2RhbCIsInB1Ymxpc2hDb3Vyc2VMaXN0Rm9yTW9kYWwiLCJjb3Vyc2VMaXN0Iiwic2V0Q291cnNlTGlzdCIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiaXNDb3Vyc2VNb2RhbFZpc2libGUiLCJzZXRJc0NvdXJzZU1vZGFsVmlzaWJsZSIsImlzQWRkQ291cnNlTW9kYWxWaXNpYmxlIiwic2V0SXNBZGRDb3Vyc2VNb2RhbFZpc2libGUiLCJpc0VkaXRDb3Vyc2VNb2RhbFZpc2libGUiLCJzZXRJc0VkaXRDb3Vyc2VNb2RhbFZpc2libGUiLCJpc0FkZFNlcmllc01vZGFsVmlzaWJsZSIsInNldElzQWRkU2VyaWVzTW9kYWxWaXNpYmxlIiwiaXNBZGRUYWdNb2RhbFZpc2libGUiLCJzZXRJc0FkZFRhZ01vZGFsVmlzaWJsZSIsImlzUHVibGlzaFNlcmllc01vZGFsVmlzaWJsZSIsInNldElzUHVibGlzaFNlcmllc01vZGFsVmlzaWJsZSIsImlzUHVibGlzaENvdXJzZU1vZGFsVmlzaWJsZSIsInNldElzUHVibGlzaENvdXJzZU1vZGFsVmlzaWJsZSIsImVkaXRpbmdDb3Vyc2UiLCJzZXRFZGl0aW5nQ291cnNlIiwic2VhcmNoS2V5d29yZCIsInNldFNlYXJjaEtleXdvcmQiLCJ0ZWFjaGVycyIsInNldFRlYWNoZXJzIiwiY291cnNlVGFncyIsInNldENvdXJzZVRhZ3MiLCJjb3ZlckltYWdlVXJsIiwic2V0Q292ZXJJbWFnZVVybCIsInNlcmllc0xpc3QiLCJzZXRTZXJpZXNMaXN0Iiwic2VyaWVzQ291cnNlc01hcCIsInNldFNlcmllc0NvdXJzZXNNYXAiLCJNYXAiLCJleHBhbmRlZFNlcmllcyIsInNldEV4cGFuZGVkU2VyaWVzIiwiU2V0Iiwic2VyaWVzTG9hZGluZyIsInNldFNlcmllc0xvYWRpbmciLCJzZWxlY3RlZFNlcmllc0ZvclB1Ymxpc2giLCJzZXRTZWxlY3RlZFNlcmllc0ZvclB1Ymxpc2giLCJ1bmRlZmluZWQiLCJzZWxlY3RlZENvdXJzZUZvclB1Ymxpc2giLCJzZXRTZWxlY3RlZENvdXJzZUZvclB1Ymxpc2giLCJwdWJsaXNoU2VyaWVzQ291cnNlcyIsInNldFB1Ymxpc2hTZXJpZXNDb3Vyc2VzIiwicHVibGlzaExvYWRpbmciLCJzZXRQdWJsaXNoTG9hZGluZyIsInB1Ymxpc2hTZXJpZXNPcHRpb25zIiwic2V0UHVibGlzaFNlcmllc09wdGlvbnMiLCJzZXRQdWJsaXNoU2VyaWVzTGlzdEZvck1vZGFsIiwic2V0UHVibGlzaENvdXJzZUxpc3RGb3JNb2RhbCIsInB1Ymxpc2hGb3JtTG9hZGluZyIsInNldFB1Ymxpc2hGb3JtTG9hZGluZyIsImNvdXJzZVNlcmllcyIsInNldENvdXJzZVNlcmllcyIsImNvdXJzZUNvdmVySW1hZ2VVcmwiLCJzZXRDb3Vyc2VDb3ZlckltYWdlVXJsIiwiYWRkaXRpb25hbEZpbGVzIiwic2V0QWRkaXRpb25hbEZpbGVzIiwiY291cnNlVmlkZW9VcmwiLCJzZXRDb3Vyc2VWaWRlb1VybCIsImNvdXJzZVZpZGVvTmFtZSIsInNldENvdXJzZVZpZGVvTmFtZSIsImNvdXJzZURvY3VtZW50VXJsIiwic2V0Q291cnNlRG9jdW1lbnRVcmwiLCJjb3Vyc2VEb2N1bWVudE5hbWUiLCJzZXRDb3Vyc2VEb2N1bWVudE5hbWUiLCJjb3Vyc2VBdWRpb1VybCIsInNldENvdXJzZUF1ZGlvVXJsIiwiY291cnNlQXVkaW9OYW1lIiwic2V0Q291cnNlQXVkaW9OYW1lIiwidmlkZW9EdXJhdGlvbiIsInNldFZpZGVvRHVyYXRpb24iLCJhZGRDb3Vyc2VGb3JtIiwidXNlRm9ybSIsImVkaXRDb3Vyc2VGb3JtIiwiYWRkU2VyaWVzRm9ybSIsImFkZFRhZ0Zvcm0iLCJwdWJsaXNoU2VyaWVzRm9ybSIsInB1Ymxpc2hDb3Vyc2VGb3JtIiwibm90aWZpY2F0aW9uIiwiZmV0Y2hTZXJpZXNMaXN0IiwicmVzIiwiY29uc29sZSIsImxvZyIsImRhdGEiLCJnZXRNYXJrZXRwbGFjZVNlcmllcyIsInBhZ2UiLCJwYWdlU2l6ZSIsImNvZGUiLCJsaXN0IiwiZXJyb3IiLCJtc2ciLCJmZXRjaFNlcmllc0NvdXJzZXMiLCJzZXJpZXNJZCIsImdldFNlcmllc0NvdXJzZUxpc3QiLCJwcmV2Iiwic2V0IiwiYWRkIiwiZmV0Y2hDb3Vyc2VMaXN0IiwiaGFuZGxlQWRkQ291cnNlIiwidmFsdWVzIiwiY29udGVudENvbmZpZyIsImhhc1ZpZGVvIiwiaGFzRG9jdW1lbnQiLCJoYXNBdWRpbyIsInZpZGVvIiwidXJsIiwibmFtZSIsImRvY3VtZW50IiwiYXVkaW8iLCJjb3Vyc2VEYXRhIiwicGFyc2VJbnQiLCJ0aXRsZSIsInRyaW0iLCJkZXNjcmlwdGlvbiIsImNvdmVySW1hZ2UiLCJ0ZWFjaGluZ0luZm8iLCJ0ZWFjaGluZ09iamVjdGl2ZXMiLCJsZW5ndGgiLCJjb250ZW50IiwiQXJyYXkiLCJpc0FycmF5IiwiYWRkaXRpb25hbFJlc291cmNlcyIsIm1hcCIsImZpbGUiLCJzcGxpdCIsInBvcCIsIm9yZGVySW5kZXgiLCJKU09OIiwic3RyaW5naWZ5IiwicmV0cnlDb3VudCIsIm1heFJldHJpZXMiLCJsYXN0RXJyb3IiLCJjcmVhdGVDb3Vyc2UiLCJzdWNjZXNzIiwicmVzZXRGaWVsZHMiLCJ3YXJuaW5nIiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwiaW5jbHVkZXMiLCJyZXNwb25zZSIsInN0YXR1cyIsImVycm9yTXNnIiwiaGFuZGxlRWRpdENvdXJzZSIsInVwZGF0ZUNvdXJzZSIsImlkIiwiaGFuZGxlRGVsZXRlQ291cnNlIiwiY291cnNlSWQiLCJkZWxldGVDb3Vyc2UiLCJoYW5kbGVEZWxldGVTdWJDb3Vyc2UiLCJ0b2dnbGVTZXJpZXNFeHBhbnNpb24iLCJoYXMiLCJuZXdTZXQiLCJkZWxldGUiLCJleHBhbmRBbGxTZXJpZXMiLCJzZXJpZXMiLCJjb2xsYXBzZUFsbFNlcmllcyIsImhhbmRsZUFkZFNlcmllcyIsInNlcmllc0RhdGEiLCJjcmVhdGVDb3Vyc2VTZXJpZXMiLCJoYW5kbGVBZGRUYWciLCJjcmVhdGVDb3Vyc2VUYWciLCJmZXRjaENvdXJzZVRhZ3MiLCJoYW5kbGVQdWJsaXNoU2VyaWVzIiwicHVibGlzaENvdXJzZVNlcmllcyIsInB1Ymxpc2hEYXRhIiwicHVibGlzaFN0YXRzIiwic3RhdHMiLCJzdGF0c01lc3NhZ2UiLCJwdWJsaXNoZWRDb3Vyc2VzIiwidG90YWxDb3Vyc2VzIiwiTWF0aCIsInZpZGVvQ291cnNlQ291bnQiLCJyb3VuZCIsInRvdGFsVmlkZW9EdXJhdGlvbiIsImluZm8iLCJmZXRjaFNlcmllc0ZvclB1Ymxpc2giLCJmZXRjaFNlcmllc0RldGFpbEZvclB1Ymxpc2giLCJnZXRNYXJrZXRwbGFjZVNlcmllc0RldGFpbCIsImZldGNoUHVibGlzaFNlcmllc0xpc3QiLCJmZXRjaFB1Ymxpc2hDb3Vyc2VMaXN0IiwiY291cnNlcyIsIkVycm9yIiwibWFya2V0cGxhY2VFcnJvciIsInJlczIiLCJtYW5hZ2VtZW50RXJyb3IiLCJoYW5kbGVQdWJsaXNoU2VyaWVzQ2hhbmdlIiwic2V0RmllbGRzVmFsdWUiLCJoYW5kbGVQdWJsaXNoQ291cnNlQ2hhbmdlIiwicmVzZXRQdWJsaXNoQ291cnNlTW9kYWwiLCJvcGVuUHVibGlzaENvdXJzZU1vZGFsIiwiaGFuZGxlUHVibGlzaENvdXJzZSIsInNlbGVjdGVkQ291cnNlIiwiZmluZCIsImMiLCJwdWJsaXNoQ291cnNlIiwicmVzZXRQdWJsaXNoTW9kYWwiLCJoYW5kbGVJbWFnZVVwbG9hZCIsIm9wdGlvbnMiLCJvblN1Y2Nlc3MiLCJvbkVycm9yIiwidXBsb2FkVG9Pc3MiLCJoYW5kbGVJbWFnZVJlbW92ZSIsImhhbmRsZUNvdXJzZUNvdmVyVXBsb2FkIiwiaGFuZGxlQ291cnNlQ292ZXJSZW1vdmUiLCJoYW5kbGVBZGRpdGlvbmFsUmVzb3VyY2VVcGxvYWQiLCJoYW5kbGVBZGRpdGlvbmFsUmVzb3VyY2VSZW1vdmUiLCJmaWx0ZXIiLCJmIiwiaGFuZGxlVmlkZW9VcGxvYWQiLCJ2aWRlb0VsZW1lbnQiLCJjcmVhdGVFbGVtZW50Iiwic3JjIiwib25sb2FkZWRtZXRhZGF0YSIsImZsb29yIiwiZHVyYXRpb24iLCJoYW5kbGVWaWRlb1JlbW92ZSIsImhhbmRsZURvY3VtZW50VXBsb2FkIiwiaGFuZGxlRG9jdW1lbnRSZW1vdmUiLCJoYW5kbGVBdWRpb1VwbG9hZCIsImhhbmRsZUF1ZGlvUmVtb3ZlIiwib3BlbkVkaXRNb2RhbCIsImNvdXJzZSIsImZpbHRlcmVkQ291cnNlcyIsInRvTG93ZXJDYXNlIiwiY2F0ZWdvcnkiLCJwcmVwYXJlVGFibGVEYXRhIiwidGFibGVEYXRhIiwiZnJvbSIsImZvckVhY2giLCJwdXNoIiwia2V5IiwidHlwZSIsImlzRXhwYW5kZWQiLCJzdWJDb3Vyc2VzIiwiZ2V0IiwicGFyZW50U2VyaWVzVGl0bGUiLCJjb2x1bW5zIiwiZGF0YUluZGV4Iiwid2lkdGgiLCJyZW5kZXIiLCJ0ZXh0IiwicmVjb3JkIiwiZGl2IiwiY2xhc3NOYW1lIiwic2l6ZSIsIm9uQ2xpY2siLCJzdHlsZSIsIm1pbldpZHRoIiwiaGVpZ2h0Iiwic3BhbiIsImNvbG9yIiwiZ2V0U3RhdHVzQ29uZmlnIiwiY29uZmlnIiwiaWNvbiIsInAiLCJvbkNvbmZpcm0iLCJva1RleHQiLCJjYW5jZWxUZXh0Iiwib2tUeXBlIiwiZGFuZ2VyIiwiZ2V0Q291cnNlVGFncyIsInRhZ3MiLCJ0YWciLCJ3YXJuIiwiZmV0Y2hDb3Vyc2VTZXJpZXMiLCJwYWdpbmF0aW9uIiwidG90YWwiLCJmb3JtYXR0ZWRTZXJpZXMiLCJpdGVtIiwiaW5kZXgiLCJjYXRlZ29yeUxhYmVsIiwidGVhY2hlcklkcyIsInRhZ0lkcyIsImNyZWF0ZWRBdCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsInVwZGF0ZWRBdCIsImV4dHJhIiwiYmxvY2siLCJiYWNrZ3JvdW5kQ29sb3IiLCJib3JkZXJDb2xvciIsIm9wZW4iLCJvbkNhbmNlbCIsImZvb3RlciIsInBsYWNlaG9sZGVyIiwiYWxsb3dDbGVhciIsIm9uU2VhcmNoIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0IiwidmFsdWUiLCJzdHJvbmciLCJyZWR1Y2UiLCJkaXNhYmxlZCIsImRhdGFTb3VyY2UiLCJyb3dLZXkiLCJzaG93U2l6ZUNoYW5nZXIiLCJzaG93VG90YWwiLCJvbk9rIiwic3VibWl0IiwiZm9ybSIsImxheW91dCIsIm9uRmluaXNoIiwiSXRlbSIsImxhYmVsIiwicnVsZXMiLCJyZXF1aXJlZCIsInNob3dTZWFyY2giLCJvcHRpb25GaWx0ZXJQcm9wIiwib3ZlcmZsb3ciLCJ0ZXh0T3ZlcmZsb3ciLCJ3aGl0ZVNwYWNlIiwibWF4V2lkdGgiLCJmb250V2VpZ2h0IiwiZm9udFNpemUiLCJtYXJnaW5MZWZ0IiwiVGV4dEFyZWEiLCJyb3dzIiwic2hvd0NvdW50IiwibWF4TGVuZ3RoIiwiRHJhZ2dlciIsImN1c3RvbVJlcXVlc3QiLCJvblJlbW92ZSIsImFjY2VwdCIsIm1heENvdW50IiwibGlzdFR5cGUiLCJpbWciLCJhbHQiLCJtYXhIZWlnaHQiLCJvYmplY3RGaXQiLCJtaW4iLCJ0cmFuc2Zvcm0iLCJOdW1iZXIiLCJ0b29sdGlwIiwiY29udHJvbHMiLCJtYXJnaW5Ub3AiLCJwYWRkaW5nIiwidGV4dEFsaWduIiwibW9kZSIsIm11bHRpcGxlIiwiaW5pdGlhbFZhbHVlIiwib3B0aW9uTGFiZWxQcm9wIiwibWF4IiwiZmlsdGVyT3B0aW9uIiwiaW5wdXQiLCJvcHRpb24iLCJjaGlsZHJlbiIsImg0IiwidWwiLCJsaSIsImRlc3Ryb3lPbkNsb3NlIiwibm90Rm91bmRDb250ZW50IiwicyIsImh0bWxUeXBlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin-space/components/course-management.tsx\n"));

/***/ })

});