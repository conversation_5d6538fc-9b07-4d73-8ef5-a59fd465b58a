"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationRecord = void 0;
const typeorm_1 = require("typeorm");
const swagger_1 = require("@nestjs/swagger");
let NotificationRecord = class NotificationRecord {
    id;
    notificationType;
    targetId;
    userId;
    status;
    content;
    errorMessage;
    retryCount;
    nextRetryTime;
    maxRetryCount;
    version;
    createdAt;
    updatedAt;
};
exports.NotificationRecord = NotificationRecord;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    (0, swagger_1.ApiProperty)({ description: '主键ID' }),
    __metadata("design:type", String)
], NotificationRecord.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '通知类型', length: 32 }),
    (0, swagger_1.ApiProperty)({ description: '通知类型(payment/refund)' }),
    __metadata("design:type", String)
], NotificationRecord.prototype, "notificationType", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '目标ID', length: 36 }),
    (0, swagger_1.ApiProperty)({ description: '目标ID(订单ID/退款ID)' }),
    __metadata("design:type", String)
], NotificationRecord.prototype, "targetId", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '用户ID', length: 36 }),
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", String)
], NotificationRecord.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '通知状态', length: 32, default: 'pending' }),
    (0, swagger_1.ApiProperty)({ description: '通知状态' }),
    __metadata("design:type", String)
], NotificationRecord.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '通知内容', type: 'text', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '通知内容' }),
    __metadata("design:type", String)
], NotificationRecord.prototype, "content", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '错误信息', length: 1024, nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '错误信息' }),
    __metadata("design:type", String)
], NotificationRecord.prototype, "errorMessage", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '重试次数', type: 'int', default: 0 }),
    (0, swagger_1.ApiProperty)({ description: '重试次数' }),
    __metadata("design:type", Number)
], NotificationRecord.prototype, "retryCount", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.Column)({ comment: '下次重试时间', type: 'timestamp', nullable: true }),
    (0, swagger_1.ApiProperty)({ description: '下次重试时间' }),
    __metadata("design:type", Date)
], NotificationRecord.prototype, "nextRetryTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '最大重试次数', type: 'int', default: 3 }),
    (0, swagger_1.ApiProperty)({ description: '最大重试次数' }),
    __metadata("design:type", Number)
], NotificationRecord.prototype, "maxRetryCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ comment: '版本号（乐观锁）', type: 'int', default: 1 }),
    (0, swagger_1.ApiProperty)({ description: '版本号（乐观锁）' }),
    __metadata("design:type", Number)
], NotificationRecord.prototype, "version", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ comment: '创建时间' }),
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], NotificationRecord.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ comment: '更新时间' }),
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], NotificationRecord.prototype, "updatedAt", void 0);
exports.NotificationRecord = NotificationRecord = __decorate([
    (0, typeorm_1.Entity)('notification_record')
], NotificationRecord);
//# sourceMappingURL=notification-record.entity.js.map