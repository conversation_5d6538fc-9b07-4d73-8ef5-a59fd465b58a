"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ManagementController = exports.GetSeriesCoursesQueryDto = exports.GetMyCourseSeriesQueryDto = void 0;
const common_1 = require("@nestjs/common");
const management_service_1 = require("../application/services/management/management.service");
const swagger_1 = require("@nestjs/swagger");
const course_series_dto_1 = require("../application/dto/management/course-series.dto");
const course_dto_1 = require("../application/dto/management/course.dto");
const task_template_dto_1 = require("../application/dto/management/task-template.dto");
const course_settings_dto_1 = require("../application/dto/management/course-settings.dto");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const calculation_utils_1 = require("../utils/management/calculation.utils");
const status_utils_1 = require("../utils/management/status.utils");
const validation_utils_1 = require("../utils/management/validation.utils");
const http_response_result_service_1 = require("../../http_response_result/http_response_result.service");
class GetMyCourseSeriesQueryDto {
    page = 1;
    pageSize = 10;
    status;
    keyword;
}
exports.GetMyCourseSeriesQueryDto = GetMyCourseSeriesQueryDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], GetMyCourseSeriesQueryDto.prototype, "page", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], GetMyCourseSeriesQueryDto.prototype, "pageSize", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], GetMyCourseSeriesQueryDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GetMyCourseSeriesQueryDto.prototype, "keyword", void 0);
class GetSeriesCoursesQueryDto {
    page = 1;
    pageSize = 20;
    status;
}
exports.GetSeriesCoursesQueryDto = GetSeriesCoursesQueryDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], GetSeriesCoursesQueryDto.prototype, "page", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], GetSeriesCoursesQueryDto.prototype, "pageSize", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], GetSeriesCoursesQueryDto.prototype, "status", void 0);
let ManagementController = class ManagementController {
    managementService;
    httpResponseResultService;
    constructor(managementService, httpResponseResultService) {
        this.managementService = managementService;
        this.httpResponseResultService = httpResponseResultService;
    }
    async getMyCourseSeries(req, query) {
        const userId = req.user.id;
        const { page, pageSize } = validation_utils_1.ValidationUtils.validateAndNormalizePagination(query.page, query.pageSize);
        const { status, keyword } = query;
        const result = await this.managementService.getMyCourseSeries(userId, page, pageSize, status, keyword);
        const list = result.items.map(series => {
            const contentSummary = series['_contentSummary'];
            delete series['_contentSummary'];
            const categoryLabel = status_utils_1.StatusUtils.getCategoryLabel(series.category);
            const statusLabel = status_utils_1.StatusUtils.getCourseStatusLabel(series.status);
            return {
                ...series,
                categoryLabel,
                statusLabel,
                contentSummary
            };
        });
        const resultData = {
            list,
            pagination: {
                page: result.page,
                pageSize: result.pageSize,
                total: result.total,
                totalPages: result.totalPages,
                hasNext: result.page < result.totalPages,
                hasPrev: result.page > 1
            }
        };
        return this.httpResponseResultService.success(resultData, "success", 200);
    }
    async getSeriesCourses(req, seriesId, query) {
        const userId = req.user.id;
        const { page, pageSize } = validation_utils_1.ValidationUtils.validateAndNormalizePagination(query.page, query.pageSize || 20);
        const { status } = query;
        const result = await this.managementService.getSeriesCourses(seriesId, status, page, pageSize, userId);
        const list = result.courses.map(course => {
            const statusLabel = status_utils_1.StatusUtils.getCourseStatusLabel(course.status);
            const videoDurationLabel = calculation_utils_1.CalculationUtils.formatVideoDuration(course.videoDuration);
            return {
                ...course,
                statusLabel,
                videoDurationLabel
            };
        });
        const resultData = {
            list,
            pagination: {
                page: result.page,
                pageSize: result.pageSize,
                total: result.total,
                totalPages: Math.ceil(result.total / result.pageSize),
                hasNext: result.page < Math.ceil(result.total / result.pageSize),
                hasPrev: result.page > 1
            }
        };
        return this.httpResponseResultService.success(resultData, "success", 200);
    }
    async getSeriesDetail(id) {
        validation_utils_1.ValidationUtils.validateRequiredId(id, '系列ID');
        const result = await this.managementService.findCourseSeriesById(id);
        return this.httpResponseResultService.success(result, "success", 200);
    }
    async createCourseSeries(req, courseSeriesData) {
        const userId = req.user.id;
        const result = await this.managementService.createCourseSeries({
            ...courseSeriesData,
            creatorId: userId
        });
        return this.httpResponseResultService.success(result, "success", 200);
    }
    async updateCourseSeries(req, id, updateData) {
        const userId = req.user.id;
        const result = await this.managementService.updateCourseSeries(id, updateData, userId);
        return this.httpResponseResultService.success(result, "success", 200);
    }
    async deleteCourseSeries(req, id) {
        const userId = req.user.id;
        const result = await this.managementService.removeCourseSeries(id, userId);
        return this.httpResponseResultService.success(result, "success", 200);
    }
    async publishCourseSeries(req, seriesId) {
        const userId = req.user.id;
        const result = await this.managementService.publishCourseSeries(seriesId, userId);
        const statusLabel = status_utils_1.StatusUtils.getCourseStatusLabel(result.data.status);
        const resultData = {
            id: result.data.id,
            title: result.data.title,
            status: result.data.status,
            statusLabel,
            publishedAt: new Date().toISOString(),
            totalCourses: result.publishStats.totalCourses,
            publishedCourses: result.publishStats.publishedCourses,
            publishStats: {
                videoCourseCount: result.publishStats.videoCourseCount,
                documentCourseCount: result.publishStats.documentCourseCount,
                totalVideoDuration: result.publishStats.totalVideoDuration,
                totalResourcesCount: result.publishStats.totalResourcesCount
            }
        };
        return this.httpResponseResultService.success(resultData, "success", 200);
    }
    async createCourse(req, courseData) {
        const userId = req.user.id;
        const result = await this.managementService.createCourse({
            ...courseData,
            creatorId: userId
        });
        return this.httpResponseResultService.success(result, "success", 200);
    }
    async setCourseSettings(req, courseId, settingsData) {
        const userId = req.user.id;
        const result = await this.managementService.setCourseSettings(courseId, settingsData, userId);
        return this.httpResponseResultService.success(result, "success", 200);
    }
    async addTaskTemplate(req, courseId, templateData) {
        const userId = req.user.id;
        const result = await this.managementService.addTaskTemplate(courseId, templateData, userId);
        return this.httpResponseResultService.success(result, "success", 200);
    }
    async getCourseDetail(req, id) {
        const userId = req.user.id;
        validation_utils_1.ValidationUtils.validateRequiredId(id, '课程ID');
        const result = await this.managementService.findCourseById(id, userId);
        return this.httpResponseResultService.success(result, "success", 200);
    }
    async updateCourse(req, id, updateData) {
        const userId = req.user.id;
        const result = await this.managementService.updateCourse(id, updateData, userId);
        return this.httpResponseResultService.success(result, "success", 200);
    }
    async publishCourse(req, id) {
        const userId = req.user.id;
        const result = await this.managementService.publishCourse(id, userId);
        const statusLabel = status_utils_1.StatusUtils.getCourseStatusLabel(result.data.status);
        const resultData = {
            id: result.data.id,
            title: result.data.title,
            status: result.data.status,
            statusLabel,
            publishedAt: new Date().toISOString()
        };
        return this.httpResponseResultService.success(resultData, result.message, 200);
    }
    async deleteCourse(req, id) {
        const userId = req.user.id;
        const result = await this.managementService.removeCourse(id, userId);
        return this.httpResponseResultService.success(result, "success", 200);
    }
    async updateCourseOrders(req, seriesId, body) {
        const userId = req.user.id;
        const result = await this.managementService.updateCourseOrders(seriesId, body.courseOrders, userId);
        return this.httpResponseResultService.success(result, "success", 200);
    }
};
exports.ManagementController = ManagementController;
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '获取我的系列课程列表' }),
    (0, swagger_1.ApiQuery)({ name: 'page', description: '页码，默认1', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'pageSize', description: '每页数量，默认10', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'status', description: '状态筛选：0=草稿，1=已发布，2=已归档', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'keyword', description: '搜索关键词', required: false, type: String }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        schema: {
            example: {
                code: 200,
                message: 'success',
                data: {
                    list: [
                        {
                            id: 1,
                            title: 'JavaScript高级编程',
                            description: '深入学习JavaScript高级特性',
                            coverImage: 'https://example.com/js-cover.jpg',
                            category: 0,
                            categoryLabel: '官方',
                            status: 0,
                            statusLabel: '草稿',
                            projectMembers: '王老师、李助教',
                            totalCourses: 3,
                            totalStudents: 0,
                            contentSummary: {
                                videoCourseCount: 2,
                                documentCourseCount: 3,
                                totalResourcesCount: 8,
                                completionRate: 0.6
                            },
                            createdAt: '2024-01-20T14:30:00Z',
                            updatedAt: '2024-01-22T09:15:00Z'
                        }
                    ],
                    pagination: {
                        page: 1,
                        pageSize: 10,
                        total: 5,
                        totalPages: 1,
                        hasNext: false,
                        hasPrev: false
                    }
                }
            }
        }
    }),
    (0, common_1.Get)('my-series'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, GetMyCourseSeriesQueryDto]),
    __metadata("design:returntype", Promise)
], ManagementController.prototype, "getMyCourseSeries", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '获取系列下的课程列表' }),
    (0, swagger_1.ApiParam)({ name: 'seriesId', description: '系列ID', type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'page', description: '页码，默认1', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'pageSize', description: '每页数量，默认20', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'status', description: '状态筛选：0=草稿，1=已发布，2=已归档', required: false, type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        schema: {
            example: {
                code: 200,
                message: 'success',
                data: {
                    list: [
                        {
                            id: 10,
                            seriesId: 123,
                            title: "第四课：Hooks进阶",
                            description: "深入学习React Hooks的高级用法",
                            coverImage: "https://example.com/course4-cover.jpg",
                            orderIndex: 4,
                            status: 0,
                            statusLabel: "草稿",
                            hasVideo: 1,
                            hasDocument: 1,
                            hasAudio: 0,
                            videoDuration: 2400,
                            videoDurationLabel: "40分钟",
                            videoName: "React Hooks进阶.mp4",
                            firstTeachingTitle: "教学目标",
                            resourcesCount: 3,
                            createdAt: "2024-01-25T16:20:00Z",
                            updatedAt: "2024-01-25T16:20:00Z"
                        }
                    ],
                    pagination: {
                        page: 1,
                        pageSize: 20,
                        total: 2,
                        totalPages: 1,
                        hasNext: false,
                        hasPrev: false
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '系列不存在' }),
    (0, common_1.Get)('series/:seriesId/courses'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('seriesId')),
    __param(2, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, GetSeriesCoursesQueryDto]),
    __metadata("design:returntype", Promise)
], ManagementController.prototype, "getSeriesCourses", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '获取系列详情' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '系列ID', type: Number }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '成功获取系列详情', type: course_series_dto_1.CourseSeriesResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '系列不存在' }),
    (0, common_1.Get)('series/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ManagementController.prototype, "getSeriesDetail", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '创建系列课程' }),
    (0, swagger_1.ApiBody)({ type: course_series_dto_1.CreateCourseSeriesDto }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '系列课程创建成功', type: course_series_dto_1.CourseSeriesResponseDto }),
    (0, common_1.Post)('series'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, course_series_dto_1.CreateCourseSeriesDto]),
    __metadata("design:returntype", Promise)
], ManagementController.prototype, "createCourseSeries", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '更新系列课程' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '系列ID', type: Number }),
    (0, swagger_1.ApiBody)({ type: course_series_dto_1.UpdateCourseSeriesDto }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '系列课程更新成功', type: course_series_dto_1.CourseSeriesResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '系列不存在' }),
    (0, common_1.Put)('series/:id'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, course_series_dto_1.UpdateCourseSeriesDto]),
    __metadata("design:returntype", Promise)
], ManagementController.prototype, "updateCourseSeries", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '删除系列课程' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '系列ID', type: Number }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '系列课程删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '系列不存在' }),
    (0, common_1.Delete)('series/:id'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], ManagementController.prototype, "deleteCourseSeries", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '发布课程系列' }),
    (0, swagger_1.ApiParam)({ name: 'seriesId', description: '系列ID', type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '系列课程发布成功',
        schema: {
            example: {
                code: 200,
                message: "系列课程发布成功",
                data: {
                    id: 124,
                    title: "Node.js后端开发系列",
                    status: 1,
                    statusLabel: "已发布",
                    publishedAt: "2024-01-26T15:30:00Z",
                    totalCourses: 3,
                    publishedCourses: 3,
                    publishStats: {
                        videoCourseCount: 3,
                        documentCourseCount: 3,
                        totalVideoDuration: 10800,
                        totalResourcesCount: 12
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '发布失败：课程系列中至少需要包含一个课程' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: '无权限发布此课程系列' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '系列不存在' }),
    (0, common_1.Post)('series/:seriesId/publish'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('seriesId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], ManagementController.prototype, "publishCourseSeries", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '创建课程' }),
    (0, swagger_1.ApiBody)({
        type: course_dto_1.CreateCourseDto,
        description: '课程创建信息',
        examples: {
            example1: {
                summary: '创建Node.js基础课程',
                value: {
                    seriesId: 2,
                    title: "第一课：Node.js基础入门",
                    description: "了解Node.js的基本概念、安装配置和核心模块",
                    coverImage: "https://example.com/nodejs-basic-cover.jpg",
                    hasVideo: 1,
                    hasDocument: 1,
                    hasAudio: 0,
                    videoDuration: 3600,
                    contentConfig: {
                        hasVideo: 1,
                        hasDocument: 1,
                        hasAudio: 0,
                        video: {
                            url: "https://example.com/videos/nodejs-basics.mp4",
                            name: "Node.js基础入门讲解.mp4"
                        },
                        document: {
                            url: "https://example.com/documents/nodejs-basics-slides.pdf",
                            name: "Node.js基础入门课件.pdf"
                        }
                    },
                    teachingInfo: [
                        {
                            title: "教学目标",
                            content: [
                                "理解Node.js的基本概念和特点",
                                "掌握Node.js的安装和环境配置"
                            ]
                        }
                    ],
                    additionalResources: [
                        {
                            title: "Node.js官方文档",
                            url: "https://nodejs.org/docs/",
                            description: "Node.js官方学习资源"
                        }
                    ],
                    orderIndex: 1
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '课程创建成功', type: course_dto_1.CourseResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '系列不存在' }),
    (0, common_1.Post)('courses'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, course_dto_1.CreateCourseDto]),
    __metadata("design:returntype", Promise)
], ManagementController.prototype, "createCourse", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '设置课程配置' }),
    (0, swagger_1.ApiParam)({ name: 'courseId', description: '课程ID', type: Number }),
    (0, swagger_1.ApiBody)({
        type: course_settings_dto_1.CourseSettingsDto,
        description: '课程配置信息',
        examples: {
            example1: {
                summary: '设置课程积分和模板',
                value: {
                    templateId: 1,
                    requiredPoints: 100,
                    autoCreateTasks: 1
                }
            },
            example2: {
                summary: '仅设置所需积分',
                value: {
                    requiredPoints: 50
                }
            },
            example3: {
                summary: '关闭自动创建任务',
                value: {
                    autoCreateTasks: 0
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '课程配置设置成功', type: course_settings_dto_1.CourseSettingsResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '课程不存在' }),
    (0, common_1.Post)('courses/:courseId/settings'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('courseId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, course_settings_dto_1.CourseSettingsDto]),
    __metadata("design:returntype", Promise)
], ManagementController.prototype, "setCourseSettings", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '添加任务模板' }),
    (0, swagger_1.ApiParam)({ name: 'courseId', description: '课程ID', type: Number }),
    (0, swagger_1.ApiBody)({
        type: task_template_dto_1.CreateTaskTemplateDto,
        description: '任务模板信息',
        examples: {
            example1: {
                summary: '创建Node.js基础练习任务',
                value: {
                    taskName: "Node.js基础练习",
                    taskDescription: "创建一个简单的Node.js应用，实现文件读写和HTTP服务器功能",
                    durationDays: 7,
                    attachments: [
                        {
                            "title": "练习模板",
                            "url": "https://example.com/nodejs-template.zip",
                            "type": "file"
                        },
                        {
                            "title": "参考文档",
                            "url": "https://example.com/nodejs-reference.pdf",
                            "type": "document"
                        }
                    ],
                    workIdsStr: "200,202,203",
                    selfAssessmentItems: [
                        {
                            "content": "是否正确使用了fs模块进行文件操作？",
                            "sequence": 1
                        },
                        {
                            "content": "HTTP服务器是否能正常启动和响应请求？",
                            "sequence": 2
                        },
                        {
                            "content": "代码是否遵循了Node.js的最佳实践？",
                            "sequence": 3
                        }
                    ]
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '任务模板添加成功', type: task_template_dto_1.TaskTemplateResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '课程不存在' }),
    (0, common_1.Post)('courses/:courseId/task-templates'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('courseId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, task_template_dto_1.CreateTaskTemplateDto]),
    __metadata("design:returntype", Promise)
], ManagementController.prototype, "addTaskTemplate", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '获取课程详情' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '课程ID', type: Number }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '成功获取课程详情', type: course_dto_1.CourseResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '课程不存在' }),
    (0, common_1.Get)('courses/:id'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], ManagementController.prototype, "getCourseDetail", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '更新课程' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '课程ID', type: Number }),
    (0, swagger_1.ApiBody)({ type: course_dto_1.CreateCourseDto, description: '课程更新信息' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '课程更新成功', type: course_dto_1.CourseResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '课程不存在' }),
    (0, common_1.Put)('courses/:id'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, course_dto_1.CreateCourseDto]),
    __metadata("design:returntype", Promise)
], ManagementController.prototype, "updateCourse", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '发布课程' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '课程ID', type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '课程发布成功',
        schema: {
            example: {
                code: 200,
                message: 'success',
                data: {
                    id: 3228,
                    title: "第一课：Node.js基础入门",
                    status: 1,
                    statusLabel: "已发布",
                    publishedAt: "2024-01-15T10:30:00.000Z"
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '课程已经发布，无需重复发布' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: '无权限发布此课程' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '课程不存在' }),
    (0, common_1.Post)('courses/:id/publish'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], ManagementController.prototype, "publishCourse", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '删除课程' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '课程ID', type: Number }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '课程删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '课程不存在' }),
    (0, common_1.Delete)('courses/:id'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", Promise)
], ManagementController.prototype, "deleteCourse", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '调整课程排序' }),
    (0, swagger_1.ApiParam)({ name: 'seriesId', description: '系列ID', type: Number }),
    (0, swagger_1.ApiBody)({
        description: '课程排序信息',
        schema: {
            type: 'object',
            properties: {
                courseOrders: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            courseId: { type: 'number', description: '课程ID' },
                            orderIndex: { type: 'number', description: '新的排序索引' }
                        }
                    }
                }
            },
            example: {
                courseOrders: [
                    { courseId: 1, orderIndex: 1 },
                    { courseId: 2, orderIndex: 2 },
                    { courseId: 3, orderIndex: 3 }
                ]
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '课程排序调整成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '系列不存在' }),
    (0, common_1.Put)('series/:seriesId/course-orders'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('seriesId')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Object]),
    __metadata("design:returntype", Promise)
], ManagementController.prototype, "updateCourseOrders", null);
exports.ManagementController = ManagementController = __decorate([
    (0, swagger_1.ApiTags)('课程管理'),
    (0, common_1.Controller)('api/v1/course-management'),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    __metadata("design:paramtypes", [management_service_1.ManagementService,
        http_response_result_service_1.HttpResponseResultService])
], ManagementController);
//# sourceMappingURL=management.controller.js.map