<!DOCTYPE html>
<html>
<head>
    <title>课程发布功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-section { margin-bottom: 30px; padding: 20px; border: 1px solid #e8e8e8; border-radius: 8px; }
        .test-title { font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #333; }
        
        /* 模拟课程列表样式 */
        .course-list-items { padding: 16px; background: #f9fafb; border-radius: 8px; }
        .course-list-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .course-list-item:hover {
            border-color: #3b82f6;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
        }
        .course-list-item.active {
            border-color: #3b82f6;
            background: #eff6ff;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
        }
        .course-list-item-content {
            display: flex;
            align-items: center;
            flex: 1;
            gap: 8px;
        }
        .course-list-item-text {
            font-size: 14px;
            color: #374151;
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .course-status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
            flex-shrink: 0;
        }
        .course-status-badge.published {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        .course-status-badge.draft {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fde68a;
        }
        
        /* 底部按钮区域 */
        .course-list-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-top: 1px solid #e5e7eb;
            background: #f9fafb;
            margin-top: 20px;
        }
        .course-list-footer-left, .course-list-footer-right {
            display: flex;
            gap: 12px;
        }
        .course-list-btn {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
        }
        .course-list-btn-publish {
            background: #10b981;
            color: white;
        }
        .course-list-btn-publish:hover {
            background: #059669;
        }
        .course-list-btn-publish-course {
            background: #52c41a;
            color: white;
        }
        .course-list-btn-publish-course:hover:not(:disabled) {
            background: #73d13d;
        }
        .course-list-btn-publish-course:disabled {
            background: #d9d9d9;
            color: #999;
            cursor: not-allowed;
        }
        .course-list-btn-exit {
            background: #6b7280;
            color: white;
        }
        .course-list-btn-exit:hover {
            background: #4b5563;
        }
        .course-list-btn-save {
            background: #1890ff;
            color: white;
        }
        .course-list-btn-save:hover {
            background: #40a9ff;
        }
        
        /* API测试区域 */
        .api-test { background: #f8f9fa; padding: 15px; border-radius: 6px; margin: 15px 0; }
        .api-url { font-family: monospace; background: #e9ecef; padding: 8px; border-radius: 4px; margin: 10px 0; }
        .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; font-size: 14px; }
        .btn-primary { background: #1890ff; color: white; }
        .btn-success { background: #52c41a; color: white; }
        .btn-warning { background: #faad14; color: white; }
        .btn:disabled { background: #d9d9d9; cursor: not-allowed; }
        .log-area { background: #f5f5f5; padding: 15px; border-radius: 6px; font-family: monospace; font-size: 12px; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
        .status-stats { display: flex; gap: 20px; margin: 15px 0; }
        .stat-item { padding: 10px 15px; background: #f8f9fa; border-radius: 6px; text-align: center; }
        .stat-number { font-size: 24px; font-weight: bold; margin-bottom: 5px; }
        .stat-label { font-size: 12px; color: #666; }
        .stat-draft .stat-number { color: #92400e; }
        .stat-published .stat-number { color: #166534; }
        .stat-total .stat-number { color: #1890ff; }
        .selected-course-info { background: #e6f7ff; padding: 15px; border-radius: 6px; margin: 15px 0; border: 1px solid #91d5ff; }
        .selected-course-info h4 { margin: 0 0 10px 0; color: #1890ff; }
        .selected-course-info p { margin: 5px 0; font-size: 14px; }
        .no-selection { color: #999; font-style: italic; }
    </style>
</head>
<body>
    <div class="container">
        <h1>课程发布功能测试</h1>
        
        <div class="test-section">
            <div class="test-title">API接口测试</div>
            <div class="api-test">
                <div class="api-url">POST /api/v1/course-management/courses/{id}/publish</div>
                <div>
                    <label>课程ID: </label>
                    <input type="number" id="courseId" value="4292" style="padding: 5px; margin: 0 10px;">
                    <button class="btn btn-success" onclick="testPublishCourse()">测试发布课程</button>
                    <button class="btn btn-primary" onclick="loadMockData()">加载模拟数据</button>
                    <button class="btn btn-warning" onclick="resetAllCourses()">重置所有课程为未发布</button>
                </div>
            </div>
            
            <div class="status-stats" id="statusStats" style="display: none;">
                <div class="stat-item stat-total">
                    <div class="stat-number" id="totalCount">0</div>
                    <div class="stat-label">总课程数</div>
                </div>
                <div class="stat-item stat-published">
                    <div class="stat-number" id="publishedCount">0</div>
                    <div class="stat-label">已发布</div>
                </div>
                <div class="stat-item stat-draft">
                    <div class="stat-number" id="draftCount">0</div>
                    <div class="stat-label">未发布</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">选中课程信息</div>
            <div class="selected-course-info" id="selectedCourseInfo">
                <div class="no-selection">请点击左侧课程列表选择一个课程</div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">课程列表显示效果</div>
            <div class="course-list-items" id="courseList">
                <div style="text-align: center; color: #666; padding: 40px;">
                    点击上方按钮加载课程数据
                </div>
            </div>
            
            <!-- 底部按钮区域 -->
            <div class="course-list-footer">
                <div class="course-list-footer-left">
                    <button onclick="publishSeries()" class="course-list-btn course-list-btn-publish">
                        发布系列课程
                    </button>
                </div>
                <div class="course-list-footer-right">
                    <button onclick="exitEdit()" class="course-list-btn course-list-btn-exit">
                        退出编辑模式
                    </button>
                    <button 
                        onclick="publishSelectedCourse()" 
                        class="course-list-btn course-list-btn-publish-course"
                        id="publishCourseBtn"
                        disabled
                        title="请先选择要发布的课程"
                    >
                        发布课程
                    </button>
                    <button onclick="saveCourses()" class="course-list-btn course-list-btn-save">
                        保存
                    </button>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">测试日志</div>
            <div class="log-area" id="testLog">等待操作...</div>
        </div>
    </div>

    <script>
        let testLogs = [];
        let selectedCourseId = null;
        let courses = [];

        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            testLogs.push(`[${timestamp}] ${message}`);
            document.getElementById('testLog').textContent = testLogs.join('\n');
            console.log(message);
        }

        function updateStats(courseList) {
            const total = courseList.length;
            const published = courseList.filter(c => c.status === 1).length;
            const draft = courseList.filter(c => c.status === 0).length;
            
            document.getElementById('totalCount').textContent = total;
            document.getElementById('publishedCount').textContent = published;
            document.getElementById('draftCount').textContent = draft;
            document.getElementById('statusStats').style.display = 'flex';
            
            addLog(`📊 课程状态统计: 总数=${total}, 已发布=${published}, 未发布=${draft}`);
        }

        function updateSelectedCourseInfo() {
            const infoElement = document.getElementById('selectedCourseInfo');
            const publishBtn = document.getElementById('publishCourseBtn');
            
            if (!selectedCourseId) {
                infoElement.innerHTML = '<div class="no-selection">请点击左侧课程列表选择一个课程</div>';
                publishBtn.disabled = true;
                publishBtn.title = '请先选择要发布的课程';
                return;
            }
            
            const course = courses.find(c => c.id === selectedCourseId);
            if (!course) {
                infoElement.innerHTML = '<div class="no-selection">未找到选中的课程</div>';
                publishBtn.disabled = true;
                publishBtn.title = '未找到选中的课程';
                return;
            }
            
            const isPublished = course.status === 1;
            publishBtn.disabled = isPublished;
            publishBtn.title = isPublished ? '该课程已发布' : '发布选中的课程';
            
            infoElement.innerHTML = `
                <h4>选中课程: ${course.title}</h4>
                <p><strong>课程ID:</strong> ${course.id}</p>
                <p><strong>发布状态:</strong> ${isPublished ? '已发布' : '未发布'}</p>
                <p><strong>排序:</strong> ${course.orderIndex}</p>
                <p><strong>描述:</strong> ${course.description || '暂无描述'}</p>
                <p><strong>创建时间:</strong> ${course.createdAt || '未知'}</p>
            `;
        }

        function renderCourseList(courseList) {
            courses = courseList;
            const courseListElement = document.getElementById('courseList');
            
            if (courseList.length === 0) {
                courseListElement.innerHTML = `
                    <div style="text-align: center; color: #666; padding: 40px;">
                        暂无课程数据
                    </div>
                `;
                return;
            }
            
            courseListElement.innerHTML = courseList.map(course => `
                <div class="course-list-item ${selectedCourseId === course.id ? 'active' : ''}" 
                     onclick="selectCourse(${course.id})">
                    <div class="course-list-item-content">
                        <span class="course-list-item-text">${course.title}</span>
                        <span class="course-status-badge ${course.status === 1 ? 'published' : 'draft'}">
                            ${course.status === 1 ? '已发布' : '未发布'}
                        </span>
                    </div>
                </div>
            `).join('');
            
            addLog(`✅ 渲染课程列表完成，共 ${courseList.length} 个课程`);
            updateSelectedCourseInfo();
        }

        function selectCourse(courseId) {
            selectedCourseId = courseId;
            addLog(`🎯 选中课程: ID=${courseId}`);
            
            // 重新渲染以更新选中状态
            renderCourseList(courses);
        }

        // 模拟发布课程API调用
        async function testPublishCourse() {
            const courseId = document.getElementById('courseId').value;
            if (!courseId) {
                addLog('❌ 请输入课程ID');
                return;
            }
            
            addLog(`📤 发送请求: POST /api/v1/course-management/courses/${courseId}/publish`);
            
            try {
                // 模拟网络延迟
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 查找课程
                const course = courses.find(c => c.id == courseId);
                if (!course) {
                    throw new Error('课程不存在');
                }
                
                if (course.status === 1) {
                    throw new Error('课程已经发布，无需重复发布');
                }
                
                // 模拟成功响应
                const mockResponse = {
                    code: 200,
                    message: '课程发布成功',
                    data: {
                        id: course.id,
                        title: course.title,
                        status: 1,
                        statusLabel: '已发布',
                        publishedAt: new Date().toISOString()
                    }
                };
                
                addLog('✅ API调用成功');
                addLog(`📡 响应数据: ${JSON.stringify(mockResponse, null, 2)}`);
                
                // 更新本地数据
                course.status = 1;
                course.statusLabel = '已发布';
                course.publishedAt = mockResponse.data.publishedAt;
                
                renderCourseList(courses);
                updateStats(courses);
                
            } catch (error) {
                addLog(`❌ API调用失败: ${error.message}`);
            }
        }

        // 发布选中的课程
        async function publishSelectedCourse() {
            if (!selectedCourseId) {
                addLog('❌ 请先选择要发布的课程');
                return;
            }

            const selectedCourse = courses.find(course => course.id === selectedCourseId);
            if (!selectedCourse) {
                addLog('❌ 未找到选中的课程');
                return;
            }

            if (selectedCourse.status === 1) {
                addLog('❌ 该课程已经发布，无需重复发布');
                return;
            }

            addLog(`📤 开始发布课程，课程ID: ${selectedCourseId}`);
            addLog(`📤 课程信息: ${selectedCourse.title}`);

            try {
                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1500));
                
                const mockResponse = {
                    code: 200,
                    message: '课程发布成功',
                    data: {
                        id: selectedCourse.id,
                        title: selectedCourse.title,
                        status: 1,
                        statusLabel: '已发布',
                        publishedAt: new Date().toISOString()
                    }
                };
                
                addLog('✅ 课程发布成功');
                addLog(`📡 响应数据: ${JSON.stringify(mockResponse.data, null, 2)}`);
                
                // 更新课程状态
                selectedCourse.status = 1;
                selectedCourse.statusLabel = '已发布';
                selectedCourse.publishedAt = mockResponse.data.publishedAt;
                
                renderCourseList(courses);
                updateStats(courses);
                
            } catch (error) {
                addLog(`❌ 发布课程失败: ${error.message}`);
            }
        }

        // 加载模拟数据
        function loadMockData() {
            addLog('📝 加载预设模拟数据...');
            
            const mockCourses = [
                { 
                    id: 4291, 
                    title: '第1课时：JavaScript基础语法', 
                    description: '学习JavaScript的基本语法和概念',
                    status: 1, 
                    orderIndex: 1,
                    createdAt: '2024-01-15T10:00:00Z'
                },
                { 
                    id: 4292, 
                    title: '第2课时：变量和数据类型', 
                    description: '深入了解JavaScript的变量声明和数据类型',
                    status: 0, 
                    orderIndex: 2,
                    createdAt: '2024-01-16T14:30:00Z'
                },
                { 
                    id: 4293, 
                    title: '第3课时：函数和作用域', 
                    description: '学习函数定义、调用和作用域概念',
                    status: 1, 
                    orderIndex: 3,
                    createdAt: '2024-01-17T09:15:00Z'
                },
                { 
                    id: 4294, 
                    title: '第4课时：对象和数组', 
                    description: '掌握JavaScript中的对象和数组操作',
                    status: 0, 
                    orderIndex: 4,
                    createdAt: '2024-01-18T16:45:00Z'
                },
                { 
                    id: 4295, 
                    title: '第5课时：DOM操作', 
                    description: '学习如何操作网页DOM元素',
                    status: 0, 
                    orderIndex: 5,
                    createdAt: '2024-01-19T11:20:00Z'
                }
            ];
            
            renderCourseList(mockCourses);
            updateStats(mockCourses);
        }

        // 重置所有课程为未发布状态
        function resetAllCourses() {
            addLog('🔄 重置所有课程为未发布状态...');
            
            courses.forEach(course => {
                course.status = 0;
                course.statusLabel = '草稿';
                delete course.publishedAt;
            });
            
            renderCourseList(courses);
            updateStats(courses);
        }

        // 其他按钮功能
        function publishSeries() {
            addLog('📤 发布系列课程功能（模拟）');
        }

        function exitEdit() {
            addLog('🚪 退出编辑模式（模拟）');
        }

        function saveCourses() {
            addLog('💾 保存课程列表（模拟）');
        }

        // 初始化
        addLog('🎯 课程发布功能测试页面已加载');
        addLog('📋 测试功能:');
        addLog('  1. 选择课程并点击"发布课程"按钮');
        addLog('  2. 调用 POST /api/v1/course-management/courses/{id}/publish');
        addLog('  3. 验证按钮状态和权限控制');
        addLog('  4. 查看发布前后的状态变化');
    </script>
</body>
</html>
