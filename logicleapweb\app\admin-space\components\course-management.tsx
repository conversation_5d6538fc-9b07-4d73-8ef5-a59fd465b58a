'use client'

import { useState, useEffect } from 'react';
import { Card, Button, Table, Modal, Form, Input, Select, Space, Tag, Popconfirm, Upload, message } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SearchOutlined, UploadOutlined, InboxOutlined } from '@ant-design/icons';
import { GetNotification } from 'logic-common/dist/components/Notification';
import { courseApi, Course, CreateCourseRequest, UpdateCourseRequest, CreateCourseSeriesRequest, Teacher, CourseTag, CourseSeries } from '@/lib/api/course';
import { uploadApi } from '@/lib/api/upload';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';

const { Search } = Input;
const { Option } = Select;

interface CourseManagementProps {
  // 可以添加需要的props
}

// 课程表单数据类型
interface CourseFormData {
  name: string;
  description: string;
  category: string;
  status: 'active' | 'inactive';
  contentConfig?: {
    duration?: number;
    difficulty?: 'beginner' | 'intermediate' | 'advanced';
    prerequisites?: string[];
  };
  teachingInfo?: {
    objectives?: string[];
    methods?: string[];
    materials?: string[];
  };
}

const CourseManagement: React.FC<CourseManagementProps> = () => {
  const [courseList, setCourseList] = useState<Course[]>([]);
  const [loading, setLoading] = useState(false);
  const [isCourseModalVisible, setIsCourseModalVisible] = useState(false);
  const [isAddCourseModalVisible, setIsAddCourseModalVisible] = useState(false);
  const [isEditCourseModalVisible, setIsEditCourseModalVisible] = useState(false);
  const [isAddSeriesModalVisible, setIsAddSeriesModalVisible] = useState(false);
  const [isAddTagModalVisible, setIsAddTagModalVisible] = useState(false);
  const [isPublishSeriesModalVisible, setIsPublishSeriesModalVisible] = useState(false);
  const [isPublishCourseModalVisible, setIsPublishCourseModalVisible] = useState(false);
  const [editingCourse, setEditingCourse] = useState<Course | null>(null);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [courseTags, setCourseTags] = useState<CourseTag[]>([]);
  const [coverImageUrl, setCoverImageUrl] = useState<string>('');

  // 新增：系列课程和子课程管理相关状态
  const [seriesList, setSeriesList] = useState<any[]>([]);
  const [seriesCoursesMap, setSeriesCoursesMap] = useState<Map<number, any[]>>(new Map());
  const [expandedSeries, setExpandedSeries] = useState<Set<number>>(new Set());
  const [seriesLoading, setSeriesLoading] = useState(false);

  // 发布课程相关状态
  const [selectedSeriesForPublish, setSelectedSeriesForPublish] = useState<number | undefined>(undefined);
  const [selectedCourseForPublish, setSelectedCourseForPublish] = useState<number | undefined>(undefined);
  const [publishSeriesCourses, setPublishSeriesCourses] = useState<any[]>([]);
  const [publishLoading, setPublishLoading] = useState(false);
  const [publishSeriesOptions, setPublishSeriesOptions] = useState<any[]>([]);

  // 新的发布课程状态
  const [publishSeriesListForModal, setPublishSeriesListForModal] = useState<any[]>([]);
  const [publishCourseListForModal, setPublishCourseListForModal] = useState<any[]>([]);
  const [publishFormLoading, setPublishFormLoading] = useState(false);

  const [courseSeries, setCourseSeries] = useState<CourseSeries[]>([]);
  const [courseCoverImageUrl, setCourseCoverImageUrl] = useState<string>('');
  const [additionalFiles, setAdditionalFiles] = useState<string[]>([]);
  const [courseVideoUrl, setCourseVideoUrl] = useState<string>('');
  const [courseVideoName, setCourseVideoName] = useState<string>('');
  const [courseDocumentUrl, setCourseDocumentUrl] = useState<string>('');
  const [courseDocumentName, setCourseDocumentName] = useState<string>('');
  const [videoDuration, setVideoDuration] = useState<number>(0);

  // 教学信息状态
  const [teachingInfoList, setTeachingInfoList] = useState<Array<{
    title: string;
    content: string[];
  }>>([{ title: '', content: [''] }]);

  const [addCourseForm] = Form.useForm();
  const [editCourseForm] = Form.useForm();
  const [addSeriesForm] = Form.useForm();
  const [addTagForm] = Form.useForm();
  const [publishSeriesForm] = Form.useForm();
  const [publishCourseForm] = Form.useForm();
  const notification = GetNotification();



  // 获取系列课程列表
  const fetchSeriesList = async () => {
    try {
      setSeriesLoading(true);
      console.log('📝 获取系列课程列表...');

      const { data: res } = await courseApi.getMarketplaceSeries({
        page: 1,
        pageSize: 50
      });

      if (res.code === 200 && res.data?.list) {
        console.log('✅ 获取系列课程列表成功:', res.data.list);
        setSeriesList(res.data.list);
      } else {
        console.error('❌ 获取系列课程列表失败:', res.msg);
        message.error(res.msg || '获取系列课程列表失败');
      }
    } catch (error) {
      console.error('❌ 获取系列课程列表异常:', error);
      message.error('获取系列课程列表失败，请重试');
    } finally {
      setSeriesLoading(false);
    }
  };

  // 获取指定系列下的子课程列表
  const fetchSeriesCourses = async (seriesId: number) => {
    try {
      console.log('📝 获取系列子课程列表，系列ID:', seriesId);

      // 使用课程管理API获取所有状态的课程
      const { data: res } = await courseApi.getManagementSeriesCourses(seriesId, {
        page: 1,
        pageSize: 50
        // 不传status参数，获取所有状态的课程
      });

      if (res.code === 200 && res.data?.list) {
        console.log('✅ 获取系列子课程列表成功:', res.data.list);
        setSeriesCoursesMap(prev => new Map(prev.set(seriesId, res.data.list)));
        setExpandedSeries(prev => new Set(prev.add(seriesId)));
      } else {
        console.error('❌ 获取系列子课程列表失败:', res.msg);
        message.error(res.msg || '获取子课程列表失败');
      }
    } catch (error) {
      console.error('❌ 获取系列子课程列表异常:', error);
      message.error('获取子课程列表失败，请重试');
    }
  };

  // 获取课程列表（保留原有功能）
  const fetchCourseList = async () => {
    try {
      console.log('📝 获取课程列表...');

      // 获取系列课程列表
      await fetchSeriesList();
    } catch (error) {
      console.error('❌ 获取课程列表失败:', error);
      notification.error('获取课程列表失败，请重试');
    }
  };

  // 添加课程
  const handleAddCourse = async (values: any) => {
    try {
      // 构建内容配置，只包含有效的媒体文件
      const contentConfig: any = {
        hasVideo: courseVideoUrl ? 1 : 0,
        hasDocument: courseDocumentUrl ? 1 : 0,
      };

      if (courseVideoUrl) {
        contentConfig.video = {
          url: courseVideoUrl,
          name: courseVideoName || '课程视频.mp4'
        };
      }

      if (courseDocumentUrl) {
        contentConfig.document = {
          url: courseDocumentUrl,
          name: courseDocumentName || '课程文档.pdf'
        };
      }

      const courseData = {
        seriesId: parseInt(values.seriesId),
        title: values.title.trim(),
        description: values.description.trim(),
        coverImage: courseCoverImageUrl,
        hasVideo: courseVideoUrl ? 1 : 0,
        hasDocument: courseDocumentUrl ? 1 : 0,
        videoDuration: videoDuration || 0,
        contentConfig,
        teachingInfo: teachingInfoList.filter(info => info.title.trim() && info.content.some(c => c.trim())),
        additionalResources: additionalFiles.map(file => ({
          title: file.split('/').pop() || 'file',
          url: file,
          description: '课程附件资源'
        })),
        orderIndex: parseInt(values.orderIndex) || 0
      };

      // 验证必要字段
      if (!courseData.seriesId) {
        notification.error('请选择所属系列课程');
        return;
      }
      if (!courseData.title) {
        notification.error('请输入课程名称');
        return;
      }
      if (!courseData.coverImage) {
        notification.error('请上传课程封面');
        return;
      }

      console.log('📤 提交课程数据:', courseData);
      console.log('📊 数据大小估算:', JSON.stringify(courseData).length, '字符');

      // 添加重试机制
      let retryCount = 0;
      const maxRetries = 2;
      let lastError;

      while (retryCount <= maxRetries) {
        try {
          const { data: res } = await courseApi.createCourse(courseData);

          // 如果成功，跳出重试循环
          if (res.code === 200) {
            notification.success('创建课程成功');
            fetchCourseList();
            setIsAddCourseModalVisible(false);
            addCourseForm.resetFields();
            setCourseCoverImageUrl('');
            setAdditionalFiles([]);
            setCourseVideoUrl('');
            setCourseVideoName('');
            setCourseDocumentUrl('');
            setCourseDocumentName('');
            setVideoDuration(0);
            setTeachingInfoList([{ title: '', content: [''] }]);
            return;
          } else {
            notification.error(res.msg || '创建课程失败');
            return;
          }
        } catch (error: any) {
          lastError = error;
          retryCount++;

          if (retryCount <= maxRetries) {
            console.log(`🔄 第${retryCount}次重试...`);
            notification.warning(`网络异常，正在重试 (${retryCount}/${maxRetries})`);
            // 等待1秒后重试
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }
      }

      // 如果所有重试都失败了，抛出最后的错误
      throw lastError;
    } catch (error: any) {
      console.error('❌ 创建课程失败:', error);

      // 更详细的错误处理
      if (error.code === 'ECONNRESET' || error.message?.includes('ECONNRESET') ||
          (error.response?.data?.message && error.response.data.message.includes('ECONNRESET'))) {
        notification.error('网络连接中断，可能是网络不稳定或服务器繁忙。请稍后重试或联系管理员。');
      } else if (error.code === 'NETWORK_ERROR') {
        notification.error('网络错误，请检查网络连接');
      } else if (error.response?.status === 413) {
        notification.error('上传文件过大，请压缩后重试');
      } else if (error.response?.status === 400) {
        const errorMsg = error.response?.data?.message || error.message;
        notification.error(`请求参数错误: ${errorMsg}`);
      } else if (error.response?.status === 500) {
        notification.error('服务器内部错误，请联系管理员');
      } else {
        notification.error(`创建课程失败: ${error.message || '请稍后重试'}`);
      }

      console.log('🔍 完整错误信息:', {
        message: error.message,
        code: error.code,
        status: error.response?.status,
        data: error.response?.data
      });
    }
  };

  // 编辑课程
  const handleEditCourse = async (values: UpdateCourseRequest) => {
    if (!editingCourse) return;

    try {
      const { data: res } = await courseApi.updateCourse(editingCourse.id, values);
      if (res.code === 200) {
        notification.success('更新课程成功');
        fetchCourseList();
        setIsEditCourseModalVisible(false);
        setEditingCourse(null);
        editCourseForm.resetFields();
      } else {
        notification.error(res.msg || '更新课程失败');
      }
    } catch (error) {
      console.error('❌ 更新课程失败:', error);
      notification.error('更新课程失败，请重试');
    }
  };

  // 删除课程
  const handleDeleteCourse = async (courseId: number) => {
    try {
      const { data: res } = await courseApi.deleteCourse(courseId);
      if (res.code === 200) {
        notification.success('删除课程成功');
        fetchCourseList();
      } else {
        notification.error(res.msg || '删除课程失败');
      }
    } catch (error) {
      console.error('❌ 删除课程失败:', error);
      notification.error('删除课程失败，请重试');
    }
  };

  // 删除子课程
  const handleDeleteSubCourse = async (courseId: number, seriesId: number) => {
    try {
      console.log('🗑️ 删除子课程，课程ID:', courseId, '系列ID:', seriesId);

      const { data: res } = await courseApi.deleteCourse(courseId);

      if (res.code === 200) {
        message.success('删除子课程成功');
        console.log('✅ 子课程删除成功，重新获取系列子课程列表');

        // 重新获取该系列的子课程列表
        await fetchSeriesCourses(seriesId);

        console.log('🔄 子课程列表已刷新');
      } else {
        console.error('❌ 删除子课程失败:', res.msg);
        message.error(res.msg || '删除子课程失败');
      }
    } catch (error) {
      console.error('❌ 删除子课程异常:', error);
      message.error('删除子课程失败，请重试');
    }
  };

  // 切换系列展开/收起状态
  const toggleSeriesExpansion = async (seriesId: number) => {
    console.log('🔄 切换系列展开状态，系列ID:', seriesId);
    console.log('📊 当前展开状态:', expandedSeries.has(seriesId));

    if (expandedSeries.has(seriesId)) {
      // 收起
      console.log('📁 收起系列:', seriesId);
      setExpandedSeries(prev => {
        const newSet = new Set(prev);
        newSet.delete(seriesId);
        return newSet;
      });
    } else {
      // 展开，需要获取子课程数据
      console.log('📂 展开系列，获取子课程:', seriesId);
      await fetchSeriesCourses(seriesId);
    }
  };

  // 展开所有系列
  const expandAllSeries = async () => {
    console.log('📂 展开所有系列课程');
    for (const series of seriesList) {
      if (!expandedSeries.has(series.id)) {
        await fetchSeriesCourses(series.id);
      }
    }
  };

  // 收起所有系列
  const collapseAllSeries = () => {
    console.log('📁 收起所有系列课程');
    setExpandedSeries(new Set());
  };

  // 添加系列课程
  const handleAddSeries = async (values: CreateCourseSeriesRequest) => {
    try {
      const seriesData = {
        ...values,
        coverImage: coverImageUrl
      };

      console.log('创建系列课程数据:', seriesData);

      const { data: res } = await courseApi.createCourseSeries(seriesData);

      if (res.code === 200) {
        notification.success('创建系列课程成功');
        fetchCourseList();
        setIsAddSeriesModalVisible(false);
        addSeriesForm.resetFields();
        setCoverImageUrl('');
      } else {
        notification.error(res.msg || '创建系列课程失败');
      }
    } catch (error) {
      console.error('❌ 创建系列课程失败:', error);
      notification.error('创建系列课程失败，请重试');
    }
  };

  // 创建课程标签
  const handleAddTag = async (values: any) => {
    try {
      console.log('🏷️ 创建课程标签数据:', values);

      const { data: res } = await courseApi.createCourseTag(values);

      if (res.code === 200) {
        notification.success('创建标签成功');
        setIsAddTagModalVisible(false);
        addTagForm.resetFields();
        // 重新获取标签列表
        fetchCourseTags();
      } else {
        notification.error(res.msg || '创建标签失败');
      }
    } catch (error) {
      console.error('❌ 创建标签失败:', error);
      notification.error('创建标签失败，请重试');
    }
  };

  // 发布系列课程
  const handlePublishSeries = async (values: any) => {
    try {
      console.log('📢 发布系列课程数据:', values);

      const { data: res } = await courseApi.publishCourseSeries(values.seriesId);

      if (res.code === 200) {
        notification.success('发布系列课程成功');
        setIsPublishSeriesModalVisible(false);
        publishSeriesForm.resetFields();

        // 显示发布结果信息
        const publishData = res.data;
        console.log('✅ 发布成功，系列信息:', publishData);

        // 可以选择显示发布统计信息
        if (publishData.publishStats) {
          const stats = publishData.publishStats;
          const statsMessage = `已发布 ${publishData.publishedCourses}/${publishData.totalCourses} 个课程，包含 ${stats.videoCourseCount} 个视频课程，总时长 ${Math.round(stats.totalVideoDuration / 60)} 分钟`;
          notification.info(statsMessage);
        }
      } else {
        notification.error(res.msg || '发布系列课程失败');
      }
    } catch (error) {
      console.error('❌ 发布系列课程失败:', error);
      notification.error('发布系列课程失败，请重试');
    }
  };

  // 获取发布用的系列课程列表
  const fetchSeriesForPublish = async () => {
    try {
      setPublishLoading(true);
      console.log('📝 获取发布用系列课程列表...');

      const { data: res } = await courseApi.getMarketplaceSeries({
        page: 1,
        pageSize: 50
      });

      if (res.code === 200 && res.data?.list) {
        console.log('✅ 获取发布用系列课程列表成功:', res.data.list);
        return res.data.list;
      } else {
        console.error('❌ 获取发布用系列课程列表失败:', res.msg);
        message.error(res.msg || '获取系列课程列表失败');
        return [];
      }
    } catch (error) {
      console.error('❌ 获取发布用系列课程列表异常:', error);
      message.error('获取系列课程列表失败，请重试');
      return [];
    } finally {
      setPublishLoading(false);
    }
  };

  // 获取指定系列的课程详情
  const fetchSeriesDetailForPublish = async (seriesId: number) => {
    try {
      setPublishLoading(true);
      console.log('📝 获取系列课程详情，系列ID:', seriesId);

      const { data: res } = await courseApi.getMarketplaceSeriesDetail(seriesId);

      if (res.code === 200 && res.data) {
        console.log('✅ 获取系列课程详情成功:', res.data);
        return res.data;
      } else {
        console.error('❌ 获取系列课程详情失败:', res.msg);
        message.error(res.msg || '获取系列课程详情失败');
        return null;
      }
    } catch (error) {
      console.error('❌ 获取系列课程详情异常:', error);
      message.error('获取系列课程详情失败，请重试');
      return null;
    } finally {
      setPublishLoading(false);
    }
  };

  // 获取发布弹窗的系列课程列表
  const fetchPublishSeriesList = async () => {
    try {
      setPublishFormLoading(true);
      console.log('📝 获取发布弹窗的系列课程列表...');

      const { data: res } = await courseApi.getMarketplaceSeries({
        page: 1,
        pageSize: 50
      });

      if (res.code === 200 && res.data?.list) {
        console.log('✅ 获取发布弹窗系列课程列表成功:', res.data.list);
        setPublishSeriesListForModal(res.data.list);
      } else {
        console.error('❌ 获取发布弹窗系列课程列表失败:', res.msg);
        message.error(res.msg || '获取系列课程列表失败');
      }
    } catch (error) {
      console.error('❌ 获取发布弹窗系列课程列表异常:', error);
      message.error('获取系列课程列表失败，请重试');
    } finally {
      setPublishFormLoading(false);
    }
  };

  // 获取指定系列下的子课程列表（用于发布弹窗）
  const fetchPublishCourseList = async (seriesId: number) => {
    try {
      console.log('📝 获取发布弹窗的子课程列表，系列ID:', seriesId);
      console.log('🔄 使用课程管理API获取草稿状态的子课程...');

      // 使用课程管理API获取草稿状态的课程
      const { data: res } = await courseApi.getManagementSeriesCourses(seriesId, {
        page: 1,
        pageSize: 50,
        status: 0  // 只获取草稿状态的课程
      });

      console.log('🔍 课程管理API响应:', res);

      if (res.code === 200 && res.data?.list) {
        console.log('✅ 获取草稿状态子课程列表成功，数量:', res.data.list.length);
        setPublishCourseListForModal(res.data.list);

        if (res.data.list.length === 0) {
          message.info('该系列暂无草稿状态的课程可发布');
        }
      } else {
        console.log('⚠️ 该系列暂无子课程或API调用失败');
        setPublishCourseListForModal([]);
        message.info('该系列暂无草稿状态的课程可发布');
      }

    } catch (error) {
      console.error('❌ 获取发布弹窗子课程列表异常:', error);
      message.error('获取子课程列表失败，请重试');
      setPublishCourseListForModal([]);
    }
  };

  // 处理系列选择（发布弹窗）
  const handlePublishSeriesChange = async (seriesId: number) => {
    console.log('📚 发布弹窗选择系列ID:', seriesId);
    console.log('📚 当前系列列表:', publishSeriesListForModal);

    setSelectedSeriesForPublish(seriesId);
    setSelectedCourseForPublish(undefined);
    setPublishCourseListForModal([]);

    // 重置表单中的课程选择
    publishCourseForm.setFieldsValue({ courseId: undefined });

    // 获取该系列下的子课程
    if (seriesId) {
      console.log('🔄 开始获取系列子课程...');
      setPublishFormLoading(true);

      try {
        await fetchPublishCourseList(seriesId);
        console.log('✅ 子课程获取完成');
      } catch (error) {
        console.error('❌ 子课程获取失败:', error);
      } finally {
        setPublishFormLoading(false);
      }
    }
  };

  // 处理课程选择（发布弹窗）
  const handlePublishCourseChange = (courseId: number) => {
    console.log('📖 发布弹窗选择课程ID:', courseId);
    setSelectedCourseForPublish(courseId);
  };

  // 重置发布课程弹窗状态
  const resetPublishCourseModal = () => {
    setIsPublishCourseModalVisible(false);
    setSelectedSeriesForPublish(undefined);
    setSelectedCourseForPublish(undefined);
    setPublishSeriesListForModal([]);
    setPublishCourseListForModal([]);
    publishCourseForm.resetFields();
  };

  // 打开发布课程弹窗
  const openPublishCourseModal = async () => {
    setIsPublishCourseModalVisible(true);
    await fetchPublishSeriesList();
  };

  // 发布课程
  const handlePublishCourse = async (values: any) => {
    try {
      if (!selectedCourseForPublish || !selectedSeriesForPublish) {
        message.error('请选择系列课程和子课程');
        return;
      }

      setPublishFormLoading(true);
      console.log('📢 发布课程，课程ID:', selectedCourseForPublish);
      console.log('📢 系列ID:', selectedSeriesForPublish);
      console.log('📤 表单数据:', values);

      // 获取当前选中的课程信息
      const selectedCourse = publishCourseListForModal.find(c => c.id === selectedCourseForPublish);
      if (!selectedCourse) {
        message.error('未找到选中的课程信息');
        return;
      }

      console.log('📖 当前课程信息:', selectedCourse);

      // 使用专门的发布课程API
      console.log('📤 调用发布课程API，课程ID:', selectedCourseForPublish);
      const { data: res } = await courseApi.publishCourse(selectedCourseForPublish);

      if (res.code === 200) {
        message.success('发布课程成功');
        resetPublishCourseModal();

        // 显示发布结果信息
        console.log('✅ 发布成功，课程信息:', res.data);

        // 刷新课程列表
        await fetchCourseList();

        // 如果当前系列已展开，刷新子课程列表
        if (selectedSeriesForPublish && expandedSeries.has(selectedSeriesForPublish)) {
          await fetchSeriesCourses(selectedSeriesForPublish);
        }
      } else {
        console.error('❌ 发布课程失败:', res.msg);
        message.error(res.msg || '发布课程失败');
      }
    } catch (error: any) {
      console.error('❌ 发布课程异常:', error);
      console.error('❌ 错误详情:', error.response?.data);
      message.error('发布课程失败，请重试');
    } finally {
      setPublishFormLoading(false);
    }
  };

  // 重置发布弹窗状态
  const resetPublishModal = () => {
    setIsPublishCourseModalVisible(false);
    setSelectedSeriesForPublish(undefined);
    setSelectedCourseForPublish(undefined);
    setPublishSeriesCourses([]);
    setPublishSeriesOptions([]);
    publishCourseForm.resetFields();
  };





  // 处理图片上传
  const handleImageUpload: UploadProps['customRequest'] = async (options) => {
    const { file, onSuccess, onError } = options;

    try {
      // 调用实际的OSS上传API
      const url = await uploadApi.uploadToOss(file as File);
      console.log('系列封面图片上传成功，URL:', url);

      setCoverImageUrl(url);
      onSuccess?.({ url: url });
      message.success('图片上传成功');
    } catch (error: any) {
      console.error('系列封面图片上传失败:', error);
      message.error(`上传失败: ${error.message || '请稍后重试'}`);
      onError?.(error);
    }
  };

  // 处理图片删除
  const handleImageRemove = async () => {
    setCoverImageUrl('');
    return true;
  };

  // 处理课程封面图片上传
  const handleCourseCoverUpload: UploadProps['customRequest'] = async (options) => {
    const { file, onSuccess, onError } = options;

    try {
      // 调用实际的OSS上传API
      const url = await uploadApi.uploadToOss(file as File);
      console.log('课程封面图片上传成功，URL:', url);

      setCourseCoverImageUrl(url);
      onSuccess?.({ url: url });
      message.success('课程封面上传成功');
    } catch (error: any) {
      console.error('课程封面图片上传失败:', error);
      message.error(`上传失败: ${error.message || '请稍后重试'}`);
      onError?.(error);
    }
  };

  // 处理课程封面删除
  const handleCourseCoverRemove = async () => {
    setCourseCoverImageUrl('');
    return true;
  };

  // 处理附件资源上传
  const handleAdditionalResourceUpload: UploadProps['customRequest'] = async (options) => {
    const { file, onSuccess, onError } = options;

    try {
      // 调用实际的OSS上传API
      const url = await uploadApi.uploadToOss(file as File);
      console.log('附件资源上传成功，URL:', url);

      setAdditionalFiles(prev => [...prev, url]);
      onSuccess?.({ url: url, name: (file as File).name });
      message.success(`附件 ${(file as File).name} 上传成功`);
    } catch (error: any) {
      console.error('附件资源上传失败:', error);
      message.error(`附件 ${(file as File).name} 上传失败: ${error.message || '请稍后重试'}`);
      onError?.(error);
    }
  };

  // 处理附件删除
  const handleAdditionalResourceRemove = async (file: any) => {
    const url = file.url || file.response?.url;
    setAdditionalFiles(prev => prev.filter(f => f !== url));
    return true;
  };
  // 处理视频上传
  const handleVideoUpload: UploadProps['customRequest'] = async (options) => {
    const { file, onSuccess, onError } = options;

    try {
      const url = await uploadApi.uploadToOss(file as File);
      console.log('课程视频上传成功，URL:', url);

      setCourseVideoUrl(url);
      setCourseVideoName((file as File).name);

      // 如果是视频文件，尝试获取时长
      const videoElement = document.createElement('video');
      videoElement.src = url;
      videoElement.onloadedmetadata = () => {
        setVideoDuration(Math.floor(videoElement.duration));
      };

      onSuccess?.({ url: url });
      message.success('课程视频上传成功');
    } catch (error: any) {
      console.error('课程视频上传失败:', error);
      message.error(`视频上传失败: ${error.message || '请稍后重试'}`);
      onError?.(error);
    }
  };

  // 处理视频删除
  const handleVideoRemove = async () => {
    setCourseVideoUrl('');
    setCourseVideoName('');
    setVideoDuration(0);
    return true;
  };

  // 处理文档上传
  const handleDocumentUpload: UploadProps['customRequest'] = async (options) => {
    const { file, onSuccess, onError } = options;

    try {
      const url = await uploadApi.uploadToOss(file as File);
      console.log('课程文档上传成功，URL:', url);

      setCourseDocumentUrl(url);
      setCourseDocumentName((file as File).name);
      onSuccess?.({ url: url });
      message.success('课程文档上传成功');
    } catch (error: any) {
      console.error('课程文档上传失败:', error);
      message.error(`文档上传失败: ${error.message || '请稍后重试'}`);
      onError?.(error);
    }
  };

  // 处理文档删除
  const handleDocumentRemove = async () => {
    setCourseDocumentUrl('');
    setCourseDocumentName('');
    return true;
  };
  // 教学信息管理函数
  const addTeachingInfo = () => {
    setTeachingInfoList([...teachingInfoList, { title: '', content: [''] }]);
  };

  const removeTeachingInfo = (index: number) => {
    if (teachingInfoList.length > 1) {
      const newList = teachingInfoList.filter((_, i) => i !== index);
      setTeachingInfoList(newList);
    }
  };

  const updateTeachingInfoTitle = (index: number, title: string) => {
    const newList = [...teachingInfoList];
    newList[index].title = title;
    setTeachingInfoList(newList);
  };

  const updateTeachingInfoContent = (index: number, contentIndex: number, content: string) => {
    const newList = [...teachingInfoList];
    newList[index].content[contentIndex] = content;
    setTeachingInfoList(newList);
  };

  const addTeachingInfoContent = (index: number) => {
    const newList = [...teachingInfoList];
    newList[index].content.push('');
    setTeachingInfoList(newList);
  };

  const removeTeachingInfoContent = (index: number, contentIndex: number) => {
    const newList = [...teachingInfoList];
    if (newList[index].content.length > 1) {
      newList[index].content.splice(contentIndex, 1);
      setTeachingInfoList(newList);
    }
  };







  // 打开编辑模态框
  const openEditModal = async (course: Course) => {
    setEditingCourse(course);
    editCourseForm.setFieldsValue(course);
    setIsEditCourseModalVisible(true);
  };

  // 过滤课程列表
  const filteredCourses = (courseList || []).filter(course =>
    course.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
    course.description.toLowerCase().includes(searchKeyword.toLowerCase()) ||
    course.category.toLowerCase().includes(searchKeyword.toLowerCase())
  );

  // 准备表格数据：将系列课程和子课程合并为一个扁平列表
  const prepareTableData = () => {
    const tableData: any[] = [];

    console.log('🔄 准备表格数据...');
    console.log('📊 系列课程列表:', seriesList);
    console.log('📊 展开的系列:', Array.from(expandedSeries));
    console.log('📊 子课程映射:', seriesCoursesMap);

    seriesList.forEach(series => {
      // 添加系列课程行
      tableData.push({
        key: `series-${series.id}`,
        id: series.id,
        title: series.title,
        status: series.status,
        type: 'series',
        isExpanded: expandedSeries.has(series.id),
        seriesId: series.id
      });

      // 如果系列已展开，添加子课程行
      if (expandedSeries.has(series.id)) {
        const subCourses = seriesCoursesMap.get(series.id) || [];
        console.log(`📚 系列 ${series.id} 的子课程:`, subCourses);

        subCourses.forEach(course => {
          tableData.push({
            key: `course-${course.id}`,
            id: course.id,
            title: course.title,
            status: course.status,
            type: 'course',
            seriesId: series.id,
            parentSeriesTitle: series.title
          });
        });
      }
    });

    console.log('📋 最终表格数据:', tableData);
    return tableData;
  };

  // 表格列定义
  const columns = [
    {
      title: '系列课程ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
    },
    {
      title: '系列课程/子课程名称',
      dataIndex: 'title',
      key: 'title',
      render: (text: string, record: any) => {
        if (record.type === 'series') {
          return (
            <div className="flex items-center gap-2">
              <Button
                type="text"
                size="small"
                onClick={() => toggleSeriesExpansion(record.id)}
                className="p-0 min-w-0 hover:bg-blue-50"
                style={{ minWidth: '20px', height: '20px' }}
              >
                {record.isExpanded ? '▼' : '▶'}
              </Button>
              <span className="font-medium text-blue-600 text-base">{text}</span>
              <Tag color="blue" className="text-xs">系列</Tag>
            </div>
          );
        } else {
          return (
            <div className="ml-8 flex items-center gap-2">
              <span className="text-gray-400">└─</span>
              <span className="text-gray-700">{text}</span>
              <Tag color="green" className="text-xs">子课程</Tag>
            </div>
          );
        }
      },
    },
    {
      title: '发布状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: number, record: any) => {
        const getStatusConfig = (status: number) => {
          switch (status) {
            case 1: return { color: 'green', text: '已发布' };
            case 0: return { color: 'orange', text: '草稿' };
            case 2: return { color: 'red', text: '已归档' };
            default: return { color: 'gray', text: '未知' };
          }
        };

        const config = getStatusConfig(status);
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (record: any) => {
        if (record.type === 'series') {
          return (
            <Space size="small">
              <Button
                type="link"
                size="small"
                icon={<EditOutlined />}
                onClick={() => {
                  message.info('系列课程编辑功能待实现');
                }}
              >
                编辑
              </Button>
            </Space>
          );
        } else {
          return (
            <Space size="small">
              <Button
                type="link"
                size="small"
                icon={<EditOutlined />}
                onClick={() => {
                  message.info('子课程编辑功能待实现');
                }}
                className="text-blue-600 hover:text-blue-800"
              >
                编辑
              </Button>
              <Popconfirm
                title={
                  <div>
                    <p>确定要删除这个子课程吗？</p>
                    <p className="text-gray-500 text-sm">课程名称：{record.title}</p>
                    <p className="text-gray-500 text-sm">所属系列：{record.parentSeriesTitle}</p>
                  </div>
                }
                onConfirm={() => {
                  console.log('🗑️ 用户确认删除子课程:', record);
                  handleDeleteSubCourse(record.id, record.seriesId);
                }}
                okText="确定删除"
                cancelText="取消"
                okType="danger"
              >
                <Button
                  type="link"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                  className="text-red-600 hover:text-red-800"
                >
                  删除
                </Button>
              </Popconfirm>
            </Space>
          );
        }
      },
    },
  ];
  // 获取课程标签列表 - 使用课程市场API
  const fetchCourseTags = async () => {
    try {
      console.log('🏷️ 开始获取课程标签列表...');
      const { data: res } = await courseApi.getCourseTags({
        page: 1,
        pageSize: 100, // 获取更多标签用于选择
        status: 1      // 只获取启用的标签
      });

      console.log('📨 getCourseTags API响应:', res);

      if (res.code === 200 && res.data && res.data.list) {
        const tags = res.data.list.map((tag: any) => ({
          id: tag.id,
          name: tag.name,
          color: tag.color,
          category: tag.category,
          description: tag.description || ''
        }));

        setCourseTags(tags);
        console.log('✅ 成功获取课程标签列表:', tags);
      } else {
        console.warn('⚠️ API返回数据格式异常:', res);
        setCourseTags([]);
        notification.warning('获取标签列表失败，请检查网络连接');
      }
    } catch (error) {
      console.error('❌ 获取课程标签失败:', error);
      setCourseTags([]);
      notification.error('获取标签列表失败，请重试');
    }
  };

  // 获取课程系列列表 - 使用课程市场API
  const fetchCourseSeries = async () => {
    try {
      console.log('🔄 开始获取课程市场系列课程列表...');
      const { data: res } = await courseApi.getMarketplaceSeries({
        page: 1,
        pageSize: 50 // 课程市场API限制最大50
      });

      console.log('📨 getMarketplaceSeries API响应:', res);

      // 检查是否有更多数据
      if (res.data?.pagination?.total > 50) {
        console.log(`⚠️ 注意：总共有 ${res.data.pagination.total} 个系列课程，当前只显示前50个`);
      }

      if (res.code === 200 && res.data) {
        console.log('📊 API返回的完整数据结构:', res.data);

        if (res.data.list && Array.isArray(res.data.list)) {
          console.log(`📋 获取到 ${res.data.list.length} 个系列课程`);

          // 将课程市场API返回的数据转换为组件需要的格式
          const formattedSeries = res.data.list.map((item: any, index: number) => {
            console.log(`🔍 处理第 ${index + 1} 个系列:`, {
              id: item.id,
              title: item.title,
              category: item.category,
              categoryLabel: item.categoryLabel,
              tags: item.tags
            });

            return {
              id: item.id,
              title: item.title,
              description: item.description,
              coverImage: item.coverImage || '',
              category: item.categoryLabel || (item.category === 0 ? '官方' : '社区'), // 使用categoryLabel或转换category
              teacherIds: [], // 课程市场API不返回teacherIds
              tagIds: item.tags?.map((tag: any) => tag.id) || [], // 从tags数组中提取ID
              createdAt: item.createdAt || new Date().toISOString(),
              updatedAt: item.updatedAt || new Date().toISOString()
            };
          });

          setCourseSeries(formattedSeries);
          console.log('✅ 成功获取系列课程列表:', formattedSeries);
        } else {
          console.warn('⚠️ API返回数据中没有list字段或list不是数组:', res.data);
          setCourseSeries([]);
        }
      } else {
        console.warn('⚠️ API返回数据格式异常:', {
          code: res.code,
          message: res.message,
          data: res.data
        });
        setCourseSeries([]);
      }
    } catch (error) {
      console.error('❌ 获取课程系列失败:', error);
      setCourseSeries([]);
      notification.error('获取系列课程列表失败，请重试');
    }
  };

  useEffect(() => {
    fetchCourseList();

    fetchCourseTags();
    fetchCourseSeries();
  }, []);

  return (
    <>
      <Card
        title="课程管理"
        extra={<Button type="primary" onClick={() => {
          fetchCourseList();
          setIsCourseModalVisible(true);
        }}>查看全部</Button>}
        className="shadow-sm"
      >
        <div className="space-y-4">
          <Button block onClick={() => setIsAddCourseModalVisible(true)}>
            添加课程
          </Button>
          <Button block onClick={() => setIsAddSeriesModalVisible(true)}>
            添加系列课程
          </Button>
          <Button block onClick={() => setIsAddTagModalVisible(true)} type="dashed">
            添加课程标签
          </Button>
          <Button block onClick={openPublishCourseModal} style={{ backgroundColor: 'white', borderColor: '#d9d9d9', color: '#000000d9' }}>
            发布课程
          </Button>
          <Button block onClick={() => setIsPublishSeriesModalVisible(true)} style={{ backgroundColor: 'white', borderColor: '#d9d9d9', color: '#000000d9' }}>
            发布系列课程
          </Button>
        </div>
      </Card>

      {/* 课程管理主模态框 */}
      <Modal
        title="课程管理"
        open={isCourseModalVisible}
        onCancel={() => setIsCourseModalVisible(false)}
        footer={null}
        width={1000}
      >
        <div className="mb-4">
          <div className="flex justify-between items-center mb-3">
            <Search
              placeholder="搜索系列课程名称"
              allowClear
              style={{ width: 300 }}
              onSearch={setSearchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
            />
            <div className="flex gap-2">
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setIsAddCourseModalVisible(true)}
              >
                添加课程
              </Button>
              <Button
                type="default"
                icon={<PlusOutlined />}
                onClick={() => setIsAddSeriesModalVisible(true)}
              >
                添加系列课程
              </Button>
            </div>
          </div>

          {/* 统计信息和快捷操作 */}
          <div className="flex justify-between items-center bg-gray-50 p-3 rounded">
            <div className="flex gap-4 text-sm text-gray-600">
              <span>系列课程总数: <strong className="text-blue-600">{seriesList.length}</strong></span>
              <span>已展开系列: <strong className="text-green-600">{expandedSeries.size}</strong></span>
              <span>已加载子课程: <strong className="text-orange-600">
                {Array.from(seriesCoursesMap.values()).reduce((total, courses) => total + courses.length, 0)}
              </strong></span>
            </div>

            <div className="flex gap-2">
              <Button
                size="small"
                type="text"
                onClick={expandAllSeries}
                disabled={seriesLoading}
                className="text-blue-600 hover:text-blue-800"
              >
                展开所有
              </Button>
              <Button
                size="small"
                type="text"
                onClick={collapseAllSeries}
                disabled={seriesLoading}
                className="text-gray-600 hover:text-gray-800"
              >
                收起所有
              </Button>
            </div>
          </div>
        </div>

        <Table
          columns={columns}
          dataSource={prepareTableData()}
          rowKey="key"
          loading={seriesLoading}
          pagination={{
            pageSize: 20,
            showSizeChanger: false,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          size="small"
        />
      </Modal>

      {/* 添加课程模态框 */}
      <Modal
        title="添加课程"
        open={isAddCourseModalVisible}
        onCancel={() => {
          setIsAddCourseModalVisible(false);
          addCourseForm.resetFields();
          setCourseCoverImageUrl('');
          setAdditionalFiles([]);
          setCourseVideoUrl('');
          setCourseVideoName('');
          setCourseDocumentUrl('');
          setCourseDocumentName('');
          setVideoDuration(0);
          setTeachingInfoList([{ title: '', content: [''] }]);
        }}
        onOk={() => addCourseForm.submit()}
        okText="确定"
        cancelText="取消"
      >
        <Form
          form={addCourseForm}
          layout="vertical"
          onFinish={handleAddCourse}
        >
          <Form.Item
            name="seriesId"
            label="所属系列课程"
            rules={[{ required: true, message: '请选择所属系列课程' }]}
          >
            <Select
              placeholder="请选择系列课程"
              showSearch
              optionFilterProp="children"
              style={{ width: '100%' }}
            >
              {courseSeries.map(series => (
                <Option key={series.id} value={series.id} title={`${series.title} - ${series.description}`}>
                  <div style={{
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    maxWidth: '100%'
                  }}>
                    <span style={{ fontWeight: 500 }}>{series.title}</span>
                    <span style={{ fontSize: '12px', color: '#666', marginLeft: '8px' }}>
                      ({series.category})
                    </span>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="title"
            label="课程名称"
            rules={[{ required: true, message: '请输入课程名称' }]}
          >
            <Input placeholder="请输入课程名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="课程描述"
            rules={[{ required: true, message: '请输入课程描述' }]}
          >
            <Input.TextArea
              rows={4}
              placeholder="请详细描述课程内容、目标和特色..."
              showCount
              maxLength={500}
            />
          </Form.Item>

          <Form.Item
            label="课程封面"
            rules={[{ required: true, message: '请上传课程封面' }]}
          >
            <Upload.Dragger
              name="courseCover"
              customRequest={handleCourseCoverUpload}
              onRemove={handleCourseCoverRemove}
              accept="image/*"
              maxCount={1}
              listType="picture"
            >
              {courseCoverImageUrl ? (
                <div>
                  <img src={courseCoverImageUrl} alt="课程封面预览" style={{ width: '100%', maxHeight: '200px', objectFit: 'cover' }} />
                </div>
              ) : (
                <div>
                  <p className="ant-upload-drag-icon">
                    <InboxOutlined />
                  </p>
                  <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                  <p className="ant-upload-hint">
                    支持单个文件上传，建议上传jpg、png格式图片，大小不超过2MB
                  </p>
                </div>
              )}
            </Upload.Dragger>
          </Form.Item>

          <Form.Item
            name="orderIndex"
            label="课程序号"
            rules={[
              { required: true, message: '请输入课程序号' },
              {
                type: 'number',
                min: 0,
                message: '课程序号必须大于等于0',
                transform: (value) => Number(value)
              }
            ]}
            tooltip="在系列课程中的排序位置，数字越小排序越靠前，从0开始"
          >
            <Input type="number" placeholder="请输入课程在系列中的序号（从0开始）" min={0} />
          </Form.Item>



          {/* 视频上传 */}
          <Form.Item
            label="课程视频"
            tooltip="上传课程视频文件，系统将自动识别时长等信息"
          >
            <Upload.Dragger
              name="courseVideo"
              customRequest={handleVideoUpload}
              onRemove={handleVideoRemove}
              accept="video/*"
              maxCount={1}
              listType="picture"
            >
              {courseVideoUrl ? (
                <div>
                  <video
                    src={courseVideoUrl}
                    style={{ width: '100%', maxHeight: '200px' }}
                    controls
                  />
                  <p style={{ marginTop: 8, color: '#666' }}>
                    {courseVideoName || '课程视频'}
                  </p>
                </div>
              ) : (
                <div>
                  <p className="ant-upload-drag-icon">
                    <InboxOutlined />
                  </p>
                  <p className="ant-upload-text">点击或拖拽视频文件到此区域上传</p>
                  <p className="ant-upload-hint">
                    支持MP4、AVI、MOV等格式，大小不超过100MB
                  </p>
                </div>
              )}
            </Upload.Dragger>
          </Form.Item>

          {/* 文档上传 */}
          <Form.Item
            label="课程文档"
            tooltip="上传课程相关文档，如PPT、PDF等"
          >
            <Upload.Dragger
              name="courseDocument"
              customRequest={handleDocumentUpload}
              onRemove={handleDocumentRemove}
              accept=".pdf,.doc,.docx,.ppt,.pptx"
              maxCount={1}
              listType="picture"
            >
              {courseDocumentUrl ? (
                <div>
                  <div style={{ padding: '20px', textAlign: 'center' }}>
                    <InboxOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
                    <p style={{ marginTop: 8, color: '#666' }}>
                      {courseDocumentName || '课程文档'}
                    </p>
                  </div>
                </div>
              ) : (
                <div>
                  <p className="ant-upload-drag-icon">
                    <InboxOutlined />
                  </p>
                  <p className="ant-upload-text">点击或拖拽文档文件到此区域上传</p>
                  <p className="ant-upload-hint">
                    支持PDF、Word、PPT格式，大小不超过50MB
                  </p>
                </div>
              )}
            </Upload.Dragger>
          </Form.Item>



          {/* 教学信息 */}
          <Form.Item
            label="教学信息"
            tooltip="课程的教学目标、方法等信息"
          >
            <div style={{ border: '1px solid #d9d9d9', borderRadius: '6px', padding: '16px' }}>
              {teachingInfoList.map((info, index) => (
                <Card
                  key={index}
                  size="small"
                  title={`教学信息 ${index + 1}`}
                  extra={
                    teachingInfoList.length > 1 && (
                      <Button
                        type="link"
                        danger
                        size="small"
                        onClick={() => removeTeachingInfo(index)}
                      >
                        删除
                      </Button>
                    )
                  }
                  style={{ marginBottom: index < teachingInfoList.length - 1 ? '12px' : '0' }}
                >
                  <div style={{ marginBottom: '12px' }}>
                    <label style={{ display: 'block', marginBottom: '4px', fontWeight: 500 }}>
                      标题:
                    </label>
                    <Input
                      placeholder="请输入教学信息标题，如：教学目标、教学方法等"
                      value={info.title}
                      onChange={(e) => updateTeachingInfoTitle(index, e.target.value)}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '4px', fontWeight: 500 }}>
                      内容:
                    </label>
                    {info.content.map((content, contentIndex) => (
                      <div key={contentIndex} style={{ display: 'flex', marginBottom: '8px', alignItems: 'center' }}>
                        <Input
                          placeholder="请输入具体内容"
                          value={content}
                          onChange={(e) => updateTeachingInfoContent(index, contentIndex, e.target.value)}
                          style={{ flex: 1, marginRight: '8px' }}
                        />
                        {info.content.length > 1 && (
                          <Button
                            type="link"
                            danger
                            size="small"
                            onClick={() => removeTeachingInfoContent(index, contentIndex)}
                          >
                            删除
                          </Button>
                        )}
                      </div>
                    ))}
                    <Button
                      type="dashed"
                      size="small"
                      onClick={() => addTeachingInfoContent(index)}
                      style={{ width: '100%' }}
                    >
                      + 添加内容项
                    </Button>
                  </div>
                </Card>
              ))}

              <Button
                type="dashed"
                onClick={addTeachingInfo}
                style={{ width: '100%', marginTop: '12px' }}
              >
                + 添加教学信息组
              </Button>
            </div>
          </Form.Item>

          <Form.Item
            label="附件资源"
            tooltip="上传课程相关的附件资源，如PPT、文档、代码等"
          >
            <Upload
              name="additionalResources"
              customRequest={handleAdditionalResourceUpload}
              onRemove={handleAdditionalResourceRemove}
              multiple
              accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.zip,.rar,.txt"
            >
              <Button icon={<UploadOutlined />}>上传附件资源</Button>
            </Upload>
            <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
              支持上传PDF、Office文档、压缩包等格式文件，单个文件不超过10MB
            </div>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑课程模态框 */}
      <Modal
        title="编辑课程"
        open={isEditCourseModalVisible}
        onCancel={() => {
          setIsEditCourseModalVisible(false);
          setEditingCourse(null);
          editCourseForm.resetFields();
        }}
        onOk={() => editCourseForm.submit()}
        okText="确定"
        cancelText="取消"
      >
        <Form
          form={editCourseForm}
          layout="vertical"
          onFinish={handleEditCourse}
        >
          <Form.Item
            name="name"
            label="课程名称"
            rules={[{ required: true, message: '请输入课程名称' }]}
          >
            <Input placeholder="请输入课程名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="课程描述"
            rules={[{ required: true, message: '请输入课程描述' }]}
          >
            <Input.TextArea rows={3} placeholder="请输入课程描述" />
          </Form.Item>

          <Form.Item
            name="category"
            label="课程分类"
            rules={[{ required: true, message: '请选择课程分类' }]}
          >
            <Select placeholder="请选择课程分类">
              <Option value="编程基础">编程基础</Option>
              <Option value="编程进阶">编程进阶</Option>
              <Option value="算法思维">算法思维</Option>
              <Option value="项目实战">项目实战</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="status"
            label="课程状态"
            rules={[{ required: true, message: '请选择课程状态' }]}
          >
            <Select>
              <Option value="active">启用</Option>
              <Option value="inactive">禁用</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 添加系列课程模态框 */}
      <Modal
        title="创建系列课程"
        open={isAddSeriesModalVisible}
        onCancel={() => {
          setIsAddSeriesModalVisible(false);
          addSeriesForm.resetFields();
          setCoverImageUrl('');
        }}
        onOk={() => addSeriesForm.submit()}
        okText="创建系列课程"
        cancelText="取消"
        width={800}
      >
        <Form
          form={addSeriesForm}
          layout="vertical"
          onFinish={handleAddSeries}
        >
          <Form.Item
            name="title"
            label="系列课程名称"
            rules={[{ required: true, message: '请输入系列课程名称' }]}
          >
            <Input placeholder="例如：React全栈开发实战" />
          </Form.Item>

          <Form.Item
            name="description"
            label="课程介绍"
            rules={[{ required: true, message: '请输入课程介绍' }]}
          >
            <Input.TextArea
              rows={4}
              placeholder="请详细描述系列课程的内容、目标和特色..."
              showCount
              maxLength={500}
            />
          </Form.Item>

          <Form.Item
            label="封面图片"
            rules={[{ required: true, message: '请上传封面图片' }]}
          >
            <Upload.Dragger
              name="coverImage"
              customRequest={handleImageUpload}
              onRemove={handleImageRemove}
              accept="image/*"
              maxCount={1}
              listType="picture"
            >
              {coverImageUrl ? (
                <div>
                  <img src={coverImageUrl} alt="封面预览" style={{ width: '100%', maxHeight: '200px', objectFit: 'cover' }} />
                </div>
              ) : (
                <div>
                  <p className="ant-upload-drag-icon">
                    <InboxOutlined />
                  </p>
                  <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                  <p className="ant-upload-hint">
                    支持单个文件上传，建议上传jpg、png格式图片，大小不超过2MB
                  </p>
                </div>
              )}
            </Upload.Dragger>
          </Form.Item>



          <Form.Item
            name="category"
            label="是否为官方系列课程"
            rules={[{ required: true, message: '请选择是否为官方系列课程' }]}
            initialValue={0}
          >
            <Select placeholder="请选择">
              <Option value={1}>是（官方）</Option>
              <Option value={0}>否（社区）</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="projectMembers"
            label="课程成员"
            rules={[{ required: true, message: '请输入课程成员' }]}
          >
            <Input
              placeholder="请输入课程成员，如：王老师、李助教、张同学"
              showCount
              maxLength={200}
            />
          </Form.Item>

          <Form.Item
            name="tagIds"
            label="标签选择"
            rules={[{ required: true, message: '请选择标签' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择相关标签"
              optionLabelProp="label"
            >
              {courseTags.map(tag => (
                <Option key={tag.id} value={tag.id} label={tag.name}>
                  <Tag color={tag.color}>{tag.name}</Tag>
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 添加课程标签模态框 */}
      <Modal
        title="创建课程标签"
        open={isAddTagModalVisible}
        onCancel={() => {
          setIsAddTagModalVisible(false);
          addTagForm.resetFields();
        }}
        onOk={() => addTagForm.submit()}
        okText="创建标签"
        cancelText="取消"
        width={600}
      >
        <Form
          form={addTagForm}
          layout="vertical"
          onFinish={handleAddTag}
        >
          <Form.Item
            name="name"
            label="标签名称"
            rules={[
              { required: true, message: '请输入标签名称' },
              { max: 20, message: '标签名称不能超过20个字符' }
            ]}
          >
            <Input placeholder="例如：高级、编程、实战" />
          </Form.Item>

          <Form.Item
            name="color"
            label="标签颜色"
            rules={[{ required: true, message: '请选择标签颜色' }]}
            initialValue="#007bff"
          >
            <Select placeholder="请选择标签颜色">
              <Option value="#007bff">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 rounded" style={{ backgroundColor: '#007bff' }}></div>
                  蓝色 (#007bff)
                </div>
              </Option>
              <Option value="#28a745">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 rounded" style={{ backgroundColor: '#28a745' }}></div>
                  绿色 (#28a745)
                </div>
              </Option>
              <Option value="#dc3545">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 rounded" style={{ backgroundColor: '#dc3545' }}></div>
                  红色 (#dc3545)
                </div>
              </Option>
              <Option value="#ffc107">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 rounded" style={{ backgroundColor: '#ffc107' }}></div>
                  黄色 (#ffc107)
                </div>
              </Option>
              <Option value="#6f42c1">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 rounded" style={{ backgroundColor: '#6f42c1' }}></div>
                  紫色 (#6f42c1)
                </div>
              </Option>
              <Option value="#fd7e14">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 rounded" style={{ backgroundColor: '#fd7e14' }}></div>
                  橙色 (#fd7e14)
                </div>
              </Option>
              <Option value="#20c997">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 rounded" style={{ backgroundColor: '#20c997' }}></div>
                  青色 (#20c997)
                </div>
              </Option>
              <Option value="#6c757d">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 rounded" style={{ backgroundColor: '#6c757d' }}></div>
                  灰色 (#6c757d)
                </div>
              </Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="category"
            label="标签分类"
            rules={[{ required: true, message: '请选择标签分类' }]}
            initialValue={1}
          >
            <Select placeholder="请选择标签分类">
              <Option value={0}>难度标签</Option>
              <Option value={1}>类型标签</Option>
              <Option value={2}>特色标签</Option>
              <Option value={3}>其他标签</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="标签描述"
            rules={[{ max: 100, message: '标签描述不能超过100个字符' }]}
          >
            <Input.TextArea
              rows={3}
              placeholder="请输入标签的详细描述..."
              showCount
              maxLength={100}
            />
          </Form.Item>

          <Form.Item
            name="status"
            label="标签状态"
            rules={[{ required: true, message: '请选择标签状态' }]}
            initialValue={1}
          >
            <Select placeholder="请选择标签状态">
              <Option value={1}>启用</Option>
              <Option value={0}>禁用</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 发布系列课程模态框 */}
      <Modal
        title="发布系列课程"
        open={isPublishSeriesModalVisible}
        onCancel={() => {
          setIsPublishSeriesModalVisible(false);
          publishSeriesForm.resetFields();
        }}
        onOk={() => publishSeriesForm.submit()}
        okText="发布系列"
        cancelText="取消"
        width={600}
      >
        <Form
          form={publishSeriesForm}
          layout="vertical"
          onFinish={handlePublishSeries}
        >
          <Form.Item
            name="seriesId"
            label="选择要发布的系列课程"
            rules={[{ required: true, message: '请选择要发布的系列课程' }]}
          >
            <Select
              placeholder="请选择系列课程"
              showSearch
              filterOption={(input, option) =>
                (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
              }
            >
              {courseSeries.map((series) => (
                <Option key={series.id} value={series.id}>
                  {series.title} ({series.category})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="publishNote"
            label="发布说明"
            rules={[{ required: false }]}
          >
            <Input.TextArea
              placeholder="请输入发布说明（可选）"
              rows={3}
              maxLength={200}
              showCount
            />
          </Form.Item>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-sm font-medium text-gray-700 mb-2">发布说明：</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 发布后系列课程将在课程市场中公开显示</li>
              <li>• 只有已完成的课程才会被发布</li>
              <li>• 发布后可以查看详细的发布统计信息</li>
              <li>• 发布状态可以随时修改</li>
            </ul>
          </div>
        </Form>
      </Modal>

      {/* 发布课程模态框 */}
      <Modal
        title="发布课程"
        open={isPublishCourseModalVisible}
        onCancel={resetPublishCourseModal}
        footer={null}
        width={700}
        destroyOnClose
      >
        <Form
          form={publishCourseForm}
          layout="vertical"
          onFinish={handlePublishCourse}
          className="mt-4"
        >
          {/* 第一个下拉框：选择系列课程 */}
          <Form.Item
            name="seriesId"
            label="选择系列课程"
            rules={[{ required: true, message: '请选择系列课程' }]}
          >
            <Select
              placeholder="请选择系列课程"
              loading={publishFormLoading}
              onChange={handlePublishSeriesChange}
              showSearch
              filterOption={(input, option) =>
                (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
              }
            >
              {publishSeriesListForModal.map(series => (
                <Option key={series.id} value={series.id}>
                  {series.title} (ID: {series.id})
                </Option>
              ))}
            </Select>
          </Form.Item>

          {/* 第二个下拉框：选择子课程 */}
          <Form.Item
            name="courseId"
            label="选择子课程"
            rules={[{ required: true, message: '请选择要发布的子课程' }]}
          >
            <Select
              placeholder={selectedSeriesForPublish ? "请选择要发布的子课程" : "请先选择系列课程"}
              disabled={!selectedSeriesForPublish}
              loading={publishFormLoading && !!selectedSeriesForPublish}
              onChange={handlePublishCourseChange}
              showSearch
              filterOption={(input, option) =>
                (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
              }
              notFoundContent={
                publishFormLoading && selectedSeriesForPublish
                  ? "正在加载子课程..."
                  : selectedSeriesForPublish
                    ? "该系列暂无子课程"
                    : "请先选择系列课程"
              }
            >
              {publishCourseListForModal.length > 0 ? (
                publishCourseListForModal.map(course => (
                  <Option key={course.id} value={course.id}>
                    <div className="flex justify-between items-center">
                      <span>{course.title}</span>
                      <div className="flex gap-2">
                        <Tag color={course.status === 1 ? 'green' : course.status === 0 ? 'orange' : 'red'} className="text-xs">
                          {course.status === 1 ? '已发布' : course.status === 0 ? '草稿' : '已归档'}
                        </Tag>
                        <span className="text-gray-400 text-xs">ID: {course.id}</span>
                      </div>
                    </div>
                  </Option>
                ))
              ) : (
                selectedSeriesForPublish && !publishFormLoading ? (
                  <Option disabled value="no-courses">
                    该系列暂无子课程
                  </Option>
                ) : null
              )}
            </Select>
          </Form.Item>

          {/* 调试信息显示 */}
          <div className="bg-gray-50 p-3 rounded-lg mb-4 text-xs">
            <h4 className="font-medium text-gray-700 mb-2">调试信息</h4>
            <div className="space-y-1 text-gray-600">
              <p>已选择系列ID: {selectedSeriesForPublish || '未选择'}</p>
              <p>已选择课程ID: {selectedCourseForPublish || '未选择'}</p>
              <p>系列列表数量: {publishSeriesListForModal.length}</p>
              <p>子课程列表数量: {publishCourseListForModal.length}</p>
              <p>加载状态: {publishFormLoading ? '加载中' : '空闲'}</p>
              {publishCourseListForModal.length > 0 && (
                <div>
                  <p>子课程列表:</p>
                  <ul className="ml-4 list-disc">
                    {publishCourseListForModal.map(course => (
                      <li key={course.id}>ID: {course.id}, 名称: {course.title}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>

          {/* 选中课程信息显示 */}
          {selectedCourseForPublish && (
            <div className="bg-blue-50 p-4 rounded-lg mb-4">
              <h4 className="text-sm font-medium text-blue-900 mb-2">即将发布的课程</h4>
              <div className="text-sm text-blue-700">
                <p>课程ID: {selectedCourseForPublish}</p>
                <p>所属系列: {publishSeriesListForModal.find(s => s.id === selectedSeriesForPublish)?.title}</p>
                <p>课程名称: {publishCourseListForModal.find(c => c.id === selectedCourseForPublish)?.title}</p>
                <p className="mt-2 text-blue-600 font-medium">点击"发布此课程"将调用发布API：POST /api/v1/course-management/courses/{selectedCourseForPublish}/publish</p>
              </div>
            </div>
          )}

          <div className="flex justify-between items-center pt-4 border-t">
            <div className="text-sm text-gray-500">
              {selectedCourseForPublish ? (
                <span className="text-green-600">✓ 已选择课程，可以发布</span>
              ) : (
                <span>请先选择系列课程和子课程</span>
              )}
            </div>
            <div className="flex gap-2">
              <Button onClick={resetPublishCourseModal}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={publishFormLoading}
                disabled={!selectedCourseForPublish}
                className={selectedCourseForPublish ? 'bg-green-600 hover:bg-green-700 border-green-600' : ''}
              >
                {publishFormLoading ? '发布中...' : '发布此课程'}
              </Button>
            </div>
          </div>
        </Form>
      </Modal>
    </>
  );
};

export default CourseManagement;
